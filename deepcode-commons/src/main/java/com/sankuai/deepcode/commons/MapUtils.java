package com.sankuai.deepcode.commons;


import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.deepcode.commons.function.OverrideBinaryOperator;
import org.apache.commons.collections4.SetUtils;
import org.apache.commons.collections4.keyvalue.MultiKey;
import org.apache.commons.collections4.map.MultiKeyMap;

import java.util.*;
import java.util.function.BinaryOperator;
import java.util.function.Function;
import java.util.function.Supplier;
import java.util.stream.Collectors;

/**
 * Package: com.sankuai.banma.qa.util
 * Description:
 *
 * <AUTHOR>
 * @since 2020/12/23 12:32
 */
public class MapUtils {
    /**
     * 操作 Map 的 put 时，忽略空值的操作
     *
     * @param map   需要操作的 map
     * @param key   需要放入的 Key 名称
     * @param value 需要放入的 Value 的值
     * @param <K>   Key 的类型
     * @param <V>   Value 的类型
     */
    public static <K, V> void putIfNotNullValue(Map<K, V> map, K key, V value) {
        if (map == null) {
            return;
        }
        if (value != null) {
            map.put(key, value);
        }
    }

    /**
     * 将一个 Map 作为基准，把另一个 Map 的非空值，放入基准 Map 并返回
     *
     * @param targetMap 需要操作的基准 Map
     * @param append    需要存入基准 Map 的对象
     * @param <K>       Key 的类型
     * @param <V>       Value 的类型
     */
    public static <K, V> void putIfNotNullValue(Map<K, V> targetMap, Map<? extends K, ? extends V> append) {
        if (append == null || targetMap == null) {
            return;
        }
        targetMap.putAll(append.entrySet().stream().filter(e -> Objects.nonNull(e.getValue())).collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue)));
    }

    /**
     * 获取一个 Map 的值，如果值为空，则调用 supplier 产生一个值
     *
     * @param map      需要操作的map
     * @param key      获取 的key
     * @param supplier 如果获取为空时，需要执行的函数
     * @param <K>      key的类型
     * @param <V>      返回值的类型
     * @return
     */
    public static <K, V> V getOrSupply(Map<K, V> map, K key, Supplier<V> supplier) {
        return Optional.ofNullable(map.get(key)).orElseGet(supplier);
    }

    /**
     * 静态工具，转换集合成为一个Map
     *
     * @param collection
     * @param keyMapper
     * @param valueMapper
     * @param mergeFunction
     * @param <T>
     * @param <K>
     * @param <U>
     * @return
     */
    public static <T, K, U> Map<K, U> toMap(Collection<? extends T> collection, Function<? super T, ? extends K> keyMapper, Function<? super T, ? extends U> valueMapper, BinaryOperator<U> mergeFunction) {
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(collection)) {
            return Maps.newHashMap();
        }
        if (keyMapper == null || valueMapper == null || mergeFunction == null) {
            return Maps.newHashMap();
        }
        return collection.stream().collect(Collectors.toMap(keyMapper, valueMapper, mergeFunction));
    }

    @SuppressWarnings("unchecked")
    public static <T, K, U> Map<K, U> toMap(Collection<? extends T> collection, Function<? super T, ? extends K> keyMapper) {
        return (Map<K, U>) toMap(collection, keyMapper, Function.identity(), OverrideBinaryOperator::keepFirst);
    }

    public static <K, V> MultiKeyMap<K, V> toMultiKeyMap(Map<K, V> map, Function<? super V, ? extends K> keyMapper) {
        if (map == null) {
            return new MultiKeyMap<>();
        }
        MultiKeyMap<K, V> result = new MultiKeyMap<>();
        map.forEach((key, value) -> result.put(key, keyMapper.apply(value), value));
        return result;
    }


    public static <K, V> Map<K, V> difference(Map<K, V> leftMap, Map<K, V> rightMap) {
        Map<K, V> result = Maps.newHashMap();
        if (leftMap == null) {
            return result;
        }
        if (rightMap == null) {
            return Maps.newHashMap(leftMap);
        }
        for (K key : SetUtils.difference(leftMap.keySet(), rightMap.keySet())) {
            result.put(key, leftMap.get(key));
        }

        return result;
    }

    public static <K, V> Map<K, V> difference(Map<K, V> leftMap, Map<K, V> rightMap, Function<? super V, ? extends K> keyMapper) {
        Map<K, V> result = Maps.newHashMap();
        if (leftMap == null) {
            return result;
        }
        if (rightMap == null) {
            return Maps.newHashMap(leftMap);
        }
        MultiKeyMap<K, V> leftMultiKeyMap = toMultiKeyMap(leftMap, keyMapper);
        MultiKeyMap<K, V> rightMultiKeyMap = toMultiKeyMap(rightMap, keyMapper);
        MultiKeyMap<K, V> temp = new MultiKeyMap<>();
        for (MultiKey<? extends K> key : SetUtils.difference(leftMultiKeyMap.keySet(), rightMultiKeyMap.keySet())) {
            temp.put(key, leftMultiKeyMap.get(key));
        }

        return temp.entrySet().stream().collect(Collectors.toMap(e -> e.getKey().getKey(0), Map.Entry::getValue, (v1, v2) -> v1));
    }

    public static <K, V> List<V> differenceValues(Map<K, V> leftMap, Map<K, V> rightMap) {
        List<V> result = Lists.newArrayList();
        if (leftMap == null) {
            return result;
        }
        if (rightMap == null) {
            return Lists.newArrayList(leftMap.values());
        }
        for (K key : SetUtils.difference(leftMap.keySet(), rightMap.keySet())) {
            result.add(leftMap.get(key));
        }

        return result;
    }

    public static <K, V> List<V> differenceValues(Map<K, V> leftMap, Map<K, V> rightMap, Function<? super V, ? extends K> keyMapper) {
        List<V> result = Lists.newArrayList();
        if (leftMap == null) {
            return result;
        }
        if (rightMap == null) {
            return Lists.newArrayList(leftMap.values());
        }
        MultiKeyMap<K, V> leftMultiKeyMap = toMultiKeyMap(leftMap, keyMapper);
        MultiKeyMap<K, V> rightMultiKeyMap = toMultiKeyMap(rightMap, keyMapper);
        for (MultiKey<? extends K> key : SetUtils.difference(leftMultiKeyMap.keySet(), rightMultiKeyMap.keySet())) {
            result.add(leftMultiKeyMap.get(key));
        }

        return result;
    }

    public static <K, V> Map<K, V> union(Map<K, V> leftMap, Map<K, V> rightMap) {
        if (leftMap == null) {
            return rightMap;
        }
        if (rightMap == null) {
            return leftMap;
        }
        Map<K, V> result = Maps.newHashMap();
        for (K key : SetUtils.union(leftMap.keySet(), rightMap.keySet())) {
            if (leftMap.containsKey(key)) {
                result.put(key, leftMap.get(key));
            } else {
                result.put(key, rightMap.get(key));
            }
        }
        return result;
    }

    public static <K, V> List<V> unionValues(Map<K, V> leftMap, Map<K, V> rightMap) {
        if (leftMap == null) {
            return Lists.newArrayList(rightMap.values());
        }
        if (rightMap == null) {
            return Lists.newArrayList(leftMap.values());
        }
        List<V> result = Lists.newArrayList();
        for (K key : SetUtils.union(leftMap.keySet(), rightMap.keySet())) {
            if (leftMap.containsKey(key)) {
                result.add(leftMap.get(key));
            } else {
                result.add(rightMap.get(key));
            }
        }
        return result;
    }

    public static <K, V> Map<K, V> intersection(Map<K, V> leftMap, Map<K, V> rightMap) {
        if (leftMap == null || rightMap == null) {
            return Maps.newHashMap();
        }
        Map<K, V> result = Maps.newHashMap();
        for (K key : SetUtils.intersection(leftMap.keySet(), rightMap.keySet())) {
            // 交集只取一个map中的就可以
            result.put(key, leftMap.get(key));
        }

        return result;
    }

    public static <K, V> List<V> intersectionValues(Map<K, V> leftMap, Map<K, V> rightMap) {
        List<V> result = Lists.newArrayList();
        if (leftMap == null || rightMap == null) {
            return result;
        }
        for (K key : SetUtils.intersection(leftMap.keySet(), rightMap.keySet())) {
            // 交集只取一个map中的就可以
            result.add(leftMap.get(key));
        }

        return result;
    }
}
