package com.sankuai.deepcode.commons;


import com.meituan.mtrace.Tracer;
import com.sankuai.deepcode.commons.ex.BizException;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;

@Setter
@Getter
@ToString
public class CommonResult<T> implements Serializable {

    /**
     * serialVersionUID:.
     */
    private static final long serialVersionUID = -7268040542410707954L;

    /**
     * 是否成功
     */
    private boolean success = false;

    /**
     * 返回信息
     */
    private String message;

    /**
     * 装在数据
     */
    private T data;

    /**
     * 错误代码
     */
    private int code;

    /**
     * traceId
     */
    private String traceId;

    /**
     * 默认构造器
     */
    public CommonResult() {
        this.traceId = Tracer.id();
    }

    /**
     * @param success 是否成功
     * @param message 返回的消息
     */
    public CommonResult(boolean success, String message) {
        this();
        this.success = success;
        this.message = message;
    }

    /**
     * @param success 是否成功
     */
    public CommonResult(boolean success) {
        this();
        this.success = success;
    }

    /**
     * @param code    error code
     * @param message success or error messages
     */
    public CommonResult(int code, String message) {
        this();
        this.code = code;
        this.message = message;
    }

    /**
     * @param success 是否成功
     * @param message 消息
     * @param data    数据
     */
    public CommonResult(boolean success, String message, T data) {
        this();
        this.success = success;
        this.message = message;
        this.data = data;
    }

    public CommonResult(ICodeEnum errorCodeEnum) {
        this(errorCodeEnum.getCode(), errorCodeEnum.getDesc());
    }

    public static <T> CommonResult<T> success() {
        CommonResult<T> result = new CommonResult<>(200, "成功");
        result.setSuccess(true);
        return result;
    }

    public static <T> CommonResult<T> success(T data) {
        CommonResult<T> result = success();
        result.setData(data);
        return result;
    }

    public static <T> CommonResult<T> success(T data, String customMessage) {
        CommonResult<T> result = success(data);
        result.setMessage(customMessage);
        return result;
    }

    public static <T> CommonResult<T> fail(int code, String msg) {
        CommonResult<T> result = new CommonResult<>(code, msg);
        result.setSuccess(false);
        return result;
    }

    public static <T> CommonResult<T> fail(String msg) {
        return fail(500, msg);
    }

    public static <T> CommonResult<T> fail(ICodeEnum errorCodeEnum) {
        return fail(errorCodeEnum.getCode(), errorCodeEnum.getDesc());
    }

    public static <T> CommonResult<T> fail(ICodeEnum errorCodeEnum, String customMessage) {
        return fail(errorCodeEnum.getCode(), customMessage);
    }

    public static <T> CommonResult<T> fail(BizException exception) {
        return fail(exception.getCode(), exception.getMessage());
    }

    public static <T> CommonResult<T> fail(T data, int code, String customMessage) {
        CommonResult<T> result =  fail(code, customMessage);
        result.setData(data);
        return result;
    }

    public static <T> CommonResult<T> fail(T data, ICodeEnum errorCodeEnum) {
        return fail(data, errorCodeEnum.getCode(), errorCodeEnum.getDesc());
    }

    public static <T> CommonResult<T> fail(T data, ICodeEnum codeEnum, String customMessage) {
        return fail(data, codeEnum.getCode(), customMessage);
    }

    public static long getSerialVersionUID() {
        return serialVersionUID;
    }

}