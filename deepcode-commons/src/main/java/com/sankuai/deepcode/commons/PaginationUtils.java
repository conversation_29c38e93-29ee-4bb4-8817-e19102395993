package com.sankuai.deepcode.commons;

import org.apache.commons.collections4.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

/**
 * Package: IntelliJ IDEA
 * Description:
 *
 * <AUTHOR>
 * @since 2025/01/06 20:46
 */
public class PaginationUtils {
    /**
     * 对列表进行分页处理的通用方法。
     *
     * @param sourceList    源列表数据
     * @param pageSize      每页的大小
     * @param pageProcessor 分页处理器，接收当前页的数据列表，返回处理后的结果
     * @param <T>           源列表中的数据类型
     * @param <R>           处理结果的数据类型
     * @return 分页处理后的结果列表
     */
    public static <T, R> List<R> processPagesAndGetResults(List<T> sourceList, int pageSize, Function<List<T>, R> pageProcessor) {
        if (CollectionUtils.isEmpty(sourceList)) {
            return Collections.emptyList();
        }
        int totalSize = sourceList.size();
        int pageCount = (totalSize + pageSize - 1) / pageSize; // 计算总页数

        return IntStream.range(0, pageCount)
                .mapToObj(page -> {
                    int start = page * pageSize;
                    int end = Math.min((page + 1) * pageSize, totalSize);
                    List<T> pageData = sourceList.subList(start, end);
                    return pageProcessor.apply(pageData);
                })
                .collect(Collectors.toList());
    }

    /**
     * 对列表进行分页处理的方法
     *
     * @param sourceList    源数据列表
     * @param pageSize      每页的大小
     * @param pageProcessor 处理每一页数据的消费者函数
     * @param <T>           列表元素的类型
     */
    public static <T> void processPages(List<T> sourceList, int pageSize, Consumer<List<T>> pageProcessor) {
        if (CollectionUtils.isEmpty(sourceList)) {
            return;
        }
        if (pageSize <= 0) {
            pageSize = 30;
        }
        if (pageSize < sourceList.size()) {
            pageSize = sourceList.size();
        }
        int finalPageSize = pageSize;
        IntStream.range(0, (sourceList.size() + pageSize - 1) / pageSize)
                .mapToObj(i -> sourceList.subList(i * finalPageSize, Math.min((i + 1) * finalPageSize, sourceList.size())))
                .forEach(pageProcessor);
    }

}
