package com.sankuai.deepcode.commons;

import com.github.rholder.retry.*;
import com.google.common.base.Preconditions;
import lombok.extern.slf4j.Slf4j;

import java.util.Arrays;
import java.util.concurrent.TimeUnit;
import java.util.function.Predicate;

/**
 * Package com.sankuai.banma.accurate.coverage.util Description:
 *
 * <AUTHOR>
 * @since 2021/08/18 10:29
 */
@Slf4j
public class RetryUtils<T> {
    /**
     * 构造重试器(所有异常都重试)
     *
     * @param retryTimes 重试次数
     * @return 重试器
     */
    public static <T> Retryer<T> buildRetryer(int retryTimes) {
        return buildRetryer(retryTimes, 0, Boolean.FALSE::equals, t -> true);
    }

    /**
     * 构造重试器(所有异常都重试)
     *
     * @param retryTimes 重试次数
     * @param waitMills  重试等待毫秒数
     * @return 重试器
     */
    public static <T> Retryer<T> buildRetryer(int retryTimes, long waitMills) {
        return buildRetryer(retryTimes, waitMills, Boolean.FALSE::equals, t -> true);
    }

    /**
     * 构造重试器(指定异常才重试)
     *
     * @param retryTimes     重试次数
     * @param exceptionClass 需要重试的异常类型
     * @return 重试器
     */
    public static <T> Retryer<T> buildRetryer(int retryTimes, Class<? extends Throwable> exceptionClass) {
        return buildRetryer(retryTimes, 0, Boolean.FALSE::equals, exceptionClass);
    }

    /**
     * 构造重试器(指定异常才重试)
     *
     * @param retryTimes     重试次数
     * @param waitMills      重试等待毫秒数
     * @param exceptionClass 需要重试的异常类型
     * @return 重试器
     */
    public static <T> Retryer<T> buildRetryer(int retryTimes, long waitMills, Class<? extends Throwable> exceptionClass) {
        return buildRetryer(retryTimes, waitMills, Boolean.FALSE::equals, exceptionClass);
    }

    /**
     * 构造重试器（出现异常，或是指定条件未满足时重试）
     *
     * @param retryTimes     重试次数
     * @param waitMills      重试等待时间
     * @param retryPredicate 判断是否重试的条件，不满足时进行重试
     * @param <T>            判断条件的类型
     * @return
     */
    public static <T> Retryer<T> buildRetryer(int retryTimes, long waitMills, Predicate<T> retryPredicate) {
        return buildRetryer(retryTimes, waitMills, retryPredicate, t -> true);
    }


    public static <T> Retryer<T> buildRetryer(int retryTimes, long waitMills, Predicate<T> retryPredicate, Class<? extends Throwable> exceptionClass) {
        Preconditions.checkArgument(retryTimes >= 1, "retryTimes must be >= 1 but is " + retryTimes);
        Preconditions.checkArgument(waitMills >= 0, "waitMills must be >= 0 but is " + waitMills);
        return RetryerBuilder.<T>newBuilder()
                .retryIfExceptionOfType(exceptionClass)
                .retryIfResult(retryPredicate::test)
                .withStopStrategy(StopStrategies.stopAfterAttempt(retryTimes))
                .withWaitStrategy(waitMills == 0 ? WaitStrategies.noWait() : WaitStrategies.fixedWait(waitMills, TimeUnit.MILLISECONDS))
                .withRetryListener(new MyRetryListener())
                .build();
    }


    public static <T> Retryer<T> buildRetryer(int retryTimes, long waitMills, Predicate<T> retryPredicate, Predicate<Throwable> exceptionPredicate) {
        Preconditions.checkArgument(retryTimes >= 1, "retryTimes must be >= 1 but is " + retryTimes);
        Preconditions.checkArgument(waitMills >= 0, "waitMills must be >= 0 but is " + waitMills);
        return RetryerBuilder.<T>newBuilder()
                //如果异常会重试
                .retryIfException(exceptionPredicate::test)
                //如果结果为 True 会重试
                .retryIfResult(retryPredicate::test)
                //重调策略
                .withStopStrategy(StopStrategies.stopAfterAttempt(retryTimes))
                //Default blocking strategy: thread sleep
                //.withBlockStrategy(BlockStrategies.threadSleepStrategy())
                .withBlockStrategy(BlockStrategies.threadSleepStrategy())
                //尝试次数
                .withWaitStrategy(waitMills == 0 ? WaitStrategies.noWait() : WaitStrategies.fixedWait(waitMills, TimeUnit.MILLISECONDS))
                .withRetryListener(new MyRetryListener())
                .build();
    }

    @SafeVarargs
    public static <T> Retryer<T> buildRetryer(int retryTimes, long waitMills, Predicate<T> retryPredicate, Predicate<Throwable>... exceptionPredicate) {
        Preconditions.checkArgument(retryTimes >= 1, "retryTimes must be >= 1 but is " + retryTimes);
        Preconditions.checkArgument(waitMills >= 0, "waitMills must be >= 0 but is " + waitMills);
        RetryerBuilder<T> retryerBuilder = RetryerBuilder.<T>newBuilder()
                //如果结果为 True 会重试
                .retryIfResult(retryPredicate::test)
                //重调策略
                .withStopStrategy(StopStrategies.stopAfterAttempt(retryTimes))
                //尝试次数
                .withWaitStrategy(waitMills == 0 ? WaitStrategies.noWait() : WaitStrategies.fixedWait(waitMills, TimeUnit.MILLISECONDS))
                .withRetryListener(new MyRetryListener());
        Arrays.stream(exceptionPredicate).forEach(t -> retryerBuilder.retryIfException(t::test));

        return retryerBuilder.build();
    }


    public static class MyRetryListener implements RetryListener {
        @Override
        public <V> void onRetry(Attempt<V> attempt) {
            log.info("[Retryer] time={}, delay={}, hasException={}, hasResult = {}",
                    // 重试的执行次数
                    attempt.getAttemptNumber(),
                    // 距离第一次重试的延迟
                    attempt.getDelaySinceFirstAttempt(),
                    // 重试结果: 是异常终止, 还是正常返回
                    attempt.hasException(),
                    // 重新是否有结果
                    attempt.hasResult()
            );
            // 是什么原因导致异常
            if (attempt.hasException()) {
                log.error("[Retryer] causeBy={}", attempt.getExceptionCause().getMessage(), attempt.getExceptionCause());
            } else {
                // 正常返回时的结果
                log.info("[Retryer] result={}", attempt.getResult());
            }
        }
    }
}
