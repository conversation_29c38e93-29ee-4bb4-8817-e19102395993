package com.sankuai.deepcode.commons.function;

import java.util.function.BinaryOperator;

/**
 * <AUTHOR>
 * @since 2022-11-24 11:51 AM
 */
public interface OverrideBinaryOperator<T> extends BinaryOperator<T> {

    static <T> T override(T t, T t2) {
        return keepSecond(t, t2);
    }

    /**
     * 保留第一个参数
     *
     * @param retain
     * @param discard
     * @param <T>
     * @return
     */
    static <T> T keepFirst(T retain, T discard) {
        return retain;
    }

    /**
     * 保留第二个参数
     *
     * @param discard
     * @param retain
     * @param <T>
     * @return
     */
    static <T> T keepSecond(T discard, T retain) {
        return retain;
    }
}