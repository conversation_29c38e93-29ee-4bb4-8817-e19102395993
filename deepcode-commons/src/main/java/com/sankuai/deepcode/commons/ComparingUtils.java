package com.sankuai.deepcode.commons;

import org.apache.commons.collections4.CollectionUtils;

import java.util.Collection;
import java.util.Comparator;
import java.util.Iterator;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Package: com.sankuai.banma.qa.util
 * Description:
 *
 * <AUTHOR>
 * @since 2023/2/28 09:39
 */
public class ComparingUtils {
    /**
     * 判断集合/序列，是否是有序 状态
     *
     * @param iterable
     * @param comparator
     * @param <T>
     * @return
     */
    public static <T> boolean isInOrder(Iterable<? extends T> iterable, Comparator<T> comparator) {
        DeepCodePreconditions.checkBizArgument(Objects.nonNull(comparator), "传入比较器不能为空");
        Iterator<? extends T> it = iterable.iterator();
        if (it.hasNext()) {
            T prev = it.next();
            while (it.hasNext()) {
                T next = it.next();
                if (comparator.compare(prev, next) > 0) {
                    return false;
                }
                prev = next;
            }
        }
        return true;
    }

    /**
     * 判断传入的集合，是否在某个维度存在重复数据
     *
     * @param collection 需要判断的集合，空集合返回不重复
     * @param keyMapper  需要解析的数据『维度』
     * @param <T>        集合中元素类型
     * @param <K>        判断是否重复的维度的数据类型
     * @return 返回集合是否存在重复元素
     */
    public static <T, K> boolean isDuplicated(Collection<? extends T> collection,
                                              Function<? super T, ? extends K> keyMapper) {
        DeepCodePreconditions.checkBizArgument(Objects.nonNull(keyMapper), "传入属性解析器不能为空");
        if (CollectionUtils.isEmpty(collection)) {
            return false;
        }
        return collection.stream().collect(Collectors.toMap(keyMapper, e -> 1, Integer::sum)).entrySet().stream()
                .anyMatch(entry -> entry.getValue() > 1);
    }
}
