package com.sankuai.deepcode.commons.collectors;

import com.google.common.collect.Sets;
import org.apache.commons.collections4.keyvalue.MultiKey;
import org.apache.commons.collections4.map.MultiKeyMap;

import java.util.Set;
import java.util.function.BiConsumer;
import java.util.function.BinaryOperator;
import java.util.function.Function;
import java.util.function.Supplier;
import java.util.stream.Collector;

/**
 * Package: IntelliJ IDEA
 * Description:
 *
 * <AUTHOR>
 * @since 2024/6/6 21:59
 */
public class MultiKeyMapCollector<T, K, V> implements Collector<T, MultiKeyMap<K, V>, MultiKeyMap<K, V>> {

    private final Function<T, K> keyMapper1;
    private final Function<T, K> keyMapper2;
    private final Function<T, V> valueMapper;

    public MultiKeyMapCollector(Function<T, K> keyMapper1, Function<T, K> keyMapper2, Function<T, V> valueMapper) {
        this.keyMapper1 = keyMapper1;
        this.keyMapper2 = keyMapper2;
        this.valueMapper = valueMapper;
    }

    @Override
    public Supplier<MultiKeyMap<K, V>> supplier() {
        return MultiKeyMap::new;
    }

    @Override
    public BiConsumer<MultiKeyMap<K, V>, T> accumulator() {
        return (map, element) -> {
            K keyPart1 = keyMapper1.apply(element);
            K keyPart2 = keyMapper2.apply(element);
            V value = valueMapper.apply(element);
            map.put(new MultiKey<>(keyPart1, keyPart2), value);
        };
    }

    @Override
    public BinaryOperator<MultiKeyMap<K, V>> combiner() {
        return (map1, map2) -> {
            map1.putAll(map2);
            return map1;
        };
    }

    @Override
    public Function<MultiKeyMap<K, V>, MultiKeyMap<K, V>> finisher() {
        return Function.identity();
    }

    @Override
    public Set<Characteristics> characteristics() {
        return Sets.newHashSet(Characteristics.IDENTITY_FINISH);
    }

    public static <T, K, V> MultiKeyMapCollector<T, K, V> toMultiKeyMap(
            Function<T, K> keyMapper1,
            Function<T, K> keyMapper2,
            Function<T, V> valueMapper) {
        return new MultiKeyMapCollector<>(keyMapper1, keyMapper2, valueMapper);
    }
}
