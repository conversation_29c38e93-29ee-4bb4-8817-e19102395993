package com.sankuai.deepcode.commons;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.*;
import com.fasterxml.jackson.databind.module.SimpleModule;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ValueNode;
import com.fasterxml.jackson.databind.ser.BeanPropertyWriter;
import com.fasterxml.jackson.databind.ser.BeanSerializerModifier;
import com.fasterxml.jackson.databind.ser.std.NullSerializer;
import com.fasterxml.jackson.databind.type.CollectionType;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.time.Duration;
import java.util.*;

/**
 * Package com.sankuai.banma.actionserver.util Description:
 *
 * <AUTHOR>
 * @since 2020/8/13 20:59
 */
public class JacksonUtils {
    private static final Logger log = LoggerFactory.getLogger(JacksonUtils.class);
    private static final ObjectMapper MAPPER;

    public static final JsonSerializer<Object> NULL_STRING = new NullStringSerializer();
    public static final JsonSerializer<Object> NULL_INT = new NullIntSerializer();
    public static final JsonSerializer<Object> NULL_ARRAY = new NullArraySerializer();
    public static final JsonSerializer<Object> NULL_FLOAT = new NullFloatSerializer();
    public static final JsonSerializer<Object> NULL_OBJECT = new NullObjectSerializer();

    static {
        MAPPER = new ObjectMapper();
        configMapper();
    }

    private static void configMapper() {
        // ====================================== 序列化配置（Java类-》Json 字符串）====================================== //
        SimpleModule module = new SimpleModule();
        module.addDeserializer(Duration.class, new DurationDeserializer());
        module.addSerializer(Duration.class, new DurationSerializer());
        // 序列化时，如果值为空，则使用 "" 空字符串替换
        //MAPPER.getSerializerProvider().setNullValueSerializer(new JsonSerializer<Object>() {
        //    @Override
        //    public void serialize(Object o, JsonGenerator jsonGenerator, SerializerProvider serializerProvider) throws IOException {
        //        jsonGenerator.writeString("");
        //    }
        //});
        // 数字进行序列化时，也添加引号
        /// MAPPER.configure(JsonGenerator.Feature.WRITE_NUMBERS_AS_STRINGS, true);
        /// MAPPER.configure(JsonGenerator.Feature.QUOTE_NON_NUMERIC_NUMBERS, true);

        // 序列化时，属性类型是数组、列表、集合等类型时，如果size()==1 ，则不使用 [] 包裹
        /// MAPPER.configure(SerializationFeature.WRITE_SINGLE_ELEM_ARRAYS_UNWRAPPED, true);

        // ====================================== 反序列化配置 （Json 字符串-》Java类）====================================== //
        // 列表、数组接受单个元素
        MAPPER.configure(DeserializationFeature.ACCEPT_SINGLE_VALUE_AS_ARRAY, true);
        MAPPER.registerModule(module);
    }

    /**
     * 暴露原生的 Jackson ObjectMapper 对象用于其他原生调用，复制一份避免影响原有配置
     *
     * @return MAPPER 的副本
     */
    public static ObjectMapper getMapper() {
        return MAPPER.copy();
    }


    public static Map<String, String> beanToStringMap(Object object) {
        Map<String, String> result = Maps.newHashMap();
        try {
            Map<String, Object> tmpMap = beanToObjectMap(object);
            for (Map.Entry<String, Object> entry : tmpMap.entrySet()) {
                result.put(entry.getKey(), (entry.getValue() instanceof String ? (String) entry.getValue() : toJsonString(entry.getValue())));
            }
        } catch (Exception e) {
            log.error("对象转换 Map 时出现异常：{}, 对象内容：{}", e.getMessage(), object.toString(), e);

        }
        return result;
    }

    public static Map<String, Object> beanToObjectMap(Object object) {
        return beanToObjectMap(object, false);
    }

    /**
     * 转换 Map 时，可以设置忽略空值的键值对
     *
     * @param object               需要转换的对象
     * @param ignoreNullValueEntry 是否忽略空值的键值对
     * @return 返回没有空值键值对的 Map 结果
     */
    public static Map<String, Object> beanToObjectMap(Object object, boolean ignoreNullValueEntry) {
        if (!ignoreNullValueEntry) {
            return MAPPER.convertValue(object, new TypeReference<Map<String, Object>>() {
            });
        }
        ObjectMapper newMapper = getMapper();
        newMapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
        return newMapper.convertValue(object, new TypeReference<Map<String, Object>>() {
        });
    }


    public static Map<String, Object> beanToFlatMap(Object object) {
        return beanToFlatMap(object, false);
    }

    /**
     * 将 Pojo 类转换成扁平的 Map 结构
     *
     * @param object               对象
     * @param ignoreNullValueEntry 是否忽略对象的空值
     * @return
     */
    public static Map<String, Object> beanToFlatMap(Object object, boolean ignoreNullValueEntry) {
        Map<String, Object> result = Maps.newHashMap();
        try {
            parseFlatMap("", MAPPER.readTree(MAPPER.writeValueAsBytes(object)), ignoreNullValueEntry, result);
        } catch (IOException e) {

        }

        return result;
    }

    private static void parseFlatMap(String currentPath, JsonNode jsonNode, boolean ignoreNullValueEntry, Map<String, Object> result) {
        if (jsonNode.isObject()) {
            Iterator<Map.Entry<String, JsonNode>> iterNodes = jsonNode.fields();
            String pathPrefix = StringUtils.isEmpty(currentPath) ? "" : currentPath + ".";
            while (iterNodes.hasNext()) {
                Map.Entry<String, JsonNode> entry = iterNodes.next();
                if (Objects.isNull(entry.getValue()) || entry.getValue().isNull()) {
                    continue;
                }
                parseFlatMap(pathPrefix + entry.getKey(), entry.getValue(), ignoreNullValueEntry, result);
            }
        } else if (jsonNode.isArray()) {
            ArrayNode arrayNode = (ArrayNode) jsonNode;
            for (int i = 0; i < arrayNode.size(); i++) {
                parseFlatMap(currentPath + "[" + i + "]", arrayNode.get(i), ignoreNullValueEntry, result);
            }
        } else if (jsonNode.isValueNode()) {
            if (ignoreNullValueEntry && (jsonNode.isNull() || StringUtils.isBlank(jsonNode.asText()) || "\"\"".equals(jsonNode.textValue()))) {
                return;
            }
            ValueNode valueNode = (ValueNode) jsonNode;
            result.put(currentPath, parseValue(valueNode));
        }
    }

    private static Object parseValue(ValueNode valueNode) {
        switch (valueNode.asToken()) {
            case VALUE_TRUE:
            case VALUE_FALSE:
                return valueNode.booleanValue();
            case VALUE_STRING:
                return valueNode.textValue();
            case VALUE_NUMBER_FLOAT:
                return valueNode.floatValue();
            case VALUE_NUMBER_INT:
                return valueNode.longValue();
            default:
                return null;
        }
    }

    public static Map<String, String> jsonStrToStringMap(String jsonString) {
        Map<String, String> result = Maps.newHashMap();
        try {

            Map<String, Object> tmpMap = readValue(jsonString, new TypeReference<HashMap<String, Object>>() {
            }).orElseGet(HashMap::new);

            for (Map.Entry<String, Object> entry : tmpMap.entrySet()) {
                result.put(entry.getKey(), (entry.getValue() instanceof String ? (String) entry.getValue() : toJsonString(entry.getValue())));
            }
        } catch (Exception e) {
            log.error("字符串:{} 转换 Map 时出现异常：{}, 字符串：{}", jsonString, e.getMessage(), jsonString, e);
        }
        return result;
    }


    public static <T> Optional<T> readValue(String jsonString, Class<T> valueType) {
        try {
            return Optional.of(MAPPER.readValue(jsonString, valueType));
        } catch (IOException e) {
            log.error("将 JsonString:{} 转换成：{} 时出现读取数据异常：{}", jsonString, valueType.getName(), e.getMessage(), e);
        }
        return Optional.empty();
    }

    @SafeVarargs
    public static <T> Optional<T> readValue(String jsonString, Class<T> valueType, Pair<DeserializationFeature, Boolean>... deserializationConfigs) {
        ObjectMapper mapper = getMapper();
        Optional.ofNullable(deserializationConfigs).ifPresent(configs -> {
            Arrays.stream(configs).forEach(config -> mapper.configure(config.getLeft(), config.getRight()));
        });
        try {
            return Optional.of(
                    mapper.readValue(jsonString, valueType)
            );
        } catch (IOException e) {
            log.error("将 JsonString:{} 转换成：{} 时出现读取数据异常：{}", jsonString, valueType.getName(), e.getMessage(), e);
        }
        return Optional.empty();
    }

    /**
     * json转特殊类型
     *
     * @param jsonString   json 字符串
     * @param valueTypeRef 需要转换的类型
     * @return 转换成功的元素
     */
    public static <T> Optional<T> readValue(String jsonString, TypeReference<T> valueTypeRef) {
        try {
            return Optional.of(MAPPER.readValue(jsonString, valueTypeRef));
        } catch (Exception e) {
            log.error("将 JsonString:{} 转换成：{} 时出现异常：{}", jsonString, valueTypeRef.getType(), e.getMessage(), e);
        }

        return Optional.empty();
    }

    @SafeVarargs
    public static <T> Optional<T> readValue(String jsonString, TypeReference<T> valueTypeRef, Pair<DeserializationFeature, Boolean>... deserializationConfigs) {
        ObjectMapper mapper = getMapper();
        Optional.ofNullable(deserializationConfigs).ifPresent(configs -> {
            Arrays.stream(configs).forEach(config -> mapper.configure(config.getLeft(), config.getRight()));
        });
        try {
            return Optional.of(mapper.readValue(jsonString, valueTypeRef));
        } catch (Exception e) {
            log.error("将 JsonString:{} 转换成：{} 时出现异常：{}", jsonString, valueTypeRef.getType(), e.getMessage(), e);
        }

        return Optional.empty();
    }


    /**
     * json数组转List
     *
     * @param jsonString   Json字符串
     * @param valueTypeRef 需要转换的列表元素类型
     * @return 转换成功的 Collection 对象
     */
    public static <T> Optional<T> readValue(String jsonString, CollectionType valueTypeRef) {
        try {
            return Optional.of(MAPPER.readValue(jsonString, valueTypeRef));
        } catch (Exception e) {
            log.error("将 JsonString:{} 转换成：{} 时出现异常：{}", jsonString, valueTypeRef.getTypeName(), e.getMessage(), e);
        }

        return Optional.empty();
    }

    public static <T> Optional<T> convertValue(Object object, Class<T> valueType) {
        return Optional.of(MAPPER.convertValue(object, valueType));
    }

    public static <T> Optional<T> convertValue(Object object, TypeReference<T> typeRef) {
        return Optional.of(MAPPER.convertValue(object, typeRef));
    }

    public static CollectionType constructCollectionType(Class<? extends Collection<?>> collectionClass, Class<?> elementClass) {
        return MAPPER.getTypeFactory()
                .constructCollectionType(collectionClass, elementClass);
    }

    public static Optional<JsonNode> readTree(String jsonString) {
        try {
            return Optional.of(MAPPER.readTree(jsonString));
        } catch (IOException e) {
            log.error("由字符串:{} 转换 JsonNode 对象异常", jsonString, e);
        }
        return Optional.empty();
    }

    public static Optional<JsonNode> readTree(JsonParser parser) {
        try {
            return Optional.of(MAPPER.readTree(parser));
        } catch (IOException e) {
            log.error("读取 Json 数据失败", e);
        }
        return Optional.empty();
    }


    /**
     * Java 对象转换为 JsonNode
     * <p>如果是传入参数是 String，会进一步校验是否为基本的 JsonNode对象，进一步处理成 JsonNode</p>
     *
     * @param value 传入参数
     * @return JsonNode 实例
     */
    public static Optional<JsonNode> valueToTree(Object value) {
        if (value instanceof String) {
            return readTree(getParser((String) value)
                    .orElseThrow(() -> new IllegalArgumentException("转换 Json 数据失败，请检查输入字符串是否符合 jsonNode 语法")));
        }
        return Optional.of(MAPPER.valueToTree(value));
    }

    public static Optional<JsonParser> getParser(String jsonStr) {
        try {
            return Optional.of(MAPPER.getFactory().createParser(jsonStr));
        } catch (IOException e) {
            log.error("转换Json 数据失败");
        }
        return Optional.empty();
    }

    public static Optional<JsonNode> readTree(File jsonFile) {
        try {
            return Optional.of(MAPPER.readTree(jsonFile));
        } catch (IOException e) {
            log.error("读取 Json 数据异常", e);
        }

        return Optional.empty();
    }

    public static Optional<JsonNode> readTree(InputStream inputStream) {
        try {
            return Optional.of(MAPPER.readTree(inputStream));
        } catch (IOException e) {
            log.error("读取 Json 数据异常", e);
        }

        return Optional.empty();
    }

    /**
     * 把JavaBean转换为json字符串
     *
     * @param object 需要转换的对象
     * @return 转成成功的字符串
     */
    public static String toJsonString(Object object) {
        return toJsonString(object, false);
    }

    /**
     * 把JavaBean转换为json字符串
     *
     * @param object 需要转换的对象
     * @return 转成成功的字符串
     */
    public static String toJsonStringFilterNull(Object object) {
        String jsonString = toJsonString(object, false);
//        jsonString = StringEscapeUtils.escapeJava(jsonString);//无需转义
        try {
            ObjectMapper mapper = getMapper();
            mapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
            jsonString = mapper.writeValueAsString(mapper.readValue(jsonString, Object.class));
        } catch (JsonProcessingException e) {
            e.printStackTrace();
        }
        return jsonString;
    }

    /**
     * 把JavaBean转换为json字符串
     *
     * @param object 需要转换的对象
     * @param pretty 是否以缩进的方式打印
     * @return 转成成功的字符串
     */
    public static String toJsonString(Object object, boolean pretty) {
        try {
            if (pretty) {
                return MAPPER.writerWithDefaultPrettyPrinter().writeValueAsString(object);
            }
            return MAPPER.writeValueAsString(object);
        } catch (JsonProcessingException e) {
            log.error("对象：{} 转换为 JsonString 出现异常", object, e);
        }
        return null;
    }

    private static class NullStringSerializer extends JsonSerializer<Object> {
        @Override
        public void serialize(Object value, JsonGenerator gen, SerializerProvider serializers) throws IOException {
            if (null == value) {
                gen.writeString("");
            } else {
                gen.writeObject(value);
            }
        }
    }

    private static class NullIntSerializer extends JsonSerializer<Object> {
        @Override
        public void serialize(Object value, JsonGenerator gen, SerializerProvider serializers) throws IOException {
            if (value == null) {
                gen.writeNumber(0);
            } else {
                gen.writeObject(value);
            }

        }
    }

    private static class NullFloatSerializer extends JsonSerializer<Object> {
        @Override
        public void serialize(Object value, JsonGenerator gen, SerializerProvider serializers) throws IOException {
            if (value == null) {
                gen.writeNumber(0.0F);
            } else {
                gen.writeObject(value);
            }
        }
    }

    private static class NullArraySerializer extends JsonSerializer<Object> {
        @Override
        public void serialize(Object value, JsonGenerator gen, SerializerProvider serializers) throws IOException {
            if (value == null) {
                gen.writeStartArray();
                gen.writeEndArray();
            } else {
                gen.writeObject(value);
            }
        }
    }

    private static class NullObjectSerializer extends JsonSerializer<Object> {
        @Override
        public void serialize(Object value, JsonGenerator gen, SerializerProvider serializers) throws IOException {
            if (value == null) {
                gen.writeStartObject();
                gen.writeEndObject();
            } else {
                gen.writeObject(value);
            }
        }
    }

    public static class NullFieldSerializerModifier extends BeanSerializerModifier {
        public static final NullFieldSerializerModifier INSTANCE = new NullFieldSerializerModifier();

        private NullFieldSerializerModifier() {
        }

        @Override
        public List<BeanPropertyWriter> changeProperties(SerializationConfig config, BeanDescription beanDesc, List<BeanPropertyWriter> beanProperties) {
            for (BeanPropertyWriter writer : beanProperties) {
                writer.assignNullSerializer(getSerializerByType(writer.getType().getRawClass()));
            }
            return super.changeProperties(config, beanDesc, beanProperties);
        }

        private boolean checkSubClass(Set<Class<?>> classSet, Class<?> rawCls) {
            return classSet.contains(rawCls) || classSet.parallelStream().anyMatch(rawCls::isAssignableFrom);
        }

        private JsonSerializer<Object> getSerializerByType(Class<?> rawClass) {
            Set<Class<?>> intSet = Sets.newHashSet(Integer.class, Short.class, Long.class, Byte.class);
            Set<Class<?>> stringSet = Sets.newHashSet(String.class, Character.class);
            Set<Class<?>> floatSet = Sets.newHashSet(Float.class, Double.class, BigDecimal.class);
            Set<Class<?>> arraySet = Sets.newHashSet(Set.class, List.class);

            if (checkSubClass(intSet, rawClass)) {
                return NULL_INT;
            } else if (rawClass.isAssignableFrom(CharSequence.class) || checkSubClass(stringSet, rawClass)) {
                return NULL_STRING;
            } else if (checkSubClass(floatSet, rawClass)) {
                return NULL_FLOAT;
            } else if (rawClass.isArray() || checkSubClass(arraySet, rawClass)) {
                return NULL_ARRAY;
            } else if (rawClass.equals(Boolean.class)) {
                return NullSerializer.instance;
            } else {
                return NULL_OBJECT;
            }
        }
    }

    public static class DurationSerializer extends JsonSerializer<Duration> {
        @Override
        public void serialize(Duration duration, JsonGenerator jsonGenerator, SerializerProvider serializerProvider) throws IOException {
            jsonGenerator.writeString(duration.toString());
        }
    }

    public static class DurationDeserializer extends JsonDeserializer<Duration> {

        @Override
        public Duration deserialize(JsonParser jsonParser, DeserializationContext deserializationContext) throws IOException {
            return Duration.parse(jsonParser.getText());
        }
    }
}