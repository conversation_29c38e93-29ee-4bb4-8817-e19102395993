package com.sankuai.deepcode.commons;


import com.sankuai.deepcode.commons.ex.BizException;

import java.util.function.Supplier;

/**
 * Package: com.sankuai.deepcode.commons
 * Description:
 *
 * <AUTHOR>
 * @since 2023/2/2 13:48
 */

public class DeepCodePreconditions {
    /**
     * 检查参数值，当为false时，抛出supplier里的异常
     *
     * @param expression        需要检查的结果
     * @param exceptionSupplier 异常supplier
     * @param <X>               异常类型
     * @throws X 抛出的自定义异常
     */
    public static <X extends Throwable> void checkArgument(boolean expression, Supplier<? extends X> exceptionSupplier) throws X {
        if (!expression) {
            throw exceptionSupplier.get();
        }
    }

    /**
     * 检查结果，不符合条件时，抛出指定 BizException 的异常
     * 此时 异常Code将统一为 5000
     *
     * @param expression   需要检查的结果
     * @param errorMessage 抛出异常的消息
     */
    public static void checkBizArgument(boolean expression, Object errorMessage) {
        if (!expression) {
            throw new BizException(String.valueOf(errorMessage));
        }
    }

    public static void checkBizArgument(boolean expression, int code, Object errorMessage) {
        if (!expression) {
            throw new BizException(code, String.valueOf(errorMessage));
        }
    }

    public static void checkBizArgument(boolean expression, ICodeEnum code, String errorMessage) {
        if (!expression) {
            throw new BizException(code.getCode(), errorMessage);
        }
    }

    public static void checkBizArgument(boolean expression, ICodeEnum code) {
        if (!expression) {
            throw new BizException(code);
        }
    }

    public static void checkBizArgument(int code, Object errorMessage) {
        if (!(code == 200)) {
            throw new BizException(code, String.valueOf(errorMessage));
        }
    }

    /**
     * 检查结果，不符合条件时抛出指定异常，符合条件时，返回对应处理的结果
     *
     * @param expression        需要检查的结果
     * @param exceptionSupplier 结果不符合时抛出的异常
     * @param afterSupplier     结果符合时，需要处理的结果
     * @param <X>               不符合时的异常类型
     * @param <R>               结果类型
     * @return 返回预期结果
     * @throws X 异常实例
     */
    public static <X extends Throwable, R> R checkArgumentAndThen(boolean expression, Supplier<? extends X> exceptionSupplier, Supplier<R> afterSupplier) throws X {
        checkArgument(expression, exceptionSupplier);
        return afterSupplier.get();
    }

}
