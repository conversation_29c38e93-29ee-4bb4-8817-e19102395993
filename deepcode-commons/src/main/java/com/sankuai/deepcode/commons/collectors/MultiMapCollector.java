package com.sankuai.deepcode.commons.collectors;

import com.google.common.collect.ArrayListMultimap;
import com.google.common.collect.Multimap;

import java.util.function.Function;
import java.util.function.Supplier;
import java.util.stream.Collector;

/**
 * Package: IntelliJ IDEA
 * Description:
 *
 * <AUTHOR>
 * @since 2024/6/6 22:40
 */
public class MultiMapCollector<T, K, V> {

    // 提供自定义 MultiMap 实现的方法
    public static <T, K, V, M extends Multimap<K, V>> Collector<T, ?, M> toMultiMap(
            Function<? super T, ? extends K> keyMapper,
            Function<? super T, ? extends V> valueMapper,
            Supplier<M> mapSupplier) {
        return Collector.of(
                mapSupplier, // 供应源
                (map, element) -> map.put(keyMapper.apply(element), valueMapper.apply(element)), // 累加器
                (left, right) -> {
                    left.putAll(right);
                    return left;
                }, // 组合器
                Collector.Characteristics.IDENTITY_FINISH // 特征
        );
    }

    // 提供默认实现的方法
    public static <T, K, V> Collector<T, ?, Multimap<K, V>> toMultiMap(
            Function<? super T, ? extends K> keyMapper,
            Function<? super T, ? extends V> valueMapper) {
        return toMultiMap(keyMapper, valueMapper, ArrayListMultimap::create);
    }
}
