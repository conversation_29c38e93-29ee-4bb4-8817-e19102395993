package com.sankuai.deepcode.commons.ex;

import com.sankuai.deepcode.commons.ICodeEnum;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @since 2022-11-24 19:14:08
 */
@Setter
@Getter
public class BizException extends RuntimeException {
    private int code;
    private String message;

    public BizException() {
        super();
    }


    public BizException(int code, String message) {
        super(message);
        this.code = code;
        this.message = message;
    }

    public BizException(ICodeEnum serverCode) {
        super(serverCode.getDesc());
        this.code = serverCode.getCode();
        this.message = serverCode.getDesc();
    }


    public BizException(String message) {
        super(message);
        this.code = 500;
        this.message = message;
    }

    public BizException(String message, Throwable cause) {
        super(message, cause);
        this.code = 500;
        this.message = message;
    }

    public BizException(Throwable cause) {
        super(cause);
        this.code = 500;
        this.message = cause.getMessage();
    }

    public BizException(ICodeEnum serverCode, String customMessage) {
        super(serverCode.getDesc());
        this.code = serverCode.getCode();
        this.message = customMessage;
    }
}
