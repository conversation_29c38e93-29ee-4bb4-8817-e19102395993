package com.sankuai.deepcode.commons;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.IterableUtils;

import java.util.Collection;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * Package: com.sankuai.banma.qa.util
 * Description:
 *
 * <AUTHOR>
 * @since 2023/2/16 14:51
 */
public class RandomUtils extends org.apache.commons.lang3.RandomUtils {
    public static <E> Optional<E> random(Collection<E> collection) {
        return random(collection, null);
    }

    /**
     * 随机返回集合中的一个元素
     *
     * @param collection
     * @param <E>
     * @return
     */
    public static <E> Optional<E> random(Collection<E> collection, E exclude) {
        if (CollectionUtils.isEmpty(collection)) {
            return Optional.empty();
        }
        if (CollectionUtils.size(collection) == 1) {
            // 只有一个数据时，不用随机，直接返回
            E result = IterableUtils.get(collection, 0);
            if (Objects.equals(result, exclude)) {
                return Optional.empty();
            }
            return Optional.of(result);
        }

        AtomicBoolean isInclude = new AtomicBoolean(false);
        return collection.stream()
                // 过滤掉不需要随机的一个元素
                .filter(e -> {
                    if (Objects.equals(e, exclude)) {
                        isInclude.set(true);
                        return false;
                    }
                    return true;
                })
                .skip(nextInt(0, collection.size() - (isInclude.get() ? 1 : 0)))
                .findFirst();
    }


}
