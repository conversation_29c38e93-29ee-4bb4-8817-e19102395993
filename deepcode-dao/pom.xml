<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.sankuai.deepcode</groupId>
        <artifactId>deepcode_platform</artifactId>
        <version>1.0.0-SNAPSHOT</version>
    </parent>
    <artifactId>deepcode-dao</artifactId>

    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>

    <!-- 添加资源配置，确保 spring.factories 文件被复制到 target 目录 -->
    <build>
        <resources>
            <resource>
                <directory>src/main/resources</directory>
                <includes>
                    <include>**/*.factories</include>
                    <include>**/*.properties</include>
                    <include>**/*.yml</include>
                    <include>**/*.xml</include>
                </includes>
            </resource>
        </resources>
    </build>

    <dependencies>
        <dependency>
            <groupId>com.meituan.xframe</groupId>
            <artifactId>zebra-xframe-boot-starter</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>mdp-boot-starter-zebra</artifactId>
                    <groupId>com.meituan.mdp.boot</groupId>
                </exclusion>
            </exclusions>
        </dependency>


        <dependency>
            <groupId>com.meituan.xframe</groupId>
            <artifactId>mybatisplus-xframe-boot-starter</artifactId>
            <!--            <version>2.6.2</version>-->
            <exclusions>
                <exclusion>
                    <artifactId>mybatis-plus</artifactId>
                    <groupId>com.baomidou</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>mapstruct</artifactId>
                    <groupId>org.mapstruct</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>mapstruct-processor</artifactId>
                    <groupId>org.mapstruct</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-boot-starter</artifactId>
            <version>3.5.5</version>
        </dependency>
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-generator</artifactId>
            <version>3.5.3.1</version>
            <scope>test</scope>
        </dependency>
        <dependency> <!-- mybatis-plus-generator 生成数据时，使用的freemarker模板引擎 -->
            <groupId>org.freemarker</groupId>
            <artifactId>freemarker</artifactId>
            <version>2.3.31</version> <!-- 使用最新的版本 -->
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>${project.groupId}</groupId>
            <artifactId>deepcode-commons</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>io.jsonwebtoken</groupId>
            <artifactId>jjwt</artifactId>
            <version>0.9.0</version>
            <scope>compile</scope>
        </dependency>

        <dependency>
            <groupId>io.milvus</groupId>
            <artifactId>milvus-sdk-java</artifactId>
            <version>2.5.4</version>
            <exclusions>
                <exclusion>
                    <groupId>com.google.protobuf</groupId>
                    <artifactId>protobuf-java</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.google.protobuf</groupId>
            <artifactId>protobuf-java</artifactId>
            <version>3.24.0</version>
        </dependency>
    </dependencies>
</project>