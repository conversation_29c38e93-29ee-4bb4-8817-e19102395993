package com.sankuai.deepcode.dao.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sankuai.deepcode.dao.domain.CodeClassAnalysis;
import com.sankuai.deepcode.dao.mapper.CodeClassAnalysisMapper;
import com.sankuai.deepcode.dao.service.CodeClassAnalysisService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

/**
 * <p>
 * 代码class解析 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-11
 */
@Service
public class CodeClassAnalysisServiceImpl extends ServiceImpl<CodeClassAnalysisMapper, CodeClassAnalysis> implements CodeClassAnalysisService {
    @Override
    public CodeClassAnalysis getByClassVid(Long itemId, String classVid) {
        return getOne(new LambdaQueryWrapper<>(CodeClassAnalysis.class)
                .eq(CodeClassAnalysis::getItemId, itemId)
                .eq(CodeClassAnalysis::getClassVid, classVid)
                .eq(CodeClassAnalysis::getValid, 1)
                .last("limit 1")
        );
    }

    @Override
    public CodeClassAnalysis getByClassName(Long itemId, String className) {
        return getOne(new LambdaQueryWrapper<>(CodeClassAnalysis.class)
                .eq(CodeClassAnalysis::getItemId, itemId)
                .eq(CodeClassAnalysis::getClassName, className)
                .eq(CodeClassAnalysis::getValid, 1)
                .last("limit 1")
        );
    }

    @Override
    public List<CodeClassAnalysis> getByClassNames(Long itemId, List<String> classNames) {
        if (CollectionUtils.isEmpty(classNames)) {
            return Collections.emptyList();
        }
        return list(
                new LambdaQueryWrapper<>(CodeClassAnalysis.class)
                        .eq(CodeClassAnalysis::getItemId, itemId)
                        .in(CodeClassAnalysis::getClassName, classNames)
                        .eq(CodeClassAnalysis::getValid, 1)
        );
    }

    @Override
    public List<CodeClassAnalysis> getByItemId(long itemId) {
        return list(new LambdaQueryWrapper<>(CodeClassAnalysis.class)
                .eq(CodeClassAnalysis::getItemId, itemId)
                .eq(CodeClassAnalysis::getValid, 1)
        );
    }

    @Override
    public void batchInsert(List<CodeClassAnalysis> list) {
        getBaseMapper().batchInsert(list);
    }

    @Override
    public List<CodeClassAnalysis> getByItemIdAndFileVid(long itemId, String fileVid) {
        return list(
                new LambdaQueryWrapper<>(CodeClassAnalysis.class)
                        .eq(CodeClassAnalysis::getItemId, itemId)
                        .eq(CodeClassAnalysis::getFileVid, fileVid)
                        .eq(CodeClassAnalysis::getValid, 1)
        );
    }

}
