package com.sankuai.deepcode.dao.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.sankuai.deepcode.dao.handlers.BooleanIntegerTypeHandler;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <p>
 * 代码file解析
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-11
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("code_file_analysis")
public class CodeFileAnalysis {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 任务id
     */
    @TableField("item_id")
    private Long itemId;

    /**
     * vid
     */
    @TableField("file_vid")
    private String fileVid;

    /**
     * module名称
     */
    @TableField("module_name")
    private String moduleName;

    /**
     * 文件名
     */
    @TableField("file_name")
    private String fileName;

    /**
     * 文件路径
     */
    @TableField("file_path")
    private String filePath;

    /**
     * 文件类型类型
     */
    @TableField("file_type")
    private String fileType;

    /**
     * 变更类型
     */
    @TableField("change_type")
    private Integer changeType;

    /**
     * 变更行
     */
    @TableField("change_lines")
    private String changeLines;

    /**
     * 冲突类型
     */
    @TableField("check_type")
    private Integer checkType;

    /**
     * 冲突行
     */
    @TableField("check_lines")
    private String checkLines;

    /**
     * 起始行
     */
    @TableField("start_line")
    private Integer startLine;

    /**
     * 终止行
     */
    @TableField("end_line")
    private Integer endLine;

    /**
     * 注释行
     */
    @TableField("comment_lines")
    private String commentLines;

    /**
     * 半年内commit次数
     */
    @TableField("commit_count")
    private Integer commitCount;

    @TableField(value = "valid")
    private Boolean valid;
}
