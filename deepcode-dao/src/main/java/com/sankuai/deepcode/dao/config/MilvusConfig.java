package com.sankuai.deepcode.dao.config;

import com.dianping.lion.client.log.Logger;
import com.dianping.lion.client.log.LoggerFactory;
import io.milvus.v2.client.ConnectConfig;
import io.milvus.v2.client.MilvusClientV2;
import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Service;

@Service
public class MilvusConfig {
    private static final Logger LOGGER = LoggerFactory.getLogger(MilvusConfig.class);

    String CLUSTER_ENDPOINT = "http://10.101.112.145:19530";

    @Bean()
    public MilvusClientV2 dataSource() {
        MilvusClientV2 client = null;
        try {
            ConnectConfig connectConfig = ConnectConfig.builder()
                    .uri(CLUSTER_ENDPOINT)
                    .dbName("default")
                    .build();

            client = new MilvusClientV2(connectConfig);
        } catch (Exception e) {
            LOGGER.error("MilvusConfig dataSource e:", e);
            throw new RuntimeException(e);
        }
        return client;
    }

}
