package com.sankuai.deepcode.dao.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sankuai.deepcode.dao.domain.RuleTaskDetail;
import com.sankuai.deepcode.dao.mapper.RuleTaskDetailMapper;
import com.sankuai.deepcode.dao.service.RuleTaskDetailService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;


@Service
public class RuleTaskDetailServiceImpl extends ServiceImpl<RuleTaskDetailMapper, RuleTaskDetail> implements RuleTaskDetailService {

    @Override
    public int insert(RuleTaskDetail ruleTaskDetail) {
        LocalDateTime now = LocalDateTime.now();
//        RuleTaskDetail ruleTaskDetail = new RuleTaskDetail();
//        ruleTaskDetail.setClassId(ruleTaskDetail.getClassId());
//        ruleTaskDetail.setStartLine(ruleTaskDetail.getStartLine());
//        ruleTaskDetail.setType(ruleTaskDetail.getType());

//        ruleTaskDetail.setValue(ruleInfoDetail.getValue());
        ruleTaskDetail.setValid(true);
        ruleTaskDetail.setCreateTime(now);
        ruleTaskDetail.setUpdateTime(now);
        return getBaseMapper().insert(ruleTaskDetail);
    }

    @Override
    public List<RuleTaskDetail> getRuleList(RuleTaskDetail ruleTaskDetail) {
        LambdaQueryWrapper<RuleTaskDetail> queryWrapper = new LambdaQueryWrapper<>();
        if (ruleTaskDetail.getRuleId() > 0) {
            queryWrapper.eq(RuleTaskDetail::getRuleId, ruleTaskDetail.getRuleId());
        }
        if (ruleTaskDetail.getType() > 0) {
            queryWrapper.eq(RuleTaskDetail::getType, ruleTaskDetail.getType());
        }
        if (StringUtils.isNotBlank(ruleTaskDetail.getFileVid())) {
            queryWrapper.eq(RuleTaskDetail::getFileVid, ruleTaskDetail.getFileVid());
        }

        return list(queryWrapper.eq(RuleTaskDetail::isValid, true));
    }

    @Override
    public boolean del(long ruleTaskDetailId) {
        RuleTaskDetail ruleTaskDetail = new RuleTaskDetail()
                .setId(ruleTaskDetailId)
                .setValid(false)
                .setUpdateTime(LocalDateTime.now());
        return updateById(ruleTaskDetail);
    }

    @Override
    public List<RuleTaskDetail> getByItemAndFileVid(Long itemId, String fileVid) {
        return list(new LambdaQueryWrapper<>(RuleTaskDetail.class)
                .eq(RuleTaskDetail::getItemId, itemId)
                .eq(RuleTaskDetail::getFileVid, fileVid)
                .eq(RuleTaskDetail::isValid, true)
        );
    }


}
