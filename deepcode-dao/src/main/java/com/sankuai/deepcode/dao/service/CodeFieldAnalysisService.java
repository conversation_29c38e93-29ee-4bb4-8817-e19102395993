package com.sankuai.deepcode.dao.service;

import com.sankuai.deepcode.dao.domain.CodeClassAnalysis;
import com.sankuai.deepcode.dao.domain.CodeFieldAnalysis;
import com.baomidou.mybatisplus.extension.service.IService;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

/**
 * <p>
 * 代码field解析 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-11
 */
public interface CodeFieldAnalysisService extends IService<CodeFieldAnalysis> {
    List<CodeFieldAnalysis> getByClassName(long itemId, String className);

    CodeFieldAnalysis getByFieldVid(long itemId, String fieldVid);

    List<CodeFieldAnalysis> getByItemId(long itemId);

    List<CodeFieldAnalysis> getByVids(long itemId, Set<String> vids);

    void batchInsert(List<CodeFieldAnalysis> list);
}
