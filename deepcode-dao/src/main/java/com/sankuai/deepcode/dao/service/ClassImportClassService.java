package com.sankuai.deepcode.dao.service;

import com.sankuai.deepcode.dao.domain.ClassImportClass;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * 类引用类 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-11
 */
public interface ClassImportClassService extends IService<ClassImportClass> {
    List<ClassImportClass> getByItemAndSourceClassVid(Long itemId, String sourceClassVid);

    List<ClassImportClass> getByItemId(Long itemId);

    void batchInsert(List<ClassImportClass> list);

}
