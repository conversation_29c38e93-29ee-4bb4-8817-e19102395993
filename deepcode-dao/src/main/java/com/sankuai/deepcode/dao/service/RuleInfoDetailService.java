package com.sankuai.deepcode.dao.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.sankuai.deepcode.dao.domain.RuleBindInfo;
import com.sankuai.deepcode.dao.domain.RuleInfoDetail;
import com.sankuai.deepcode.dao.domain.SysUser;

import java.util.List;
import java.util.Optional;


public interface RuleInfoDetailService extends IService<RuleInfoDetail> {

    int insert(RuleInfoDetail ruleInfoDetail);

    List<RuleInfoDetail> getRuleList(RuleInfoDetail ruleInfoDetail);


    boolean edit(long ruleId, long userId, String value);

    boolean del(long ruleId);

    List<RuleInfoDetail> getRuleListByIds(List<RuleBindInfo> ruleBindInfoList);

    List<RuleInfoDetail> getRuleInfoDetailsByRuleIds(List<Long> ruleIds);
}
