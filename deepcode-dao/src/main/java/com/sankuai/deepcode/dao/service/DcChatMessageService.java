package com.sankuai.deepcode.dao.service;

import com.sankuai.deepcode.dao.domain.DcChatMessage;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;
import java.util.Optional;

/**
 * <p>
 * 聊天对话消息记录 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-14
 */
public interface DcChatMessageService extends IService<DcChatMessage> {

    List<DcChatMessage> getByChatId(String chatUuid, Long userId);

    Optional<DcChatMessage> getMsgByUuId(String uuid, Long userId);
}
