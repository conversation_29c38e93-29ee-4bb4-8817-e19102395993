package com.sankuai.deepcode.dao.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.sankuai.deepcode.dao.domain.RuleTaskInfo;

import java.util.List;


public interface RuleTaskInfoService extends IService<RuleTaskInfo> {

    long insert(long projectId);

//    List<RuleTaskInfo> getRuleList(RuleTaskInfo ruleTaskInfo);

    List<RuleTaskInfo> getTaskList(RuleTaskInfo ruleTaskInfo);
    RuleTaskInfo getTaskLastByItemId(long itemId);


    boolean delByItemId(long projectId);

    boolean updateStatus(long ruleTaskId, int i);
}
