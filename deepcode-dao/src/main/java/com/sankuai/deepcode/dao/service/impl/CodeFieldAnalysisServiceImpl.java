package com.sankuai.deepcode.dao.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sankuai.deepcode.dao.domain.CodeClassAnalysis;
import com.sankuai.deepcode.dao.domain.CodeFieldAnalysis;
import com.sankuai.deepcode.dao.mapper.CodeFieldAnalysisMapper;
import com.sankuai.deepcode.dao.service.CodeFieldAnalysisService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Set;

/**
 * <p>
 * 代码field解析 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-11
 */
@Service
public class CodeFieldAnalysisServiceImpl extends ServiceImpl<CodeFieldAnalysisMapper, CodeFieldAnalysis> implements CodeFieldAnalysisService {
    @Override
    public List<CodeFieldAnalysis> getByClassName(long itemId, String className) {
        return list(new LambdaQueryWrapper<>(CodeFieldAnalysis.class)
                .eq(CodeFieldAnalysis::getItemId, itemId)
                .eq(CodeFieldAnalysis::getClassName, className)
                .eq(CodeFieldAnalysis::getValid, 1)
        );
    }

    @Override
    public CodeFieldAnalysis getByFieldVid(long itemId, String fieldVid) {
        return getOne(new LambdaQueryWrapper<>(CodeFieldAnalysis.class)
                .eq(CodeFieldAnalysis::getItemId, itemId)
                .eq(CodeFieldAnalysis::getFieldVid, fieldVid)
                .eq(CodeFieldAnalysis::getValid, 1)
                .last("limit 1")
        );
    }

    @Override
    public List<CodeFieldAnalysis> getByItemId(long itemId) {
        return list(new LambdaQueryWrapper<>(CodeFieldAnalysis.class)
                .eq(CodeFieldAnalysis::getItemId, itemId)
                .eq(CodeFieldAnalysis::getValid, 1)
        );
    }

    @Override
    public List<CodeFieldAnalysis> getByVids(long itemId, Set<String> vids) {
        if (CollectionUtils.isEmpty(vids)) {
            return Collections.emptyList();
        }
        return list(new LambdaQueryWrapper<>(CodeFieldAnalysis.class)
                .eq(CodeFieldAnalysis::getItemId, itemId)
                .in(CodeFieldAnalysis::getFieldVid, vids)
                .eq(CodeFieldAnalysis::getValid, 1)
        );
    }

    @Override
    public void batchInsert(List<CodeFieldAnalysis> list) {
        getBaseMapper().batchInsert(list);
    }
}
