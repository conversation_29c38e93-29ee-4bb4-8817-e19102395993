package com.sankuai.deepcode.dao.service;

import com.sankuai.deepcode.dao.domain.SysUserDetail;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * 用户auth2.0详情表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-11
 */
public interface SysUserDetailService extends IService<SysUserDetail> {
    List<SysUserDetail> getDetailsByUserId(Long userId);

    List<SysUserDetail> getDetailsByUserName(String userName);
}
