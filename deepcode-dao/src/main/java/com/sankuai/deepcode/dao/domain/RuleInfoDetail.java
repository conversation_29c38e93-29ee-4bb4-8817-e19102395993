package com.sankuai.deepcode.dao.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * <p>
 * 用户auth2.0详情表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-11
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("rule_info_detail")
public class RuleInfoDetail {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @TableField("user_id")
    private Long userId;

    //变量声明不能是中文
    @TableField("value")
    private String value;

    @TableField("type")
    private int type;

    @TableField("languages")
    private String languages;

    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * valid
     */
    @TableField(value = "valid")
    private boolean valid;
}
