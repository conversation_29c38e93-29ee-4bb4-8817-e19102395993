package com.sankuai.deepcode.dao.domain;

import com.baomidou.mybatisplus.annotation.*;

import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <p>
 * 用户token表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-11
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("sys_token")
public class SysToken {
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @TableField("user_id")
    private Long userId;

    @TableField("token")
    private String token;

    /**
     * token信息
     */
    @TableField("token_info")
    private String tokenInfo;

    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * valid
     */
    @TableField("valid")
    @TableLogic(value = "1", delval = "0")
    private Integer valid;
}
