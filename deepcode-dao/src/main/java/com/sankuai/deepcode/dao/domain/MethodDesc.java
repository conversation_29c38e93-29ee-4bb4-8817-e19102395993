package com.sankuai.deepcode.dao.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.sankuai.deepcode.dao.handlers.BooleanIntegerTypeHandler;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <p>
 * 方法描述
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-11
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("method_desc")
public class MethodDesc {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 任务id
     */
    @TableField("item_id")
    private Long itemId;

    /**
     * vid
     */
    @TableField("method_vid")
    private String methodVid;

    /**
     * 方法注释
     */
    @TableField("method_common_desc")
    private String methodCommonDesc;

    /**
     * 方法描述
     */
    @TableField("method_chat_desc")
    private String methodChatDesc;

    @TableField(value = "valid")
    private Boolean valid;
}
