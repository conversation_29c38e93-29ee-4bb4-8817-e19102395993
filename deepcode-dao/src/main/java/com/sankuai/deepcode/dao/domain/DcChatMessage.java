package com.sankuai.deepcode.dao.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.sankuai.deepcode.dao.po.ChatMsgChildrenPO;
import com.sankuai.deepcode.dao.po.ChatMsgContentPO;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * <p>
 * 聊天对话消息记录
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-17
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName(value = "dc_chat_message", autoResultMap = true)
public class DcChatMessage {

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 用户ID
     */
    @TableField("user_id")
    private Long userId;

    /**
     * 项目ID
     */
    @TableField("project_id")
    private Long projectId;

    /**
     * 分析ID（防止同一个项目有换绑情况）
     */
    @TableField("item_id")
    private Long itemId;

    /**
     * 消息所属的会话uuid
     */
    @TableField("chat_uuid")
    private String chatUuid;

    /**
     * 消息uuid
     */
    @TableField("uuid")
    private String uuid;

    /**
     * 上一条消息ID
     */
    @TableField("prev_msg_id")
    private String prevMsgId;

    /**
     * 子消息id 列表
     */
    @TableField(value = "children_ids", typeHandler = JacksonTypeHandler.class)
    private ChatMsgChildrenPO childrenIds;

    /**
     * 消息发起者身份，system/user/assistant 等
     */
    @TableField("role")
    private String role;

    /**
     * 发出的消息
     */
    @TableField(value = "content", typeHandler = JacksonTypeHandler.class)
    private ChatMsgContentPO content;

    /**
     * 其他扩展信息
     */
    @TableField("extra")
    private String extra;

    /**
     * 是否有效
     */
    @TableField("valid")
    private boolean valid;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    public void addChildrenId(String userMsgUuid) {
        if (childrenIds == null) {
            childrenIds = new ChatMsgChildrenPO();
        }
        childrenIds.addChildId(userMsgUuid);
    }
}
