package com.sankuai.deepcode.dao.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <p>
 * 提示词模板管理
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-03
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("dc_prompt_template")
public class DcPromptTemplate {

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * prompt的唯一key
     */
    @TableField("prompt_key")
    private String promptKey;

    /**
     * prompt内容
     */
    @TableField("content")
    private String content;

    /**
     * 同一个key的多个版本，最后一个为有效版本
     */
    @TableField("version")
    private Integer version;

    /**
     * 创建时间
     */
    @TableField("create_at")
    private LocalDateTime createAt;

    /**
     * 更新时间
     */
    @TableField("update_at")
    private LocalDateTime updateAt;
}
