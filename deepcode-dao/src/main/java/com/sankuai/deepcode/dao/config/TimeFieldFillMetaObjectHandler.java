package com.sankuai.deepcode.dao.config;

import com.baomidou.mybatisplus.core.handlers.MetaObjectHandler;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.reflection.MetaObject;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;

/**
 * Package: IntelliJ IDEA
 * Description:
 *
 * <AUTHOR>
 * @since 2023/3/30 11:13
 */
@Slf4j
@Component
public class TimeFieldFillMetaObjectHandler implements MetaObjectHandler {
    /**
     * FIXME: Entity 字段上，必须存在 FieldFill.INSERT 策略
     *
     * @param metaObject 元对象
     */
    @Override
    public void insertFill(MetaObject metaObject) {
        log.info("insertFill#: {}", metaObject);
        this.strictInsertFill(metaObject, "createTime", LocalDateTime.class, LocalDateTime.now());
        this.strictInsertFill(metaObject, "updateTime", LocalDateTime.class, LocalDateTime.now());
    }

    /**
     * FIXME: Entity 字段上，必须存在 FieldFill.INSERT_UPDATE 策略
     *
     * @param metaObject 元对象
     */
    @Override
    public void updateFill(MetaObject metaObject) {
        log.info("updateFill#, {}", metaObject);
        log.info("updateFill#, {}, updateTime: {}", metaObject, metaObject.getValue("updateTime"));
        this.setFieldValByName("updateTime", LocalDateTime.now(), metaObject);
    }
}
