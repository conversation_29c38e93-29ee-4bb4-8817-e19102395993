package com.sankuai.deepcode.dao.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sankuai.deepcode.dao.domain.MethodInvokeMethod;
import com.sankuai.deepcode.dao.domain.MethodQuoteField;
import com.sankuai.deepcode.dao.mapper.MethodQuoteFieldMapper;
import com.sankuai.deepcode.dao.service.MethodQuoteFieldService;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Set;

/**
 * <p>
 * 方法引用变量 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-11
 */
@Service
public class MethodQuoteFieldServiceImpl extends ServiceImpl<MethodQuoteFieldMapper, MethodQuoteField> implements MethodQuoteFieldService {
    @Override
    public List<MethodQuoteField> getByTargets(long itemId, Set<String> vids) {
        return list(
                new LambdaQueryWrapper<>(MethodQuoteField.class)
                        .eq(MethodQuoteField::getItemId, itemId)
                        .eq(MethodQuoteField::getValid, 1)
                        .in(MethodQuoteField::getSource, vids)
        );
    }

    @Override
    public void batchInsert(List<MethodQuoteField> list) {
        getBaseMapper().batchInsert(list);
    }
}
