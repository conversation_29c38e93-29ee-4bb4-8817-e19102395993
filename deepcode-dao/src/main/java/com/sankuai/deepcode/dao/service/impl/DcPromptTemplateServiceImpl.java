package com.sankuai.deepcode.dao.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sankuai.deepcode.dao.domain.DcPromptTemplate;
import com.sankuai.deepcode.dao.mapper.DcPromptTemplateMapper;
import com.sankuai.deepcode.dao.service.DcPromptTemplateService;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 提示词模板管理 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-03
 */
@Service
public class DcPromptTemplateServiceImpl extends ServiceImpl<DcPromptTemplateMapper, DcPromptTemplate> implements DcPromptTemplateService {

    @Override
    public DcPromptTemplate getPromptTemplateByKey(String promptKey) {
        return getOne(new LambdaQueryWrapper<>(DcPromptTemplate.class)
                        .eq(DcPromptTemplate::getPromptKey, promptKey)
                        .orderByAsc(DcPromptTemplate::getVersion)
                        .last("limit 1"),
                false
        );
    }
}
