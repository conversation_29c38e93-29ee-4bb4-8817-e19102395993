package com.sankuai.deepcode.dao.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Objects;

import java.util.Optional;
import java.util.stream.Stream;

/**
 * Package: com.sankuai.deepcode.dao.enums
 * Description:
 *
 * <AUTHOR>
 * @since 2025/3/12 14:37
 */
@Getter
@AllArgsConstructor
public enum ItemStatusEnum {
    INITIATING(-1, "初始化"),
    PROCESSING(0, "处理中"),
    SUCCESS(1, "成功"),
    FAILED(2, "失败"),

    UNKNOWN(9999, "未知状态");

    @EnumValue
    private final Integer code;
    private final String desc;

    public static ItemStatusEnum getByCode(Integer code) {
        return Arrays.stream(ItemStatusEnum.values())
                .filter(value -> Objects.equals(value.getCode(), code))
                .findFirst()
                .orElse(UNKNOWN);
    }

    public static Optional<ItemStatusEnum> of(Integer code) {
        return Stream.of(ItemStatusEnum.values())
                .filter(itemStatusEnum -> itemStatusEnum.getCode().equals(code))
                .findFirst();
    }
}
