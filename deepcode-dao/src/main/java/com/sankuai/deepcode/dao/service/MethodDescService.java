package com.sankuai.deepcode.dao.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.sankuai.deepcode.dao.domain.MethodDesc;

import java.util.List;

/**
 * <p>
 * 方法描述 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-11
 */
public interface MethodDescService extends IService<MethodDesc> {
    MethodDesc getByMethodVid(long itemId, String methodVid);

    List<MethodDesc> getByItemId(long itemId);
}
