package com.sankuai.deepcode.dao.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.sankuai.deepcode.dao.handlers.BooleanIntegerTypeHandler;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <p>
 * 代码field解析
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-11
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("code_field_analysis")
public class CodeFieldAnalysis {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 任务id
     */
    @TableField("item_id")
    private Long itemId;

    /**
     * vid
     */
    @TableField("field_vid")
    private String fieldVid;

    /**
     * 来源 local本地 unknown未知
     */
    @TableField("source")
    private String source;

    /**
     * module名称
     */
    @TableField("module_name")
    private String moduleName;

    /**
     * 变量名
     */
    @TableField("field_name")
    private String fieldName;

    /**
     * 类路径
     */
    @TableField("class_name")
    private String className;

    /**
     * 内部类名称
     */
    @TableField("in_class_name")
    private String inClassName;

    /**
     * 修饰符
     */
    @TableField("access")
    private String access;

    /**
     * 签名
     */
    @TableField("signatures")
    private String signatures;

    /**
     * 变量类型
     */
    @TableField("field_type")
    private String fieldType;

    /**
     * 值
     */
    @TableField("field_value")
    private String fieldValue;

    /**
     * 注解
     */
    @TableField("annotations")
    private String annotations;

    /**
     * 变更类型
     */
    @TableField("change_type")
    private Integer changeType;

    /**
     * 变更行
     */
    @TableField("change_lines")
    private String changeLines;

    /**
     * 冲突类型
     */
    @TableField("check_type")
    private Integer checkType;

    /**
     * 冲突行
     */
    @TableField("check_lines")
    private String checkLines;

    /**
     * 起始行
     */
    @TableField("start_line")
    private Integer startLine;

    /**
     * 终止行
     */
    @TableField("end_line")
    private Integer endLine;

    /**
     * 注释行
     */
    @TableField("comment_lines")
    private String commentLines;

    @TableField(value = "valid")
    private Boolean valid;
}
