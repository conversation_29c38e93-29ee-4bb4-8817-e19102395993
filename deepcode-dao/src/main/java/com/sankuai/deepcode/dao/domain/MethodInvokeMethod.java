package com.sankuai.deepcode.dao.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.sankuai.deepcode.dao.handlers.BooleanIntegerTypeHandler;
import com.sankuai.deepcode.dao.po.InvokeLinePO;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <p>
 * 方法调用方法
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-11
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName(value = "method_invoke_method", autoResultMap = true)
public class MethodInvokeMethod {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 任务id
     */
    @TableField("item_id")
    private Long itemId;

    /**
     * 起点vid
     */
    @TableField("source")
    private String source;

    /**
     * 起点来源
     */
    @TableField("source_type")
    private String sourceType;

    /**
     * 终点vid
     */
    @TableField("target")
    private String target;

    /**
     * 终点来源
     */
    @TableField("target_type")
    private String targetType;

    @TableField(value = "invoke_params", typeHandler = JacksonTypeHandler.class)
    private List<InvokeLinePO> invokeParams;

    @TableField(value = "valid")
    private Boolean valid;
}
