package com.sankuai.deepcode.dao.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.sankuai.deepcode.dao.handlers.BooleanIntegerTypeHandler;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <p>
 * 代码method解析
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-11
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("code_method_analysis")
@EqualsAndHashCode(of = "methodVid")
public class CodeMethodAnalysis {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 任务id
     */
    @TableField("item_id")
    private Long itemId;

    /**
     * vid
     */
    @TableField("method_vid")
    private String methodVid;

    /**
     * 来源 local本地 unknown未知
     */
    @TableField("source")
    private String source;

    @TableField("module_name")
    private String moduleName;

    /**
     * 方法名
     */
    @TableField("method_name")
    private String methodName;

    /**
     * 类路径
     */
    @TableField("class_name")
    private String className;

    /**
     * 内部类名称
     */
    @TableField("in_class_name")
    private String inClassName;

    /**
     * 修饰符
     */
    @TableField("access")
    private String access;

    /**
     * 参数
     */
    @TableField("params")
    private String params;

    /**
     * 返回值
     */
    @TableField("return_info")
    private String returnInfo;

    /**
     * 注解
     */
    @TableField("annotations")
    private String annotations;

    /**
     * 异常
     */
    @TableField("exceptions")
    private String exceptions;

    /**
     * 变更类型
     */
    @TableField("change_type")
    private Integer changeType;

    /**
     * 变更行
     */
    @TableField("change_lines")
    private String changeLines;

    /**
     * 冲突类型
     */
    @TableField("check_type")
    private Integer checkType;

    /**
     * 冲突行
     */
    @TableField("check_lines")
    private String checkLines;

    /**
     * 起始行
     */
    @TableField("start_line")
    private Integer startLine;

    /**
     * 终止行
     */
    @TableField("end_line")
    private Integer endLine;

    /**
     * 注释行
     */
    @TableField("comment_lines")
    private String commentLines;

    /**
     * 圈复杂度
     */
    @TableField("complexity")
    private Integer complexity;

    @TableField(value = "valid")
    private Boolean valid;
}
