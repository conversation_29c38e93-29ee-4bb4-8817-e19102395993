package com.sankuai.deepcode.dao.milvus;

import com.dianping.lion.client.log.Logger;
import com.dianping.lion.client.log.LoggerFactory;
import com.google.gson.JsonObject;
import com.sankuai.deepcode.dao.config.MilvusConfig;
import io.milvus.v2.client.MilvusClientV2;
import io.milvus.v2.service.collection.request.DropCollectionReq;
import io.milvus.v2.service.collection.request.GetLoadStateReq;
import io.milvus.v2.service.vector.request.*;
import io.milvus.v2.service.vector.request.data.BaseVector;
import io.milvus.v2.service.vector.request.data.EmbeddedText;
import io.milvus.v2.service.vector.request.ranker.BaseRanker;
import io.milvus.v2.service.vector.request.ranker.RRFRanker;
import io.milvus.v2.service.vector.request.ranker.WeightedRanker;
import io.milvus.v2.service.vector.response.InsertResp;
import io.milvus.v2.service.vector.response.QueryResp;
import io.milvus.v2.service.vector.response.SearchResp;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

@Service
public class MilvusService {

    private static final Logger LOGGER = LoggerFactory.getLogger(MilvusService.class);

    @Autowired
    private MilvusConfig milvusConfig;

    public boolean isExist(String collectionName) {
        Boolean res = null;
        try {
            GetLoadStateReq classReq = GetLoadStateReq.builder()
                    .collectionName(collectionName)
                    .build();

            res = milvusConfig.dataSource().getLoadState(classReq);
        } catch (Exception e) {
            LOGGER.error("isCreat error", e);
        }
        return res;
    }

    public void drop(String collectionName) {
        try {
            DropCollectionReq dropCollectionReq = DropCollectionReq.builder()
                    .collectionName(collectionName)
                    .build();

            milvusConfig.dataSource().dropCollection(dropCollectionReq);
        } catch (Exception e) {
            LOGGER.error("delete error", e);
        }
    }

    public InsertResp insert(String collectionNam, String partitionName, List<JsonObject> data) {
        try {
            MilvusClientV2 milvusClientV2 = milvusConfig.dataSource();
            InsertReq insertReq = InsertReq.builder()
                    .collectionName(collectionNam)
                    .partitionName(partitionName)
                    .data(data)
                    .build();
            return milvusClientV2.insert(insertReq);
        } catch (Exception e) {
            LOGGER.error("insert error e:", e);
        }
        return InsertResp.builder().build();
    }


    public SearchResp codeSearch(String collectionName, String partitionName, List<BaseVector> data, String annsField) {
        try {
            List<String> outputFields = new ArrayList<>();
            outputFields.add("file_vid");
            outputFields.add("chunk_id");
            outputFields.add("start_line");
            outputFields.add("end_line");
            outputFields.add("code");
            SearchReq searchReq = SearchReq.builder()
                    .collectionName(collectionName)
                    .partitionNames(Collections.singletonList(partitionName))
                    .data(data)
                    .outputFields(outputFields)
                    .topK(10)
                    .annsField(annsField)
                    .build();
            MilvusClientV2 milvusClientV2 = milvusConfig.dataSource();
            return milvusClientV2.search(searchReq);
        } catch (Exception e) {
            LOGGER.error("search error e:", e);
        }
        return SearchResp.builder().build();
    }

    public SearchResp descSearch(String collectionName, String partitionName, List<BaseVector> data, String annsField) {
        try {
            List<String> outputFields = new ArrayList<>();
            outputFields.add("file_vid");
            outputFields.add("desc");
            SearchReq searchReq = SearchReq.builder()
                    .collectionName(collectionName)
                    .partitionNames(Collections.singletonList(partitionName))
                    .data(data)
                    .outputFields(outputFields)
                    .topK(10)
                    .annsField(annsField)
                    .build();
            MilvusClientV2 milvusClientV2 = milvusConfig.dataSource();
            return milvusClientV2.search(searchReq);
        } catch (Exception e) {
            LOGGER.error("search error e:", e);
        }
        return SearchResp.builder().build();
    }

    public SearchResp bm25CodeSearch(String collectionName, String partitionName, List<String> keys) {
        try {
            StringBuffer sb = new StringBuffer();
            for (String key : keys) {
                sb.append(key).append(" ");
            }
            List<String> outputFields = new ArrayList<>();
            outputFields.add("file_vid");
            outputFields.add("chunk_id");
            outputFields.add("start_line");
            outputFields.add("end_line");
            outputFields.add("code");
            Map<String, Object> searchParams = new HashMap<>();
            searchParams.put("drop_ratio_search", 0.2); // Proportion of small vector values to ignore during the search
            MilvusClientV2 milvusClientV2 = milvusConfig.dataSource();
            SearchReq searchReq = SearchReq.builder()
                    .collectionName(collectionName)
                    .partitionNames(Collections.singletonList(partitionName))
                    .data(Collections.singletonList(new EmbeddedText(sb.toString())))
                    .annsField("code_embeddings")
                    .topK(5)
                    .searchParams(searchParams)
                    .outputFields(outputFields)
                    .build();
            return milvusClientV2.search(searchReq);
        } catch (Exception e) {
            LOGGER.error("bm25CodeSearch error e:", e);
        }
        return SearchResp.builder().build();
    }


    public SearchResp bm25DescSearch(String collectionName, String partitionName, List<String> keys) {
        try {
            StringBuffer sb = new StringBuffer();
            for (String key : keys) {
                sb.append(key).append(" ");
            }
            List<String> outputFields = new ArrayList<>();
            outputFields.add("file_vid");
            outputFields.add("desc");
            Map<String, Object> searchParams = new HashMap<>();
            searchParams.put("drop_ratio_search", 0.2); // Proportion of small vector values to ignore during the search
            MilvusClientV2 milvusClientV2 = milvusConfig.dataSource();
            SearchReq searchReq = SearchReq.builder()
                    .collectionName(collectionName)
                    .partitionNames(Collections.singletonList(partitionName))
                    .data(Collections.singletonList(new EmbeddedText(sb.toString())))
                    .annsField("desc_embeddings")
                    .topK(5)
                    .searchParams(searchParams)
                    .outputFields(outputFields)
                    .build();
            return milvusClientV2.search(searchReq);
        } catch (Exception e) {
            LOGGER.error("bm25DescSearch error e:", e);
        }
        return SearchResp.builder().build();
    }


    public SearchResp codeHybridSearch(String collectionName, String partitionName, List<AnnSearchReq> searchRequests) {
        try {
            List<String> outputFields = new ArrayList<>();
            outputFields.add("file_vid");
            outputFields.add("chunk_id");
            outputFields.add("start_line");
            outputFields.add("end_line");
            outputFields.add("code");
            BaseRanker reranker = new RRFRanker(100);
            HybridSearchReq hybridSearchReq = HybridSearchReq.builder()
                    .collectionName(collectionName)
                    .partitionNames(Collections.singletonList(partitionName))
                    .searchRequests(searchRequests)
                    .outFields(outputFields)
                    .ranker(reranker)
                    .topK(10)
                    .build();
            MilvusClientV2 milvusClientV2 = milvusConfig.dataSource();
            return milvusClientV2.hybridSearch(hybridSearchReq);
        } catch (Exception e) {
            LOGGER.error("hybridSearch error e:", e);
        }
        return SearchResp.builder().build();
    }


    public SearchResp descHybridSearch(String collectionName, String partitionName, List<AnnSearchReq> searchRequests) {
        try {
            List<String> outputFields = new ArrayList<>();
            outputFields.add("file_vid");
            outputFields.add("desc");
            BaseRanker reranker = new RRFRanker(100);
            HybridSearchReq hybridSearchReq = HybridSearchReq.builder()
                    .collectionName(collectionName)
                    .partitionNames(Collections.singletonList(partitionName))
                    .searchRequests(searchRequests)
                    .outFields(outputFields)
                    .ranker(reranker)
                    .topK(10)
                    .build();
            MilvusClientV2 milvusClientV2 = milvusConfig.dataSource();
            return milvusClientV2.hybridSearch(hybridSearchReq);
        } catch (Exception e) {
            LOGGER.error("hybridSearch error e:", e);
        }
        return SearchResp.builder().build();
    }


    public SearchResp hybridSearch(String collectionName, String partitionName, List<AnnSearchReq> searchRequests) {
        try {
            List<String> outputFields = new ArrayList<>();
            outputFields.add("vid");
            outputFields.add("chat_desc");
            outputFields.add("chat_desc_view");
            outputFields.add("desc");
            outputFields.add("desc_view");
            RRFRanker ranker = new RRFRanker(60);
            List<Float> weights = new ArrayList<>();
            weights.add(0.5f);
            weights.add(0.5f);
            WeightedRanker weightedRanker = new WeightedRanker(weights);
            HybridSearchReq hybridSearchReq = HybridSearchReq.builder()
                    .collectionName(collectionName)
                    .partitionNames(Collections.singletonList(partitionName))
                    .searchRequests(searchRequests)
                    .outFields(outputFields)
                    .ranker(weightedRanker)
                    .topK(5)
                    .build();
            MilvusClientV2 milvusClientV2 = milvusConfig.dataSource();
            return milvusClientV2.hybridSearch(hybridSearchReq);
        } catch (Exception e) {
            LOGGER.error("hybridSearch error e:", e);
        }
        return SearchResp.builder().build();
    }

    public List<QueryResp.QueryResult> query(String collectionName, String partitionName) {
        List<QueryResp.QueryResult> res = new ArrayList<>();
        try {
            List<String> outputFields = new ArrayList<>();
            outputFields.add("vid");
            outputFields.add("chat_desc");
            outputFields.add("chat_desc_view");
            outputFields.add("desc");
            outputFields.add("desc_view");
            MilvusClientV2 milvusClientV2 = milvusConfig.dataSource();

            long offset = 0;
            int BATCH_SIZE = 500;
            boolean hasMoreData = true;

            while (hasMoreData) {
                QueryReq queryReq = QueryReq.builder()
                        .collectionName(collectionName)
                        .partitionNames(Collections.singletonList(partitionName))
                        .outputFields(outputFields)
                        .offset(offset)
                        .limit(BATCH_SIZE)
                        .filter("")
                        .build();
                QueryResp queryResp = milvusClientV2.query(queryReq);
                List<QueryResp.QueryResult> list = new ArrayList<>();
                if (null != queryResp.getQueryResults()) {
                    for (QueryResp.QueryResult queryResult : queryResp.getQueryResults()) {
                        list.add(queryResult);
                    }
                }
                res.addAll(list);
                if (list.size() < BATCH_SIZE) {
                    hasMoreData = false;
                } else {
                    offset += BATCH_SIZE;
                }
            }
            return res;
        } catch (Exception e) {
            LOGGER.error("query error e:", e);
        }
        return res;
    }


    public SearchResp searchApiChat(String collectionName, String partitionName, List<BaseVector> data, String annsField) {
        try {
            List<String> outputFields = new ArrayList<>();
            outputFields.add("id");
            outputFields.add("appkey");
            outputFields.add("class_name");
            outputFields.add("method_name");
            outputFields.add("desc_by_qwen");
            SearchReq searchReq = SearchReq.builder()
                    .collectionName(collectionName)
                    .partitionNames(Collections.singletonList(partitionName))
                    .data(data)
                    .outputFields(outputFields)
                    .topK(20)
                    .annsField(annsField)
                    .build();
            MilvusClientV2 milvusClientV2 = milvusConfig.dataSource();
            return milvusClientV2.search(searchReq);
        } catch (Exception e) {
            LOGGER.error("search error e:", e);
        }
        return SearchResp.builder().build();
    }

    public QueryResp query(String collectionName, String partitionName, String fileVid) {
        try {
            List<String> outputFields = new ArrayList<>();
            outputFields.add("file_vid");
            outputFields.add("desc");
            List<Object> ids = new ArrayList<>();
            QueryReq queryReq = QueryReq.builder()
                    .collectionName(collectionName)
                    .partitionNames(Collections.singletonList(partitionName))
                    .filter("file_vid == '" + fileVid + "'")
                    .build();
            MilvusClientV2 milvusClientV2 = milvusConfig.dataSource();
            return milvusClientV2.query(queryReq);
        } catch (Exception e) {
            LOGGER.error("search error e:", e);
        }
        return QueryResp.builder().build();
    }


}
