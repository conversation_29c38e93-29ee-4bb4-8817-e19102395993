package com.sankuai.deepcode.dao.enums;

import com.baomidou.mybatisplus.annotation.IEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 */

@Getter
@AllArgsConstructor
public enum StageTaskStatus implements IEnum<Integer> {
    PENDING(0, "待处理"),
    IN_PROGRESS(1, "进行中"),
    COMPLETED(2, "已完成"),
    CANCELLED(3, "已取消"),
    FAILED(4, "已失败");

    private final Integer value;
    private final String description;


    public static StageTaskStatus fromCode(int code) {
        for (StageTaskStatus status : StageTaskStatus.values()) {
            if (status.getValue() == code) {
                return status;
            }
        }
        throw new IllegalArgumentException("未知的阶段任务状态码: " + code);
    }
}
