package com.sankuai.deepcode.dao.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sankuai.deepcode.dao.domain.MethodInvokeMethod;
import com.sankuai.deepcode.dao.mapper.MethodInvokeMethodMapper;
import com.sankuai.deepcode.dao.service.MethodInvokeMethodService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Set;

/**
 * <p>
 * 方法调用方法 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-11
 */
@Service
public class MethodInvokeMethodServiceImpl extends ServiceImpl<MethodInvokeMethodMapper, MethodInvokeMethod> implements MethodInvokeMethodService {
    @Override
    public List<MethodInvokeMethod> getBySource(long itemId, String source) {
        return list(
                new LambdaQueryWrapper<>(MethodInvokeMethod.class)
                        .eq(MethodInvokeMethod::getItemId, itemId)
                        .eq(MethodInvokeMethod::getSource, source)
                        .eq(MethodInvokeMethod::getValid, 1)
        );
    }

    @Override
    public List<MethodInvokeMethod> getByTarget(long itemId, String target) {
        return list(
                new LambdaQueryWrapper<>(MethodInvokeMethod.class)
                        .eq(MethodInvokeMethod::getItemId, itemId)
                        .eq(MethodInvokeMethod::getTarget, target)
                        .eq(MethodInvokeMethod::getValid, 1)
        );
    }

    @Override
    public List<MethodInvokeMethod> getByTargets(long itemId, Set<String> vids) {
        if (CollectionUtils.isEmpty(vids)) {
            return Collections.emptyList();
        }
        return list(
                new LambdaQueryWrapper<>(MethodInvokeMethod.class)
                        .eq(MethodInvokeMethod::getItemId, itemId)
                        .eq(MethodInvokeMethod::getValid, 1)
                        .in(MethodInvokeMethod::getTarget, vids)
        );
    }

    @Override
    public void batchInsert(List<MethodInvokeMethod> list) {
        getBaseMapper().batchInsert(list);
    }

    @Override
    public List<MethodInvokeMethod> getByItemAndMethodVids(Long itemId, List<String> methodVids) {
        if (CollectionUtils.isEmpty(methodVids)) {
            return Collections.emptyList();
        }
        return list(
                new LambdaQueryWrapper<>(MethodInvokeMethod.class)
                        .eq(MethodInvokeMethod::getItemId, itemId)
                        .eq(MethodInvokeMethod::getValid, 1)
                        .in(MethodInvokeMethod::getSource, methodVids)
        );
    }

    @Override
    public List<MethodInvokeMethod> getByTargetMethodVid(Long itemId, String targetMethodVid) {
        return list(
                new LambdaQueryWrapper<>(MethodInvokeMethod.class)
                        .eq(MethodInvokeMethod::getItemId, itemId)
                        .eq(MethodInvokeMethod::getTarget, targetMethodVid)
                        .eq(MethodInvokeMethod::getValid, 1)
        );
    }

    @Override
    public List<MethodInvokeMethod> recursivelyGetMethodInvokes(Long itemId, String beginMethodVid, Integer invokeType) {
        if (StringUtils.isBlank(beginMethodVid)) {
            return Collections.emptyList();
        }
        if (invokeType == 2) {
            return getBaseMapper().recursivelyGetUpLinkMethodInvokes(itemId, beginMethodVid);
        } else if (invokeType == 1) {
            return getBaseMapper().recursivelyGetDownLinkMethodInvokes(itemId, beginMethodVid);
        }
        return Collections.emptyList();
    }
}
