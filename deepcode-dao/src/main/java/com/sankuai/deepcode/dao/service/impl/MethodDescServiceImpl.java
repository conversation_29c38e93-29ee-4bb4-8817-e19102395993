package com.sankuai.deepcode.dao.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sankuai.deepcode.dao.domain.MethodDesc;
import com.sankuai.deepcode.dao.mapper.MethodDescMapper;
import com.sankuai.deepcode.dao.service.MethodDescService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 方法描述 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-11
 */
@Service
public class MethodDescServiceImpl extends ServiceImpl<MethodDescMapper, MethodDesc> implements MethodDescService {
    @Override
    public MethodDesc getByMethodVid(long itemId, String methodVid) {
        return getOne(
                new LambdaQueryWrapper<>(MethodDesc.class)
                        .eq(MethodDesc::getItemId, itemId)
                        .eq(MethodDesc::getMethodVid, methodVid)
                        .eq(MethodDesc::getValid, 1)
                        .last("limit 1")
        );
    }

    @Override
    public List<MethodDesc> getByItemId(long itemId) {
        return list(
                new LambdaQueryWrapper<>(MethodDesc.class)
                        .eq(MethodDesc::getItemId, itemId)
                        .eq(MethodDesc::getValid, 1)
        );
    }
}
