package com.sankuai.deepcode.dao.config;

import com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor;
import com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean;
import com.dianping.zebra.group.jdbc.GroupDataSource;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.stereotype.Component;

import javax.sql.DataSource;
import java.io.IOException;

@Component
public class DataSourceConfig {

    @Autowired
    private MybatisPlusProperties mybatisPlusProperties;

    @Autowired
    private ZebraProperties zebraProperties;

    @Bean
    public DataSource zebraDataSource() {
        GroupDataSource ds = new GroupDataSource(zebraProperties.getJdbcref());
        ds.setExtraJdbcUrlParams(zebraProperties.getExtraParams());
        ds.setMaxPoolSize(zebraProperties.getMaxPoolSize());
        ds.init();
        return ds;
    }

    @Bean(name = "sqlSessionFactory")
    public MybatisSqlSessionFactoryBean sqlSessionFactory(
            DataSource dataSource,
            MybatisPlusInterceptor interceptor,
            TimeFieldFillMetaObjectHandler metaObjectHandler
    ) throws IOException {
        MybatisSqlSessionFactoryBean ssfb = new MybatisSqlSessionFactoryBean();
        ssfb.setDataSource(dataSource);
        ssfb.setPlugins(interceptor);

        // 从配置文件读取 mapper 位置
        PathMatchingResourcePatternResolver resolver = new PathMatchingResourcePatternResolver();
        ssfb.setMapperLocations(resolver.getResources(mybatisPlusProperties.getMapperLocations()));

        // 从配置文件读取别名包
        ssfb.setTypeAliasesPackage(mybatisPlusProperties.getTypeAliasesPackage());

        // 从配置文件读取枚举包
        ssfb.setTypeEnumsPackage(mybatisPlusProperties.getTypeEnumsPackage());

        // 使用配置文件中的全局配置
        ssfb.setGlobalConfig(mybatisPlusProperties.convertToGlobalConfig(metaObjectHandler));
        return ssfb;
    }
}
