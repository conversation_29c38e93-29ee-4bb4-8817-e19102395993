package com.sankuai.deepcode.dao.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sankuai.deepcode.dao.domain.CodeAnalysisItemDetail;
import com.sankuai.deepcode.dao.enums.ItemStatusEnum;
import com.sankuai.deepcode.dao.mapper.CodeAnalysisItemDetailMapper;
import com.sankuai.deepcode.dao.service.CodeAnalysisItemDetailService;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

/**
 * <p>
 * 代码解析任务详情表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-12
 */
@Service
public class CodeAnalysisItemDetailServiceImpl extends ServiceImpl<CodeAnalysisItemDetailMapper, CodeAnalysisItemDetail> implements CodeAnalysisItemDetailService {

    @Override
    public List<CodeAnalysisItemDetail> getByItemId(Long itemId) {
        if (itemId == null || itemId <= 0) {
            return Collections.emptyList();
        }
        return list(new LambdaQueryWrapper<>(CodeAnalysisItemDetail.class)
                .eq(CodeAnalysisItemDetail::getItemId, itemId)
                .eq(CodeAnalysisItemDetail::isValid, true)
                .orderByAsc(CodeAnalysisItemDetail::getUtime)
        );
    }

    @Override
    public List<CodeAnalysisItemDetail> getByItemIdAndType(Long itemId, String type) {
        return list(new LambdaQueryWrapper<>(CodeAnalysisItemDetail.class)
                .eq(CodeAnalysisItemDetail::getItemId, itemId)
                .eq(CodeAnalysisItemDetail::getType, type)
                .eq(CodeAnalysisItemDetail::isValid, true)
        );
    }

    @Override
    public CodeAnalysisItemDetail getTimeOutDetailByItemId(Long itemId) {
        List<CodeAnalysisItemDetail> list = list(new LambdaQueryWrapper<>(CodeAnalysisItemDetail.class)
                .eq(CodeAnalysisItemDetail::getItemId, itemId)
                .eq(CodeAnalysisItemDetail::getStatus, ItemStatusEnum.PROCESSING.getCode())
                .eq(CodeAnalysisItemDetail::isValid, true)
                .last("limit 1"));
        return list.get(0);
    }

    @Override
    public List<CodeAnalysisItemDetail> getAllAsyncByItemId(Long itemId) {
        return list(new LambdaQueryWrapper<>(CodeAnalysisItemDetail.class)
                .eq(CodeAnalysisItemDetail::getItemId, itemId)
                .eq(CodeAnalysisItemDetail::isAsync, 1)
                .eq(CodeAnalysisItemDetail::isValid, true)
        );
    }

}
