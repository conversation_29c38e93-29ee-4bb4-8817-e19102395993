package com.sankuai.deepcode.dao.milvus;

import com.dianping.lion.client.log.Logger;
import com.dianping.lion.client.log.LoggerFactory;
import com.sankuai.deepcode.dao.config.MilvusConfig;
import io.milvus.common.clientenum.FunctionType;
import io.milvus.v2.common.DataType;
import io.milvus.v2.common.IndexParam;
import io.milvus.v2.service.collection.request.AddFieldReq;
import io.milvus.v2.service.collection.request.CreateCollectionReq;
import io.milvus.v2.service.collection.request.GetLoadStateReq;
import io.milvus.v2.service.partition.request.CreatePartitionReq;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

@Service
public class KnowledgeInit {

    private static final Logger LOGGER = LoggerFactory.getLogger(KnowledgeInit.class);

    @Autowired
    private MilvusConfig milvusConfig;

    public void createCollection(long itemId) {
        try {
            CreateCollectionReq classRequest = CreateCollectionReq.builder()
                    .collectionName("item_desc_" + itemId)
                    .collectionSchema(addField())
                    .indexParams(addIndex())
                    .build();
            milvusConfig.dataSource().createCollection(classRequest);

            GetLoadStateReq classReq = GetLoadStateReq.builder()
                    .collectionName("item_desc_" + itemId)
                    .build();

            Boolean res = milvusConfig.dataSource().getLoadState(classReq);
            LOGGER.info("create item_desc collection res:{}", res);

            CreatePartitionReq classPartitionReq = CreatePartitionReq.builder()
                    .collectionName("item_desc_" + itemId)
                    .partitionName("partition0")
                    .build();
            milvusConfig.dataSource().createPartition(classPartitionReq);
        } catch (Exception e) {
            LOGGER.error("create item_desc_ collection error", e);
        }
    }

    public CreateCollectionReq.CollectionSchema addField() {
        CreateCollectionReq.CollectionSchema schema = null;
        try {
            schema = milvusConfig.dataSource().createSchema();
            schema.addField(AddFieldReq.builder()
                    .fieldName("id")
                    .dataType(DataType.Int64)
                    .isPrimaryKey(true)
                    .autoID(true)
                    .build());

            schema.addField(AddFieldReq.builder()
                    .fieldName("embedding")
                    .dataType(DataType.FloatVector)
                    .dimension(1024)
                    .build());

            schema.addField(AddFieldReq.builder()
                    .fieldName("file_vid")
                    .dataType(DataType.VarChar)
                    .maxLength(32)
                    .build());

            Map<String, Object> analyzerParams = new HashMap<>();
            analyzerParams.put("tokenizer", "standard");
            schema.addField(AddFieldReq.builder()
                    .fieldName("desc")
                    .dataType(DataType.valueOf("VarChar"))
                    .maxLength(65535)
                    .analyzerParams(analyzerParams)
                    .enableMatch(true)
                    .enableAnalyzer(true)
                    .build());

            schema.addField(AddFieldReq.builder()
                    .fieldName("desc_embeddings")
                    .dataType(DataType.SparseFloatVector)
                    .build());

            schema.addFunction(CreateCollectionReq.Function.builder()
                    .name("BM25_0")
                    .inputFieldNames(Arrays.asList("desc"))
                    .outputFieldNames(Arrays.asList("desc_embeddings"))
                    .functionType(FunctionType.BM25)
                    .build()
            );
        } catch (IllegalArgumentException e) {
            LOGGER.error("addField error", e);
        } finally {
            milvusConfig.dataSource().close();
        }
        return schema;
    }

    public List<IndexParam> addIndex() {
        List<IndexParam> indexParams = null;
        try {
            IndexParam id = IndexParam.builder()
                    .fieldName("id")
                    .indexType(IndexParam.IndexType.AUTOINDEX)
                    .build();

            IndexParam embedding = IndexParam.builder()
                    .fieldName("embedding")
                    .indexType(IndexParam.IndexType.AUTOINDEX)
                    .metricType(IndexParam.MetricType.COSINE)
                    .build();

            IndexParam file_vid = IndexParam.builder()
                    .fieldName("file_vid")
                    .indexType(IndexParam.IndexType.AUTOINDEX)
                    .build();

            IndexParam desc = IndexParam.builder()
                    .fieldName("desc")
                    .indexType(IndexParam.IndexType.AUTOINDEX)
                    .build();

            IndexParam desc_embeddings = IndexParam.builder()
                    .fieldName("desc_embeddings")
                    .indexType(IndexParam.IndexType.AUTOINDEX)
                    .metricType(IndexParam.MetricType.BM25)
                    .build();


            indexParams = new ArrayList<>();
            indexParams.add(id);
            indexParams.add(embedding);
            indexParams.add(file_vid);
            indexParams.add(desc);
            indexParams.add(desc_embeddings);
        } catch (Exception e) {
            LOGGER.error("addIndex error", e);
        } finally {
            milvusConfig.dataSource().close();
        }
        return indexParams;
    }
}
