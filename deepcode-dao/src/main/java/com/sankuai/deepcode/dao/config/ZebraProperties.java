package com.sankuai.deepcode.dao.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * Zebra 数据源配置属性类
 * <AUTHOR>
 */
@Data
@Component
@ConfigurationProperties(prefix = "zebra.datasource")
public class ZebraProperties {
    
    /**
     * Zebra 数据源 JDBC 引用名
     */
    private String jdbcref;
    
    /**
     * 最大连接池大小
     */
    private int maxPoolSize = 100;
    
    /**
     * 额外的 JDBC 参数
     */
    private String extraParams;
}