package com.sankuai.deepcode.dao.enums;

import java.util.stream.Stream;

public enum ItemStepEnum {
    DOWNLOAD(0, "download", "下载代码", false),
    FILE(1, "file", "文件diff与归类", false),
    JAVA(2, "java", "解析java", false),
    PYTHON(3, "python", "解析python", false),
    FOREEND(4, "foreEnd", "解析前端资源文件", false),
    DATA(5, "data", "关系数据存储", false),
    EMBEDDING(6, "embedding", "源文本向量化", true),
    KNOWLEDGE(7, "knowledge", "源文本知识图谱 ", true),
    AI_REVIEW(8, "aiReview", "AI代码审查", true),
    ;

    private int code;
    private String typeName;
    private String desc;
    private boolean async;

    ItemStepEnum(int code, String typeName, String desc, boolean async) {
        this.code = code;
        this.typeName = typeName;
        this.desc = desc;
        this.async = async;
    }

    public int getCode() {
        return this.code;
    }

    public String getTypeName() {
        return this.typeName;
    }

    public String getDesc() {
        return desc;
    }

    public boolean isAsync() {
        return async;
    }

    public static ItemStepEnum fromType(String typeName) {
        return Stream.of(ItemStepEnum.values())
                .filter(itemStepEnum -> itemStepEnum.getTypeName().equals(typeName))
                .findFirst()
                .orElse(null);
    }

    @Override
    public String toString() {
        return "ItemStepEnum{" +
                "code=" + code +
                ", typeName='" + typeName + '\'' +
                ", desc='" + desc + '\'' +
                '}';
    }
}
