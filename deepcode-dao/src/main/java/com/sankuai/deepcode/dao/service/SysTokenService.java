package com.sankuai.deepcode.dao.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.sankuai.deepcode.dao.domain.SysToken;

import java.util.List;
import java.util.Optional;

/**
 * <p>
 * 用户token表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-11
 */
public interface SysTokenService extends IService<SysToken> {
    List<SysToken> getTokenByUserId(Long userId);

    /**
     * 获取用户最新的token
     *
     * @param userId 用户id
     * @return 最新的token
     */
    Optional<SysToken> getLatestTokenByUserId(Long userId);
}
