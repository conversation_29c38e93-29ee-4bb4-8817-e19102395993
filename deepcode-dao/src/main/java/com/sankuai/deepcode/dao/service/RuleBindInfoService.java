package com.sankuai.deepcode.dao.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.sankuai.deepcode.dao.domain.RuleBindInfo;

import java.util.List;


public interface RuleBindInfoService extends IService<RuleBindInfo> {


    List<RuleBindInfo> getRuleBindInfoByProjectId(long projectId);

    /**
     * 获取用户「公共」的规则绑定
     *
     * @param userId
     * @return
     */
    List<RuleBindInfo> getRuleBindInfoByUserId(Long userId);

    boolean unbind(Long bindId, long userId);

    int bind(Long ruleId, Long projectId, long userId);
}
