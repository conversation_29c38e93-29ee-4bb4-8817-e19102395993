package com.sankuai.deepcode.dao.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <p>
 * 前端代码解析
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-28
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("fore_end_node")
public class ForeEndNode {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 任务id
     */
    @TableField("item_id")
    private Long itemId;

    /**
     * vid
     */
    @TableField("node_vid")
    private String nodeVid;

    /**
     * 类型  0 html，1 js method，2 js field ，3样式
     */
    @TableField("node_type")
    private Integer nodeType;

    @TableField("file_vid")
    private String fileVid;

    /**
     * 文件路径
     */
    @TableField("file_path")
    private String filePath;

    /**
     * 注解
     */
    @TableField("annotations")
    private String annotations;

    /**
     * 变更类型
     */
    @TableField("change_type")
    private Integer changeType;

    /**
     * 变更行
     */
    @TableField("change_lines")
    private String changeLines;

    /**
     * 冲突类型
     */
    @TableField("check_type")
    private Integer checkType;

    /**
     * 冲突行
     */
    @TableField("check_lines")
    private String checkLines;

    /**
     * 起始行
     */
    @TableField("start_line")
    private Integer startLine;

    /**
     * 终止行
     */
    @TableField("end_line")
    private Integer endLine;

    /**
     * 注释行
     */
    @TableField("comment_lines")
    private String commentLines;

    /**
     * 拓展字段
     */
    @TableField("expand")
    private String expand;

    @TableField("valid")
    private Boolean valid;
}
