package com.sankuai.deepcode.dao.service.impl;

import com.sankuai.deepcode.dao.domain.ClassImportClass;
import com.sankuai.deepcode.dao.domain.ForeEndEdge;
import com.sankuai.deepcode.dao.mapper.ForeEndEdgeMapper;
import com.sankuai.deepcode.dao.service.ForeEndEdgeService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 前端边关系表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-28
 */
@Service
public class ForeEndEdgeServiceImpl extends ServiceImpl<ForeEndEdgeMapper, ForeEndEdge> implements ForeEndEdgeService {

    @Override
    public void batchInsert(List<ForeEndEdge> list) {
        getBaseMapper().batchInsert(list);
    }
}
