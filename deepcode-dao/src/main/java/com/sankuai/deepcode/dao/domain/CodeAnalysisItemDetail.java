package com.sankuai.deepcode.dao.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;

import java.time.LocalDateTime;

import com.sankuai.deepcode.dao.handlers.BooleanIntegerTypeHandler;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <p>
 * 代码解析任务详情表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-12
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("code_analysis_item_detail")
public class CodeAnalysisItemDetail {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 关联的item表id
     */
    @TableField("item_id")
    private Long itemId;

    /**
     * 任务标题
     */
    @TableField("type")
    private String type;

    /**
     * 任务开始内容
     */
    @TableField("content_start")
    private String contentStart;

    /**
     * 任务结束内容
     */
    @TableField("content_end")
    private String contentEnd;

    /**
     * 当前状态 -1初始化 0进行中 1成功 2失败
     */
    @TableField("status")
    private Integer status;

    /**
     * 拓展字段
     */
    @TableField("extern")
    private String extern;

    /**
     * 是否是异步任务
     */
    @TableField("async")
    private boolean async;

    /**
     * 创建时间
     */
    @TableField("ctime")
    private LocalDateTime ctime;

    /**
     * 更新时间
     */
    @TableField("utime")
    private LocalDateTime utime;

    @TableField(value = "valid")
    private boolean valid;
}
