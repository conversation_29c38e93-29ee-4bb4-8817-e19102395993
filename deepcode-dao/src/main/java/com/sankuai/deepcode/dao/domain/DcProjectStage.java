package com.sankuai.deepcode.dao.domain;

import com.baomidou.mybatisplus.annotation.*;

import java.time.LocalDateTime;

import com.sankuai.deepcode.dao.enums.StageEnum;
import com.sankuai.deepcode.dao.enums.StageStatus;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <p>
 * 项目分析阶段信息
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-10
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("dc_project_stage")
public class DcProjectStage {

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 关联的项目ID
     */
    @TableField("project_id")
    private Long projectId;

    /**
     * 阶段类型
     */
    @TableField("stage")
    private StageEnum stage;

    /**
     * 阶段状态
     */
    @TableField("stage_status")
    private StageStatus stageStatus;

    /**
     * 阶段排序值
     */
    @TableField("stage_order")
    private Integer stageOrder;

    /**
     * 阶段描述
     */
    @TableField("stage_desc")
    private String stageDesc;

    /**
     * 阶段结果数据（JSON格式）
     */
    @TableField("result_data")
    private String resultData;

    /**
     * 阶段错误信息
     */
    @TableField("error_info")
    private String errorInfo;

    /**
     * 是否有效：1-有效，0-无效
     */
    @TableField("valid")
    @TableLogic
    private Integer valid;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;
}
