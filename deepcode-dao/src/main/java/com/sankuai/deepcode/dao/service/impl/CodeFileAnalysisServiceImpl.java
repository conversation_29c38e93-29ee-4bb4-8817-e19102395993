package com.sankuai.deepcode.dao.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sankuai.deepcode.dao.domain.CodeFileAnalysis;
import com.sankuai.deepcode.dao.mapper.CodeFileAnalysisMapper;
import com.sankuai.deepcode.dao.service.CodeFileAnalysisService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

/**
 * <p>
 * 代码file解析 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-11
 */
@Service
public class CodeFileAnalysisServiceImpl extends ServiceImpl<CodeFileAnalysisMapper, CodeFileAnalysis> implements CodeFileAnalysisService {
    @Override
    public List<CodeFileAnalysis> getByItemId(long itemId) {
        return list(new LambdaQueryWrapper<>(CodeFileAnalysis.class)
                .eq(CodeFileAnalysis::getItemId, itemId)
                .eq(CodeFileAnalysis::getValid, 1)
        );
    }

    @Override
    public List<CodeFileAnalysis> getAllFilePathByItemId(long itemId) {
        return list(
                new LambdaQueryWrapper<>(CodeFileAnalysis.class)
                        .eq(CodeFileAnalysis::getItemId, itemId)
                        .eq(CodeFileAnalysis::getValid, 1)
        );
    }

    @Override
    public Optional<CodeFileAnalysis> getByItemIdAndFilePath(Long itemId, String filePath) {
        return getOneOpt(
                new LambdaQueryWrapper<>(CodeFileAnalysis.class)
                        .eq(CodeFileAnalysis::getItemId, itemId)
                        .eq(CodeFileAnalysis::getFilePath, filePath)
                        .eq(CodeFileAnalysis::getValid, 1)
                        .last("limit 1")
        );
    }

    @Override
    public Optional<CodeFileAnalysis> getByItemAndVid(Long itemId, String fileVid) {
        return getOneOpt(
                new LambdaQueryWrapper<>(CodeFileAnalysis.class)
                        .eq(CodeFileAnalysis::getItemId, itemId)
                        .eq(CodeFileAnalysis::getFileVid, fileVid)
                        .eq(CodeFileAnalysis::getValid, 1)
                        .last("limit 1")
        );
    }

    @Override
    public void batchInsert(List<CodeFileAnalysis> list) {
        getBaseMapper().batchInsert(list);
    }

    @Override
    public List<CodeFileAnalysis> getDiffCodeFilesByItemId(Long itemId) {
        return list(new LambdaQueryWrapper<>(CodeFileAnalysis.class)
                .eq(CodeFileAnalysis::getItemId, itemId)
                // 0-无变更 1-新增 2-变更
                .in(CodeFileAnalysis::getChangeType, Arrays.asList(1, 2))
        );
    }

    @Override
    public List<CodeFileAnalysis> getByItemAndVids(Long itemId, List<String> fileVids) {
        if (CollectionUtils.isEmpty(fileVids)) {
            return Collections.emptyList();
        }
        return list(
                new LambdaQueryWrapper<>(CodeFileAnalysis.class)
                        .eq(CodeFileAnalysis::getItemId, itemId)
                        .in(CodeFileAnalysis::getFileVid, fileVids)
                        .eq(CodeFileAnalysis::getValid, 1)
        );
    }
}
