package com.sankuai.deepcode.dao.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;


@Getter
@Setter
@Accessors(chain = true)
@TableName("rule_task_detail")
public class RuleTaskDetail {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @TableField("item_id")
    private Long itemId;

    @TableField("rule_id")
    private long ruleId;

    @TableField("file_vid")
    private String fileVid;

    @TableField("start_line")
    private int startLine;

    @TableField("value")
    private String value;


    @TableField("type")
    private int type;

    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * valid
     */
    @TableField(value = "valid")
    private boolean valid;

}
