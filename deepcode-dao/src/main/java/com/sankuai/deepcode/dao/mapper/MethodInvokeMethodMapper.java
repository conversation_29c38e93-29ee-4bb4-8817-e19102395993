package com.sankuai.deepcode.dao.mapper;

import com.sankuai.deepcode.dao.domain.MethodInvokeMethod;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

import java.util.List;

/**
 * <p>
 * 方法调用方法 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-11
 */
public interface MethodInvokeMethodMapper extends BaseMapper<MethodInvokeMethod> {
    void batchInsert(List<MethodInvokeMethod> list);

    List<MethodInvokeMethod> recursivelyGetUpLinkMethodInvokes(Long itemId, String beginMethodVid);

    List<MethodInvokeMethod> recursivelyGetDownLinkMethodInvokes(Long itemId, String beginMethodVid);
}
