package com.sankuai.deepcode.dao.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sankuai.deepcode.dao.domain.DcProject;
import com.sankuai.deepcode.dao.mapper.DcProjectMapper;
import com.sankuai.deepcode.dao.service.DcProjectService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.Optional;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-04
 */
@Service
@Slf4j
public class DcProjectServiceImpl extends ServiceImpl<DcProjectMapper, DcProject> implements DcProjectService {

    @Override
    public Page<DcProject> getProjectListByUser(Long userId, Integer pageNum, Integer pageSize) {
        Page<DcProject> page = new Page<>(pageNum, pageSize);
        QueryWrapper<DcProject> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
                .eq(DcProject::getUserId, userId)
                .eq(DcProject::isValid, 1)
                .orderByDesc(DcProject::getCreateTime);

        return page(page, queryWrapper);
    }

    @Override
    public boolean updateProjectItem(Long projectId, Long itemId) {
        Optional<DcProject> project = getOptById(projectId);
        if (!project.isPresent()) {
            log.warn("#DcProjectServiceImpl.updateProjectItem#项目不存在, projectId:{}", projectId);
            return false;
        }
        DcProject updateProject = new DcProject()
                .setId(projectId)
                .setItemId(itemId)
                .setUpdateTime(LocalDateTime.now());

        return updateById(updateProject);
    }

    @Override
    public DcProject getProjectByItemId(Long itemId) {
        return getOne(new QueryWrapper<DcProject>().lambda().eq(DcProject::getItemId, itemId)
                .orderByAsc(DcProject::getCreateTime).last("limit 1"), false);
    }
}
