package com.sankuai.deepcode.dao.service;

import com.sankuai.deepcode.dao.domain.CodeFileAnalysis;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;
import java.util.Optional;

/**
 * <p>
 * 代码file解析 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-11
 */
public interface CodeFileAnalysisService extends IService<CodeFileAnalysis> {
    List<CodeFileAnalysis> getByItemId( long itemId);

    List<CodeFileAnalysis> getAllFilePathByItemId(long itemId);

    Optional<CodeFileAnalysis> getByItemIdAndFilePath(Long itemId, String filePath);

    Optional<CodeFileAnalysis> getByItemAndVid(Long itemId, String fileVid);

    List<CodeFileAnalysis> getByItemAndVids(Long itemId, List<String> fileVids);

    void batchInsert(List<CodeFileAnalysis> list);


    List<CodeFileAnalysis> getDiffCodeFilesByItemId(Long itemId);
}
