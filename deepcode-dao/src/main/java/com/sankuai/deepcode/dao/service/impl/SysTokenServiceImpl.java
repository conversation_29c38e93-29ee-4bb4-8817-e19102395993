package com.sankuai.deepcode.dao.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sankuai.deepcode.dao.domain.SysToken;
import com.sankuai.deepcode.dao.mapper.SysTokenMapper;
import com.sankuai.deepcode.dao.service.SysTokenService;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

/**
 * <p>
 * 用户token表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-11
 */
@Service
public class SysTokenServiceImpl extends ServiceImpl<SysTokenMapper, SysToken> implements SysTokenService {
    @Override
    public List<SysToken> getTokenByUserId(Long userId) {
        return list(
                new LambdaQueryWrapper<>(SysToken.class)
                        .eq(SysToken::getUserId, userId)
                        .orderByDesc(SysToken::getCreateTime)
        );
    }

    @Override
    public Optional<SysToken> getLatestTokenByUserId(Long userId) {
        return getOneOpt(
                new LambdaQueryWrapper<>(SysToken.class)
                        .eq(SysToken::getUserId, userId)
                        .orderByDesc(SysToken::getCreateTime)
                        .last("limit 1")
        );
    }
}
