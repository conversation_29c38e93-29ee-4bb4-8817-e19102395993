package com.sankuai.deepcode.dao.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sankuai.deepcode.dao.domain.DcChatMessage;
import com.sankuai.deepcode.dao.mapper.DcChatMessageMapper;
import com.sankuai.deepcode.dao.service.DcChatMessageService;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

/**
 * <p>
 * 聊天对话消息记录 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-14
 */
@Service
public class DcChatMessageServiceImpl extends ServiceImpl<DcChatMessageMapper, DcChatMessage> implements DcChatMessageService {

    @Override
    public List<DcChatMessage> getByChatId(String chatUuid, Long userId) {
        return list(
                new LambdaQueryWrapper<>(DcChatMessage.class)
                        .eq(DcChatMessage::getChatUuid, chatUuid)
                        .eq(DcChatMessage::getUserId, userId)
                        .eq(DcChatMessage::isValid, 1)
        );
    }

    @Override
    public Optional<DcChatMessage> getMsgByUuId(String uuid, Long userId) {
        return getOneOpt(new LambdaQueryWrapper<>(DcChatMessage.class)
                .eq(DcChatMessage::getUuid, uuid)
                .eq(DcChatMessage::getUserId, userId)
                .eq(DcChatMessage::isValid, 1)
                .last("limit 1")
        );
    }
}
