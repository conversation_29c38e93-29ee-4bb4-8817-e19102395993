package com.sankuai.deepcode.dao.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.sankuai.deepcode.dao.domain.DcProjectTask;
import com.sankuai.deepcode.dao.enums.StageEnum;
import com.sankuai.deepcode.dao.enums.StageTaskEnum;

import java.util.List;
import java.util.Optional;

/**
 * <p>
 * 项目的阶段和阶段任务信息 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-07
 */
public interface DcProjectTaskService extends IService<DcProjectTask> {
    /**
     * 获取项目的阶段和阶段任务信息
     *
     * @param projectId 项目ID
     * @return 项目阶段和阶段任务信息列表
     */
    public List<DcProjectTask> getProjectTasks(Long projectId);


    /**
     * 获取指定项目指定阶段的阶段任务信息列表
     *
     * @param projectId 项目ID
     * @param stageEnum     阶段
     * @return 阶段任务信息列表
     */
    public List<DcProjectTask> getProjectTasksByStage(Long projectId, StageEnum stageEnum);

    /**
     * 获取指定项目指定阶段任务类型的阶段任务信息
     */
    public Optional<DcProjectTask> getProjectTaskByProjectAndTaskType(Long projectId, StageTaskEnum taskType);
}
