package com.sankuai.deepcode.dao.config;

import com.baomidou.mybatisplus.core.config.GlobalConfig;
import com.baomidou.mybatisplus.core.config.GlobalConfig.DbConfig;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * MyBatisPlus 配置属性类，用于读取 application-dao.yml 中的配置
 * <AUTHOR>
 */
@Data
@Component
@ConfigurationProperties(prefix = "mybatis-plus")
public class MybatisPlusProperties {

    private String mapperLocations;
    private String typeAliasesPackage;
    private String typeEnumsPackage;
    private Configuration configuration;
    private GlobalConfiguration globalConfig;

    @Data
    public static class Configuration {
        private String logImpl;
        private boolean mapUnderscoreToCamelCase;
    }

    @Data
    public static class GlobalConfiguration {
        private DbConfiguration dbConfig;

        @Data
        public static class DbConfiguration {
            private String logicDeleteField;
            private String logicDeleteValue;
            private String logicNotDeleteValue;
        }
    }

    /**
     * 将配置属性转换为 MyBatisPlus 的 GlobalConfig 对象
     */
    public GlobalConfig convertToGlobalConfig(TimeFieldFillMetaObjectHandler metaObjectHandler) {
        GlobalConfig globalConfig = new GlobalConfig();

        // 设置字段自动填充处理器
        globalConfig.setMetaObjectHandler(metaObjectHandler);

        // 设置逻辑删除配置
        if (this.globalConfig != null && this.globalConfig.getDbConfig() != null) {
            DbConfig dbConfig = new DbConfig();
            GlobalConfiguration.DbConfiguration dbConfiguration = this.globalConfig.getDbConfig();

            dbConfig.setLogicDeleteField(dbConfiguration.getLogicDeleteField());
            dbConfig.setLogicDeleteValue(dbConfiguration.getLogicDeleteValue());
            dbConfig.setLogicNotDeleteValue(dbConfiguration.getLogicNotDeleteValue());

            globalConfig.setDbConfig(dbConfig);
        }

        return globalConfig;
    }
}