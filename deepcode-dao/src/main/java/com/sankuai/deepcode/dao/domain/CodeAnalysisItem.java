package com.sankuai.deepcode.dao.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import java.time.LocalDate;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <p>
 * 代码解析任务表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-12
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("code_analysis_item")
public class CodeAnalysisItem {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * git url
     */
    @TableField("git_url")
    private String gitUrl;

    /**
     * 源分支
     */
    @TableField("from_branch")
    private String fromBranch;

    /**
     * 目标分支
     */
    @TableField("to_branch")
    private String toBranch;

    /**
     * 部署分支
     */
    @TableField("build_branch")
    private String buildBranch;

    /**
     * 源分支commitId
     */
    @TableField("from_commit_id")
    private String fromCommitId;

    /**
     * 目标分支commitId
     */
    @TableField("to_commit_id")
    private String toCommitId;

    /**
     * 部署分支commitId
     */
    @TableField("build_commit_id")
    private String buildCommitId;

    /**
     * 是否做增量diff
     */
    @TableField("diff")
    private boolean diff;

    /**
     * 同步任务当前状态 -1初始化 0进行中 1成功 2失败
     */
    @TableField("status")
    private Integer status;

    /**
     * 异步任务类型集合
     */
    @TableField("async_types")
    private String asyncTypes;

    /**
     * 异步任务状态 -1初始化 0进行中 1成功 2失败
     */
    @TableField("async_status")
    private Integer asyncStatus;

    /**
     * 解析耗时ms
     */
    @TableField("analysis_cost")
    private Long analysisCost;

    /**
     * 最后一次commit时间
     */
    @TableField("last_commit_time")
    private LocalDateTime lastCommitTime;

    /**
     * 最后一次commit msg
     */
    @TableField("last_commit_msg")
    private String lastCommitMsg;

    /**
     * 更新时间
     */
    @TableField("utime")
    private LocalDateTime utime;

    /**
     * 创建时间
     */
    @TableField("ctime")
    private LocalDateTime ctime;

    @TableField("valid")
    private Boolean valid;
}
