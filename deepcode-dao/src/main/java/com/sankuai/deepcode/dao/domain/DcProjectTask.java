package com.sankuai.deepcode.dao.domain;

import com.baomidou.mybatisplus.annotation.*;

import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <p>
 * 项目的阶段和阶段任务信息
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-11
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("dc_project_task")
public class DcProjectTask {

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 关联的项目ID
     */
    @TableField("project_id")
    private Integer projectId;

    /**
     * 阶段ID
     */
    @TableField("stage_id")
    private Integer stageId;

    /**
     * 阶段类型
     */
    @TableField("stage")
    private Integer stage;

    /**
     * 阶段任务类型
     */
    @TableField("task")
    private Integer task;

    /**
     * 阶段任务状态
     */
    @TableField("task_status")
    private Integer taskStatus;

    /**
     * 同一阶段内任务排序值
     */
    @TableField("task_order")
    private Integer taskOrder;

    /**
     * 任务描述
     */
    @TableField("task_desc")
    private String taskDesc;

    /**
     * 任务结果数据（JSON格式）
     */
    @TableField("result_data")
    private String resultData;

    /**
     * 执行错误信息
     */
    @TableField("error_info")
    private String errorInfo;

    /**
     * 是否有效：1-有效，0-无效
     */
    @TableField("valid")
    @TableLogic
    private Integer valid;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;
}
