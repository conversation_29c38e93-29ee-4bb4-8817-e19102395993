package com.sankuai.deepcode.dao.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sankuai.deepcode.dao.domain.CodeViewAnalysis;
import com.sankuai.deepcode.dao.mapper.CodeViewAnalysisMapper;
import com.sankuai.deepcode.dao.service.CodeViewAnalysisService;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * 源代码解析 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-11
 */
@Service
public class CodeViewAnalysisServiceImpl extends ServiceImpl<CodeViewAnalysisMapper, CodeViewAnalysis> implements CodeViewAnalysisService {
    @Override
    public List<CodeViewAnalysis> getBySourceVid(long itemId, String sourceVid) {
        return list(
                new LambdaQueryWrapper<>(CodeViewAnalysis.class)
                        .eq(CodeViewAnalysis::getItemId, itemId)
                        .eq(CodeViewAnalysis::getSourceVid, sourceVid)
                        .eq(CodeViewAnalysis::getValid, 1)
        );
    }

    @Override
    public List<CodeViewAnalysis> getByStartAndEnd(long itemId, String sourceVid, int startLine, int endLine) {
        return list(
                new LambdaQueryWrapper<>(CodeViewAnalysis.class)
                        .eq(CodeViewAnalysis::getItemId, itemId)
                        .eq(CodeViewAnalysis::getSourceVid, sourceVid)
                        .between(CodeViewAnalysis::getCodeLine, startLine, endLine)
                        .eq(CodeViewAnalysis::getValid, 1)
        );
    }

    @Override
    public List<CodeViewAnalysis> getAllContent(Long itemId, String fileVid) {
        return list(
                new LambdaQueryWrapper<>(CodeViewAnalysis.class)
                        .eq(CodeViewAnalysis::getItemId, itemId)
                        .eq(CodeViewAnalysis::getSourceVid, fileVid)
        );
    }

    @Override
    public void batchInsert(List<CodeViewAnalysis> list) {
        getBaseMapper().batchInsert(list);
    }

    @Override
    public List<String> getContentByLines(Long itemId, String fileVid, Integer startLine, Integer endLine) {
        LambdaQueryWrapper<CodeViewAnalysis> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.select(CodeViewAnalysis::getCodeView)
                .eq(CodeViewAnalysis::getItemId, itemId)
                .eq(CodeViewAnalysis::getSourceVid, fileVid)
                .eq(CodeViewAnalysis::getValid, 1);
        if (startLine != null && endLine != null) {
            queryWrapper.between(CodeViewAnalysis::getCodeLine, startLine, endLine);
        } else {
            queryWrapper.ge(CodeViewAnalysis::getCodeLine, startLine);
        }
        queryWrapper.orderByAsc(CodeViewAnalysis::getCodeLine);

        return list(queryWrapper).stream().map(CodeViewAnalysis::getCodeView).collect(Collectors.toList());
    }
}
