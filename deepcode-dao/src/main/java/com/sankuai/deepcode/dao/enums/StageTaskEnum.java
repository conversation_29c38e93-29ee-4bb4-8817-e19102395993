package com.sankuai.deepcode.dao.enums;

import com.baomidou.mybatisplus.annotation.IEnum;
import com.sankuai.deepcode.dao.domain.DcProject;
import com.sankuai.deepcode.dao.domain.DcProjectStage;
import com.sankuai.deepcode.dao.domain.DcProjectTask;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * Package: com.sankuai.deepcode.dao.enums
 * Description:
 *
 * <AUTHOR>
 * @since 2025/3/7 10:23
 */
@Getter
@AllArgsConstructor
public enum StageTaskEnum implements IEnum<Integer> {
    // 前置分析阶段任务
    PRE_CHECK(101, "代码仓库检查", 1, StageEnum.PRE_ANALYSIS),
    DUPLICATE_CHECK(102, "分析任务重复性检查", 2, StageEnum.PRE_ANALYSIS),

    // 代码分析阶段任务
    REPO_CLONE(201, "代码克隆", 1, StageEnum.CODE_ANALYSIS),
    AST_ANALYSE(202, "代码语法树解析", 2, StageEnum.CODE_ANALYSIS),
    CODE_RELATIONSHIP(203, "代码关系建立", 3, StageEnum.CODE_ANALYSIS),
    CODE_INFO_STORE(204, "分析结果存储", 4, StageEnum.CODE_ANALYSIS),
    CODE_INDEX(205, "代码信息存储", 5, StageEnum.CODE_ANALYSIS),

    // 架构设计阶段任务
    CODE_CHUNK_EMBEDDING(301, "代码分块&向量化", 1, StageEnum.KNOWLEDGE_BUILD),
    EMBEDDING_RELATIONSHIP(302, "向量结果存储并建立源码关联", 2, StageEnum.KNOWLEDGE_BUILD),
    SUBDIVISION_INFO(303, "项目&模块&类&方法内容总结", 3, StageEnum.KNOWLEDGE_BUILD),
    PROJECT_INFO(304, "工程描述生成", 4, StageEnum.KNOWLEDGE_BUILD),

    // 添加其他阶段任务

    // 所有步骤分析完成
//   COMPLETE(9901, "完成", 1, StageEnum.ALL_DONE)
    ;

    private final Integer value;
    private final String name;
    private final Integer order;
    private final StageEnum stageEnumType;


    public static StageTaskEnum fromCode(int code) {
        return java.util.Arrays.stream(values())
                .filter(type -> type.getValue() == code)
                .findFirst()
                .orElseThrow(() -> new IllegalArgumentException("未知的阶段任务类型编码: " + code));
    }

    /**
     * 获取指定阶段下的所有任务类型
     */
    public static StageTaskEnum[] getTaskTypesByStage(StageEnum stageEnumType) {
        return java.util.Arrays.stream(values())
                .filter(taskType -> taskType.getStageEnumType() == stageEnumType)
                .toArray(StageTaskEnum[]::new);
    }

    public DcProjectTask buildTask(DcProject project, DcProjectStage projectStage) {
        if (project == null || projectStage == null) {
            return null;
        }

        DcProjectTask projectTask = new DcProjectTask();

//        projectTask.setProjectId(project.getId());
//        projectTask.setStageId(projectStage.getId());
//        projectTask.setStage(projectStage.getStage());
//
//        projectTask.setTask(this);
//        projectTask.setTaskStatus(StageTaskStatus.PENDING);
        projectTask.setTaskOrder(this.getOrder());
        projectTask.setTaskDesc(this.getName());

        projectTask.setValid(1);

        return projectTask;
    }
}
