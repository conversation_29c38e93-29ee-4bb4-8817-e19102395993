package com.sankuai.deepcode.dao.service;

import com.sankuai.deepcode.dao.domain.CodeAnalysisItemDetail;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * 代码解析任务详情表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-12
 */
public interface CodeAnalysisItemDetailService extends IService<CodeAnalysisItemDetail> {

    List<CodeAnalysisItemDetail> getByItemId(Long itemId);

    List<CodeAnalysisItemDetail> getByItemIdAndType(Long itemId, String type);

    CodeAnalysisItemDetail getTimeOutDetailByItemId(Long itemId);

    List<CodeAnalysisItemDetail> getAllAsyncByItemId(Long itemId);
}
