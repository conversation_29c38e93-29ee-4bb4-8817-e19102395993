package com.sankuai.deepcode.dao.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <p>
 * 前端边关系表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-28
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("fore_end_edge")
public class ForeEndEdge {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 任务id
     */
    @TableField("item_id")
    private Long itemId;

    /**
     * 边类型，0html调用方法，1html引用变量，2方法调用方法，3html引用样式
     */
    @TableField("type")
    private Integer type;

    /**
     * 起点vid
     */
    @TableField("source")
    private String source;

    /**
     * 起点来源
     */
    @TableField("source_type")
    private String sourceType;

    /**
     * 终点vid
     */
    @TableField("target")
    private String target;

    /**
     * 终点来源
     */
    @TableField("target_type")
    private String targetType;

    @TableField("invoke_params")
    private String invokeParams;

    @TableField("valid")
    private Boolean valid;
}
