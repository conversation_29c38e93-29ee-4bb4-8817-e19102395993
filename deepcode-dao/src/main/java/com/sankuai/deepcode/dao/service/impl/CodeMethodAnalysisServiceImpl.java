package com.sankuai.deepcode.dao.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sankuai.deepcode.dao.domain.CodeMethodAnalysis;
import com.sankuai.deepcode.dao.mapper.CodeMethodAnalysisMapper;
import com.sankuai.deepcode.dao.service.CodeMethodAnalysisService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Set;

/**
 * <p>
 * 代码method解析 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-11
 */
@Service
public class CodeMethodAnalysisServiceImpl extends ServiceImpl<CodeMethodAnalysisMapper, CodeMethodAnalysis> implements CodeMethodAnalysisService {
    @Override
    public List<CodeMethodAnalysis> getByItemId(long itemId) {
        return list(
                new LambdaQueryWrapper<>(CodeMethodAnalysis.class)
                        .eq(CodeMethodAnalysis::getItemId, itemId)
                        .eq(CodeMethodAnalysis::getValid, 1)
        );
    }

    @Override
    public List<CodeMethodAnalysis> getByItemAndClassName(long itemId, String className) {
        return list(
                new LambdaQueryWrapper<>(CodeMethodAnalysis.class)
                        .eq(CodeMethodAnalysis::getItemId, itemId)
                        .eq(CodeMethodAnalysis::getClassName, className)
                        .eq(CodeMethodAnalysis::getValid, 1)
        );
    }

    @Override
    public List<CodeMethodAnalysis> getByItemAndClassNames(long itemId, List<String> classNames) {
        if (CollectionUtils.isEmpty(classNames)) {
            return Collections.emptyList();
        }
        return list(
                new LambdaQueryWrapper<>(CodeMethodAnalysis.class)
                        .eq(CodeMethodAnalysis::getItemId, itemId)
                        .in(CodeMethodAnalysis::getClassName, classNames)
                        .eq(CodeMethodAnalysis::getValid, 1)
        );
    }

    @Override
    public CodeMethodAnalysis getByMethodVid(long itemId, String methodVid) {
        return getOne(
                new LambdaQueryWrapper<>(CodeMethodAnalysis.class)
                        .eq(CodeMethodAnalysis::getItemId, itemId)
                        .eq(CodeMethodAnalysis::getMethodVid, methodVid)
                        .eq(CodeMethodAnalysis::getValid, 1)
                        .last("limit 1")
        );
    }

    @Override
    public List<CodeMethodAnalysis> getByMethodVids(Long itemId, List<String> methodVids) {
        if (CollectionUtils.isEmpty(methodVids)) {
            return Collections.emptyList();
        }
        return list(
                new LambdaQueryWrapper<>(CodeMethodAnalysis.class)
                        .eq(CodeMethodAnalysis::getItemId, itemId)
                        .in(CodeMethodAnalysis::getMethodVid, methodVids)
                        .eq(CodeMethodAnalysis::getValid, 1)
        );
    }

    @Override
    public List<CodeMethodAnalysis> getByVids(long itemId, Set<String> vids) {
        if (CollectionUtils.isEmpty(vids)) {
            return Collections.emptyList();
        }
        return list(
                new LambdaQueryWrapper<>(CodeMethodAnalysis.class)
                        .eq(CodeMethodAnalysis::getItemId, itemId)
                        .eq(CodeMethodAnalysis::getValid, 1)
                        .in(CodeMethodAnalysis::getMethodVid, vids)
        );
    }

    @Override
    public void batchInsert(List<CodeMethodAnalysis> list) {
        getBaseMapper().batchInsert(list);
    }

    @Override
    public List<CodeMethodAnalysis> getByItemAndMethodName(long itemId, String methodName) {
        return list(
                new LambdaQueryWrapper<>(CodeMethodAnalysis.class)
                        .eq(CodeMethodAnalysis::getItemId, itemId)
                        .eq(CodeMethodAnalysis::getMethodName, methodName)
                        .eq(CodeMethodAnalysis::getValid, 1)
        );
    }
}
