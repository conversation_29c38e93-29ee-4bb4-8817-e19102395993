package com.sankuai.deepcode.dao.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sankuai.deepcode.dao.domain.DcProjectTask;
import com.sankuai.deepcode.dao.enums.StageEnum;
import com.sankuai.deepcode.dao.enums.StageTaskEnum;
import com.sankuai.deepcode.dao.mapper.DcProjectTaskMapper;
import com.sankuai.deepcode.dao.service.DcProjectTaskService;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

/**
 * <p>
 * 项目的阶段和阶段任务信息 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-07
 */
@Service
public class DcProjectTaskServiceImpl extends ServiceImpl<DcProjectTaskMapper, DcProjectTask> implements DcProjectTaskService {

    @Override
    public List<DcProjectTask> getProjectTasks(Long projectId) {
        return list(new LambdaQueryWrapper<DcProjectTask>()
                .eq(DcProjectTask::getProjectId, projectId)
                .eq(DcProjectTask::getValid, 1)
                .orderByAsc(DcProjectTask::getTaskOrder)
        );
    }

    @Override
    public List<DcProjectTask> getProjectTasksByStage(Long projectId, StageEnum stageEnum) {
        return list(new LambdaQueryWrapper<DcProjectTask>()
                .eq(DcProjectTask::getProjectId, projectId)
                .eq(DcProjectTask::getStage, stageEnum)
                .eq(DcProjectTask::getValid, 1)
                .orderByAsc(DcProjectTask::getTaskOrder)
        );
    }

    @Override
    public Optional<DcProjectTask> getProjectTaskByProjectAndTaskType(Long projectId, StageTaskEnum taskType) {
        return getOneOpt(new LambdaQueryWrapper<DcProjectTask>()
                .eq(DcProjectTask::getProjectId, projectId)
                .eq(DcProjectTask::getTask, taskType)
                .eq(DcProjectTask::getValid, 1)
                .last("limit 1")
        );
    }
}
