package com.sankuai.deepcode.dao.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.sankuai.deepcode.dao.domain.CodeAnalysisItem;

import java.util.List;

/**
 * <p>
 * 代码解析任务表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-11
 */
public interface CodeAnalysisItemService extends IService<CodeAnalysisItem> {
    List<CodeAnalysisItem> getItemsByGitUrlAndBuildBranch(String gitUrl, String buildBranch);

    IPage<CodeAnalysisItem> getPageItemsByGitUrl(String gitUrl, int pageNum, int pageSize);

    /**
     * 根据gitUrl和分支获取任务列表
     *
     * @param gitUrl       git地址
     * @param sourceBranch 对比源分支
     * @param targetBranch 对比目标分支
     * @return 任务列表
     */
    List<CodeAnalysisItem> getItemsByGitUrlAndBranch(String gitUrl, String sourceBranch, String targetBranch);

    List<CodeAnalysisItem> getByStatus(int status);

    List<CodeAnalysisItem> getByStatusAndAsyncStatus(int status, int asyncStatus);

    Long countGitUrlAndBranches(String gitUrl, String sourceBranch, String targetBranch);
}
