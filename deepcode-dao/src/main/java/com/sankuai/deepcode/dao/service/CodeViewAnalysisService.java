package com.sankuai.deepcode.dao.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.sankuai.deepcode.dao.domain.CodeViewAnalysis;

import java.util.List;

/**
 * <p>
 * 源代码解析 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-11
 */
public interface CodeViewAnalysisService extends IService<CodeViewAnalysis> {
    List<CodeViewAnalysis> getBySourceVid(long itemId, String sourceVid);


    List<CodeViewAnalysis> getByStartAndEnd(long itemId, String sourceVid, int startLine, int endLine);

    List<CodeViewAnalysis> getAllContent(Long itemId, String fileVid);

    void batchInsert(List<CodeViewAnalysis> list);

    List<String> getContentByLines(Long itemId, String fileVid, Integer startLine, Integer endLine);
}
