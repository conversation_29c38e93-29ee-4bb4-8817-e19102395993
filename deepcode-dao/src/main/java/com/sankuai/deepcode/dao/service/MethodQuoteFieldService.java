package com.sankuai.deepcode.dao.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.sankuai.deepcode.dao.domain.CodeFieldAnalysis;
import com.sankuai.deepcode.dao.domain.MethodQuoteField;

import java.util.List;
import java.util.Set;

/**
 * <p>
 * 方法引用变量 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-11
 */
public interface MethodQuoteFieldService extends IService<MethodQuoteField> {
    List<MethodQuoteField> getByTargets(long itemId, Set<String> vids);

    void batchInsert(List<MethodQuoteField> list);
}
