package com.sankuai.deepcode.dao.po;

import com.fasterxml.jackson.annotation.*;
import lombok.*;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * Package: com.sankuai.deepcode.dao.po
 * Description:
 *
 * <AUTHOR>
 * @since 2025/3/25 09:42
 */
@Setter
@Getter
@ToString
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class ChatMsgContentPO {
    /**
     * LLM返回的消息ID
     * 如果是 user 时，此项为空
     */
    String id;
    /**
     * 消息对应的角色
     */
    String role;
    /**
     * 大模型返回的消息内容
     */
    String content;
    /**
     * 消息时间戳
     */
    Long timestamp;
    /**
     * 使用的模型
     */
    String model;
    /**
     * 大模型如果有推理过程时，推理过程的内容
     */
    String reasoningContent;

    /**
     * 消息消耗的token数
     */
    Usage usage;
    /**
     * 处理过程的内容
     */
    String processingContent;

    /**
     * 资源信息
     */
    List<MediaInfo> mediaInfos;

    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonIgnoreProperties(ignoreUnknown = true)
    @Setter
    @Getter
    @ToString
    @Accessors(fluent = true)
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class Usage {
        @JsonProperty("totalTokens")
        private Integer totalTokens;
        @JsonProperty("promptTokens")
        private Integer promptTokens;
        @JsonProperty("completionTokens")
        private Integer completionTokens;

        @JsonProperty("promptTokensDetails")
        private PromptTokensDetails promptTokensDetails;
        @JsonProperty("completionTokensDetails")
        private CompletionTokensDetails completionTokensDetails;
    }

    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonIgnoreProperties(ignoreUnknown = true)
    @Setter
    @Getter
    @ToString
    @Accessors(fluent = true)
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class PromptTokensDetails {
        @JsonProperty("promptTokens")
        private Integer cachedTokens;
    }

    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonIgnoreProperties(ignoreUnknown = true)
    @Setter
    @Getter
    @ToString
    @Accessors(fluent = true)
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class CompletionTokensDetails {
        @JsonProperty("reasoningTokens")
        private Integer reasoningTokens;
    }

    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonIgnoreProperties(ignoreUnknown = true)
    @Setter
    @Getter
    @ToString
    @Accessors(fluent = true)
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class MediaInfo {
        private String type;
        @JsonProperty("media")
        @JsonTypeInfo(use = JsonTypeInfo.Id.NAME, property = "type")
        @JsonSubTypes({
                @JsonSubTypes.Type(value = ImageInfo.class, name = "image"),
                @JsonSubTypes.Type(value = FileInfo.class, name = "file")
        })
        private Media media;
    }

    @JsonTypeInfo(use = JsonTypeInfo.Id.NAME, property = "type")
    public interface Media {
        String type();
    }

    @Getter
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonIgnoreProperties(ignoreUnknown = true)
    @ToString
    @Setter
    public static class ImageInfo implements Media {
        @JsonProperty("url")
        private String url;
        @JsonProperty("name")
        private String name;
        @JsonProperty("base64Data")
        private String base64Data;

        @JsonCreator
        public ImageInfo(
                @JsonProperty("url") String url,
                @JsonProperty("name") String name,
                @JsonProperty("base64Data") String base64Data
        ) {
            this.url = url;
            this.name = name;
            this.base64Data = base64Data;
        }

        @Override
        public String type() {
            return "image";
        }
    }

    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonIgnoreProperties(ignoreUnknown = true)
    @Getter
    @Setter
    @ToString
    public static class FileInfo implements Media {
        @JsonProperty("fileId")
        private Long fileId;
        @JsonProperty("vid")
        private String vid;
        @JsonProperty("name")
        private String name;
        @JsonProperty("path")
        private String path;
        @JsonProperty("url")
        private String url;
        @JsonProperty("start")
        private LineInfo start;
        @JsonProperty("end")
        private LineInfo end;

        @JsonCreator
        public FileInfo(
                @JsonProperty("fileId") Long fileId,
                @JsonProperty("vid") String vid,
                @JsonProperty("name") String name,
                @JsonProperty("path") String path,
                @JsonProperty("url") String url,
                @JsonProperty("start") LineInfo start,
                @JsonProperty("end") LineInfo end
        ) {
            this.fileId = fileId;
            this.vid = vid;
            this.name = name;
            this.path = path;
            this.url = url;
            this.start = start;
            this.end = end;
        }

        @Override
        public String type() {
            return "file";
        }
    }

    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonIgnoreProperties(ignoreUnknown = true)
    @ToString
    @Getter
    @Setter
    @NoArgsConstructor
    @AllArgsConstructor
    public static class LineInfo {
        @JsonProperty("line")
        Integer line;
        @JsonProperty("column")
        Integer column;
    }

}
