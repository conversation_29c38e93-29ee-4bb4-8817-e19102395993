package com.sankuai.deepcode.dao.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.sankuai.deepcode.dao.domain.DcChat;

import java.time.LocalDateTime;

/**
 * <p>
 * 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-14
 */
public interface DcChatService extends IService<DcChat> {

    IPage<DcChat> getUserChatByPage(Integer pageNo, Integer pageSize, Long projectId, Long userId);

    DcChat getChatByUuid(String chatUuid);

    void touchUpdateTime(DcChat chat, LocalDateTime updateTime);
}
