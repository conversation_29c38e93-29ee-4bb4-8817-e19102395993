package com.sankuai.deepcode.dao.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.sankuai.deepcode.dao.domain.DcProjectStage;
import com.sankuai.deepcode.dao.enums.StageEnum;

import java.util.List;
import java.util.Optional;

/**
 * <p>
 * 项目分析阶段信息 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-10
 */
public interface DcProjectStageService extends IService<DcProjectStage> {
    /**
     * 获取项目所有的阶段信息
     *
     * @param projectId 项目ID
     * @return 项目所有的阶段信息
     */
    List<DcProjectStage> getProjectStages(Long projectId);

    /**
     * 获取项目指定阶段的阶段信息
     *
     * @param projectId 项目ID
     * @param stage     阶段类型
     * @return 指定阶段的阶段信息
     */
    Optional<DcProjectStage> getProjectStagesByStageType(Long projectId, StageEnum stage);
}
