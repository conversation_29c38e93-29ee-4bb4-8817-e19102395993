package com.sankuai.deepcode.dao.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.sankuai.deepcode.dao.domain.RuleInfoDetail;
import com.sankuai.deepcode.dao.domain.RuleTaskDetail;
import com.sankuai.deepcode.dao.domain.SysUser;

import java.util.List;
import java.util.Optional;


public interface RuleTaskDetailService extends IService<RuleTaskDetail> {

    int insert(RuleTaskDetail ruleTaskDetail);

    List<RuleTaskDetail> getRuleList(RuleTaskDetail ruleTaskDetail);

    boolean del(long ruleTaskDetailId);

    List<RuleTaskDetail> getByItemAndFileVid(Long itemId, String fileVid);
}
