package com.sankuai.deepcode.dao.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.sankuai.deepcode.dao.handlers.BooleanIntegerTypeHandler;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <p>
 * 代码class解析
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-11
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("code_class_analysis")
public class CodeClassAnalysis {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 任务id
     */
    @TableField("item_id")
    private Long itemId;

    /**
     * vid
     */
    @TableField("class_vid")
    private String classVid;

    /**
     * 文件vid
     */
    @TableField("file_vid")
    private String fileVid;

    /**
     * 文件路径
     */
    @TableField("class_path")
    private String classPath;

    /**
     * module名称
     */
    @TableField("module_name")
    private String moduleName;

    /**
     * 类名称
     */
    @TableField("class_name")
    private String className;

    /**
     * 内部类名称
     */
    @TableField("in_class_name")
    private String inClassName;

    /**
     * 类类型  class、enum、interface、annotation
     */
    @TableField("class_type")
    private String classType;

    /**
     * 泛型
     */
    @TableField("generics")
    private String generics;

    /**
     * 父类
     */
    @TableField("super_class")
    private String superClass;

    /**
     * 接口类
     */
    @TableField("interfaces")
    private String interfaces;

    /**
     * 修饰符
     */
    @TableField("access")
    private String access;

    /**
     * 注解
     */
    @TableField("annotations")
    private String annotations;

    /**
     * 变更类型
     */
    @TableField("change_type")
    private Integer changeType;

    /**
     * 变更行
     */
    @TableField("change_lines")
    private String changeLines;

    /**
     * 冲突类型
     */
    @TableField("check_type")
    private Integer checkType;

    /**
     * 冲突行
     */
    @TableField("check_lines")
    private String checkLines;

    /**
     * 起始行
     */
    @TableField("start_line")
    private Integer startLine;

    /**
     * 终止行
     */
    @TableField("end_line")
    private Integer endLine;

    /**
     * 注释行
     */
    @TableField("comment_lines")
    private String commentLines;

    /**
     * 半年内commit次数
     */
    @TableField("commit_count")
    private Integer commitCount;

    @TableField(value = "valid")
    private Boolean valid;
}
