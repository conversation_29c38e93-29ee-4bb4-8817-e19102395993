package com.sankuai.deepcode.dao.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sankuai.deepcode.dao.domain.SysUser;
import com.sankuai.deepcode.dao.mapper.SysUserMapper;
import com.sankuai.deepcode.dao.service.SysUserService;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

/**
 * <p>
 * 用户表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-11
 */
@Service
public class SysUserServiceImpl extends ServiceImpl<SysUserMapper, SysUser> implements SysUserService {
    @Override
    public Optional<SysUser> getUserByUserName(String userName) {
        return getOneOpt(
                new LambdaQueryWrapper<>(SysUser.class)
                        .eq(SysUser::getUserName, userName)
                        .eq(SysUser::getStatus, "0")
                        .last("limit 1")
        );
    }


    @Override
    public List<SysUser> getUserListByIds(List<Long> userIds) {
        return list(
                new LambdaQueryWrapper<>(SysUser.class)
                        .in(SysUser::getId, userIds)
        );
    }
}
