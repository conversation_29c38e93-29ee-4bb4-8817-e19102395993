package com.sankuai.deepcode.dao.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.sankuai.deepcode.dao.domain.CodeAnalysisItem;
import com.sankuai.deepcode.dao.enums.ItemStatusEnum;
import com.sankuai.deepcode.dao.mapper.CodeAnalysisItemMapper;
import com.sankuai.deepcode.dao.service.CodeAnalysisItemService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.io.Serializable;
import java.util.List;
import java.util.Set;

/**
 * <p>
 * 代码解析任务表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-11
 */
@Service
public class CodeAnalysisItemServiceImpl extends ServiceImpl<CodeAnalysisItemMapper, CodeAnalysisItem> implements CodeAnalysisItemService {
    // 查询可用的状态列表，包括成功、初始化中和处理中的状态
    private final Set<Integer> availableStatus = Sets.newHashSet(
            ItemStatusEnum.SUCCESS.getCode(),
            ItemStatusEnum.INITIATING.getCode(),
            ItemStatusEnum.PROCESSING.getCode()
    );

    @Override
    public List<CodeAnalysisItem> getItemsByGitUrlAndBuildBranch(String gitUrl, String buildBranch) {
        return list(
                new LambdaQueryWrapper<>(CodeAnalysisItem.class)
                        .eq(CodeAnalysisItem::getGitUrl, gitUrl)
                        .eq(CodeAnalysisItem::getBuildBranch, buildBranch)
                        .eq(CodeAnalysisItem::getValid, true)
        );
    }

    @Override
    public IPage<CodeAnalysisItem> getPageItemsByGitUrl(String gitUrl, int pageNum, int pageSize) {
        if (pageNum <= 0) {
            pageNum = 1;
        }
        if (pageSize <= 0) {
            pageSize = 10;
        }
        IPage<CodeAnalysisItem> page = Page.of(pageNum, pageSize);

        LambdaQueryWrapper<CodeAnalysisItem> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CodeAnalysisItem::getValid, true);
        if (StringUtils.isNotBlank(gitUrl)) {
            queryWrapper.like(CodeAnalysisItem::getGitUrl, gitUrl);
        }

        return page(page, queryWrapper);
    }

    @Override
    public List<CodeAnalysisItem> getItemsByGitUrlAndBranch(String gitUrl, String sourceBranch, String targetBranch) {
        if (StringUtils.isBlank(gitUrl)) {
            return Lists.newArrayList();
        }

        return list(
                new LambdaQueryWrapper<>(CodeAnalysisItem.class)
                        .eq(CodeAnalysisItem::getGitUrl, gitUrl)
                        .eq(CodeAnalysisItem::getFromBranch, sourceBranch)
                        .eq(CodeAnalysisItem::getToBranch, targetBranch)
                        .in(CodeAnalysisItem::getStatus, availableStatus)
                        .eq(CodeAnalysisItem::getValid, 1)
        );
    }

    @Override
    public List<CodeAnalysisItem> getByStatus(int status) {
        return list(
                new LambdaQueryWrapper<>(CodeAnalysisItem.class)
                        .eq(CodeAnalysisItem::getStatus, status)
                        .eq(CodeAnalysisItem::getValid, 1)
        );
    }

    @Override
    public List<CodeAnalysisItem> getByStatusAndAsyncStatus(int status, int asyncStatus) {
        return list(
                new LambdaQueryWrapper<>(CodeAnalysisItem.class)
                        .eq(CodeAnalysisItem::getStatus, status)
                        .eq(CodeAnalysisItem::getAsyncStatus, asyncStatus)
                        .eq(CodeAnalysisItem::getValid, 1)
        );
    }

    @Override
    public Long countGitUrlAndBranches(String gitUrl, String sourceBranch, String targetBranch) {
        return count(
                new LambdaQueryWrapper<>(CodeAnalysisItem.class)
                        .eq(CodeAnalysisItem::getGitUrl, gitUrl)
                        .eq(CodeAnalysisItem::getFromBranch, sourceBranch)
                        .eq(CodeAnalysisItem::getToBranch, targetBranch)
                        .in(CodeAnalysisItem::getStatus, availableStatus)
        );
    }

    /**
     * 根据主键ID查询, 添加判空处理
     *
     * @param id 主键ID
     * @return
     */
    @Override
    public CodeAnalysisItem getById(Serializable id) {
        if (id == null) {
            return null;
        }
        return super.getById(id);
    }
}