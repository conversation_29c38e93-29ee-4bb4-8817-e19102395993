package com.sankuai.deepcode.dao.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.sankuai.deepcode.dao.domain.CodeClassAnalysis;

import java.util.List;

/**
 * <p>
 * 代码class解析 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-11
 */
public interface CodeClassAnalysisService extends IService<CodeClassAnalysis> {
    CodeClassAnalysis getByClassVid(Long itemId, String classVid);

    CodeClassAnalysis getByClassName(Long itemId, String className);

    List<CodeClassAnalysis> getByClassNames(Long itemId, List<String> classNames);

    List<CodeClassAnalysis> getByItemId(long itemId);

    void batchInsert(List<CodeClassAnalysis> list);

    /**
     * 根据itemId和fileVid获取类信息
     * 一个文件，可能会定义多个类，此处按 [文件 1: -> :N 类] 的形式进行查找
     *
     * @param itemId  项目id
     * @param fileVid 文件vid
     * @return 类信息列表
     */
    List<CodeClassAnalysis> getByItemIdAndFileVid(long itemId, String fileVid);
}
