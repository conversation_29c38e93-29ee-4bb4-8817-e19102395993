package com.sankuai.deepcode.dao.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * <p>
 * 用户auth2.0详情表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-11
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("rule_bind_info")
public class RuleBindInfo {

    @TableId(value = "id", type = IdType.AUTO)
    private long id;

    @TableField("rule_id")
    private long ruleId;

    @TableField("project_id")
    private long projectId;


    @TableField("user_id")
    private long userId;

    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * valid
     */
    @TableField(value = "valid")
    private boolean valid;

}
