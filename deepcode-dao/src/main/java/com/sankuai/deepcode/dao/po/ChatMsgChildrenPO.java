package com.sankuai.deepcode.dao.po;

import com.google.common.collect.Lists;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.util.Collections;
import java.util.List;

/**
 * Package: com.sankuai.deepcode.dao.po
 * Description:
 *
 * <AUTHOR>
 * @since 2025/3/17 11:35
 */
@Setter
@Getter
@ToString
@Accessors(chain = true)
public class ChatMsgChildrenPO {
    List<String> childrenIds = Lists.newArrayList();

    public static ChatMsgChildrenPO of(String msgId) {
        ChatMsgChildrenPO childrenPo = new ChatMsgChildrenPO();
        childrenPo.setChildrenIds(Lists.newArrayList(msgId));
        return childrenPo;
    }

    public void addChildId(String userMsgUuid) {
        if (childrenIds == null) {
            childrenIds = Lists.newArrayList();
        }
        childrenIds.add(userMsgUuid);
    }
}
