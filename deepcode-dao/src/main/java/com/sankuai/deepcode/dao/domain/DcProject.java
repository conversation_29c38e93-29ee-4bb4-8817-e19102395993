package com.sankuai.deepcode.dao.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-04
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("dc_project")
public class DcProject {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 工程创建人
     */
    @TableField("user_id")
    private Long userId;

    /**
     * 工程git链接
     */
    @TableField("git_url")
    private String gitUrl;

    /**
     * 工程名称（可能是 git 工程名）
     */
    @TableField("project_name")
    private String projectName;

    /**
     * 分析的源分支名
     */
    @TableField("source_branch")
    private String sourceBranch;

    /**
     * 分析的目标分支名
     */
    @TableField("target_branch")
    private String targetBranch;

    /**
     * 工程状态
     */
    @TableField("status")
    private Integer status;

    @TableField("item_id")
    private Long itemId;

    /**
     * 工程是否有效
     */
    @TableField("valid")
    private boolean valid;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;
}
