package com.sankuai.deepcode.dao.enums;

import com.baomidou.mybatisplus.annotation.IEnum;
import com.sankuai.deepcode.dao.domain.DcProjectStage;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.time.LocalDateTime;
import java.util.stream.Stream;

/**
 * Package: com.sankuai.deepcode.dao.enums
 * Description:
 *
 * <AUTHOR>
 * @since 2025/3/7 10:21
 */
@Getter
@AllArgsConstructor
public enum StageEnum implements IEnum<Integer> {
    PRE_ANALYSIS(1, "前置检查", 1, "项目初始分析阶段"),
    CODE_ANALYSIS(2, "代码分析", 2, "深度代码知识分析阶段"),
    KNOWLEDGE_BUILD(3, "知识构建", 3, "项目代码知识库处理"),
    // 添加其他阶段

//    ALL_DONE(99, "分析结束", 99, "所有分析任务结束")
    ;

    private final Integer value;
    private final String name;
    private final Integer order;
    private final String description;

    public static StageEnum fromCode(int code) {
        return Stream.of(StageEnum.values())
                .filter(type -> type.getValue() == code)
                .findFirst()
                .orElseThrow(() -> new IllegalArgumentException("未知的阶段类型编码: " + code));
    }

    public DcProjectStage buildStage(Long projectId) {
        DcProjectStage stage = new DcProjectStage();
        stage.setProjectId(projectId);
        stage.setStage(this);
        stage.setStageStatus(StageStatus.NOT_STARTED);
        stage.setStageOrder(this.getOrder());
        stage.setStageDesc(this.getDescription());

        stage.setValid(1);
        return stage;
    }
}
