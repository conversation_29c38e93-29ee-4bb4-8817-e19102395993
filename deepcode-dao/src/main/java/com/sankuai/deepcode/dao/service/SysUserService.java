package com.sankuai.deepcode.dao.service;

import com.sankuai.deepcode.dao.domain.SysUser;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;
import java.util.Optional;

/**
 * <p>
 * 用户表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-11
 */
public interface SysUserService extends IService<SysUser> {
    Optional<SysUser> getUserByUserName(String userName);

    List<SysUser> getUserListByIds(List<Long> userIds);
}
