package com.sankuai.deepcode.dao.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.sankuai.deepcode.dao.handlers.BooleanIntegerTypeHandler;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <p>
 * 方法引用变量
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-11
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("method_quote_field")
public class MethodQuoteField {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 任务id
     */
    @TableField("item_id")
    private Long itemId;

    /**
     * 起点vid
     */
    @TableField("source")
    private String source;

    /**
     * 起点来源
     */
    @TableField("source_type")
    private String sourceType;

    /**
     * 终点vid
     */
    @TableField("target")
    private String target;

    /**
     * 终点来源
     */
    @TableField("target_type")
    private String targetType;

    @TableField("quote_lines")
    private String quoteLines;

    @TableField(value = "valid")
    private Boolean valid;
}
