package com.sankuai.deepcode.dao.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sankuai.deepcode.dao.domain.SysUserDetail;
import com.sankuai.deepcode.dao.mapper.SysUserDetailMapper;
import com.sankuai.deepcode.dao.service.SysUserDetailService;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

/**
 * <p>
 * 用户auth2.0详情表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-11
 */
@Service
public class SysUserDetailServiceImpl extends ServiceImpl<SysUserDetailMapper, SysUserDetail> implements SysUserDetailService {
    @Override
    public List<SysUserDetail> getDetailsByUserId(Long userId) {
        return list(
                new LambdaQueryWrapper<>(SysUserDetail.class)
                        .eq(SysUserDetail::getUserId, userId)
                        .eq(SysUserDetail::getValid, 1)
        );
    }

    @Override
    public List<SysUserDetail> getDetailsByUserName(String userName) {
        return list(
                new LambdaQueryWrapper<>(SysUserDetail.class)
                        .eq(SysUserDetail::getUserName, userName)
                        .eq(SysUserDetail::getValid, 1)
        );
    }



}
