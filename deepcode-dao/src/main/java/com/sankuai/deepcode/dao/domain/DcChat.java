package com.sankuai.deepcode.dao.domain;

import com.baomidou.mybatisplus.annotation.*;

import java.time.LocalDateTime;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-17
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("dc_chat")
public class DcChat {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 对话的uuid
     */
    @TableField("uuid")
    private String uuid;

    /**
     * 用户ID
     */
    @TableField("user_id")
    private Long userId;

    /**
     * 对应基于的项目ID
     */
    @TableField("project_id")
    private Long projectId;

    /**
     * 对话基于的项目分析条目ID
     */
    @TableField("item_id")
    private Long itemId;

    /**
     * 会话标题
     */
    @TableField("title")
    private String title;

    /**
     * 是否有效
     */
    @TableField("valid")
    private boolean valid;

    /**
     * 会话创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 会话更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE, updateStrategy = FieldStrategy.ALWAYS)
    private LocalDateTime updateTime;
}
