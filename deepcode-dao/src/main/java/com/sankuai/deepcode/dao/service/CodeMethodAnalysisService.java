package com.sankuai.deepcode.dao.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.sankuai.deepcode.dao.domain.CodeFieldAnalysis;
import com.sankuai.deepcode.dao.domain.CodeMethodAnalysis;

import java.util.List;
import java.util.Set;

/**
 * <p>
 * 代码method解析 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-11
 */
public interface CodeMethodAnalysisService extends IService<CodeMethodAnalysis> {

    List<CodeMethodAnalysis> getByItemId(long itemId);


    List<CodeMethodAnalysis> getByItemAndClassName(long itemId, String className);
    List<CodeMethodAnalysis> getByItemAndClassNames(long itemId, List<String> classNames);

    CodeMethodAnalysis getByMethodVid(long itemId, String methodVid);
    List<CodeMethodAnalysis> getByMethodVids(Long itemId, List<String> methodVids);

    List<CodeMethodAnalysis> getByVids(long itemId, Set<String> vids);

    void batchInsert(List<CodeMethodAnalysis> list);

    List<CodeMethodAnalysis> getByItemAndMethodName(long itemId, String methodName);

}
