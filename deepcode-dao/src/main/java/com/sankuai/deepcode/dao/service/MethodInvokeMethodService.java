package com.sankuai.deepcode.dao.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.sankuai.deepcode.dao.domain.MethodInvokeMethod;

import java.util.List;
import java.util.Set;

/**
 * <p>
 * 方法调用方法 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-11
 */
public interface MethodInvokeMethodService extends IService<MethodInvokeMethod> {

    List<MethodInvokeMethod> getBySource(long itemId, String source);


    List<MethodInvokeMethod> getByTarget(long itemId, String target);

    List<MethodInvokeMethod> getByTargets(long itemId, Set<String> vids);

    void batchInsert(List<MethodInvokeMethod> list);

    List<MethodInvokeMethod> getByItemAndMethodVids(Long itemId, List<String> collect);

    List<MethodInvokeMethod> getByTargetMethodVid(Long itemId, String targetMethodVid);

    List<MethodInvokeMethod> recursivelyGetMethodInvokes(Long itemId, String beginMethodVid, Integer invokeType);
}
