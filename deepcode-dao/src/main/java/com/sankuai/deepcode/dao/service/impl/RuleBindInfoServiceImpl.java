package com.sankuai.deepcode.dao.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sankuai.deepcode.dao.domain.RuleBindInfo;
import com.sankuai.deepcode.dao.mapper.RuleBindInfoMapper;
import com.sankuai.deepcode.dao.service.RuleBindInfoService;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;


@Service
public class RuleBindInfoServiceImpl extends ServiceImpl<RuleBindInfoMapper, RuleBindInfo> implements RuleBindInfoService {
    @Override
    public List<RuleBindInfo> getRuleBindInfoByProjectId(long projectId) {
        return list(
                new LambdaQueryWrapper<>(RuleBindInfo.class)
                        .eq(RuleBindInfo::getProjectId, projectId)
                        .eq(RuleBindInfo::isValid, true)
        );
    }

    @Override
    public boolean unbind(Long bindId, long userId) {
        RuleBindInfo ruleBindInfo = new RuleBindInfo()
                .setId(bindId)
                .setUserId(userId)
                .setValid(false)
                .setUpdateTime(LocalDateTime.now());
        return updateById(ruleBindInfo);
    }

    @Override
    public int bind(Long ruleId, Long projectId, long userId) {
        LocalDateTime now = LocalDateTime.now();
        RuleBindInfo ruleBindInfo = new RuleBindInfo();
        ruleBindInfo.setRuleId(ruleId);
        ruleBindInfo.setProjectId(projectId);
        ruleBindInfo.setUserId(userId);
        ruleBindInfo.setValid(true);
        ruleBindInfo.setCreateTime(now);
        ruleBindInfo.setUpdateTime(now);
        return getBaseMapper().insert(ruleBindInfo);
    }

    @Override
    public List<RuleBindInfo> getRuleBindInfoByUserId(Long userId) {
        return list(new LambdaQueryWrapper<>(RuleBindInfo.class)
                .eq(RuleBindInfo::getUserId, userId)
                .eq(RuleBindInfo::getProjectId, 0)
        );
    }

}
