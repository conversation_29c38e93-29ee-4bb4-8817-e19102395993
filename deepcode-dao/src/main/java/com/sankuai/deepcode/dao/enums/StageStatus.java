package com.sankuai.deepcode.dao.enums;

import com.baomidou.mybatisplus.annotation.IEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 任务阶段状态枚举
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum StageStatus implements IEnum<Integer> {
    NOT_STARTED(0, "未开始"),
    IN_PROGRESS(1, "进行中"),
    COMPLETED(2, "已完成"),
    CANCELLED(3, "已取消"),
    FAILED(4, "已失败");

    private final Integer value;
    private final String description;


    public static StageStatus fromCode(int code) {
        for (StageStatus status : StageStatus.values()) {
            if (status.getValue() == code) {
                return status;
            }
        }
        throw new IllegalArgumentException("未知的阶段状态码: " + code);
    }
}
