package com.sankuai.deepcode.dao.service.impl;

import com.sankuai.deepcode.dao.domain.ForeEndEdge;
import com.sankuai.deepcode.dao.domain.ForeEndNode;
import com.sankuai.deepcode.dao.mapper.ForeEndNodeMapper;
import com.sankuai.deepcode.dao.service.ForeEndNodeService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 前端代码解析 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-28
 */
@Service
public class ForeEndNodeServiceImpl extends ServiceImpl<ForeEndNodeMapper, ForeEndNode> implements ForeEndNodeService {

    @Override
    public void batchInsert(List<ForeEndNode> list) {
        getBaseMapper().batchInsert(list);
    }
}
