package com.sankuai.deepcode.dao.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.sankuai.deepcode.dao.handlers.BooleanIntegerTypeHandler;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <p>
 * 源代码解析
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-11
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("code_view_analysis")
public class CodeViewAnalysis {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 任务id
     */
    @TableField("item_id")
    private Long itemId;

    /**
     * 源码文件vid
     */
    @TableField("source_vid")
    private String sourceVid;

    /**
     * 唯一id
     */
    @TableField("code_id")
    private Integer codeId;

    /**
     * 行号
     */
    @TableField("code_line")
    private Integer codeLine;

    /**
     * 类型  1无变更  2新增 3删除
     */
    @TableField("code_type")
    private Integer codeType;

    /**
     * 源码内容
     */
    @TableField("code_view")
    private String codeView;

    @TableField(value = "valid")
    private Boolean valid;
}
