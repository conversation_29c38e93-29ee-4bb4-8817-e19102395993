package com.sankuai.deepcode.dao.domain;

import com.baomidou.mybatisplus.annotation.*;

import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <p>
 * 用户auth2.0详情表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-11
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("sys_user_detail")
public class SysUserDetail {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @TableField("user_id")
    private Long userId;

    /**
     * 三方系统唯一名称
     */
    @TableField("user_name")
    private String userName;

    /**
     * 展示用昵称
     */
    @TableField("nick_name")
    private String nickName;

    /**
     * 三方系统唯一认证信息
     */
    @TableField("out_auth")
    private String outAuth;

    /**
     * 三方系统类型 0=github 1=meituan
     */
    @TableField("type")
    private Integer type;

    /**
     * 三方系统唯一认证code
     */
    @TableField("code")
    private String code;

    /**
     * auth2.0认证信息
     */
    @TableField("auth_info")
    private String authInfo;

    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * valid
     */
    @TableField("valid")
    private Integer valid;

    @TableField("email")
    private String email;
}
