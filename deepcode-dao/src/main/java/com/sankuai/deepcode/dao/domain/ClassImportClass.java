package com.sankuai.deepcode.dao.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.sankuai.deepcode.dao.handlers.BooleanIntegerTypeHandler;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <p>
 * 类引用类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-11
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("class_import_class")
public class ClassImportClass {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 任务id
     */
    @TableField("item_id")
    private Long itemId;

    /**
     * 起点vid
     */
    @TableField("source")
    private String source;

    /**
     * 终点vid
     */
    @TableField("target")
    private String target;

    @TableField(value = "valid")
    private Boolean valid;
}
