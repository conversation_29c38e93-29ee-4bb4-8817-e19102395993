package com.sankuai.deepcode.dao.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sankuai.deepcode.dao.domain.RuleBindInfo;
import com.sankuai.deepcode.dao.domain.RuleInfoDetail;
import com.sankuai.deepcode.dao.mapper.RuleInfoDetailMapper;
import com.sankuai.deepcode.dao.service.RuleInfoDetailService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;


@Service
public class RuleInfoDetailServiceImpl extends ServiceImpl<RuleInfoDetailMapper, RuleInfoDetail> implements RuleInfoDetailService {

    @Override
    public int insert(RuleInfoDetail ruleInfoDetail) {
        LocalDateTime now = LocalDateTime.now();
        RuleInfoDetail ruleBindInfo = new RuleInfoDetail();
        ruleBindInfo.setUserId(ruleInfoDetail.getUserId());
        ruleBindInfo.setType(ruleInfoDetail.getType());
        ruleBindInfo.setValue(ruleInfoDetail.getValue());
        ruleBindInfo.setValid(true);
        ruleBindInfo.setCreateTime(now);
        ruleBindInfo.setUpdateTime(now);
        return getBaseMapper().insert(ruleBindInfo);
    }

    @Override
    public List<RuleInfoDetail> getRuleList(RuleInfoDetail ruleInfoDetail) {
        LambdaQueryWrapper<RuleInfoDetail> queryWrapper = new LambdaQueryWrapper<>();
        if (ruleInfoDetail.getUserId() != null) {
            queryWrapper.eq(RuleInfoDetail::getUserId, ruleInfoDetail.getUserId());
        }
        if (ruleInfoDetail.getType() != 0) {
            queryWrapper.eq(RuleInfoDetail::getType, ruleInfoDetail.getType());
        }
        if (StringUtils.isNotEmpty(ruleInfoDetail.getValue())) {
            queryWrapper.like(RuleInfoDetail::getValue, ruleInfoDetail.getValue());
        }

        return list(queryWrapper.eq(RuleInfoDetail::isValid, true));
    }




    @Override
    public boolean edit(long ruleId, long userId, String value) {
        RuleInfoDetail ruleInfoDetail = new RuleInfoDetail()
                .setId(ruleId)
                .setUserId(userId)
                .setValue(value)
                .setUpdateTime(LocalDateTime.now());
        return updateById(ruleInfoDetail);
    }


    @Override
    public boolean del(long ruleId) {
        RuleInfoDetail ruleInfoDetail = new RuleInfoDetail()
                .setId(ruleId)
                .setValid(false)
                .setUpdateTime(LocalDateTime.now());
        return updateById(ruleInfoDetail);
    }

    @Override
    public List<RuleInfoDetail> getRuleListByIds(List<RuleBindInfo> ruleBindInfoList) {

        List<Long> ruleIds = ruleBindInfoList.stream().map(RuleBindInfo::getRuleId).collect(Collectors.toList());
        //全局规则
        ruleIds.add(0L);
        LambdaQueryWrapper<RuleInfoDetail> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(RuleInfoDetail::getId, ruleIds).eq(RuleInfoDetail::isValid, true);
        return list(queryWrapper);
    }

    @Override
    public List<RuleInfoDetail> getRuleInfoDetailsByRuleIds(List<Long> ruleIds) {
        if (CollectionUtils.isEmpty(ruleIds)) {
            return Collections.emptyList();
        }
        return list(new LambdaQueryWrapper<>(RuleInfoDetail.class)
                .in(RuleInfoDetail::getId, ruleIds)
        );
    }

}
