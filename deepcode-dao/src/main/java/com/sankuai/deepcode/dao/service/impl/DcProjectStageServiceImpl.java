package com.sankuai.deepcode.dao.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sankuai.deepcode.dao.domain.DcProjectStage;
import com.sankuai.deepcode.dao.enums.StageEnum;
import com.sankuai.deepcode.dao.mapper.DcProjectStageMapper;
import com.sankuai.deepcode.dao.service.DcProjectStageService;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

/**
 * <p>
 * 项目分析阶段信息 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-10
 */
@Service
public class DcProjectStageServiceImpl extends ServiceImpl<DcProjectStageMapper, DcProjectStage> implements DcProjectStageService {

    @Override
    public List<DcProjectStage> getProjectStages(Long projectId) {
        return list(new LambdaQueryWrapper<>(DcProjectStage.class)
                .eq(DcProjectStage::getProjectId, projectId)
                .eq(DcProjectStage::getValid, 1)
                .orderByAsc(DcProjectStage::getStageOrder)
        );
    }

    @Override
    public Optional<DcProjectStage> getProjectStagesByStageType(Long projectId, StageEnum stage) {
        return getOneOpt(new LambdaQueryWrapper<>(DcProjectStage.class)
                .eq(DcProjectStage::getProjectId, projectId)
                .eq(DcProjectStage::getStage, stage)
                .eq(DcProjectStage::getValid, 1)
                .last("limit 1")
        );
    }
}
