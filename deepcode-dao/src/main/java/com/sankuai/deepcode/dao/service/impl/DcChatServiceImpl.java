package com.sankuai.deepcode.dao.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sankuai.deepcode.dao.domain.DcChat;
import com.sankuai.deepcode.dao.mapper.DcChatMapper;
import com.sankuai.deepcode.dao.service.DcChatService;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.Objects;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-14
 */
@Service
public class DcChatServiceImpl extends ServiceImpl<DcChatMapper, DcChat> implements DcChatService {

    @Override
    public IPage<DcChat> getUserChatByPage(Integer pageNo, Integer pageSize, Long projectId, Long userId) {
        if (Objects.isNull(pageNo) || pageNo <= 0) {
            pageNo = 1;
        }
        if (Objects.isNull(pageSize) || pageSize <= 0) {
            pageSize = 10;
        }
        if (Objects.isNull(userId) || userId <= 0) {
            return new Page<>();
        }

        LambdaQueryWrapper<DcChat> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(DcChat::getUserId, userId)
                .eq(DcChat::getProjectId, projectId)
                .eq(DcChat::isValid, true)
                .orderByDesc(DcChat::getUpdateTime)
        ;

        return page(new Page<>(pageNo, pageSize), queryWrapper);
    }

    @Override
    public DcChat getChatByUuid(String chatUuid) {
        return getOne(
                new LambdaQueryWrapper<DcChat>()
                        .eq(DcChat::getUuid, chatUuid)
                        .eq(DcChat::isValid, true)
                        .last("limit 1")
        );
    }

    @Override
    public void touchUpdateTime(DcChat chat, LocalDateTime updateTime) {
        chat.setUpdateTime(updateTime);
        lambdaUpdate()
                .eq(DcChat::getId, chat.getId())
                .set(DcChat::getUpdateTime, updateTime)
                .update();
    }
}
