package com.sankuai.deepcode.dao.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.sankuai.deepcode.dao.domain.DcProject;

/**
 * <p>
 * 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-04
 */
public interface DcProjectService extends IService<DcProject> {

    /**
     * 根据用户查询项目列表
     */
    Page<DcProject> getProjectListByUser(Long userId, Integer pageNum, Integer pageSize);

    /**
     * 更新项目分析关联关系
     */
    boolean updateProjectItem(Long projectId, Long itemId);

    DcProject getProjectByItemId(Long itemId);
}
