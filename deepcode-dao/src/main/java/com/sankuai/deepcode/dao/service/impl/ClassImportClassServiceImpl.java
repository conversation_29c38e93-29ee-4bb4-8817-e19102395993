package com.sankuai.deepcode.dao.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sankuai.deepcode.dao.domain.ClassImportClass;
import com.sankuai.deepcode.dao.domain.MethodInvokeMethod;
import com.sankuai.deepcode.dao.mapper.ClassImportClassMapper;
import com.sankuai.deepcode.dao.service.ClassImportClassService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 类引用类 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-11
 */
@Service
public class ClassImportClassServiceImpl extends ServiceImpl<ClassImportClassMapper, ClassImportClass> implements ClassImportClassService {
    public List<ClassImportClass> getByItemAndSourceClassVid(Long itemId, String sourceClassVid) {
        return list(new LambdaQueryWrapper<>(ClassImportClass.class)
                .eq(ClassImportClass::getItemId, itemId)
                .eq(ClassImportClass::getSource, sourceClassVid)
                .eq(ClassImportClass::getValid, 1)
        );
    }

    @Override
    public List<ClassImportClass> getByItemId(Long itemId) {
        return list(
                new LambdaQueryWrapper<>(ClassImportClass.class)
                        .eq(ClassImportClass::getItemId, itemId)
                        .eq(ClassImportClass::getValid, 1)
        );
    }

    @Override
    public void batchInsert(List<ClassImportClass> list) {
        getBaseMapper().batchInsert(list);
    }
}
