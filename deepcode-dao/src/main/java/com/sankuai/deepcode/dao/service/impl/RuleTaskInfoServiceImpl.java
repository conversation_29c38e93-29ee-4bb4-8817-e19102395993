package com.sankuai.deepcode.dao.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sankuai.deepcode.dao.domain.RuleTaskInfo;
import com.sankuai.deepcode.dao.mapper.RuleTaskInfoMapper;
import com.sankuai.deepcode.dao.service.RuleTaskInfoService;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;


@Service
public class RuleTaskInfoServiceImpl extends ServiceImpl<RuleTaskInfoMapper, RuleTaskInfo> implements RuleTaskInfoService {

    @Override
    public long insert(long itemId) {
        LocalDateTime now = LocalDateTime.now();
        RuleTaskInfo ruleTaskInfo = new RuleTaskInfo();
        ruleTaskInfo.setItemId(itemId);
        ruleTaskInfo.setStatus(0);
        ruleTaskInfo.setValid(true);
        ruleTaskInfo.setCreateTime(now);
        ruleTaskInfo.setUpdateTime(now);
        int res = getBaseMapper().insert(ruleTaskInfo);
        return  ruleTaskInfo.getId();
    }

    @Override
    public List<RuleTaskInfo> getTaskList(RuleTaskInfo ruleTaskInfo) {
        LambdaQueryWrapper<RuleTaskInfo> queryWrapper = new LambdaQueryWrapper<>();
        if (ruleTaskInfo.getId() != null&&ruleTaskInfo.getId() > 0) {
            queryWrapper.eq(RuleTaskInfo::getId, ruleTaskInfo.getId());
        }
        if (ruleTaskInfo.getItemId() > 0) {
            queryWrapper.eq(RuleTaskInfo::getItemId, ruleTaskInfo.getItemId());
        }
        return list(queryWrapper.eq(RuleTaskInfo::isValid, true));
    }

    @Override
    public RuleTaskInfo getTaskLastByItemId(long itemId){
        LambdaQueryWrapper<RuleTaskInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(RuleTaskInfo::getItemId, itemId);
        queryWrapper.eq(RuleTaskInfo::isValid, true);
        queryWrapper.orderByDesc(RuleTaskInfo::getId);
        return getOne(queryWrapper);
    }



    @Override
    public boolean delByItemId(long itemId) {
        RuleTaskInfo ruleTaskInfo = new RuleTaskInfo()
                .setItemId(itemId)
                .setValid(false)
                .setUpdateTime(LocalDateTime.now());
        return updateById(ruleTaskInfo);
    }

    @Override
    public boolean updateStatus(long ruleTaskId, int i) {
        RuleTaskInfo ruleTaskInfo = new RuleTaskInfo()
                .setId(ruleTaskId)
                .setStatus(i)
                .setUpdateTime(LocalDateTime.now());
        return updateById(ruleTaskInfo);
    }

}
