package com.sankuai.deepcode.dao.milvus;

import com.dianping.lion.client.log.Logger;
import com.dianping.lion.client.log.LoggerFactory;
import com.sankuai.deepcode.dao.config.MilvusConfig;
import io.milvus.common.clientenum.FunctionType;
import io.milvus.v2.common.DataType;
import io.milvus.v2.common.IndexParam;
import io.milvus.v2.service.collection.request.AddFieldReq;
import io.milvus.v2.service.collection.request.CreateCollectionReq;
import io.milvus.v2.service.collection.request.GetLoadStateReq;
import io.milvus.v2.service.partition.request.CreatePartitionReq;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

@Service
public class EmbeddingInit {

    private static final Logger LOGGER = LoggerFactory.getLogger(EmbeddingInit.class);

    @Autowired
    private MilvusConfig milvusConfig;

    public void createCollection(long itemId) {
        try {
            CreateCollectionReq classRequest = CreateCollectionReq.builder()
                    .collectionName("item_code_" + itemId)
                    .collectionSchema(addField())
                    .indexParams(addIndex())
                    .build();
            milvusConfig.dataSource().createCollection(classRequest);

            GetLoadStateReq classReq = GetLoadStateReq.builder()
                    .collectionName("item_code_" + itemId)
                    .build();

            Boolean res = milvusConfig.dataSource().getLoadState(classReq);
            LOGGER.info("create item_code collection res:{}", res);

            CreatePartitionReq classPartitionReq = CreatePartitionReq.builder()
                    .collectionName("item_code_" + itemId)
                    .partitionName("partition0")
                    .build();
            milvusConfig.dataSource().createPartition(classPartitionReq);
        } catch (Exception e) {
            LOGGER.error("create item_code collection error", e);
        }
    }

    public CreateCollectionReq.CollectionSchema addField() {
        CreateCollectionReq.CollectionSchema schema = null;
        try {
            schema = milvusConfig.dataSource().createSchema();
            schema.addField(AddFieldReq.builder()
                    .fieldName("id")
                    .dataType(DataType.Int64)
                    .isPrimaryKey(true)
                    .autoID(true)
                    .build());

            schema.addField(AddFieldReq.builder()
                    .fieldName("embedding")
                    .dataType(DataType.FloatVector)
                    .dimension(1024)
                    .build());

            schema.addField(AddFieldReq.builder()
                    .fieldName("file_vid")
                    .dataType(DataType.VarChar)
                    .maxLength(32)
                    .build());

            schema.addField(AddFieldReq.builder()
                    .fieldName("chunk_id")
                    .dataType(DataType.Int32)
                    .build());

            schema.addField(AddFieldReq.builder()
                    .fieldName("start_line")
                    .dataType(DataType.Int32)
                    .build());

            schema.addField(AddFieldReq.builder()
                    .fieldName("end_line")
                    .dataType(DataType.Int32)
                    .build());

            Map<String, Object> analyzerParams = new HashMap<>();
            analyzerParams.put("tokenizer", "standard");
            schema.addField(AddFieldReq.builder()
                    .fieldName("code")
                    .dataType(DataType.valueOf("VarChar"))
                    .maxLength(65535)
                    .analyzerParams(analyzerParams)
                    .enableMatch(true)
                    .enableAnalyzer(true)
                    .build());

            schema.addField(AddFieldReq.builder()
                    .fieldName("code_embeddings")
                    .dataType(DataType.SparseFloatVector)
                    .build());

            schema.addFunction(CreateCollectionReq.Function.builder()
                    .name("BM25_0")
                    .inputFieldNames(Arrays.asList("code"))
                    .outputFieldNames(Arrays.asList("code_embeddings"))
                    .functionType(FunctionType.BM25)
                    .build()
            );
        } catch (IllegalArgumentException e) {
            LOGGER.error("addField error", e);
        } finally {
            milvusConfig.dataSource().close();
        }
        return schema;
    }

    public List<IndexParam> addIndex() {
        List<IndexParam> indexParams = null;
        try {
            IndexParam id = IndexParam.builder()
                    .fieldName("id")
                    .indexType(IndexParam.IndexType.AUTOINDEX)
                    .build();

            IndexParam embedding = IndexParam.builder()
                    .fieldName("embedding")
                    .indexType(IndexParam.IndexType.AUTOINDEX)
                    .metricType(IndexParam.MetricType.COSINE)
                    .build();

            IndexParam file_vid = IndexParam.builder()
                    .fieldName("file_vid")
                    .indexType(IndexParam.IndexType.AUTOINDEX)
                    .build();

            IndexParam chunk_id = IndexParam.builder()
                    .fieldName("chunk_id")
                    .indexType(IndexParam.IndexType.AUTOINDEX)
                    .build();

            IndexParam start_line = IndexParam.builder()
                    .fieldName("start_line")
                    .indexType(IndexParam.IndexType.AUTOINDEX)
                    .build();

            IndexParam end_line = IndexParam.builder()
                    .fieldName("end_line")
                    .indexType(IndexParam.IndexType.AUTOINDEX)
                    .build();


            IndexParam code = IndexParam.builder()
                    .fieldName("code")
                    .indexType(IndexParam.IndexType.AUTOINDEX)
                    .build();

            IndexParam code_embeddings = IndexParam.builder()
                    .fieldName("code_embeddings")
                    .indexType(IndexParam.IndexType.AUTOINDEX)
                    .metricType(IndexParam.MetricType.BM25)
                    .build();


            indexParams = new ArrayList<>();
            indexParams.add(id);
            indexParams.add(embedding);
            indexParams.add(file_vid);
            indexParams.add(chunk_id);
            indexParams.add(start_line);
            indexParams.add(end_line);
            indexParams.add(code);
            indexParams.add(code_embeddings);
        } catch (Exception e) {
            LOGGER.error("addIndex error", e);
        } finally {
            milvusConfig.dataSource().close();
        }
        return indexParams;
    }
}
