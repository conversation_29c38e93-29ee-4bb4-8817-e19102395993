<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sankuai.deepcode.dao.mapper.SysTokenMapper" >
  <resultMap id="BaseResultMap" type="com.sankuai.deepcode.dao.domain.SysToken" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="user_id" property="userId" jdbcType="BIGINT" />
    <result column="token_info" property="tokenInfo" jdbcType="VARCHAR" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
    <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
    <result column="valid" property="valid" jdbcType="INTEGER" />
  </resultMap>
  <resultMap id="ResultMapWithBLOBs" type="com.sankuai.deepcode.dao.domain.SysToken" extends="BaseResultMap" >
    <result column="token" property="token" jdbcType="LONGVARCHAR" />
  </resultMap>

</mapper>