<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sankuai.deepcode.dao.mapper.RuleTaskDetailMapper" >
  <resultMap id="BaseResultMap" type="com.sankuai.deepcode.dao.domain.RuleTaskDetail" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="task_id" property="taskId" jdbcType="INTEGER" />
    <result column="file_vid" property="fileVid" jdbcType="INTEGER" />
    <result column="start_line" property="startLine" jdbcType="INTEGER" />
    <result column="value" property="value" jdbcType="INTEGER" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
    <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
    <result column="valid" property="valid" jdbcType="INTEGER" />
  </resultMap>

</mapper>