<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sankuai.deepcode.dao.mapper.RuleTaskInfoMapper" >
  <resultMap id="BaseResultMap" type="com.sankuai.deepcode.dao.domain.RuleTaskInfo" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="item_id" property="itemId" jdbcType="INTEGER" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
    <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
    <result column="valid" property="valid" jdbcType="TINYINT" />
  </resultMap>
</mapper>