<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sankuai.deepcode.dao.mapper.SysUserDetailMapper" >
  <resultMap id="BaseResultMap" type="com.sankuai.deepcode.dao.domain.SysUserDetail" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="user_id" property="userId" jdbcType="BIGINT" />
    <result column="user_name" property="userName" jdbcType="VARCHAR" />
    <result column="nick_name" property="nickName" jdbcType="VARCHAR" />
    <result column="out_auth" property="outAuth" jdbcType="VARCHAR" />
    <result column="type" property="type" jdbcType="INTEGER" />
    <result column="code" property="code" jdbcType="VARCHAR" />
    <result column="auth_info" property="authInfo" jdbcType="VARCHAR" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
    <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
    <result column="valid" property="valid" jdbcType="INTEGER" />
    <result column="email" property="email" jdbcType="VARCHAR" />
  </resultMap>

</mapper>