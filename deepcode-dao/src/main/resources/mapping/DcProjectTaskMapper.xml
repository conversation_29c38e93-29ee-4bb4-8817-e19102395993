<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.deepcode.dao.mapper.DcProjectTaskMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.sankuai.deepcode.dao.domain.DcProjectTask">
        <id column="id" property="id" />
        <result column="project_id" property="projectId" />
        <result column="stage_id" property="stageId" />
        <result column="stage" property="stage" />
        <result column="task" property="task" />
        <result column="task_status" property="taskStatus" />
        <result column="task_order" property="taskOrder" />
        <result column="task_desc" property="taskDesc" />
        <result column="result_data" property="resultData" />
        <result column="error_info" property="errorInfo" />
        <result column="valid" property="valid" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>

</mapper>
