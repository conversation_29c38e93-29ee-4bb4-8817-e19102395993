<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.deepcode.dao.mapper.CodeClassAnalysisMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.sankuai.deepcode.dao.domain.CodeClassAnalysis">
        <id column="id" property="id" />
        <result column="item_id" property="itemId" />
        <result column="class_vid" property="classVid" />
        <result column="file_vid" property="fileVid" />
        <result column="class_path" property="classPath" />
        <result column="module_name" property="moduleName" />
        <result column="class_name" property="className" />
        <result column="in_class_name" property="inClassName" />
        <result column="class_type" property="classType" />
        <result column="generics" property="generics" />
        <result column="super_class" property="superClass" />
        <result column="interfaces" property="interfaces" />
        <result column="access" property="access" />
        <result column="annotations" property="annotations" />
        <result column="change_type" property="changeType" />
        <result column="change_lines" property="changeLines" />
        <result column="check_type" property="checkType" />
        <result column="check_lines" property="checkLines" />
        <result column="start_line" property="startLine" />
        <result column="end_line" property="endLine" />
        <result column="comment_lines" property="commentLines" />
        <result column="commit_count" property="commitCount" />
        <result column="valid" property="valid" />
    </resultMap>
    <insert id="batchInsert" parameterType="java.util.List">
        <selectKey resultType="java.lang.Long" keyProperty="id" order="AFTER">
            SELECT LAST_INSERT_ID()
        </selectKey>
        insert into code_class_analysis (item_id, class_vid, file_vid,class_path,
        module_name, class_name, in_class_name,
        class_type, generics, super_class,
        interfaces, access, annotations,
        change_type, change_lines, check_type,
        check_lines, start_line, end_line,
        comment_lines, commit_count, valid
        )
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.itemId,jdbcType=BIGINT}, #{item.classVid,jdbcType=VARCHAR}, #{item.fileVid,jdbcType=VARCHAR}, #{item.classPath,jdbcType=VARCHAR},
            #{item.moduleName,jdbcType=VARCHAR}, #{item.className,jdbcType=VARCHAR}, #{item.inClassName,jdbcType=VARCHAR},
            #{item.classType,jdbcType=VARCHAR}, #{item.generics,jdbcType=OTHER}, #{item.superClass,jdbcType=OTHER},
            #{item.interfaces,jdbcType=OTHER}, #{item.access,jdbcType=VARCHAR}, #{item.annotations,jdbcType=OTHER},
            #{item.changeType,jdbcType=INTEGER}, #{item.changeLines,jdbcType=OTHER}, #{item.checkType,jdbcType=INTEGER},
            #{item.checkLines,jdbcType=OTHER}, #{item.startLine,jdbcType=INTEGER}, #{item.endLine,jdbcType=INTEGER},
            #{item.commentLines,jdbcType=OTHER}, #{item.commitCount,jdbcType=INTEGER}, #{item.valid,jdbcType=BIT}
            )
        </foreach>
    </insert>

</mapper>
