<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.deepcode.dao.mapper.CodeAnalysisItemMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.sankuai.deepcode.dao.domain.CodeAnalysisItem">
        <id column="id" property="id" />
        <result column="git_url" property="gitUrl" />
        <result column="from_branch" property="fromBranch" />
        <result column="to_branch" property="toBranch" />
        <result column="build_branch" property="buildBranch" />
        <result column="from_commit_id" property="fromCommitId" />
        <result column="to_commit_id" property="toCommitId" />
        <result column="build_commit_id" property="buildCommitId" />
        <result column="diff" property="diff" />
        <result column="status" property="status" />
        <result column="async_types" property="asyncTypes" />
        <result column="async_status" property="asyncStatus" />
        <result column="analysis_cost" property="analysisCost" />
        <result column="last_commit_time" property="lastCommitTime" />
        <result column="last_commit_msg" property="lastCommitMsg" />
        <result column="utime" property="utime" />
        <result column="ctime" property="ctime" />
        <result column="valid" property="valid" />
    </resultMap>

</mapper>
