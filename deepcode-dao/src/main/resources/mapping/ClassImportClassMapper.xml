<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sankuai.deepcode.dao.mapper.ClassImportClassMapper" >
  <resultMap id="BaseResultMap" type="com.sankuai.deepcode.dao.domain.ClassImportClass" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="item_id" property="itemId" jdbcType="BIGINT" />
    <result column="source" property="source" jdbcType="VARCHAR" />
    <result column="target" property="target" jdbcType="VARCHAR" />
    <result column="valid" property="valid" jdbcType="BIT" />
  </resultMap>
  <insert id="batchInsert" parameterType="java.util.List">
    <selectKey resultType="java.lang.Long" keyProperty="id" order="AFTER">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into class_import_class (item_id, source, target, valid
    )
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.itemId,jdbcType=BIGINT}, #{item.source,jdbcType=VARCHAR}, #{item.target,jdbcType=VARCHAR}, #{item.valid,jdbcType=BIT}
      )
    </foreach>
  </insert>

</mapper>