<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.deepcode.dao.mapper.CodeViewAnalysisMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.sankuai.deepcode.dao.domain.CodeViewAnalysis">
        <id column="id" property="id" />
        <result column="item_id" property="itemId" />
        <result column="source_vid" property="sourceVid" />
        <result column="code_id" property="codeId" />
        <result column="code_line" property="codeLine" />
        <result column="code_type" property="codeType" />
        <result column="code_view" property="codeView" />
        <result column="valid" property="valid" />
    </resultMap>
    <insert id="batchInsert" parameterType="java.util.List">
        <selectKey resultType="java.lang.Long" keyProperty="id" order="AFTER">
            SELECT LAST_INSERT_ID()
        </selectKey>
        insert into code_view_analysis (item_id, source_vid, code_id,
        code_line, code_type, code_view, valid
        )
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.itemId,jdbcType=BIGINT}, #{item.sourceVid,jdbcType=VARCHAR}, #{item.codeId,jdbcType=INTEGER},
            #{item.codeLine,jdbcType=INTEGER}, #{item.codeType,jdbcType=INTEGER}, #{item.codeView,jdbcType=VARCHAR}, #{item.valid,jdbcType=BIT}
            )
        </foreach>
    </insert>

</mapper>
