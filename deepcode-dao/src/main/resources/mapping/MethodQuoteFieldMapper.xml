<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.deepcode.dao.mapper.MethodQuoteFieldMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.sankuai.deepcode.dao.domain.MethodQuoteField">
        <id column="id" property="id" />
        <result column="item_id" property="itemId" />
        <result column="source" property="source" />
        <result column="source_type" property="sourceType" />
        <result column="target" property="target" />
        <result column="target_type" property="targetType" />
        <result column="quote_lines" property="quoteLines" />
        <result column="valid" property="valid" />
    </resultMap>
    <insert id="batchInsert" parameterType="java.util.List">
        <selectKey resultType="java.lang.Long" keyProperty="id" order="AFTER">
            SELECT LAST_INSERT_ID()
        </selectKey>
        insert into method_quote_field (item_id, source, source_type,
        target, target_type, quote_lines,
        valid)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.itemId,jdbcType=BIGINT}, #{item.source,jdbcType=VARCHAR}, #{item.sourceType,jdbcType=VARCHAR},
            #{item.target,jdbcType=VARCHAR}, #{item.targetType,jdbcType=VARCHAR}, #{item.quoteLines,jdbcType=OTHER},
            #{item.valid,jdbcType=BIT})
        </foreach>
    </insert>
</mapper>
