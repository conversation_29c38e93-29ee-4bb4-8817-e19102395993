<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.deepcode.dao.mapper.ForeEndEdgeMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.sankuai.deepcode.dao.domain.ForeEndEdge">
        <id column="id" property="id" />
        <result column="item_id" property="itemId" />
        <result column="type" property="type" />
        <result column="source" property="source" />
        <result column="source_type" property="sourceType" />
        <result column="target" property="target" />
        <result column="target_type" property="targetType" />
        <result column="invoke_params" property="invokeParams" />
        <result column="valid" property="valid" />
    </resultMap>
    <insert id="batchInsert" parameterType="java.util.List">
        <selectKey resultType="java.lang.Long" keyProperty="id" order="AFTER">
            SELECT LAST_INSERT_ID()
        </selectKey>
        insert into fore_end_edge (item_id, type, source, source_type,
        target, target_type, invoke_params,
        valid)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.itemId,jdbcType=BIGINT}, #{item.type,jdbcType=INTEGER}, #{item.source,jdbcType=VARCHAR}, #{item.sourceType,jdbcType=VARCHAR},
            #{item.target,jdbcType=VARCHAR}, #{item.targetType,jdbcType=VARCHAR}, #{item.invokeParams,jdbcType=OTHER},
            #{item.valid,jdbcType=BIT})
        </foreach>
    </insert>
</mapper>
