<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sankuai.deepcode.dao.mapper.SysUserMapper" >
  <resultMap id="BaseResultMap" type="com.sankuai.deepcode.dao.domain.SysUser" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="user_name" property="userName" jdbcType="VARCHAR" />
    <result column="nick_name" property="nickName" jdbcType="VARCHAR" />
    <result column="password" property="password" jdbcType="VARCHAR" />
    <result column="status" property="status" jdbcType="CHAR" />
    <result column="email" property="email" jdbcType="VARCHAR" />
    <result column="phonenumber" property="phonenumber" jdbcType="VARCHAR" />
    <result column="sex" property="sex" jdbcType="CHAR" />
    <result column="avatar" property="avatar" jdbcType="VARCHAR" />
    <result column="user_type" property="userType" jdbcType="CHAR" />
    <result column="create_by" property="createBy" jdbcType="BIGINT" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
    <result column="update_by" property="updateBy" jdbcType="BIGINT" />
    <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
    <result column="del_flag" property="delFlag" jdbcType="INTEGER" />
  </resultMap>

</mapper>