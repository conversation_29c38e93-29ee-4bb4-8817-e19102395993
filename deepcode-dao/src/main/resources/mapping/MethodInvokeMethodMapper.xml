<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.deepcode.dao.mapper.MethodInvokeMethodMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.sankuai.deepcode.dao.domain.MethodInvokeMethod">
        <id column="id" property="id"/>
        <result column="item_id" property="itemId"/>
        <result column="source" property="source"/>
        <result column="source_type" property="sourceType"/>
        <result column="target" property="target"/>
        <result column="target_type" property="targetType"/>
        <result column="invoke_params" property="invokeParams"
                typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"/>
        <result column="valid" property="valid"/>
    </resultMap>

    <insert id="batchInsert" parameterType="java.util.List">
        <selectKey resultType="java.lang.Long" keyProperty="id" order="AFTER">
            SELECT LAST_INSERT_ID()
        </selectKey>
        insert into method_invoke_method (item_id, source, source_type,
        target, target_type, invoke_params,
        valid)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.itemId,jdbcType=BIGINT}, #{item.source,jdbcType=VARCHAR}, #{item.sourceType,jdbcType=VARCHAR},
            #{item.target,jdbcType=VARCHAR}, #{item.targetType,jdbcType=VARCHAR},
            #{item.invokeParams,typeHandler=com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler},
            #{item.valid,jdbcType=BIT})
        </foreach>
    </insert>

    <select id="recursivelyGetUpLinkMethodInvokes"
            resultMap="BaseResultMap">
        WITH RECURSIVE uplink_cte AS (
            -- 基础查询：找到直接调用目标方法的方法
            SELECT id, item_id, source, source_type, target, target_type, invoke_params, valid, 1 as level
            FROM method_invoke_method
            WHERE item_id = #{itemId}
              AND target = #{beginMethodVid}
              AND valid = 1

            UNION ALL

            -- 递归查询：以找到的source作为新的target，继续向上查找调用它们的方法
            SELECT m.id, m.item_id, m.source, m.source_type, m.target, m.target_type, m.invoke_params, m.valid, u.level + 1
            FROM method_invoke_method m
            INNER JOIN uplink_cte u ON m.target = u.source AND m.item_id = u.item_id
            WHERE m.valid = 1
              AND u.level &lt; 50  -- 防止无限递归，限制递归深度
        )
        SELECT id, item_id, source, source_type, target, target_type, invoke_params, valid
        FROM uplink_cte
        ORDER BY level, id
    </select>

    <select id="recursivelyGetDownLinkMethodInvokes"
            resultMap="BaseResultMap">
        WITH RECURSIVE downlink_cte AS (
            -- 基础查询：找到目标方法直接调用的方法
            SELECT id, item_id, source, source_type, target, target_type, invoke_params, valid, 1 as level
            FROM method_invoke_method
            WHERE item_id = #{itemId}
              AND source = #{beginMethodVid}
              AND valid = 1

            UNION ALL

            -- 递归查询：以找到的target作为新的source，继续向下查找它们调用的方法
            SELECT m.id, m.item_id, m.source, m.source_type, m.target, m.target_type, m.invoke_params, m.valid, d.level + 1
            FROM method_invoke_method m
            INNER JOIN downlink_cte d ON m.source = d.target AND m.item_id = d.item_id
            WHERE m.valid = 1
              AND d.level &lt; 50  -- 防止无限递归，限制递归深度
        )
        SELECT id, item_id, source, source_type, target, target_type, invoke_params, valid
        FROM downlink_cte
        ORDER BY level, id
    </select>
</mapper>
