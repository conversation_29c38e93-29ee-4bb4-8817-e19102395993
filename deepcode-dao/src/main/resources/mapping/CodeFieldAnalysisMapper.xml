<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.deepcode.dao.mapper.CodeFieldAnalysisMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.sankuai.deepcode.dao.domain.CodeFieldAnalysis">
        <id column="id" property="id" />
        <result column="item_id" property="itemId" />
        <result column="field_vid" property="fieldVid" />
        <result column="source" property="source" />
        <result column="module_name" property="moduleName" />
        <result column="field_name" property="fieldName" />
        <result column="class_name" property="className" />
        <result column="in_class_name" property="inClassName" />
        <result column="access" property="access" />
        <result column="signatures" property="signatures" />
        <result column="field_type" property="fieldType" />
        <result column="field_value" property="fieldValue" />
        <result column="annotations" property="annotations" />
        <result column="change_type" property="changeType" />
        <result column="change_lines" property="changeLines" />
        <result column="check_type" property="checkType" />
        <result column="check_lines" property="checkLines" />
        <result column="start_line" property="startLine" />
        <result column="end_line" property="endLine" />
        <result column="comment_lines" property="commentLines" />
        <result column="valid" property="valid" />
    </resultMap>
    <insert id="batchInsert" parameterType="java.util.List">
        <selectKey resultType="java.lang.Long" keyProperty="id" order="AFTER">
            SELECT LAST_INSERT_ID()
        </selectKey>
        insert into code_field_analysis (item_id, field_vid, source,
        module_name, field_name, class_name,
        in_class_name, access, signatures, field_type,
        field_value, annotations, change_type,
        change_lines, check_type, check_lines,
        start_line, end_line, comment_lines,
        valid)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.itemId,jdbcType=BIGINT}, #{item.fieldVid,jdbcType=VARCHAR}, #{item.source,jdbcType=VARCHAR},
            #{item.moduleName,jdbcType=VARCHAR}, #{item.fieldName,jdbcType=VARCHAR}, #{item.className,jdbcType=VARCHAR},
            #{item.inClassName,jdbcType=VARCHAR}, #{item.access,jdbcType=VARCHAR}, #{item.signatures,jdbcType=OTHER},
            #{item.fieldType,jdbcType=VARCHAR}, #{item.fieldValue,jdbcType=VARCHAR}, #{item.annotations,jdbcType=OTHER},
            #{item.changeType,jdbcType=INTEGER},#{item.changeLines,jdbcType=OTHER}, #{item.checkType,jdbcType=INTEGER},
            #{item.checkLines,jdbcType=OTHER},#{item.startLine,jdbcType=INTEGER}, #{item.endLine,jdbcType=INTEGER},
            #{item.commentLines,jdbcType=OTHER},#{item.valid,jdbcType=BIT})
        </foreach>
    </insert>

</mapper>
