<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.deepcode.dao.mapper.DcChatMessageMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.sankuai.deepcode.dao.domain.DcChatMessage">
        <id column="id" property="id"/>
        <result column="user_id" property="userId"/>
        <result column="project_id" property="projectId"/>
        <result column="item_id" property="itemId"/>
        <result column="chat_uuid" property="chatUuid"/>
        <result column="uuid" property="uuid"/>
        <result column="prev_msg_id" property="prevMsgId"/>
        <result column="children_ids" property="childrenIds"
                typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"/>
        <result column="role" property="role"/>
        <result column="content" property="content"
                typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"/>
        <result column="extra" property="extra"/>
        <result column="valid" property="valid"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

</mapper>
