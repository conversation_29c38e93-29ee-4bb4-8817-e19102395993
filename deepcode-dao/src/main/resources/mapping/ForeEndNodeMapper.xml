<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.deepcode.dao.mapper.ForeEndNodeMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.sankuai.deepcode.dao.domain.ForeEndNode">
        <id column="id" property="id" />
        <result column="item_id" property="itemId" />
        <result column="node_vid" property="nodeVid" />
        <result column="node_type" property="nodeType" />
        <result column="file_vid" property="fileVid" />
        <result column="file_path" property="filePath" />
        <result column="annotations" property="annotations" />
        <result column="change_type" property="changeType" />
        <result column="change_lines" property="changeLines" />
        <result column="check_type" property="checkType" />
        <result column="check_lines" property="checkLines" />
        <result column="start_line" property="startLine" />
        <result column="end_line" property="endLine" />
        <result column="comment_lines" property="commentLines" />
        <result column="expand" property="expand" />
        <result column="valid" property="valid" />
    </resultMap>
    <insert id="batchInsert" parameterType="java.util.List">
        <selectKey resultType="java.lang.Long" keyProperty="id" order="AFTER">
            SELECT LAST_INSERT_ID()
        </selectKey>
        insert into fore_end_node (item_id, node_vid, node_type, file_vid,file_path,annotations,change_type,
        change_lines, check_type, check_lines,start_line,end_line,comment_lines,expand,
        valid)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.itemId,jdbcType=BIGINT}, #{item.nodeVid,jdbcType=VARCHAR}, #{item.nodeType,jdbcType=INTEGER},
            #{item.fileVid,jdbcType=VARCHAR},#{item.filePath,jdbcType=VARCHAR}, #{item.annotations,jdbcType=OTHER},
            #{item.changeType,jdbcType=INTEGER}, #{item.changeLines,jdbcType=OTHER}, #{item.checkType,jdbcType=INTEGER},
            #{item.checkLines,jdbcType=OTHER}, #{item.startLine,jdbcType=INTEGER}, #{item.endLine,jdbcType=INTEGER},
            #{item.commentLines,jdbcType=OTHER},#{item.expand,jdbcType=OTHER},
            #{item.valid,jdbcType=BIT})
        </foreach>
    </insert>
</mapper>
