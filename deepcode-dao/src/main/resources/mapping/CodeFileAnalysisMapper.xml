<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.deepcode.dao.mapper.CodeFileAnalysisMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.sankuai.deepcode.dao.domain.CodeFileAnalysis">
        <id column="id" property="id" />
        <result column="item_id" property="itemId" />
        <result column="file_vid" property="fileVid" />
        <result column="module_name" property="moduleName" />
        <result column="file_name" property="fileName" />
        <result column="file_path" property="filePath" />
        <result column="file_type" property="fileType" />
        <result column="change_type" property="changeType" />
        <result column="change_lines" property="changeLines" />
        <result column="check_type" property="checkType" />
        <result column="check_lines" property="checkLines" />
        <result column="start_line" property="startLine" />
        <result column="end_line" property="endLine" />
        <result column="comment_lines" property="commentLines" />
        <result column="commit_count" property="commitCount" />
        <result column="valid" property="valid" />
    </resultMap>
    <insert id="batchInsert" parameterType="java.util.List">
        <selectKey resultType="java.lang.Long" keyProperty="id" order="AFTER">
            SELECT LAST_INSERT_ID()
        </selectKey>
        insert into code_file_analysis (item_id, file_vid, module_name,
        file_name, file_path, file_type,
        change_type, change_lines, check_type,
        check_lines, start_line, end_line,
        comment_lines, commit_count, valid
        )
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.itemId,jdbcType=BIGINT}, #{item.fileVid,jdbcType=VARCHAR}, #{item.moduleName,jdbcType=VARCHAR},
            #{item.fileName,jdbcType=VARCHAR}, #{item.filePath,jdbcType=VARCHAR}, #{item.fileType,jdbcType=VARCHAR},
            #{item.changeType,jdbcType=INTEGER}, #{item.changeLines,jdbcType=OTHER}, #{item.checkType,jdbcType=INTEGER},
            #{item.checkLines,jdbcType=OTHER}, #{item.startLine,jdbcType=INTEGER}, #{item.endLine,jdbcType=INTEGER},
            #{item.commentLines,jdbcType=OTHER}, #{item.commitCount,jdbcType=INTEGER}, #{item.valid,jdbcType=BIT}
            )
        </foreach>
    </insert>

</mapper>
