mybatis-plus:
  mapper-locations: classpath:mapping/*Mapper.xml
  type-aliases-package: com.sankuai.deepcode.dao.domain.*
  typeEnumsPackage: com.sankuai.deepcode.dao.enums
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
    map-underscore-to-camel-case: true
  global-config:
    db-config:
      logic-delete-field: valid
      logic-delete-value: 0 # 删除值
      logic-not-delete-value: 1 # 未删除值

# 自定义 Zebra 数据源配置
zebra:
  datasource:
    jdbcref: deepcode_deepcode_test
    max-pool-size: 100
    extra-params: zeroDateTimeBehavior=convertToNull
