package com.sankuai.deepcode.dao;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.generator.FastAutoGenerator;
import com.baomidou.mybatisplus.generator.config.OutputFile;
import com.baomidou.mybatisplus.generator.config.rules.DbColumnType;
import com.baomidou.mybatisplus.generator.engine.FreemarkerTemplateEngine;
import com.baomidou.mybatisplus.generator.fill.Column;
import com.baomidou.mybatisplus.generator.query.DefaultQuery;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.io.BufferedReader;
import java.io.File;
import java.io.IOException;
import java.io.InputStreamReader;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.sql.Types;

/**
 * Package: IntelliJ IDEA
 * Description:
 *
 * <AUTHOR>
 * @since 2024/05/10 09:51
 */
@Slf4j
public class MyBatisPlusGenerator {
    public static final String MODULE_NAME = "deepcode-dao";
    public static final String BASE_PACKAGE = "com.sankuai.deepcode.dao";

    @Getter
    private enum ModuleDbInfo {
        /**
         * deepcode数据库
         */
        DEEP_CODE("", "*****************************************************************************", "rds_deepcode", "$jJU4eOJyW*dJo"),
        ;
        private final String moduleName;
        private final String dbUrl;
        private final String username;
        private final String password;

        ModuleDbInfo(String moduleName, String dbUrl, String username, String password) {
            this.moduleName = moduleName;
            this.dbUrl = dbUrl;
            this.username = username;
            this.password = password;
        }
    }

    public static void main(String[] args) {
        ModuleDbInfo moduleDbInfo = ModuleDbInfo.DEEP_CODE;

        String systemUser = System.getProperty("user.name", "zhouyong15");
        String gitAuthor = null;
        try {
            Process process = Runtime.getRuntime().exec("git config user.name");
            BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()));
            gitAuthor = reader.readLine();
        } catch (IOException e) {
            log.error("获取git用户名出现异常：{}", e.getMessage(), e);
        }

        String finalGitAuthor = gitAuthor;
        FastAutoGenerator.create(
                        moduleDbInfo.getDbUrl(), moduleDbInfo.getUsername(), moduleDbInfo.getPassword()
                ).globalConfig(builder -> {
                    builder.author(StringUtils.isNotEmpty(finalGitAuthor) ? finalGitAuthor : systemUser)
                            // 指定输出目录
                            .outputDir(getProjectModulePath().toString())
                    ;
                }).dataSourceConfig(builder -> {
                    builder.typeConvertHandler((globalConfig, typeRegistry, metaInfo) -> {
                        int typeCode = metaInfo.getJdbcType().TYPE_CODE;
                        // 自定义类型转换
                        if (typeCode == Types.SMALLINT) {
                            return DbColumnType.INTEGER;
                        } else if (typeCode == Types.BIT || typeCode == Types.TINYINT) {
                            return DbColumnType.BASE_BOOLEAN;
                        } else if (typeCode == Types.JAVA_OBJECT) {
                            return DbColumnType.OBJECT;
                        }
                        return typeRegistry.getColumnType(metaInfo);
                    });
                    builder.schema("deepcode").databaseQueryClass(DefaultQuery.class);
                }).packageConfig(builder -> {
                    String newPackageName = StringUtils.isEmpty(moduleDbInfo.getModuleName()) ? BASE_PACKAGE : BASE_PACKAGE + "." + moduleDbInfo.getModuleName();
                    // 设置父包名
                    builder.parent(BASE_PACKAGE)
                            // 单独指定并更换 service、serviceImpl 的包路径
                            .service("service")
                            .serviceImpl("service.impl")
                            // 设置父包模块名
                            .moduleName(moduleDbInfo.getModuleName())
                            // 设置mapperXml生成路径
                            .pathInfo(ImmutableMap.<OutputFile, String>builder()
                                    .put(OutputFile.xml, getModuleResourcesPath(StringUtils.isEmpty(moduleDbInfo.getModuleName()) ? "mapping" : "mapping" + "/" + moduleDbInfo.getModuleName()).toAbsolutePath().toString())
                                    .put(OutputFile.entity, getModulePackagePath(newPackageName).resolve("domain").toAbsolutePath().toString())
                                    .put(OutputFile.mapper, getModulePackagePath(newPackageName).resolve("mapper").toAbsolutePath().toString())
                                    .put(OutputFile.service, getModulePackagePath(newPackageName).resolve("service").toAbsolutePath().toString())
                                    .put(OutputFile.serviceImpl, getModulePackagePath(newPackageName).resolve("service/impl").toAbsolutePath().toString())
                                    .put(OutputFile.controller, getModulePackagePath(newPackageName).resolve("controller").toAbsolutePath().toString())
                                    .build()
                            )
                            // 设置实体类包名
                            .entity("domain")
                    ;
                }).strategyConfig(builder -> {
                    // 设置需要生成的表名
                    builder.addInclude(
                            Lists.newArrayList(
                                    "dc_prompt_template"
                            )
                    )
                    // 设置过滤表前缀
                    //.addTablePrefix("t_")
                    ;

                    // Controller 配置
                    builder.controllerBuilder()
                            .enableFileOverride();

                    // Service 配置
                    builder.serviceBuilder()
                            // FIXME: service 部分不能覆盖【避免已编写的方法被覆盖】
//                            .enableFileOverride()
                            .convertServiceFileName(entityName -> entityName + "Service")
                            .convertServiceImplFileName(entityName -> entityName + "ServiceImpl");

                    // Mapper 配置
                    builder.mapperBuilder()
                            // FIXME: 【MAPPER】部分，谨慎覆盖
                            .enableFileOverride()
                            .enableBaseResultMap();

                    // Entity 配置
                    builder.entityBuilder()
                            .disableSerialVersionUID()
                            .enableTableFieldAnnotation()
                            .enableLombok()
                            .enableChainModel()
                            .enableFileOverride()
                            .addTableFills(
                                    new Column("create_time", FieldFill.INSERT),
                                    new Column("update_time", FieldFill.INSERT_UPDATE)
                            )
                    ;
                })
                // 使用Freemarker引擎模板，默认的是Velocity引擎模板
                .templateEngine(new FreemarkerTemplateEngine())
                // 设置不生成 Controller
                .templateConfig(builder -> builder.controller(""))
                .execute();
        System.out.println("同步成功~~");
    }

    public static Path getProjectModulePath() {
        return Paths.get(
                System.getProperty("user.dir"), MODULE_NAME
        );
    }

    public static Path getModulePackagePath(String packageName) {
        return Paths.get(
                getProjectModulePath().toString(),
                "src", "main", "java",
                packageName.replace(".", File.separator)
        );
    }

    public static Path getModuleResourcesPath(String resourcePath) {
        return Paths.get(
                getProjectModulePath().toString(),
                "src", "main", "resources",
                resourcePath
        );
    }
}
