package com.sankuai.deepcode.manage.server.controller;

import com.sankuai.deepcode.ai.llm.model.chat.ChatByStreamParam;
import com.sankuai.deepcode.ai.llm.model.chat.ChatMessage;
import com.sankuai.deepcode.manage.server.DeepCodePlatformStartApp;
import com.sankuai.deepcode.commons.CommonResult;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.util.ArrayList;
import java.util.List;


@RunWith(SpringRunner.class)
@SpringBootTest(classes = DeepCodePlatformStartApp.class)
public class ChatControllerTest {

    @Autowired
    private ChatController chatController;

    @Test
    public void testChatByStream() throws Exception {
        ChatByStreamParam chatByStreamParam = new ChatByStreamParam();
        List<ChatMessage> chatMessages = new ArrayList<>();
        ChatMessage chatMessage = new ChatMessage();
        chatMessage.setContent("hi");
        chatMessage.setRole("user");
        chatByStreamParam.setChatMessages(chatMessages);
        SseEmitter sseEmitter = chatController.chatByStream(chatByStreamParam);
        System.out.println(sseEmitter);
    }


    @Test
    public void chatByCompletions() throws Exception {
        ChatByStreamParam chatByStreamParam = new ChatByStreamParam();
        List<ChatMessage> chatMessages = new ArrayList<>();
        ChatMessage chatMessage = new ChatMessage();
        chatMessage.setContent("hi");
        chatMessage.setRole("user");
        chatByStreamParam.setChatMessages(chatMessages);
        CommonResult commonResult = chatController.chatByCompletions(chatByStreamParam);
        System.out.println(commonResult);
    }
}