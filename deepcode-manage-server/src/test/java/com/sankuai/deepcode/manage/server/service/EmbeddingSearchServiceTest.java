package com.sankuai.deepcode.manage.server.service;

import com.sankuai.deepcode.ai.embedding.service.EmbeddingSearchService;
import com.sankuai.deepcode.ai.hanlp.CnToEnService;
import com.sankuai.deepcode.ai.hanlp.HanlpService;
import com.sankuai.deepcode.manage.server.DeepCodePlatformStartApp;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.List;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = DeepCodePlatformStartApp.class)
class EmbeddingSearchServiceTest {

    @Autowired
    private EmbeddingSearchService embeddingSearchService;
    @Autowired
    private CnToEnService cnToEnService;
    @Autowired
    private HanlpService hanlpService;

    @Test
    void embeddingSearchService() {
        String text = "工程内定价算法都有哪些";
        embeddingSearchService.embeddingSearchService(16L, text);
    }

    @Test
    void keyWord() {
        String text = "工程内定价算法都有哪些";
        List<String> key = hanlpService.segment(text);
        List<String> res = cnToEnService.cn2en(key);
        System.out.println(res);
    }

    @Test
    void getCodeDescByFileVid() {
        long itemId = 16L;
        String fileVid = "f3bbd1da93710b1b79db4122937901e7";
        String res = embeddingSearchService.getCodeDescByFileVid(itemId, fileVid);
        System.out.println();
    }
}