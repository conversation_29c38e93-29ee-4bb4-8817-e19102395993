package com.sankuai.deepcode.manage.server.service;


import com.sankuai.deepcode.ast.common.model.java.JavaAnalysisRes;
import com.sankuai.deepcode.manage.server.DeepCodePlatformStartApp;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;


@RunWith(SpringRunner.class)
@SpringBootTest(classes = DeepCodePlatformStartApp.class)
public class AnalysisServiceTest {
    @Autowired
    private AnalysisService analysisService;

    @Test
    public void analysis() throws Exception {

//        String gitUrl = "ssh://*******************/wbqa/houyi_lion_proxy_server.git";
//        String repos = "houyi_lion_proxy_server";
////        String fromBranch = "commit2";
//        String fromBranch = "qa";
//        String toBranch = "master";
//        String buildBranch = "qa";

//        String gitUrl = "ssh://*******************/bm/banma_service_waybill_trans_server.git";
//        String repos = "banma_service_waybill_trans_server";
//        String fromBranch = "qa";
//        String toBranch = "master";
//        String buildBranch = "qa";

        String gitUrl = "ssh://*******************/bm/banma_service_pricing_quote.git";
        String repos = "banma_service_pricing_quote";
        String fromBranch = "qa";
        String toBranch = "master";
        String buildBranch = "qa";
        String buildCommit = null;
//        String gitUrl = "ssh://*******************/bm/banma_service_staff_api_server.git";
//        String repos = "banma_service_staff_api_server";
//        String fromBranch = "release-20240108";
//        String toBranch = "release-20240108";
//        String buildBranch = "release-20240108";
//        String buildCommit = "d9be165c6c62410159da1427c08300bedf4e4b09";
        JavaAnalysisRes javaAnalysisRes = analysisService.analysis(gitUrl, repos, fromBranch, toBranch, buildBranch, buildCommit);
        System.out.println();
    }


}