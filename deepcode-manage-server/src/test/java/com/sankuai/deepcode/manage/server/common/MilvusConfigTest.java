package com.sankuai.deepcode.manage.server.common;

import com.sankuai.deepcode.dao.config.MilvusConfig;
import com.sankuai.deepcode.manage.server.DeepCodePlatformStartApp;
import io.milvus.v2.client.MilvusClientV2;
import io.milvus.v2.service.database.request.CreateDatabaseReq;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = DeepCodePlatformStartApp.class)
public class MilvusConfigTest {

    @Autowired
    private MilvusConfig milvusConfig;

    @Test
    public void dataSource() {
        MilvusClientV2 client = milvusConfig.dataSource();
//        CreateCollectionParam quickSetupReq = CreateCollectionParam.newBuilder()
//                .withCollectionName("quick_setup")
//                .build();


//        // 创建集合参数
//        CreateCollectionParam createCollectionParam = CreateCollectionParam.newBuilder()
//                .withCollectionName("example_collection")
//                .withDescription("This is an example collection")
//                .withShardsNum(2)
//                .addFieldType(fieldType1)
//                .addFieldType(fieldType2)
//                .build();

//
//        CreateCollectionReq quickSetupReq = CreateCollectionReq.builder()
//                .collectionName("quick_setup")
//                .dimension(5)
//                .build();
//
//        client.createCollection(quickSetupReq);
//
//// 3. List all partitions in the collection
//        ListPartitionsReq listPartitionsReq = ListPartitionsReq.builder()
//                .collectionName("quick_setup")
//                .build();
//
//        List<String> partitionNames = client.listPartitions(listPartitionsReq);
//


        CreateDatabaseReq createDatabaseReq = CreateDatabaseReq.builder()
                .databaseName("default")
                .build();

        client.createDatabase(createDatabaseReq);

        System.out.println();
    }
}