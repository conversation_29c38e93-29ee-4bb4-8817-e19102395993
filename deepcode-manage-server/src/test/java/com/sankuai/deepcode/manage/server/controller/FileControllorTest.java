package com.sankuai.deepcode.manage.server.controller;

import com.sankuai.deepcode.ast.common.enums.NodeSourceEnum;
import com.sankuai.deepcode.commons.CommonResult;
import com.sankuai.deepcode.dao.domain.CodeClassAnalysis;
import com.sankuai.deepcode.dao.domain.CodeFieldAnalysis;
import com.sankuai.deepcode.dao.domain.CodeMethodAnalysis;
import com.sankuai.deepcode.dao.service.CodeClassAnalysisService;
import com.sankuai.deepcode.dao.service.CodeFieldAnalysisService;
import com.sankuai.deepcode.dao.service.CodeMethodAnalysisService;
import com.sankuai.deepcode.manage.server.DeepCodePlatformStartApp;
import com.sankuai.deepcode.manage.server.model.file.TreeByVidParam;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.HashSet;
import java.util.List;
import java.util.Set;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = DeepCodePlatformStartApp.class)
public class FileControllorTest {

    @Autowired
    private FileControllor fileControllor;
    @Autowired
    private CodeFieldAnalysisService codeFieldAnalysisService;
    @Autowired
    private CodeMethodAnalysisService codeMethodAnalysisService;
    @Autowired
    private CodeClassAnalysisService codeClassAnalysisService;

    @Test
    public void testGetAllFiles() {
        CommonResult commonResult = fileControllor.getAllFiles(16L);
        System.out.println();
    }

    @Test
    public void testGetInfoByFile() {
        CommonResult commonResult = fileControllor.getInfoByFile(26L, "4751122c32304cdc98c0a11208e1092a");
        System.out.println();
    }

    @Test
    public void getGraphByFile() {
        CommonResult commonResult = fileControllor.getGraphByFile(26L, "4751122c32304cdc98c0a11208e1092a");
        System.out.println();
    }

    @Test
    public void getCodeViewByVid() {
        CommonResult commonResult = fileControllor.getCodeViewByVid(59L, "********************************");
        System.out.println();
    }


    @Test
    public void testGetTreeByVid() {
        TreeByVidParam treeByVidParam = new TreeByVidParam();
        treeByVidParam.setItemId(33L);
        treeByVidParam.setVid("ff21db8b5dc24d9d026618aa7d0623e4");
        treeByVidParam.setDown(true);
        treeByVidParam.setVidType("method");
        treeByVidParam.setStep(30);
        CommonResult commonResult = fileControllor.getTreeByVid(treeByVidParam);
        System.out.println();
    }


    @Test
    public void fixSource() {
        Long itemId = 59L;
        Set<String> className = new HashSet<>();
        List<CodeClassAnalysis> codeClassAnalyses = codeClassAnalysisService.getByItemId(itemId);
        for (CodeClassAnalysis codeClassAnalysis : codeClassAnalyses) {
            className.add(codeClassAnalysis.getClassName());
        }
        List<CodeFieldAnalysis> codeFieldAnalyses = codeFieldAnalysisService.getByItemId(itemId);
        for (CodeFieldAnalysis codeFieldAnalysis : codeFieldAnalyses) {
            if (className.contains(codeFieldAnalysis.getClassName())) {
                codeFieldAnalysis.setSource(NodeSourceEnum.LOCAL.getCode());
                codeFieldAnalysisService.updateById(codeFieldAnalysis);
            } else {
                codeFieldAnalysis.setSource(NodeSourceEnum.UNKNOWN.getCode());
                codeFieldAnalysisService.updateById(codeFieldAnalysis);
            }
        }

        List<CodeMethodAnalysis> codeMethodAnalyses = codeMethodAnalysisService.getByItemId(itemId);
        for (CodeMethodAnalysis codeMethodAnalysis : codeMethodAnalyses) {
            if (className.contains(codeMethodAnalysis.getClassName())) {
                codeMethodAnalysis.setSource(NodeSourceEnum.LOCAL.getCode());
                codeMethodAnalysisService.updateById(codeMethodAnalysis);
            } else {
                codeMethodAnalysis.setSource(NodeSourceEnum.UNKNOWN.getCode());
                codeMethodAnalysisService.updateById(codeMethodAnalysis);
            }
        }
        System.out.println();
    }

    @Test
    public void testGetReposGraphByItemId() {
//        fileControllor.getReposGraphByItemId(53L);
        fileControllor.getReposGraphByItemId(59L);
    }
}