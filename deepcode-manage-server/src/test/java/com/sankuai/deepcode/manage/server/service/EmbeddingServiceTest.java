package com.sankuai.deepcode.manage.server.service;

import com.sankuai.deepcode.ai.embedding.service.EmbeddingService;
import com.sankuai.deepcode.manage.server.DeepCodePlatformStartApp;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.ArrayList;
import java.util.List;


@RunWith(SpringRunner.class)
@SpringBootTest(classes = DeepCodePlatformStartApp.class)
public class EmbeddingServiceTest {

    @Autowired
    private EmbeddingService embeddingService;

    @Test
    public void getEmbeddings() {
        String text1 = "postProcessBeanFactory";
        String text2 = "";
        List<String> texts = new ArrayList<>();
        texts.add(text1);
//        texts.add(text2);
        List<List<Float>> res = embeddingService.getEmbeddings(texts);
        System.out.println();
    }

}