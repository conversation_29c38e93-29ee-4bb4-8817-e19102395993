package com.sankuai.deepcode.manage.server.service;

import com.sankuai.deepcode.ast.common.model.java.MethodNode;
import com.sankuai.deepcode.manage.server.DeepCodePlatformStartApp;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.List;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = DeepCodePlatformStartApp.class)
public class AgentToolsServiceTest {

    @Autowired
    private AgentToolsService agentToolsService;


    @Test
    public void testGetMethodNode() {
        MethodNode methodNode = agentToolsService.getMethodNode(28L, "33beb415defcb406133c50a8b9afe004");
        List<MethodNode> ups = agentToolsService.getUpMethodNodes(28L, "33beb415defcb406133c50a8b9afe004", 3);
        List<MethodNode> downs = agentToolsService.getDownMethodNodes(28L, "33beb415defcb406133c50a8b9afe004", 3);

        System.out.println();
    }

    @Test
    public void testGetUpMethodNodes() {
    }

    @Test
    public void testGetDownMethodNodes() {
    }
}