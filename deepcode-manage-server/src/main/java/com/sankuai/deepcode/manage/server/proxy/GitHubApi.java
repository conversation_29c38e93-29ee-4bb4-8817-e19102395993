package com.sankuai.deepcode.manage.server.proxy;

import com.github.lianjiatech.retrofit.spring.boot.core.RetrofitClient;
import com.sankuai.deepcode.manage.server.jwt.vo.github.AccessToken;
import com.sankuai.deepcode.manage.server.jwt.vo.github.EmailInfo;
import com.sankuai.deepcode.manage.server.jwt.vo.github.UserRes;
import retrofit2.http.*;

import java.util.List;

/**
 * Created by zhangyuanchang
 * Date 2025/1/7 下午4:19
 * Description
 */
@RetrofitClient(baseUrl = "https://github.com")
public interface GitHubApi {
    @POST("https://github.com/login/oauth/access_token")
    @Headers("Accept:application/json")
    String getAccessToken(@Body AccessToken accessToken);

//https://api.github.com/user
    @GET("https://api.github.com/user")
    UserRes getUser(@Header("Authorization") String authorization);

    @GET("https://api.github.com/user/emails")
    List<EmailInfo> getEmails(@Header("Authorization") String authorization);
}
