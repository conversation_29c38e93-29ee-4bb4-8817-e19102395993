package com.sankuai.deepcode.manage.server.service.project;

import com.sankuai.deepcode.commons.DeepCodePreconditions;
import com.sankuai.deepcode.dao.domain.CodeAnalysisItem;
import com.sankuai.deepcode.dao.domain.DcProject;
import com.sankuai.deepcode.dao.service.CodeAnalysisItemService;
import com.sankuai.deepcode.dao.service.DcProjectService;
import com.sankuai.deepcode.manage.server.enums.ErrorCodeEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Optional;

/**
 * Package: com.sankuai.deepcode.manage.server.service.project
 * Description:
 *
 * <AUTHOR>
 * @since 2025/3/17 16:21
 */
@Service
@Slf4j
public class ProjectAnalysisService {
    private final CodeAnalysisItemService codeAnalysisItemService;
    private final DcProjectService dcProjectService;


    public ProjectAnalysisService(CodeAnalysisItemService codeAnalysisItemService, DcProjectService dcProjectService) {
        this.codeAnalysisItemService = codeAnalysisItemService;
        this.dcProjectService = dcProjectService;
    }

    public DcProject getProject(Long projectId) {
        DcProject project = dcProjectService.getById(projectId);
        DeepCodePreconditions.checkBizArgument(project != null, ErrorCodeEnum.DATA_NOT_EXISTS, "项目不存在");
        // DeepCodePreconditions.checkBizArgument(Objects.equals(project.getUserId(), UserUtils.getUserId()), ErrorCodeEnum.NOAUTH, "无权限查看");
        return project;
    }

    public CodeAnalysisItem getAnalysisItemAndValidation(Long projectId) {
        return getAnalysisItemAndValidation(getProject(projectId));
    }

    public CodeAnalysisItem getAnalysisItemAndValidation(DcProject project) {
        DeepCodePreconditions.checkBizArgument(project.getItemId() != null && project.getItemId() > 0, ErrorCodeEnum.DATA_NOT_EXISTS, "项目还未触发代码分析");

        Optional<CodeAnalysisItem> analysisItem = codeAnalysisItemService.getOptById(project.getItemId());
        DeepCodePreconditions.checkBizArgument(analysisItem.isPresent(), ErrorCodeEnum.DATA_NOT_EXISTS, "代码分析任务错误，请联系管理员！！");
        // DeepCodePreconditions.checkBizArgument(ItemStatusEnum.SUCCESS.getCode().equals(analysisItem.get().getStatus()), ErrorCodeEnum.COMING_LATTER, "代码分析任务未结束，请稍后查看！！");

        return analysisItem.get();
    }

    public Long getSameAnalysedItemCount(DcProject project) {
        return codeAnalysisItemService.countGitUrlAndBranches(
                project.getGitUrl(),
                project.getSourceBranch(),
                project.getTargetBranch()
        );
    }
}
