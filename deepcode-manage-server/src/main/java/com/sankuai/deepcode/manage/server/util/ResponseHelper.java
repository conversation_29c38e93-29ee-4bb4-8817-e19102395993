package com.sankuai.deepcode.manage.server.util;

import com.meituan.mtrace.Tracer;
import com.sankuai.deepcode.ai.llm.openai.chat.ChatCompletionChoice;
import com.sankuai.deepcode.ai.llm.openai.chat.Delta;
import com.sankuai.deepcode.ai.llm.openai.chat.Role;
import com.sankuai.deepcode.manage.server.controller.chat.res.ChatCompletionsVO;
import lombok.extern.slf4j.Slf4j;

import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.Collections;

/**
 * Package: com.sankuai.deepcode.manage.server.util
 * Description:
 *
 * <AUTHOR>
 * @since 2025/3/25 09:30
 */
@Slf4j
public class ResponseHelper {
    /**
     * 部分处理结果
     *
     * @param id                消息id
     * @param msgUuid           消息uuid
     * @param processingContent 处理内容
     * @return
     */
    public static ChatCompletionsVO partialProcessingContent(String id, String msgUuid, String processingContent) {
        ChatCompletionsVO.Builder builder = ChatCompletionsVO.builder();
        builder.msgId(msgUuid)
                .id("deepcode-" + Tracer.id())
                .model("deepcode-fake-model")
                .created((int) LocalDateTime.now().toEpochSecond(ZoneOffset.UTC))
                .choices(Collections.singletonList(
                        ChatCompletionChoice.builder()
                                .index(0)
                                .delta(
                                        Delta.builder()
                                                .role(Role.SYSTEM)
                                                .processingContent(processingContent)
                                                .build()
                                ).build()
                )).content("");

        return builder.build();
    }

    /**
     * 部分结果
     *
     * @param id             消息id
     * @param msgUuid        消息uuid
     * @param partialContent 部分内容
     * @return
     */
    public static ChatCompletionsVO partialContent(String id, String msgUuid, String partialContent) {
        ChatCompletionsVO.Builder builder = ChatCompletionsVO.builder();
        builder.msgId(id)
                .id("deepcode-" + Tracer.id())
                .model("deepcode-fake-model")
                .created((int) LocalDateTime.now().toEpochSecond(ZoneOffset.UTC))
                .choices(Collections.singletonList(
                        ChatCompletionChoice.builder()
                                .index(0)
                                .delta(
                                        Delta.builder()
                                                .role(Role.ASSISTANT)
                                                .content(partialContent)
                                                .build()
                                ).build()
                ))
                .content("");

        return builder.build();
    }
}
