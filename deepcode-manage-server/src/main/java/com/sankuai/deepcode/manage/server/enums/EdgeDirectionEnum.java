package com.sankuai.deepcode.manage.server.enums;

/**
 * 入边、出边
 */
public enum EdgeDirectionEnum {

    UP("up", "反向，即检索入边"),

    DOWN("down", "表示双向，即检索出边和入边");

    private String code;

    private String desc;

    EdgeDirectionEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return this.code;
    }


    public String getDesc() {
        return desc;
    }

    @Override
    public String toString() {
        return "EdgeDirectionEnum{" +
                "code=" + code +
                ", desc='" + desc + '\'' +
                '}';
    }
}