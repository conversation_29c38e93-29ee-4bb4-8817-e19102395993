package com.sankuai.deepcode.manage.server.controller.code;

import com.sankuai.deepcode.commons.CommonResult;
import com.sankuai.deepcode.commons.DeepCodePreconditions;
import com.sankuai.deepcode.dao.domain.CodeAnalysisItem;
import com.sankuai.deepcode.dao.domain.CodeFileAnalysis;
import com.sankuai.deepcode.dao.domain.CodeViewAnalysis;
import com.sankuai.deepcode.dao.domain.DcProject;
import com.sankuai.deepcode.dao.service.CodeFileAnalysisService;
import com.sankuai.deepcode.dao.service.CodeViewAnalysisService;
import com.sankuai.deepcode.dao.service.DcProjectService;
import com.sankuai.deepcode.manage.server.controller.code.req.FileContentReq;
import com.sankuai.deepcode.manage.server.controller.code.res.AiReviewsVO;
import com.sankuai.deepcode.manage.server.controller.code.res.FileContentVO;
import com.sankuai.deepcode.manage.server.controller.code.res.MethodInfoVO;
import com.sankuai.deepcode.manage.server.controller.code.res.TreeNodeVO;
import com.sankuai.deepcode.manage.server.enums.ErrorCodeEnum;
import com.sankuai.deepcode.manage.server.mapper.code.FileContentConvertor;
import com.sankuai.deepcode.manage.server.service.code.FileMethodService;
import com.sankuai.deepcode.manage.server.service.project.ProjectAnalysisService;
import com.sankuai.deepcode.manage.server.util.FileTreeBuilder;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Optional;

/**
 * Package: com.sankuai.deepcode.manage.server.controller.code
 * Description:
 *
 * <AUTHOR>
 * @since 2025/3/13 20:14
 */
@Tag(name = "代码相关接口")
@RestController
@RequestMapping("/code")
@Slf4j
public class CodeController {
    private final DcProjectService dcProjectService;
    private final ProjectAnalysisService projectAnalysisService;
    private final CodeFileAnalysisService codeFileAnalysisService;
    private final CodeViewAnalysisService codeViewAnalysisService;
    private final FileContentConvertor fileContentConvertor;
    private final FileMethodService fileMethodService;

    public CodeController(
            DcProjectService dcProjectService,
            ProjectAnalysisService projectAnalysisService,
            CodeFileAnalysisService codeFileAnalysisService,
            CodeViewAnalysisService codeViewAnalysisService,
            FileContentConvertor fileContentConvertor,
            FileMethodService fileMethodService
    ) {
        this.dcProjectService = dcProjectService;
        this.projectAnalysisService = projectAnalysisService;
        this.codeFileAnalysisService = codeFileAnalysisService;
        this.codeViewAnalysisService = codeViewAnalysisService;
        this.fileContentConvertor = fileContentConvertor;
        this.fileMethodService = fileMethodService;
    }

    @Operation(summary = "获取代码树", parameters = {
            @Parameter(name = "projectId", description = "项目ID", required = true),
    })
    @GetMapping("/tree/{projectId}")
    public CommonResult<List<TreeNodeVO>> getCodeTree(
            @PathVariable("projectId") Long projectId,
            @RequestParam(value = "compact", required = false, defaultValue = "true") Boolean compact
    ) {
        DcProject project = dcProjectService.getById(projectId);
        projectAnalysisService.getAnalysisItemAndValidation(project);

        List<CodeFileAnalysis> filePaths = codeFileAnalysisService.getAllFilePathByItemId(project.getItemId());
        return CommonResult.success(FileTreeBuilder.buildFileTree(filePaths, compact));
    }

    @Operation(summary = "获取代码文件内容")
    @PostMapping("/file/content")
    public CommonResult<FileContentVO> getCodeFileContent(
            @Validated @RequestBody FileContentReq contentReq
    ) {
        DcProject project = dcProjectService.getById(contentReq.getProjectId());
        CodeAnalysisItem analysisItem = projectAnalysisService.getAnalysisItemAndValidation(project);
        Optional<CodeFileAnalysis> fileAnalysis = codeFileAnalysisService.getByItemIdAndFilePath(analysisItem.getId(), contentReq.getFilePath());
        DeepCodePreconditions.checkBizArgument(fileAnalysis.isPresent(), ErrorCodeEnum.DATA_NOT_EXISTS, "文件不存在");

        List<CodeViewAnalysis> codeView = codeViewAnalysisService.getAllContent(analysisItem.getId(), fileAnalysis.get().getFileVid());
        DeepCodePreconditions.checkBizArgument(CollectionUtils.isNotEmpty(codeView), ErrorCodeEnum.DATA_NOT_EXISTS, "文件内容不存在");

        return CommonResult.success(fileContentConvertor.convert2VO(codeView, fileAnalysis.get()));
    }

    @Operation(summary = "获取文件中的方法信息", parameters = {
            @Parameter(name = "projectId", description = "项目ID", required = true),
            @Parameter(name = "fileVid", description = "文件Vid", required = true)
    })
    @GetMapping("/file/methods")
    public CommonResult<MethodInfoVO> getMethodInfo(
            @RequestParam("projectId") Long projectId,
            @RequestParam("fileVid") String fileVid
    ) {
        CodeAnalysisItem analysisItem = projectAnalysisService.getAnalysisItemAndValidation(projectId);
        return CommonResult.success(fileMethodService.getMethodInfo(analysisItem, fileVid));
    }

    @Operation(summary = "获取文件的智能评审", parameters = {
            @Parameter(name = "projectId", description = "项目ID", required = true),
            @Parameter(name = "fileVid", description = "文件Vid", required = true)
    })
    @GetMapping("/file/aiReviews")
    public CommonResult<AiReviewsVO> getAiReviews(
            @RequestParam("projectId") Long projectId,
            @RequestParam("fileVid") String fileVid
    ) {
        CodeAnalysisItem analysisItem = projectAnalysisService.getAnalysisItemAndValidation(projectId);
        return CommonResult.success(fileMethodService.getAiReviews(analysisItem, fileVid));
    }
}
