package com.sankuai.deepcode.manage.server.model.file;

import com.sankuai.deepcode.ast.common.model.base.BaseNode;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
public class GraphNode extends BaseNode {
    private String id;
    private String label;
    private String nodeType;
    private String comboId;
    private int changeType;
    private List<GraphNode> children = new ArrayList<>();

    private String path;
}
