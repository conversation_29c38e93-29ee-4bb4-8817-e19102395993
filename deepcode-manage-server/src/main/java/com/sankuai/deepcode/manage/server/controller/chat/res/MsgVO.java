package com.sankuai.deepcode.manage.server.controller.chat.res;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * Package: com.sankuai.deepcode.manage.server.controller.chat.res
 * Description:
 *
 * <AUTHOR>
 * @since 2025/3/17 10:52
 */
@Setter
@Getter
@ToString
@Accessors(chain = true)
public class MsgVO {
    @Schema(description = "消息UUID")
    private String id;
    @Schema(description = "会话UUID")
    private String chatUuid;
    @Schema(description = "父消息ID")
    private String prevMsgId;
    @Schema(description = "子消息ID列表")
    private ChildrenVO childrenIds;
    @Schema(description = "消息内容")
    private MsgContentVO content;
    @Schema(description = "角色")
    private String role;
    @Schema(description = "创建时间")
    private String createTime;
    @Schema(description = "更新时间")
    private String updateTime;


    @Setter
    @Getter
    @ToString
    @Accessors(chain = true)
    public static class ChildrenVO {
        @Schema(description = "子消息ID列表")
        private List<String> ids;
    }
}
