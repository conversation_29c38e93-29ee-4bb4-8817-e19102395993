package com.sankuai.deepcode.manage.server.service.chat;

import com.sankuai.deepcode.dao.domain.DcChat;
import com.sankuai.deepcode.dao.domain.DcChatMessage;
import com.sankuai.deepcode.dao.domain.DcProject;
import com.sankuai.deepcode.dao.service.DcChatMessageService;
import com.sankuai.deepcode.dao.service.DcProjectService;
import com.sankuai.deepcode.manage.server.controller.chat.res.ChatMsgVO;
import com.sankuai.deepcode.manage.server.controller.chat.res.MsgVO;
import com.sankuai.deepcode.manage.server.jwt.utils.UserUtils;
import com.sankuai.deepcode.manage.server.mapper.chat.ChatConvertor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * Package: com.sankuai.deepcode.manage.server.service.chat
 * Description:
 *
 * <AUTHOR>
 * @since 2025/3/17 10:24
 */
@Service
@Slf4j
public class ChatMsgService {
    private final ChatConvertor chatConvertor;
    private final DcChatMessageService dcChatMessageService;
    private final DcProjectService dcProjectService;


    public ChatMsgService(ChatConvertor chatConvertor, DcChatMessageService dcChatMessageService, DcProjectService dcProjectService) {
        this.chatConvertor = chatConvertor;
        this.dcChatMessageService = dcChatMessageService;
        this.dcProjectService = dcProjectService;
    }

    public ChatMsgVO constructChatMsg(DcChat chat, Long userId) {
        List<DcChatMessage> chatMessages = dcChatMessageService.getByChatId(chat.getUuid(), userId);
        ChatMsgVO chatMsgVO = new ChatMsgVO();
        DcProject project = dcProjectService.getById(chat.getProjectId());
        chatMsgVO.setChat(chatConvertor.convert(chat, project));
        if (CollectionUtils.isEmpty(chatMessages)) {
            return chatMsgVO;
        }
        List<MsgVO> msgVOList = chatConvertor.convertMsgList(chatMessages);
        chatMsgVO.setMessageList(msgVOList);

        return chatMsgVO;
    }


    /**
     * 获取消息链
     *
     * @param chat     聊天对象
     * @param stopUuid 停止消息的UUID
     * @return
     */
    public List<DcChatMessage> getChainedMsgList(DcChat chat, String stopUuid) {
        if (Objects.isNull(chat)) {
            return Collections.emptyList();
        }
        List<DcChatMessage> chatMessages = dcChatMessageService.getByChatId(chat.getUuid(), UserUtils.getUserId());
        if (CollectionUtils.isEmpty(chatMessages)) {
            return Collections.emptyList();
        }
        // 查找目标消息
        DcChatMessage targetMessage = findTargetMessage(chatMessages, stopUuid);
        if (targetMessage == null) {
            return Collections.emptyList();
        }

        // 构建消息链
        List<DcChatMessage> result = buildMessageChain(chatMessages, targetMessage);

        // 反转消息链顺序并返回
        Collections.reverse(result);
        return result;
    }

    private DcChatMessage findTargetMessage(List<DcChatMessage> messages, String targetUuid) {
        return messages.stream()
                .filter(msg -> Objects.equals(msg.getUuid(), targetUuid))
                .findFirst()
                .orElse(null);
    }

    private List<DcChatMessage> buildMessageChain(List<DcChatMessage> allMessages, DcChatMessage startMessage) {
        List<DcChatMessage> chain = new ArrayList<>();
        chain.add(startMessage);

        String prevMsgId = startMessage.getPrevMsgId();
        while (prevMsgId != null) {
            DcChatMessage prevMessage = findTargetMessage(allMessages, prevMsgId);
            if (prevMessage == null) {
                break;
            }
            chain.add(prevMessage);
            prevMsgId = prevMessage.getPrevMsgId();
        }

        return chain;
    }
}
