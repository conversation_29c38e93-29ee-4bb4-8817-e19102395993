package com.sankuai.deepcode.manage.server.controller;


import com.sankuai.deepcode.ai.llm.model.chat.ChatByStreamParam;
import com.sankuai.deepcode.ai.llm.model.chat.ChatMessage;
import com.sankuai.deepcode.ai.llm.model.chat.ChatMsgRes;
import com.sankuai.deepcode.ai.llm.model.chat.StreamRes;
import com.sankuai.deepcode.manage.server.enums.ErrorCodeEnum;
import com.sankuai.deepcode.commons.CommonResult;
import com.sankuai.deepcode.manage.server.service.GptApiService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.util.ArrayList;
import java.util.List;

@RestController
@RequestMapping("chat")
public class ChatController {
    private static final Logger LOGGER = LoggerFactory.getLogger(ChatController.class);

    @Autowired
    private GptApiService gptApiService;

    @PostMapping(value = "/chatByStream", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public SseEmitter chatByStream(@RequestBody ChatByStreamParam chatByStreamParam) throws Exception {
        try {
            if (null == chatByStreamParam.getItemId() || chatByStreamParam.getItemId() == 0) {
                SseEmitter emitter = new SseEmitter();
                StreamRes streamRes = new StreamRes();
                streamRes.setContent("itemId不能为空");
                streamRes.setFullContent("itemId不能为空");
                emitter.send(SseEmitter.event().data(streamRes));
                return emitter;
            }
            return gptApiService.chatStream(chatByStreamParam.getItemId(), chatByStreamParam.getChatMessages());
        } catch (Exception e) {
            LOGGER.error("chatByStream 异常e:", e);
            SseEmitter emitter = new SseEmitter();
            StreamRes streamRes = new StreamRes();
            streamRes.setContent("chatByStream 异常e::" + e.getMessage());
            streamRes.setFullContent("chatByStream 异常e::" + e.getMessage());
            emitter.send(SseEmitter.event().data(streamRes));
            return emitter;
        }
    }


    @PostMapping(value = "/test")
    public void test() throws Exception {
        try {
            // 获取系统属性 "java.class.path"
            String classPath = System.getProperty("java.class.path");
            // 使用操作系统特定的分隔符分割类路径
            String[] classPathEntries = classPath.split(System.getProperty("path.separator"));

            ChatByStreamParam chatByStreamParam = new ChatByStreamParam();
            List<ChatMessage> chatMessages = new ArrayList<>();
            ChatMessage chatMessage = new ChatMessage();
            chatMessage.setContent("查询所有变更方法并展示前十个变更方法的vid");
            chatMessage.setRole("user");
            chatMessages.add(chatMessage);
            chatByStreamParam.setChatMessages(chatMessages);
            chatByStream(chatByStreamParam);
        } catch (Exception e) {
            LOGGER.error("chatByStream 异常e:", e);
        }
    }

    @PostMapping("/chatByCompletions")
    public CommonResult chatByCompletions(@RequestBody ChatByStreamParam chatByStreamParam) throws Exception {
        try {
            ChatMsgRes res = gptApiService.completions(chatByStreamParam.getChatMessages());
            ChatMessage chatMessage = new ChatMessage();
            chatMessage.setRole("assistant");
            chatMessage.setContent(res.getChoices().get(0).getMessage().getContent());
            return CommonResult.success(chatMessage);
        } catch (Exception e) {
            LOGGER.error("chatByStream 异常e:", e);
            return CommonResult.fail(ErrorCodeEnum.FAILURE, "chatByStream异常");
        }
    }


    @PostMapping(value = "/apiChatByStream", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public SseEmitter apiChatByStream(@RequestBody ChatByStreamParam chatByStreamParam) throws Exception {
        try {
            return gptApiService.apiChatStream(chatByStreamParam.getChatMessages());
        } catch (Exception e) {
            LOGGER.error("chatByStream 异常e:", e);
            SseEmitter emitter = new SseEmitter();
            StreamRes streamRes = new StreamRes();
            streamRes.setContent("chatByStream 异常e::" + e.getMessage());
            streamRes.setFullContent("chatByStream 异常e::" + e.getMessage());
            emitter.send(SseEmitter.event().data(streamRes));
            return emitter;
        }
    }

}
