package com.sankuai.deepcode.manage.server.controller.chat.res;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * Package: com.sankuai.deepcode.manage.server.controller.chat.res
 * Description:
 *
 * <AUTHOR>
 * @since 2025/3/27 10:44
 */
@Setter
@Getter
@ToString
@Accessors(chain = true)
public class AdviceQuestionVO {
    @Schema(description = "推荐问题列表", example = "[\"这个项目是做什么的?\", \"这个项目哪些方法可以获取用户信息?\"]")
    private List<String> questions;

}
