package com.sankuai.deepcode.manage.server.controller.chat.res;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Package: com.sankuai.deepcode.manage.server.controller.chat.res
 * Description:
 *
 * <AUTHOR>
 * @since 2025/4/1 21:02
 */
@Setter
@Getter
@ToString
@Accessors(chain = true)
public class ChatProjectInfoVO {
    @Schema(description = "项目信息")
    ProjectInfo projectInfo;

    @Schema(description = "项目名称")
    String projectDesc;

    @Schema(description = "是否可以开启聊天会话")
    Boolean chatEnabled;

    @Schema(description = "是否需要轮询")
    Boolean needPolling;

    @Schema(description = "是否可以显示相关代码")
    Boolean showCode;

    @Schema(description = "项目状态")
    ProjectStatusEnum status;

    @Schema(description = "项目分析详细过程")
    List<ProcessInfo> processInfoList;

    @Setter
    @Getter
    @ToString
    @Accessors(chain = true)
    public static class ProcessInfo {
        @Schema(description = "处理类型")
        private String handleType;
        @Schema(description = "处理过程名称")
        private String name;
        @Schema(description = "处理过程描述")
        private String desc;
        @Schema(description = "处理结果")
        private String result;
        @Schema(description = "处理结果详情")
        private String resultDetail;
        @Schema(description = "处理状态")
        private Integer status;
        @Schema(description = "处理状态描述")
        private String statusDesc;

        @Schema(description = "处理进度百分比，0-100.00")
        private Double progress;

        @Schema(description = "开始时间")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
        private LocalDateTime startTime;
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
        @Schema(description = "结束时间")
        private LocalDateTime endTime;
    }

    @Setter
    @Getter
    @ToString
    @Accessors(chain = true)
    public static class ProjectInfo {
        @Schema(description = "项目ID")
        Long projectId;
        @Schema(description = "项目名称")
        String projectName;
        @Schema(description = "git地址")
        String gitUrl;
        @Schema(description = "是否差异分析")
        Boolean isDiff;
        @Schema(description = "分析模式描述")
        String modeDesc;
        @Schema(description = "源分支")
        String sourceBranch;
        @Schema(description = "源提交")
        String sourceCommit;
        @Schema(description = "源提交信息")
        String sourceCommitMessage;
        @Schema(description = "目标分支")
        String targetBranch;
        @Schema(description = "目标提交")
        String targetCommit;
        @Schema(description = "相同项目信息已分析任务数量")
        Long sameAnalysedItemCount;
    }
}
