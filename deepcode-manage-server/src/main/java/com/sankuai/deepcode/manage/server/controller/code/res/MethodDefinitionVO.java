package com.sankuai.deepcode.manage.server.controller.code.res;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 * Package: com.sankuai.deepcode.manage.server.controller.code.res
 * Description:
 *
 * <AUTHOR>
 * @since 2025/3/17 16:15
 */
@Setter
@Getter
@ToString
@Accessors(chain = true)
public class MethodDefinitionVO {
    @Schema(description = "文件路径")
    private String filePath;

    @Schema(description = "跳转展示名称")
    private String showName;

    @Schema(description = "是否可跳转")
    private boolean navigatable;

    @Schema(description = "不可跳转原因")
    private String cannotNavigateReason;

    @Schema(description = "方法vid")
    private String methodVid;


    @Schema(description = "方法定义开始行")
    private Integer startLine;
    @Schema(description = "方法定义结束行")
    private Integer endLine;
}
