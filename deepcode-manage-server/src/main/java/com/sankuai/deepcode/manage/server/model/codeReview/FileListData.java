package com.sankuai.deepcode.manage.server.model.codeReview;

import lombok.Data;

import java.util.HashSet;
import java.util.List;
import java.util.Set;

@Data
public class FileListData implements Comparable<FileListData> {
    private int id;
    private String vid;
    private String className = "";
    private String name = "";
    private String type = "";
    private int startLine = 0;
    private Set<Integer> lines = new HashSet<>();
    private int changeType = 0;

    @Override
    public int compareTo(FileListData fileListData) {
        if (this.changeType == fileListData.getChangeType()) {
            if (this.startLine == fileListData.getStartLine()) {
                return 0;
            }
            if (this.startLine > fileListData.getStartLine()) {
                return 1;//反过来排序可设置为1 下面的return改为-1。一般这都是createTime倒叙
            }
            return -1;
        }
        //自定义比较方法，如果认为此实体本身大则返回1，否则返回-1
        if (this.changeType > fileListData.getChangeType()) {
            return 1;//反过来排序可设置为1 下面的return改为-1。一般这都是createTime倒叙
        }
        return -1;
    }
}
