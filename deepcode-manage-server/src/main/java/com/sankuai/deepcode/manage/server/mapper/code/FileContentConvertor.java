package com.sankuai.deepcode.manage.server.mapper.code;

import com.sankuai.deepcode.dao.domain.CodeFileAnalysis;
import com.sankuai.deepcode.dao.domain.CodeViewAnalysis;
import com.sankuai.deepcode.dao.domain.RuleInfoDetail;
import com.sankuai.deepcode.dao.domain.RuleTaskDetail;
import com.sankuai.deepcode.manage.server.controller.code.res.AiReviewsVO;
import com.sankuai.deepcode.manage.server.controller.code.res.FileContentVO;
import org.apache.commons.collections4.CollectionUtils;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;

import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Package: com.sankuai.deepcode.manage.server.mapper.code
 * Description:
 *
 * <AUTHOR>
 * @since 2025/3/13 22:20
 */
@Mapper(
        componentModel = "spring"
)
public interface FileContentConvertor {
    default FileContentVO convert2VO(List<CodeViewAnalysis> codeViewList, CodeFileAnalysis codeFileAnalysis) {
        FileContentVO fileContentVO = new FileContentVO();
        fileContentVO.setFilePath(codeFileAnalysis.getFilePath());
        fileContentVO.setHaveDiff(codeFileAnalysis.getChangeType() != 0);
        fileContentVO.setFileType(getLanguageByFileType(codeFileAnalysis.getFileType()));

        fileContentVO.setSourceBranchFileContent(
                codeViewList.stream().filter(c -> c.getCodeType() == 1 || c.getCodeType() == 2)
                        .sorted(Comparator.comparing(CodeViewAnalysis::getCodeLine))
                        .map(CodeViewAnalysis::getCodeView)
                        .collect(Collectors.joining("\n"))
        );

        fileContentVO.setTargetBranchFileContent(
                codeViewList.stream().filter(c -> c.getCodeType() == 1 || c.getCodeType() == 3)
                        .sorted(Comparator.comparing(CodeViewAnalysis::getCodeLine))
                        .map(CodeViewAnalysis::getCodeView)
                        .collect(Collectors.joining("\n"))
        );

        return fileContentVO;
    }

    default String getLanguageByFileType(String fileType) {
        if (fileType == null) {
            return "Unknown";
        }
        switch (fileType.toLowerCase()) {
            case "html":
            case "vue":
            case "wxml":
                return "html";
            case "py":
                return "python";
            case "js":
            case "jsx":
                return "javascript";
            case "ts":
            case "tsx":
                return "typescript";
            case "less":
            case "css":
            case "scss":
            case "wxss":
                return "css";
            case "c":
            case "h":
            case "cpp":
            case "hpp":
                return "cpp";
            case "cs":
                return "csharp";
            case "rb":
                return "ruby";
            case "kt":
                return "kotlin";
            case "rs":
                return "rust";
            case "yml":
                return "yaml";
            case "sh":
                return "shell";
            case "md":
                return "markdown";
            case "toml":
            case "env":
                return "ini";
            default:
                // 未知，或是扩展名与编程 语言相同 的不做处理
                return fileType.toLowerCase();
        }
    }

    @Mappings({
            @Mapping(target = "ruleInfo.ruleId", source = "ruleInfoDetail.id"),
            @Mapping(target = "ruleInfo.ruleType", source = "ruleInfoDetail.type"),
            @Mapping(target = "ruleInfo.content", source = "ruleInfoDetail.value"),
            @Mapping(target = "ruleInfo.languages", source = "ruleInfoDetail.languages"),
            @Mapping(target = "startLine", source = "ruleTaskDetail.startLine"),
            @Mapping(target = "reviewContent", source = "ruleTaskDetail.value"),
            @Mapping(target = "reviewTime", source = "ruleTaskDetail.createTime")
    })
    AiReviewsVO.AiReviewInfo convert2AiReviewInfo(RuleTaskDetail ruleTaskDetail, RuleInfoDetail ruleInfoDetail);

    default List<AiReviewsVO.AiReviewInfo> convertReviewList(List<RuleTaskDetail> ruleTaskDetails, Map<Long, RuleInfoDetail> ruleInfoDetailMap) {
        if (CollectionUtils.isEmpty(ruleTaskDetails)) {
            return Collections.emptyList();
        }
        return ruleTaskDetails.stream().map(r -> convert2AiReviewInfo(r, ruleInfoDetailMap.get(r.getRuleId()))).collect(Collectors.toList());
    }


    default AiReviewsVO convertReviewVO(List<RuleTaskDetail> ruleTaskDetails, Map<Long, RuleInfoDetail> ruleInfoDetailMap, CodeFileAnalysis fileAnalysis) {
        if (null == fileAnalysis) {
            return null;
        }
        AiReviewsVO reviewsVO = new AiReviewsVO().setFilePath(fileAnalysis.getFilePath()).setFileVid(fileAnalysis.getFileVid());

        reviewsVO.setAiReviewInfos(convertReviewList(ruleTaskDetails, ruleInfoDetailMap));
        return reviewsVO;
    }
}
