package com.sankuai.deepcode.manage.server.common;

import com.sankuai.deepcode.commons.CommonResult;
import com.sankuai.deepcode.commons.ex.BizException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpStatus;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.validation.BindException;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.MissingServletRequestParameterException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.method.annotation.MethodArgumentTypeMismatchException;

import javax.servlet.http.HttpServletRequest;
import javax.validation.ConstraintViolationException;
import javax.validation.ValidationException;

/**
 * Package: com.sankuai.deepcode.manage.server.common
 * Description:
 *
 * <AUTHOR>
 * @since 2025-03-04 19:17:54
 */
@RestControllerAdvice
@Slf4j
@ResponseBody
public class ControllerAdvice {

    /**
     * 捕获由 Guava 工具，或是其他参数验证，抛出的{@link IllegalArgumentException}异常，组装成公共响应
     *
     * @return
     */
    @ExceptionHandler(IllegalArgumentException.class)
    public CommonResult<Object> handleIllegalArgumentException(HttpServletRequest request, Exception exception) {
        log.warn("请求接口：{} 参数验证: {} 不通过", request.getRequestURI(), exception.getMessage());
        if (exception instanceof MethodArgumentTypeMismatchException) {
            MethodArgumentTypeMismatchException e = (MethodArgumentTypeMismatchException) exception;
            return CommonResult.fail(403, "请求参数" + e.getName() + "需要" + e.getRequiredType().getName() + "类型，传入值：" + e.getValue() + " 转换错误!");
        }
        return CommonResult.fail(exception.getMessage());
    }

    /**
     * 处理Validated校验异常
     * <p>
     * 注: 常见的ConstraintViolationException异常， 也属于ValidationException异常
     *
     * @param e 捕获到的异常
     * @return 返回给前端的data
     */
    @ExceptionHandler(value = {BindException.class, ValidationException.class, MethodArgumentNotValidException.class})
    public CommonResult<Object> handleParameterVerificationException(Exception e) {
        log.warn("handleParameterVerificationException: {}", e.getMessage());
        String msg = StringUtils.EMPTY;
        if (e instanceof BindException) {
            FieldError fieldError = ((BindException) e).getFieldError();
            if (fieldError != null) {
                msg = fieldError.getDefaultMessage();
            }
        } else if (e instanceof ConstraintViolationException) {
            msg = e.getMessage();
            if (msg != null) {
                int lastIndex = msg.lastIndexOf(':');
                if (lastIndex >= 0) {
                    msg = msg.substring(lastIndex + 1).trim();
                }
            }
        } else if (e instanceof ValidationException) {
            msg = e.getMessage();
        } else {
            msg = "处理参数异常";
        }

        return CommonResult.fail(403, msg);
    }

    @ExceptionHandler({HttpMessageNotReadableException.class})
    public CommonResult<Object> requestNotReadable(HttpMessageNotReadableException exception) {
        log.warn("Handle requestNotReadable exception:{}", exception.getMessage(), exception);
        return CommonResult.fail(exception.getMessage());
    }

    @ExceptionHandler(MissingServletRequestParameterException.class)
    public CommonResult<String> requestMissingServletRequestParam(HttpServletRequest request, MissingServletRequestParameterException ex) {
        log.warn("request {} missing required parameter:{}", request.getPathInfo(), ex.getMessage());
        return CommonResult.fail(40004, ex.getMessage());
    }

    @ExceptionHandler(BizException.class)
    @ResponseStatus(HttpStatus.OK)
    public @ResponseBody CommonResult handleBizException(HttpServletRequest request, Exception e) {
        BizException bizException = (BizException) e;
        return CommonResult.fail(bizException);
    }
}
