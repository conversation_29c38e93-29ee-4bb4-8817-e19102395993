package com.sankuai.deepcode.manage.server.model.codeReview;

import com.sankuai.deepcode.ast.common.model.java.FieldNode;
import com.sankuai.deepcode.ast.common.model.java.MethodNode;
import lombok.Data;

@Data
public class MethodMatcher implements Comparable<MethodMatcher> {
    private int start;
    private int end;
    private String group;
    private MethodNode methodNode;
    private FieldNode fieldNode;

    @Override
    public int compareTo(MethodMatcher matcher) {
        if (this.start == matcher.getStart()) {
            return 0;
        }
        //自定义比较方法，如果认为此实体本身大则返回1，否则返回-1
        if (this.start > matcher.getStart()) {
            return 1;//反过来排序可设置为1 下面的return改为-1。一般这都是createTime倒叙
        }
        return -1;
    }
}
