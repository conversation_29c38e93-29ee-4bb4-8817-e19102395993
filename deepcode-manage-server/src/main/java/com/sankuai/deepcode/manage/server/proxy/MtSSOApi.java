package com.sankuai.deepcode.manage.server.proxy;

import com.github.lianjiatech.retrofit.spring.boot.core.RetrofitClient;
import com.sankuai.deepcode.manage.server.jwt.vo.mtsso.MtUserinfoParam;
import retrofit2.http.*;

/**
 * Created by zhangyuanchang
 * Date 2025/1/7 下午4:19
 * Description
 */
@RetrofitClient(baseUrl = "https://ssosv.sankuai.com/")
public interface MtSSOApi {

    //线上
//    @GET("https://ssosv.sankuai.com/sson/oauth2.0/access-token")
    @GET("http://ssosv.it.test.sankuai.com/sson/oauth2.0/access-token")
    @Headers("Accept:application/json")
    String getAccessToken(@Query("code") String code, @Header("Authorization") String authorization, @Header("Date") String date);

    //   //线上
//    @POST("https://ssosv.sankuai.com/open/api/session/userinfo")
    @POST("https://ssosv.it.test.sankuai.com/open/api/session/userinfo")
    @Headers("Accept:application/json")
    String getUserinfo(@Body MtUserinfoParam mtUserinfoParam, @Header("Authorization") String authorization, @Header("Date") String date);

    //   //线上
//    @POST("https://ssosv.sankuai.com/sson/oauth2.0/refresh-token")
    @POST("https://ssosv.it.test.sankuai.com/sson/oauth2.0/refresh-token")
    String refreshToken(@Header("Authorization") String authorization);
}
