package com.sankuai.deepcode.manage.server.controller.chat.req;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * Package: com.sankuai.deepcode.manage.server.controller.chat.req
 * Description:
 *
 * <AUTHOR>
 * @since 2025/3/20 15:18
 */
@Setter
@Getter
@ToString(callSuper = true)
public class ProjectChatRequestVO extends ChatRequestVO {
    @Schema(description = "项目ID", example = "1")
    private Long projectId;
    @Schema(description = "会话ID(UUID)", example = "d9139ae7-06ea-11f0-9977-d62c029b9c75")
    private String chatUuid;
    @Schema(description = "重新生成消息ID(UUID)，必须是最后一条 user 的Id", example = "3fc5a319-06f0-11f0-9977-d62c029b9c75")
    private String regenerateMsgId;
    @Schema(description = "上一条消息ID(UUID)", example = "3fc5a319-06f0-11f0-9977-d62c029b9c75")
    private String prevMsgId;
}
