package com.sankuai.deepcode.manage.server.controller.chat.res;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonValue;
import io.swagger.v3.oas.annotations.Hidden;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

/**
 * Package: com.sankuai.deepcode.manage.server.controller.chat.res
 * Description:
 *
 * <AUTHOR>
 * @since 2025/4/2 12:25
 */
@Getter
@AllArgsConstructor
@JsonFormat(shape = JsonFormat.Shape.OBJECT)
public enum ProjectStatusEnum {
    INITIATING(0, "初始化", "项目初始化"),
    SUCCESS(200, "成功", "项目代码分析成功"),
//    FAIL(2, "失败", "项目代码分析失败"),

    HAVE_NOT_ANALYSED(300, "无分析任务", "当前项目未触发分析任务，请手动触发分析任务或关联已有分析结果"),

    ANALYSING(400, "代码分析中", "项目代码正努力分析中"),
    ANALYSE_FINISHED(401, "代码分析完成", "项目代码分析完成"),
    ANALYSE_FAILED(402, "代码分析失败", "项目代码分析失败"),

    KNOWLEDGE_GENERATING(500, "代码知识构建中", "项目知识正在生成中"),
    KNOWLEDGE_GENERATE_FAILED(501, "代码知识生成失败", "项目知识生成失败");

    private final Integer status;
    private final String desc;
    private final String detailedDesc;

    @Hidden
    @JsonValue
    public Map<String, Object> toJson() {
        Map<String, Object> result = new HashMap<>();
        result.put("code", status);
        result.put("desc", desc);
        result.put("detailedDesc", detailedDesc);
        return result;
    }
}
