package com.sankuai.deepcode.manage.server.service.rule;

import com.sankuai.deepcode.dao.domain.RuleBindInfo;
import com.sankuai.deepcode.dao.domain.RuleInfoDetail;
import com.sankuai.deepcode.dao.domain.SysUser;
import com.sankuai.deepcode.dao.service.*;
import com.sankuai.deepcode.manage.server.model.rule.RuleInfoDetailWithBindInfo;
import com.sankuai.deepcode.manage.server.model.rule.RuleInfoDetailWithUserInfo;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class RuleService {
    @Autowired
    private RuleInfoDetailService ruleInfoDetailService;
    @Autowired
    private RuleBindInfoService ruleBindInfoService;
    @Autowired
    private RuleTaskDetailService ruleTaskDetailService;
    @Autowired
    private RuleTaskInfoService ruleTaskInfoService;

    @Autowired
    private SysUserService sysUserService;

    //新增规则
    public int addRule(RuleInfoDetail ruleInfoDetail) {
        return ruleInfoDetailService.insert(ruleInfoDetail);
    }

    //修改规则
    public boolean editRule(RuleInfoDetail ruleInfoDetail) {
        return ruleInfoDetailService.edit(ruleInfoDetail.getId(),ruleInfoDetail.getUserId(), ruleInfoDetail.getValue());
    }

    //删除规则
    public boolean delRule(long ruleId) {
        return ruleInfoDetailService.del(ruleId);
    }
    //查询规则列表
    public List<RuleInfoDetailWithUserInfo> getRuleList(RuleInfoDetail ruleInfoDetail) {
        return getRuleListWithUserInfo(ruleInfoDetail);
    }

    public List<RuleInfoDetailWithUserInfo> getRuleListWithUserInfo(RuleInfoDetail ruleInfoDetail) {
        List<RuleInfoDetailWithUserInfo> res = new ArrayList<>();
        List<RuleInfoDetail> ruleInfoDetails = ruleInfoDetailService.getRuleList(ruleInfoDetail);
        List<Long> userIds = ruleInfoDetails.stream().map(RuleInfoDetail::getUserId).distinct().collect(Collectors.toList());
        List<SysUser> users = sysUserService.getUserListByIds(userIds);
        Map<Long, SysUser> userMap = users.stream()
                .collect(Collectors.toMap(SysUser::getId, user -> user));
        for (RuleInfoDetail detail : ruleInfoDetails) {
            RuleInfoDetailWithUserInfo combined = new RuleInfoDetailWithUserInfo();
            // 复制规则详情信息
            combined.setId(detail.getId());
            combined.setUserId(detail.getUserId());
            combined.setUserName(userMap.get(detail.getUserId()).getUserName());
            combined.setValue(detail.getValue());
            combined.setType(detail.getType());
            combined.setCreateTime(detail.getCreateTime());
            combined.setUpdateTime(detail.getUpdateTime());
            res.add(combined);

        }
        return res;
    }

    //绑定规则
    public int bindRule(long ruleId,long userId, long projectId) {
        return ruleBindInfoService.bind(ruleId, projectId,userId);
    }

    //解绑规则
    public boolean unbindRule(long bindId,long userId) {
        return ruleBindInfoService.unbind(bindId,userId);
    }

    //根据projectId查询绑定
    public List<RuleBindInfo> getBindInfoByProjectId(long projectId) {
        return ruleBindInfoService.getRuleBindInfoByProjectId(projectId);
    }

    //根据projectId查询规则
    public List<RuleInfoDetailWithBindInfo> getRuleInfoByProjectId(long projectId) {
        List<RuleBindInfo> ruleBindInfoList = ruleBindInfoService.getRuleBindInfoByProjectId(projectId);
        List<RuleInfoDetail> res = ruleInfoDetailService.getRuleListByIds(ruleBindInfoList);
        return transRuleBindInfo(ruleBindInfoList,res);
    }

    public List<RuleInfoDetailWithBindInfo> transRuleBindInfo(List<RuleBindInfo> ruleBindInfoList,List<RuleInfoDetail> ruleInfoDetailList){
        List<RuleInfoDetailWithBindInfo> res = new ArrayList<>();
        if (CollectionUtils.isEmpty(ruleBindInfoList)){
            return res;
        }

        // 使用Map来存储规则ID和规则详情的映射，提高查询效率
        Map<Long, RuleInfoDetail> ruleMap = ruleInfoDetailList.stream()
                .collect(Collectors.toMap(RuleInfoDetail::getId, rule -> rule));
        List<Long> userIds = ruleBindInfoList.stream().map(RuleBindInfo::getUserId).distinct().collect(Collectors.toList());
        List<SysUser> users = sysUserService.getUserListByIds(userIds);
        Map<Long, SysUser> userMap = users.stream()
                .collect(Collectors.toMap(SysUser::getId, user -> user));

        // 遍历绑定信息，将对应的规则详情组合
        for (RuleBindInfo bindInfo : ruleBindInfoList) {
            RuleInfoDetail ruleDetail = ruleMap.get(bindInfo.getRuleId());
            if (ruleDetail != null) {
                RuleInfoDetailWithBindInfo combined = new RuleInfoDetailWithBindInfo();
                // 复制规则详情信息
                combined.setId(bindInfo.getId());
                combined.setRuleId(bindInfo.getRuleId());
                combined.setValue(ruleDetail.getValue());
                combined.setType(ruleDetail.getType());
                combined.setUserId(bindInfo.getUserId());
                combined.setUserName(userMap.get(bindInfo.getUserId()).getUserName());
                combined.setCreateTime(bindInfo.getCreateTime());
                combined.setUpdateTime(bindInfo.getUpdateTime());
                combined.setProjectId(bindInfo.getProjectId());
                res.add(combined);
            }
        }
        return res;

    }

}
