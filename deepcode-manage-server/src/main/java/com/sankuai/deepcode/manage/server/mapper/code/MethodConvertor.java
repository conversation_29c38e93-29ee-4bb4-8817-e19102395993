package com.sankuai.deepcode.manage.server.mapper.code;

import com.sankuai.deepcode.dao.domain.CodeClassAnalysis;
import com.sankuai.deepcode.dao.domain.CodeFileAnalysis;
import com.sankuai.deepcode.dao.domain.CodeMethodAnalysis;
import com.sankuai.deepcode.dao.domain.MethodInvokeMethod;
import com.sankuai.deepcode.dao.po.InvokeLinePO;
import com.sankuai.deepcode.manage.server.controller.code.res.MethodDefinitionVO;
import com.sankuai.deepcode.manage.server.controller.code.res.MethodTopologyVO;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * Package: com.sankuai.deepcode.manage.server.mapper.code
 * Description:
 *
 * <AUTHOR>
 * @since 2025/3/17 21:39
 */
@Mapper(
        componentModel = "spring"
)
public interface MethodConvertor {
    /**
     * 方法定义VO转换
     *
     * @param methodAnalysis 方法分析
     * @param classAnalysis  类解析
     * @param fileAnalysis   文件解析
     * @return
     */
    default MethodDefinitionVO convert2DefinitionVO(CodeMethodAnalysis methodAnalysis, CodeClassAnalysis classAnalysis, CodeFileAnalysis fileAnalysis) {
        if (methodAnalysis == null) {
            return new MethodDefinitionVO().setNavigatable(false)
                    .setMethodVid("method is null")
                    .setCannotNavigateReason("方法不存在");
        }
        if (Objects.equals(methodAnalysis.getSource(), "unknown")) {
            String fullMethod = StringUtils.isBlank(methodAnalysis.getClassName()) ? methodAnalysis.getMethodName() : methodAnalysis.getClassName() + "#" + methodAnalysis.getMethodName();
            return new MethodDefinitionVO().setNavigatable(false)
                    .setMethodVid(methodAnalysis.getMethodVid())
                    .setCannotNavigateReason("工程外部方法【" + fullMethod + "】不支持跳转");
        }
        if (classAnalysis == null || fileAnalysis == null) {
            return new MethodDefinitionVO().setNavigatable(false)
                    .setMethodVid(methodAnalysis.getMethodVid())
                    .setCannotNavigateReason("类或文件不存在");
        }

        return new MethodDefinitionVO()
                .setNavigatable(true)
                .setMethodVid(methodAnalysis.getMethodVid())
                .setShowName(classAnalysis.getClassName())
                .setFilePath(fileAnalysis.getFilePath())
                .setStartLine(methodAnalysis.getStartLine())
                .setEndLine(methodAnalysis.getEndLine());
    }

    default MethodDefinitionVO convert2InvokedVO(CodeFileAnalysis codeFileAnalysis, InvokeLinePO invokeLinePO, CodeMethodAnalysis methodAnalysis) {
        if (methodAnalysis == null) {
            return new MethodDefinitionVO().setNavigatable(false)
                    .setMethodVid("method is null")
                    .setCannotNavigateReason("方法不存在");
        }
        if (codeFileAnalysis == null) {
            return new MethodDefinitionVO().setNavigatable(false)
                    .setMethodVid(methodAnalysis.getMethodVid())
                    .setCannotNavigateReason("类或文件不存在");
        }
        return new MethodDefinitionVO()
                .setNavigatable(true)
                .setMethodVid(methodAnalysis.getMethodVid())
                .setFilePath(codeFileAnalysis.getFilePath())
                .setStartLine(invokeLinePO.getInvokeLine())
                .setEndLine(invokeLinePO.getInvokeLine());
    }

    @Mappings({
            @Mapping(target = "id", source = "methodAnalysis.methodVid"),
            @Mapping(target = "itemId", source = "methodAnalysis.itemId"),
            @Mapping(target = "moduleName", source = "methodAnalysis.moduleName"),
            @Mapping(target = "className", source = "methodAnalysis.className"),
            @Mapping(target = "innerClassName", source = "methodAnalysis.inClassName"),
            @Mapping(target = "methodName", source = "methodAnalysis.methodName"),
            @Mapping(target = "access", source = "methodAnalysis.access"),
            @Mapping(target = "startLine", source = "methodAnalysis.startLine"),
            @Mapping(target = "endLine", source = "methodAnalysis.endLine"),
            @Mapping(target = "source", source = "methodAnalysis.source"),
            @Mapping(target = "changeType", source = "methodAnalysis.changeType"),
            @Mapping(target = "fileVid", source = "classAnalysis.fileVid"),
            @Mapping(target = "filePath", source = "classAnalysis.classPath"),
    })
    MethodTopologyVO.TopologyNode convertNode(CodeMethodAnalysis methodAnalysis, CodeClassAnalysis classAnalysis);

    /**
     * 批量转换节点列表
     * 注意：此方法需要调用方提供 methodAnalysisList 和对应的 classAnalysisMap
     *
     * @param methodAnalysisList 方法分析列表
     * @param classAnalysisMap   类分析映射，key为className，value为CodeClassAnalysis
     * @return 转换后的节点列表
     */
    default List<MethodTopologyVO.TopologyNode> convertNodeList(List<CodeMethodAnalysis> methodAnalysisList,
                                                                Map<String, CodeClassAnalysis> classAnalysisMap) {
        if (CollectionUtils.isEmpty(methodAnalysisList)) {
            return Collections.emptyList();
        }
        return methodAnalysisList.stream()
                .map(methodAnalysis -> {
                    CodeClassAnalysis classAnalysis = classAnalysisMap.get(methodAnalysis.getClassName());
                    return convertNode(methodAnalysis, classAnalysis);
                })
                .collect(Collectors.toList());
    }

    @Mappings({
            @Mapping(target = "source", source = "methodInvokeMethod.source"),
            @Mapping(target = "target", source = "methodInvokeMethod.target"),
            @Mapping(target = "invokeLines", expression = "java(convertInvokeLines(methodInvokeMethod))")
    })
    MethodTopologyVO.TopologyEdge convertEdge(MethodInvokeMethod methodInvokeMethod);

    default String convertInvokeLines(MethodInvokeMethod methodInvokeMethod) {
        if (CollectionUtils.isEmpty(methodInvokeMethod.getInvokeParams())) {
            return StringUtils.EMPTY;
        }
        return methodInvokeMethod.getInvokeParams().stream()
                .map(InvokeLinePO::getInvokeLine)
                .map(String::valueOf)
                .collect(Collectors.joining(","));
    }

    List<MethodTopologyVO.TopologyEdge> convertEdgeList(List<MethodInvokeMethod> methodInvokeMethodList);
}
