package com.sankuai.deepcode.manage.server.jwt.service.impl;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.sankuai.deepcode.dao.domain.SysToken;
import com.sankuai.deepcode.dao.domain.SysUser;
import com.sankuai.deepcode.dao.domain.SysUserDetail;
import com.sankuai.deepcode.dao.service.SysTokenService;
import com.sankuai.deepcode.dao.service.SysUserDetailService;
import com.sankuai.deepcode.dao.service.SysUserService;
import com.sankuai.deepcode.manage.server.jwt.domain.LoginUser;
import com.sankuai.deepcode.manage.server.jwt.domain.ResponseResult;
import com.sankuai.deepcode.manage.server.jwt.service.LoginServcie;
import com.sankuai.deepcode.manage.server.jwt.utils.JwtUtil;
import com.sankuai.deepcode.manage.server.jwt.vo.github.AccessToken;
import com.sankuai.deepcode.manage.server.jwt.vo.github.AccessTokenRes;
import com.sankuai.deepcode.manage.server.jwt.vo.github.EmailInfo;
import com.sankuai.deepcode.manage.server.jwt.vo.github.UserRes;
import com.sankuai.deepcode.manage.server.jwt.vo.mtsso.MtAccessTokenRes;
import com.sankuai.deepcode.manage.server.jwt.vo.mtsso.MtAccessTokenUserInfoRes;
import com.sankuai.deepcode.manage.server.jwt.vo.mtsso.MtUserinfoParam;
import com.sankuai.deepcode.manage.server.proxy.GitHubApi;
import com.sankuai.deepcode.manage.server.proxy.MtSSOApi;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.util.*;

@Service
public class LoginServiceImpl implements LoginServcie {
    private static final String CLIENT_ID = "243f18cf28";
    private static String HOUYI_SSO_PROD_TOKEN = "e58daa2a1fae47e9a9d6e6b34bab43f8";


    @Autowired
    private AuthenticationManager authenticationManager;

    @Autowired
    SysTokenService sysTokenService;

    @Autowired
    SysUserDetailService sysUserDetailService;

    @Autowired
    SysUserService sysUserService;


    @Resource
    GitHubApi gitHubApi;
    @Resource
    MtSSOApi mtSSOApi;

    @Autowired
    private PasswordEncoder passwordEncoder;

    @Override
    public ResponseResult getInfo(Long userId) {
        //查询用户信息
//        SysUserExample exampleField = new SysUserExample();
//        SysUserExample.Criteria criteriaField = exampleField.createCriteria().andUserIdEqualTo(userId);
//        LambdaQueryWrapper<User> queryWrapper = new LambdaQueryWrapper<>();
//        queryWrapper.eq(User::getId, userId);
        SysUser user = sysUserService.getById(userId);
        //如果没有查询到用户就抛出异常
        if (Objects.isNull(user)) {
            throw new RuntimeException("用户名或者密码错误");
        }
        //userDetailMapper.selectOne(Wrappers.<UserDetail>lambdaQuery().eq(UserDetail::getUserName, userRes.getLogin()))

//        List<UserDetail> userDetails = userDetailMapper.selectList(Wrappers.<UserDetail>lambdaQuery().eq(UserDetail::getUserId, user.getId()));
        List<SysUserDetail> userDetails = sysUserDetailService.getDetailsByUserId(user.getId());

        List<String> list = new ArrayList<>(Arrays.asList("test", "admin"));


        return new ResponseResult(0, "查询成功", new LoginUser(user, list, userDetails));
    }

    @Override
    public ResponseResult bindUser(LoginUser loginUser) {
        // 查询 UserDetail
        if (loginUser == null || loginUser.getUserDetails() == null || loginUser.getUserDetails().isEmpty()) {
            return new ResponseResult(400, "用户不存在");
        }
        SysUserDetail userDetail = loginUser.getUserDetails().get(0);
        if (userDetail.getUserId() != null && userDetail.getUserId() != 0) {
            return new ResponseResult(400, "用户已绑定");
        }
        // 创建新 User
        SysUser newUser = new SysUser();
        newUser.setUserName(loginUser.getUser().getUserName());
        newUser.setNickName(loginUser.getUser().getNickName());
        loginUser.getUser().setPassword("deepCodeFakePassword");
        String encode = passwordEncoder.encode(loginUser.getUser().getPassword());
        newUser.setPassword(encode); // 默认密码，可根据需求设置
        newUser.setUserType("1");
        sysUserService.save(newUser);

        // 绑定 UserDetail 的 userId 到新创建的 User 的 id
        userDetail.setUserId(newUser.getId());
        userDetail.setUpdateTime(LocalDateTime.now());
//        UpdateWrapper<UserDetail> updateWrapper = new UpdateWrapper<>();
//        updateWrapper.set("user_Id", newUser.getId()).eq("id", userDetail.getId());
//        userDetailMapper.update(updateWrapper);
        sysUserDetailService.updateById(userDetail);

        Map<String, String> map = loginByPw(loginUser.getUser().getUserName(), loginUser.getUser().getPassword());
        return new ResponseResult(0, "绑定成功", map);
    }

    @Override
    public ResponseResult login(SysUser user) {

//        redisCache.setCacheObject("login:"+userid,loginUser);
        return new ResponseResult(0, "登录成功", loginByPw(user.getUserName(), user.getPassword()));
    }


    public Map<String, String> loginByPw(String userName, String passwd) {
        //AuthenticationManager authenticate进行用户认证
        UsernamePasswordAuthenticationToken authenticationToken = new UsernamePasswordAuthenticationToken(userName, passwd);
        Authentication authenticate = authenticationManager.authenticate(authenticationToken);
        //如果认证没通过，给出对应的提示
        if (Objects.isNull(authenticate)) {
            throw new RuntimeException("登录失败");
        }
        //如果认证通过了，使用userid生成一个jwt jwt存入ResponseResult返回
        LoginUser loginUser = (LoginUser) authenticate.getPrincipal();
        Long userid = loginUser.getUser().getId();
        String jwt = JwtUtil.createJWT(new Gson().toJson(loginUser));
        Map<String, String> map = new HashMap<>();
        map.put("token", jwt);

//    public SysToken(Object o, String jwt, Long userid, String json, int i, Date date, Date date1) {
//        this.userId = userid;
//        this.token=jwt;
//        this.tokenInfo = json;
//        this.valid = i;
//        this.createTime = date;
//        this.updateTime = date1;
//    }
        //把完整的用户信息存入redis  userid作为key
        SysToken tokenInfo = new SysToken();
        tokenInfo.setUserId(userid);
        tokenInfo.setToken(jwt);
        Gson gson = new GsonBuilder().setDateFormat("yyyy HH:mm:ss").create();
        tokenInfo.setTokenInfo(gson.toJson(loginUser));
        tokenInfo.setValid(1);
//         jwt, userid, new Gson().toJson(loginUser), 1, new Date(), new Date()

        sysTokenService.save(tokenInfo);
        return map;
    }

    @Override
    public ResponseResult loginByGithub(String code) {
        //获取token
        AccessToken accessToken = new AccessToken();
        accessToken.setCode(code);
        AccessTokenRes accessTokenRes = null;

        try {
            String accessTokenResStr = gitHubApi.getAccessToken(accessToken);
            accessTokenRes = new Gson().fromJson(accessTokenResStr, AccessTokenRes.class);

        } catch (Exception e) {
            return new ResponseResult(500, "获取token失败", null);
        }
        String accessTokenStr = "Bearer " + accessTokenRes.getAccess_token();
        UserRes userRes = gitHubApi.getUser(accessTokenStr);
        List<EmailInfo> emailRes = gitHubApi.getEmails(accessTokenStr);

        List<SysUserDetail> userDetails = sysUserDetailService.getDetailsByUserName(userRes.getLogin());
        SysUserDetail userDetail = null;
        if (!CollectionUtils.isEmpty(userDetails)) {
            userDetail = userDetails.get(0);

        }
        SysUser user = null;
        //更新accessToken
        if (userDetail != null) {
            userDetail.setOutAuth(accessTokenStr);
            sysUserDetailService.updateById(userDetail);
            if (userDetail.getUserId() != 0) {
                user = sysUserService.getById(userDetail.getUserId());
                LoginUser loginUser = new LoginUser(user, null, Collections.singletonList(userDetail));
                String jwt = JwtUtil.createJWT(new Gson().toJson(loginUser));
                loginUser.setToken(jwt);
                updateToken(loginUser.getUser().getId(), jwt);
                return new ResponseResult(0, "查询成功", loginUser);
            }
        } else {
            //获取
            userDetail = new SysUserDetail();
            userDetail.setUserName(userRes.getLogin());
            userDetail.setNickName(userRes.getLogin());
            userDetail.setAuthInfo("");
            userDetail.setCode(code);
            userDetail.setOutAuth(accessTokenStr);
            userDetail.setUserId(0L);
            userDetail.setType(0);
            userDetail.setEmail(emailRes.get(0).getEmail());
            userDetail.setValid(1);
            sysUserDetailService.save(userDetail);
            //进入关联页面
        }
        LoginUser loginUser = new LoginUser(user, null, Collections.singletonList(userDetail));
        return new ResponseResult(200, "需要新建用户", loginUser);

    }

    public static String[] getSignedHeaders(String method, String uri, String clienId, String secret) {//method为当前方法的method（"POST" | "GET"）
        if (method == null || uri == null) {
            return new String[]{};
        }
        String date = getAuthDate(new Date());
        method = method.toUpperCase();
        String authorization = getAuthorization(uri, method, date, clienId, secret);
        return new String[]{authorization, date};
    }

    public static String getAuthDate(Date date) {
        DateFormat df = new SimpleDateFormat("EEE, dd MMM yyyy HH:mm:ss z", Locale.ENGLISH);
        df.setTimeZone(TimeZone.getTimeZone("GMT"));
        return df.format(date);
    }

    public static String getAuthorization(String uri, String method, String date, String clientId, String secret) {
        String stringToSign = method + " " + uri + "\n" + date;
        String signature = getSignature(stringToSign, secret);
        String authorization = "MWS" + " " + clientId + ":" + signature;
        return authorization;
    }

    public static String getSignature(String data, String secret) {
        String result;
        try {
            SecretKeySpec signingKey = new SecretKeySpec(secret.getBytes(), "HmacSHA1");
            Mac mac = Mac.getInstance("HmacSHA1");
            mac.init(signingKey);
            byte[] rawHmac = mac.doFinal(data.getBytes());
            result = Base64.encodeBase64String(rawHmac);
        } catch (Exception e) {
            throw new RuntimeException("Failed to generate HMAC : " + e.getMessage());
        }
        return result;
    }

    @Override
    public ResponseResult loginByMt(String code) {
        //获取token
        AccessToken accessToken = new AccessToken();
        accessToken.setCode(code);
        MtAccessTokenRes accessTokenRes = null;
        long t = new Date().getTime() / 1000;
        String[] headers = getSignedHeaders("GET", "/sson/oauth2.0/access-token", CLIENT_ID, HOUYI_SSO_PROD_TOKEN);
        try {
            String accessTokenResStr = mtSSOApi.getAccessToken(code, headers[0], headers[1]);
            accessTokenRes = new Gson().fromJson(accessTokenResStr, MtAccessTokenRes.class);

        } catch (Exception e) {
            return new ResponseResult(500, "获取token失败", null);
        }
        String accessTokenStr = accessTokenRes.getData().getAccessToken();
        headers = getSignedHeaders("POST", "/open/api/session/userinfo", CLIENT_ID, HOUYI_SSO_PROD_TOKEN);
        MtUserinfoParam mtUserinfoParam = new MtUserinfoParam();
        mtUserinfoParam.setAccessToken(accessTokenStr);
        String userResStr = mtSSOApi.getUserinfo(mtUserinfoParam, headers[0], headers[1]);
        MtAccessTokenUserInfoRes userRes = new Gson().fromJson(userResStr, MtAccessTokenUserInfoRes.class);
        String email = userRes.getData().getEmail();

        List<SysUserDetail> userDetails = sysUserDetailService.getDetailsByUserName(userRes.getData().getLoginName());
        SysUserDetail userDetail = null;
        if (!CollectionUtils.isEmpty(userDetails)) {
            userDetail = userDetails.get(0);

        }

//        UserDetail userDetail = userDetailMapper.selectOne(Wrappers.<UserDetail>lambdaQuery().eq(UserDetail::getUserName, userRes.getData().getLoginName()).eq(UserDetail::getType, 1));
        SysUser user = null;
        //更新accessToken
        if (userDetail != null) {
            userDetail.setOutAuth(accessTokenStr);
            sysUserDetailService.updateById(userDetail);
            if (userDetail.getUserId() != 0) {
                user = sysUserService.getById(userDetail.getUserId());
                LoginUser loginUser = new LoginUser(user, null, Collections.singletonList(userDetail));
                String jwt = JwtUtil.createJWT(new Gson().toJson(loginUser));
                loginUser.setToken(jwt);
                updateToken(loginUser.getUser().getId(), jwt);
                return new ResponseResult(0, "查询成功", loginUser);
            }
        } else {
            //获取
            userDetail = new SysUserDetail();
            userDetail.setUserName(userRes.getData().getLoginName());
            userDetail.setNickName(userRes.getData().getName());
            userDetail.setAuthInfo("");
            userDetail.setCode(code);
            userDetail.setOutAuth(accessTokenStr);
            userDetail.setUserId(0L);
            userDetail.setType(1);
            userDetail.setEmail(email);
            userDetail.setValid(1);
            sysUserDetailService.save(userDetail);
            //进入关联页面
        }
        LoginUser loginUser = new LoginUser(user, null, Collections.singletonList(userDetail));
        return new ResponseResult(200, "需要新建用户", loginUser);

    }

    //
    public boolean updateToken(Long userId, String token) {
        List<SysToken> tokenOris = sysTokenService.getTokenByUserId(userId);
        if (CollectionUtils.isNotEmpty(tokenOris)) {
            SysToken tokenOri = tokenOris.get(0);
            tokenOri.setToken(token);
            sysTokenService.updateById(tokenOri);
        }

        return true;
    }

    @Override
    public ResponseResult logout() {
        //获取SecurityContextHolder中的用户id
        UsernamePasswordAuthenticationToken authentication = (UsernamePasswordAuthenticationToken) SecurityContextHolder.getContext().getAuthentication();
        LoginUser loginUser = (LoginUser) authentication.getPrincipal();
        Long userid = loginUser.getUser().getId();

        List<SysToken> tokenOris = sysTokenService.getTokenByUserId(userid);
        SysToken tokenOri = tokenOris.get(0);
        tokenOri.setValid(0);
        //删除redis中的值
        sysTokenService.updateById(tokenOri);
        //redisCache.deleteObject("login:"+userid);
        return new ResponseResult(0, "注销成功");
    }

//
//    public UserDetails loadUserByUserId(Long userId) throws UsernameNotFoundException {
//
//        //查询用户信息
//        LambdaQueryWrapper<User> queryWrapper = new LambdaQueryWrapper<>();
//        queryWrapper.eq(User::getId,userId);
//        User user = userMapper.selectOne(queryWrapper);
//        //如果没有查询到用户就抛出异常
//        if(Objects.isNull(user)){
//            throw new RuntimeException("用户名或者密码错误");
//        }
//        //userDetailMapper.selectOne(Wrappers.<UserDetail>lambdaQuery().eq(UserDetail::getUserName, userRes.getLogin()))
//        List<UserDetail> userDetails = userDetailMapper.selectList(Wrappers.<UserDetail>lambdaQuery().eq(UserDetail::getUserId, user.getId())) ;
//        List<String> list = new ArrayList<>(Arrays.asList("test","admin","anonymous"));
//        //List<String> list = menuMapper.selectPermsByUserId(user.getId());
//        //把数据封装成UserDetails返回
//        return new LoginUser(user,list,userDetails);
//    }
}
