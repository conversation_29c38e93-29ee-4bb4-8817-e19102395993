package com.sankuai.deepcode.manage.server.controller.chat.res;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * Package: com.sankuai.deepcode.manage.server.controller.chat.res
 * Description:
 *
 * <AUTHOR>
 * @since 2025/3/17 10:24
 */
@Setter
@Getter
@ToString
@Accessors(chain = true)
public class ChatMsgVO {
    @Schema(description = "会话信息")
    ChatVO chat;
    @Schema(description = "消息列表")
    private List<MsgVO> messageList;
}
