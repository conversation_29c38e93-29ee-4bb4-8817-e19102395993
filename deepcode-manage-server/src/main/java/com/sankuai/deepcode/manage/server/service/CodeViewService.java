package com.sankuai.deepcode.manage.server.service;

import com.alibaba.fastjson.JSON;
import com.google.common.reflect.TypeToken;
import com.sankuai.deepcode.ast.common.enums.ChangeTypeEnum;
import com.sankuai.deepcode.ast.common.enums.DiffTypeEnum;
import com.sankuai.deepcode.ast.common.model.base.CodeView;
import com.sankuai.deepcode.ast.common.model.java.ClassNode;
import com.sankuai.deepcode.ast.common.model.java.FieldNode;
import com.sankuai.deepcode.ast.common.model.java.InvokeParam;
import com.sankuai.deepcode.ast.common.model.java.MethodNode;
import com.sankuai.deepcode.dao.domain.*;
import com.sankuai.deepcode.dao.service.*;
import com.sankuai.deepcode.manage.server.enums.CodeReviewNameEnum;
import com.sankuai.deepcode.manage.server.model.codeReview.*;
import com.sankuai.deepcode.manage.server.model.file.AllFilesRes;
import com.sankuai.deepcode.manage.server.model.file.FileData;
import com.sankuai.deepcode.manage.server.util.ConvertNode;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.lang.reflect.Type;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Service
public class CodeViewService {

    private static final Logger LOGGER = LoggerFactory.getLogger(CodeViewService.class);

    @Autowired
    private CodeViewAnalysisService codeViewAnalysisService;
    @Autowired
    private CodeClassAnalysisService codeClassAnalysisService;
    @Autowired
    private CodeFileAnalysisService codeFileAnalysisService;
    @Autowired
    private CodeMethodAnalysisService codeMethodAnalysisService;
    @Autowired
    private MethodInvokeMethodService methodInvokeMethodService;
    @Autowired
    private MethodQuoteFieldService methodQuoteFieldService;
    @Autowired
    private CodeFieldAnalysisService codeFieldAnalysisService;

    public CodeReviewRes initCodeView(long itemId, String vid) throws IOException {
        CodeReviewRes res = new CodeReviewRes();
        List<CodeReviewData> codeReviewDataList = new ArrayList<>();
        List<FileListData> changeMethodInfos = new ArrayList<>();
        List<FileListData> changeFieldInfos = new ArrayList<>();
        List<CodeViewAnalysis> codeViewAnalyses = codeViewAnalysisService.getBySourceVid(itemId, vid);
        List<CodeView> codeViews = covertCodeView(codeViewAnalyses);

        int devAdd = 0;
        int devDel = 0;
        int integrationAdd = 0;
        int integrationDel = 0;
        List<FileChange> fileChanges = new ArrayList<>();
        Map<Integer, Integer> sourceMapAndId = new HashMap<>();
        if (CollectionUtils.isNotEmpty(codeViews)) {
            int oldLine = 1;
            int line = 1;
            Map<Integer, CodeReviewData> codeReviewDataMap = new HashMap<>();
            boolean checkDel = false;
            int addCheckDelLine = 0;
            int lineId = 1;
            for (CodeView codeView : codeViews) {
                CodeReviewData codeReviewData = new CodeReviewData();
                if (codeView.getType() == DiffTypeEnum.SAM.getCode()) {
                    codeReviewData.setDiffType(DiffTypeEnum.SAM.getMsg());
                    codeReviewData.setDestination(oldLine++);
                    codeReviewData.setSource(line++);
                    codeReviewDataMap.put(codeReviewData.getSource(), codeReviewData);
                } else if (codeView.getType() == DiffTypeEnum.DEL.getCode()) {
                    addFileChanges(fileChanges, codeView.getId(), oldLine);
                    codeReviewData.setDiffType(DiffTypeEnum.DEL.getMsg());
                    codeReviewData.setDestination(oldLine++);
                    devDel++;
                } else if (codeView.getType() == DiffTypeEnum.CHECKDEL.getCode()) {
                    addFileChanges(fileChanges, codeView.getId(), oldLine);
                    codeReviewData.setDiffType(DiffTypeEnum.CHECKDEL.getMsg());
                    codeReviewData.setDestination(oldLine++);
                    integrationDel++;
                    checkDel = true;
                    addCheckDelLine++;
                } else if (codeView.getType() == DiffTypeEnum.ADD.getCode()) {
                    addFileChanges(fileChanges, codeView.getId(), line);
                    codeReviewData.setDiffType(DiffTypeEnum.ADD.getMsg());
                    codeReviewData.setSource(line++);
                    codeReviewDataMap.put(codeReviewData.getSource(), codeReviewData);
                    devAdd++;
                } else if (codeView.getType() == DiffTypeEnum.CHECKADD.getCode()) {
                    addFileChanges(fileChanges, codeView.getId(), line);
                    codeReviewData.setDiffType(DiffTypeEnum.CHECKADD.getMsg());
                    codeReviewData.setSource(line++);
                    codeReviewDataMap.put(codeReviewData.getSource(), codeReviewData);
                    integrationAdd++;
                } else if (codeView.getType() == DiffTypeEnum.CHECK.getCode()) {
                    addFileChanges(fileChanges, codeView.getId(), oldLine);
                    codeReviewData.setDiffType(DiffTypeEnum.CHECK.getMsg());
                    codeReviewData.setSource(line);
                    codeReviewData.setDestination(oldLine);
                    devAdd++;
                }
                if (checkDel) {
                    if (codeView.getType() != DiffTypeEnum.CHECKDEL.getCode()) {
                        CodeReviewData checkDelData = new CodeReviewData();
                        checkDelData.setId(lineId);
                        lineId++;
                        checkDelData.setDiffType(DiffTypeEnum.CHECKDEL.getMsg());
                        List<LineView> lineViews = new ArrayList<>();
                        LineView lineView = new LineView();
                        lineView.setType("line");
                        lineView.setView("      此处有" + addCheckDelLine + "行开发分支存在，当前部署分支不存在的代码，点击左侧图标查看");
                        lineViews.add(lineView);
                        checkDelData.setLineViews(lineViews);
                        codeReviewDataList.add(checkDelData);
                        addCheckDelLine = 0;
                        checkDel = false;

                        lineViews = new ArrayList<>();
                        lineView = new LineView();
                        lineView.setType("line");
                        lineView.setView(codeView.getView());
                        lineViews.add(lineView);
                        codeReviewData.setLineViews(lineViews);
                        codeReviewData.setId(lineId);
                        sourceMapAndId.put(codeReviewData.getSource(), codeReviewData.getId());
                        lineId++;
                        codeReviewDataList.add(codeReviewData);
                    }
                } else {
                    List<LineView> lineViews = new ArrayList<>();
                    LineView lineView = new LineView();
                    lineView.setType("line");
                    lineView.setView(codeView.getView());
                    lineViews.add(lineView);
                    codeReviewData.setLineViews(lineViews);
                    codeReviewData.setId(lineId);
                    sourceMapAndId.put(codeReviewData.getSource(), codeReviewData.getId());
                    lineId++;
                    codeReviewDataList.add(codeReviewData);
                }
            }

            Map<Integer, List<CodeReviewName>> integerListMap = new HashMap<>();
            Map<String, FileListData> fileListDataMap = new HashMap<>();
            CodeClassAnalysis codeClassAnalysis = codeClassAnalysisService.getByClassVid(itemId, vid);
            if (codeClassAnalysis != null) {
                Set<String> methodVids = new HashSet<>();
                Map<Integer, MethodNode> integerMethodInfoMap = new HashMap<>();
                List<CodeMethodAnalysis> codeMethodAnalyses = codeMethodAnalysisService.getByItemAndClassName(itemId, codeClassAnalysis.getClassName());
                List<MethodNode> methodNodes = new ArrayList<>();
                for (CodeMethodAnalysis codeMethodAnalysis : codeMethodAnalyses) {
                    MethodNode methodNode = ConvertNode.convertMethodNode(codeMethodAnalysis);
                    methodNodes.add(methodNode);
                    methodVids.add(methodNode.getVid());
                }
                if (CollectionUtils.isNotEmpty(methodNodes)) {
                    for (MethodNode methodNode : methodNodes) {
                        if (StringUtils.isNumeric(methodNode.getMethodName())) {
                            continue;
                        }
                        if (methodNode.getChangeType() != ChangeTypeEnum.DEFAULT.getCode()) {
                            FileListData fileListData = new FileListData();
                            fileListData.setVid(methodNode.getVid());
                            fileListData.setClassName(methodNode.getClassName());
                            fileListData.setName(methodNode.getMethodName());
                            fileListData.setStartLine(methodNode.getStartLine());
                            fileListData.setChangeType(methodNode.getChangeType());
                            fileListData.setType(CodeReviewNameEnum.METHOD.getCode());
                            fileListData.setName(methodNode.getMethodName());
                            changeMethodInfos.add(fileListData);
                            fileListDataMap.put(fileListData.getVid(), fileListData);
                            Integer id = sourceMapAndId.get(fileListData.getStartLine());
                            if (null != id) {
                                fileListDataMap.get(methodNode.getVid()).setId(id);
                            }
                        }
                        integerMethodInfoMap.put(methodNode.getStartLine(), methodNode);
                    }
                }
                if (!integerMethodInfoMap.isEmpty()) {
                    for (Map.Entry<Integer, MethodNode> entry : integerMethodInfoMap.entrySet()) {
                        CodeReviewName codeReviewName = new CodeReviewName();
                        codeReviewName.setVid(entry.getValue().getVid());
                        codeReviewName.setName(entry.getValue().getMethodName());
                        codeReviewName.setType(CodeReviewNameEnum.METHOD.getCode());
                        codeReviewName.setChangeType(entry.getValue().getChangeType());
                        codeReviewName.setMethodNode(entry.getValue());
                        if (null == integerListMap.get(entry.getKey())) {
                            List<CodeReviewName> tmp = new ArrayList<>();
                            tmp.add(codeReviewName);
                            integerListMap.put(entry.getKey(), tmp);
                        } else {
                            integerListMap.get(entry.getKey()).add(codeReviewName);
                        }
                    }
                }

                Map<Integer, List<MethodNode>> invokeMethodMap = new HashMap<>();
                Map<Integer, Set<String>> invokeMethodVidMap = new HashMap<>();
                if (CollectionUtils.isNotEmpty(methodVids)) {
                    List<MethodInvokeMethod> invokeVids = methodInvokeMethodService.getByTargets(itemId, methodVids);
                    Set<String> vids = new HashSet<>();
                    for (MethodInvokeMethod invokeMethod : invokeVids) {
                        vids.add(invokeMethod.getTarget());
                        Type type = new TypeToken<List<InvokeParam>>() {
                        }.getType();
                        List<InvokeParam> invokeParams = JSON.parseObject(invokeMethod.getInvokeParams().toString(), type);
                        for (InvokeParam invokeParam : invokeParams) {
                            if (null == invokeMethodVidMap.get(invokeParam.getInvokeLine())) {
                                Set<String> tmp = new HashSet<>();
                                tmp.add(invokeMethod.getTarget());
                                invokeMethodVidMap.put(invokeParam.getInvokeLine(), tmp);
                            } else {
                                invokeMethodVidMap.get(invokeParam.getInvokeLine()).add(invokeMethod.getTarget());
                            }
                        }

                    }
                    List<CodeMethodAnalysis> codeMethodAnalysesList = new ArrayList<>();
                    if (CollectionUtils.isNotEmpty(vids)) {
                        codeMethodAnalysesList = codeMethodAnalysisService.getByVids(itemId, vids);
                    }
                    Map<String, MethodNode> methodNodeMap = new HashMap<>();
                    for (CodeMethodAnalysis codeMethodAnalysis : codeMethodAnalysesList) {
                        MethodNode methodNode = ConvertNode.convertMethodNode(codeMethodAnalysis);
                        methodNodeMap.put(codeMethodAnalysis.getMethodVid(), methodNode);
                    }
                    for (Map.Entry<Integer, Set<String>> entry : invokeMethodVidMap.entrySet()) {
                        if (null == invokeMethodMap.get(entry.getKey())) {
                            List<MethodNode> tmp = new ArrayList<>();
                            for (String methodVid : entry.getValue()) {
                                MethodNode methodNode = methodNodeMap.get(methodVid);
                                if (null != methodNode) {
                                    tmp.add(methodNode);
                                }
                            }
                            if (CollectionUtils.isNotEmpty(tmp)) {
                                invokeMethodMap.put(entry.getKey(), tmp);
                            }
                        } else {
                            for (String methodVid : entry.getValue()) {
                                MethodNode methodNode = methodNodeMap.get(methodVid);
                                if (null != methodNode) {
                                    invokeMethodMap.get(entry.getKey()).add(methodNode);
                                }
                            }
                        }
                    }
                }

                if (!invokeMethodMap.isEmpty()) {
                    for (Map.Entry<Integer, List<MethodNode>> entry : invokeMethodMap.entrySet()) {
                        List<CodeReviewName> tmp = new ArrayList<>();
                        for (MethodNode methodNode : entry.getValue()) {
                            CodeReviewName codeReviewName = new CodeReviewName();
                            codeReviewName.setVid(methodNode.getVid());
                            codeReviewName.setName(methodNode.getMethodName());
                            codeReviewName.setType(CodeReviewNameEnum.INVOKEMETHOD.getCode());
                            codeReviewName.setChangeType(methodNode.getChangeType());
                            codeReviewName.setMethodNode(methodNode);
                            tmp.add(codeReviewName);
                            if (null != fileListDataMap.get(methodNode.getVid())) {
                                fileListDataMap.get(methodNode.getVid()).getLines().add(entry.getKey());
                            }
                        }
                        if (null == integerListMap.get(entry.getKey())) {
                            integerListMap.put(entry.getKey(), tmp);
                        } else {
                            integerListMap.get(entry.getKey()).addAll(tmp);
                        }
                    }
                }

                Map<Integer, FieldNode> integerFieldInfoMap = new HashMap<>();
                List<CodeFieldAnalysis> codeFieldAnalyses = codeFieldAnalysisService.getByClassName(itemId, codeClassAnalysis.getClassName());
                List<FieldNode> fieldNodes = new ArrayList<>();
                for (CodeFieldAnalysis codeFieldAnalysis : codeFieldAnalyses) {
                    FieldNode fieldNode = ConvertNode.convertFieldNode(codeFieldAnalysis);
                    fieldNodes.add(fieldNode);
                }
                if (CollectionUtils.isNotEmpty(fieldNodes)) {
                    for (FieldNode fieldNode : fieldNodes) {
                        if (fieldNode.getChangeType() != ChangeTypeEnum.DEFAULT.getCode()) {
                            FileListData fileListData = new FileListData();
                            fileListData.setVid(fieldNode.getVid());
                            fileListData.setClassName(fieldNode.getClassName());
                            fileListData.setName(fieldNode.getFieldName());
                            fileListData.setStartLine(fieldNode.getStartLine());
                            fileListData.setChangeType(fieldNode.getChangeType());
                            fileListData.setType(CodeReviewNameEnum.FIELD.getCode());
                            changeFieldInfos.add(fileListData);
                            fileListDataMap.put(fileListData.getVid(), fileListData);
                            Integer id = sourceMapAndId.get(fileListData.getStartLine());
                            if (null != id) {
                                fileListDataMap.get(fileListData.getVid()).setId(id);
                            }
                        }
                        integerFieldInfoMap.put(fieldNode.getStartLine(), fieldNode);
                    }
                }
                if (!integerFieldInfoMap.isEmpty()) {
                    for (Map.Entry<Integer, FieldNode> entry : integerFieldInfoMap.entrySet()) {
                        CodeReviewName codeReviewName = new CodeReviewName();
                        codeReviewName.setVid(entry.getValue().getVid());
                        codeReviewName.setName(entry.getValue().getFieldName());
                        codeReviewName.setType(CodeReviewNameEnum.FIELD.getCode());
                        codeReviewName.setChangeType(entry.getValue().getChangeType());
                        codeReviewName.setFieldNode(entry.getValue());
                        if (null == integerListMap.get(entry.getKey())) {
                            List<CodeReviewName> tmp = new ArrayList<>();
                            tmp.add(codeReviewName);
                            integerListMap.put(entry.getKey(), tmp);
                        } else {
                            integerListMap.get(entry.getKey()).add(codeReviewName);
                        }
                    }
                }

                Map<Integer, List<FieldNode>> quoteFieldMap = new HashMap<>();
                Map<Integer, Set<String>> quoteFieldVidMap = new HashMap<>();
                if (CollectionUtils.isNotEmpty(methodVids)) {
                    List<MethodQuoteField> quoteVids = methodQuoteFieldService.getByTargets(itemId, methodVids);
                    Set<String> vids = new HashSet<>();
                    for (MethodQuoteField quoteField : quoteVids) {
                        vids.add(quoteField.getTarget());
                        Type type = new TypeToken<Set<Integer>>() {
                        }.getType();
                        Set<Integer> quoteLines = JSON.parseObject(quoteField.getQuoteLines().toString(), type);
                        for (Integer integer : quoteLines) {
                            if (null == quoteFieldMap.get(integer)) {
                                Set<String> tmp = new HashSet<>();
                                tmp.add(quoteField.getTarget());
                                quoteFieldVidMap.put(integer, tmp);
                            } else {
                                quoteFieldVidMap.get(integer).add(quoteField.getTarget());
                            }
                        }
                    }
                    List<CodeFieldAnalysis> codeFieldAnalysisList = new ArrayList<>();
                    if (CollectionUtils.isNotEmpty(vids)) {
                        codeFieldAnalysisList = codeFieldAnalysisService.getByVids(itemId, vids);
                    }
                    Map<String, FieldNode> fieldNodeMap = new HashMap<>();
                    for (CodeFieldAnalysis codeFieldAnalysis : codeFieldAnalysisList) {
                        FieldNode fieldNode = ConvertNode.convertFieldNode(codeFieldAnalysis);
                        fieldNodeMap.put(fieldNode.getVid(), fieldNode);
                    }
                    for (Map.Entry<Integer, Set<String>> entry : quoteFieldVidMap.entrySet()) {
                        if (null == quoteFieldMap.get(entry.getKey())) {
                            List<FieldNode> tmp = new ArrayList<>();
                            for (String fieldVid : entry.getValue()) {
                                FieldNode fieldNode = fieldNodeMap.get(fieldVid);
                                if (null != fieldNode) {
                                    tmp.add(fieldNode);
                                }
                            }
                            if (CollectionUtils.isNotEmpty(tmp)) {
                                quoteFieldMap.put(entry.getKey(), tmp);
                            }
                        } else {
                            for (String fieldVid : entry.getValue()) {
                                FieldNode fieldNode = fieldNodeMap.get(fieldVid);
                                if (null != fieldNode) {
                                    quoteFieldMap.get(entry.getKey()).add(fieldNode);
                                }
                            }
                        }
                    }
                }

                if (!quoteFieldMap.isEmpty()) {
                    for (Map.Entry<Integer, List<FieldNode>> entry : quoteFieldMap.entrySet()) {
                        List<CodeReviewName> tmp = new ArrayList<>();
                        for (FieldNode fieldNode : entry.getValue()) {
                            CodeReviewName codeReviewName = new CodeReviewName();
                            codeReviewName.setVid(fieldNode.getVid());
                            codeReviewName.setName(fieldNode.getFieldName());
                            codeReviewName.setType(CodeReviewNameEnum.INVOKEFIELD.getCode());
                            codeReviewName.setChangeType(fieldNode.getChangeType());
                            codeReviewName.setFieldNode(fieldNode);
                            tmp.add(codeReviewName);
                            if (null != fileListDataMap.get(fieldNode.getVid())) {
                                fileListDataMap.get(fieldNode.getVid()).getLines().add(entry.getKey());
                            }
                        }
                        if (null == integerListMap.get(entry.getKey())) {
                            integerListMap.put(entry.getKey(), tmp);
                        } else {
                            integerListMap.get(entry.getKey()).addAll(tmp);
                        }
                    }
                }

                if (!integerListMap.isEmpty()) {
                    for (Map.Entry<Integer, List<CodeReviewName>> entry : integerListMap.entrySet()) {
                        CodeReviewData codeReviewData = codeReviewDataMap.get(entry.getKey());
                        if (null != codeReviewData) {
                            initLineViews(codeReviewData, entry.getValue());
                        }
                    }
                }
            }
        }
        res.setDevAdd(devAdd);
        res.setDevDel(devDel);
        res.setIntegrationAdd(integrationAdd);
        res.setIntegrationDel(integrationDel);
        res.setCodeReviewDataList(codeReviewDataList);
        Collections.sort(changeMethodInfos);
        res.setChangeMethodInfos(changeMethodInfos);
        Collections.sort(changeFieldInfos);
        res.setChangeFieldInfos(changeFieldInfos);
        croppFileChanges(fileChanges);
        res.setFileChanges(fileChanges);
        return res;
    }


    public void initLineViews(CodeReviewData codeReviewData, List<CodeReviewName> codeReviewNames) {
        List<LineView> lineViews = codeReviewData.getLineViews();
        for (int i = 0; i < lineViews.size(); i++) {
            if (lineViews.get(i).getType().equals(CodeReviewNameEnum.LINE.getCode())) {
                for (CodeReviewName codeReviewName : codeReviewNames) {
                    Pattern pattern = null;
                    if (codeReviewName.getType().equals(CodeReviewNameEnum.METHOD.getCode())) {
                        pattern = Pattern.compile(codeReviewName.getName() + "[\\s]*\\(");
                    } else if (codeReviewName.getType().equals(CodeReviewNameEnum.INVOKEMETHOD.getCode())) {
                        pattern = Pattern.compile(codeReviewName.getName() + "[\\s]*\\(");
                    } else if (codeReviewName.getType().equals(CodeReviewNameEnum.FIELD.getCode())) {
                        pattern = Pattern.compile(codeReviewName.getName() + "[\\s]*(=|;)");
                    } else if (codeReviewName.getType().equals(CodeReviewNameEnum.INVOKEFIELD.getCode())) {
                        pattern = Pattern.compile(codeReviewName.getName() + "[\\s]*(\\.|,|\\))");
                    }
                    Matcher matcher = pattern.matcher(lineViews.get(i).getView());
                    List<MethodMatcher> methodMatchers = new ArrayList<>();
                    if (matcher.find()) {
                        MethodMatcher methodMatcher = new MethodMatcher();
                        methodMatcher.setStart(matcher.start());
                        methodMatcher.setEnd(matcher.end() - 1);
                        methodMatcher.setGroup(matcher.group().substring(0, matcher.group().length() - 1));
                        if (codeReviewName.getType().equals(CodeReviewNameEnum.METHOD.getCode()) || codeReviewName.getType().equals(CodeReviewNameEnum.INVOKEMETHOD.getCode())) {
                            methodMatcher.setMethodNode(codeReviewName.getMethodNode());
                        } else {
                            methodMatcher.setFieldNode(codeReviewName.getFieldNode());
                        }
                        methodMatchers.add(methodMatcher);
                    }
                    if (CollectionUtils.isNotEmpty(methodMatchers)) {
                        List<LineView> tmp = initMethodMatcher(lineViews.get(i).getView(), codeReviewName.getType(), methodMatchers);
                        if (CollectionUtils.isNotEmpty(tmp)) {
                            for (int j = 0; j < tmp.size(); j++) {
                                if (j == 0) {
                                    lineViews.set(i + j, tmp.get(j));
                                } else {
                                    lineViews.add(i + j, tmp.get(j));
                                }
                            }
                            i = 0;
                            continue;
                        }
                    }
                }
            }
        }
    }

    public List<LineView> initMethodMatcher(String codeView, String type, List<MethodMatcher> methodMatchers) {
        Collections.sort(methodMatchers);
        String lineEnd = null;
        List<LineView> lineViews = new ArrayList<>();
        int start = 0;
        for (MethodMatcher matcher : methodMatchers) {
            LineView lineStart = new LineView();
            lineStart.setType("line");
            lineStart.setView(codeView.substring(start, matcher.getStart()));
            lineViews.add(lineStart);

            LineView methodView = new LineView();
            methodView.setType(type);
            methodView.setView(codeView.substring(matcher.getStart(), matcher.getEnd()));
            methodView.setMethodNode(matcher.getMethodNode());
            methodView.setFieldNode(matcher.getFieldNode());
            lineViews.add(methodView);
            lineEnd = codeView.substring(matcher.getEnd());
            start = matcher.getEnd();
        }
        if (StringUtils.isNotEmpty(lineEnd)) {
            LineView lineView = new LineView();
            lineView.setType("line");
            lineView.setView(lineEnd);
            lineViews.add(lineView);
        }
        return lineViews;
    }


    public List<MethodNode> getLinkMethodInfoByVid(String lineViewType, long itemId, String vid) {
        List<MethodNode> invokedMethodInfos = new ArrayList<>();
        if (lineViewType.equals(CodeReviewNameEnum.METHOD.getCode())) {
            List<MethodInvokeMethod> methodInvokeMethods = methodInvokeMethodService.getByTarget(itemId, vid);
            for (MethodInvokeMethod invokeMethod : methodInvokeMethods) {
                CodeMethodAnalysis codeMethodAnalysis = codeMethodAnalysisService.getByMethodVid(itemId, invokeMethod.getSource());
                MethodNode methodNode = ConvertNode.convertMethodNode(codeMethodAnalysis);
                invokedMethodInfos.add(methodNode);
            }
        }
        if (lineViewType.equals(CodeReviewNameEnum.FIELD.getCode())) {
//            invokedMethodInfos = searchCodeLinksData.getMethodInfosByFieldVid(itemId, vid, 1, EdgeDirectionEnum.UP);
        }
        return invokedMethodInfos;
    }


    public CheckShowRes initCheckCodeView(long itemId, String vid, int oldLineId) throws IOException {
        CheckShowRes res = new CheckShowRes();
        List<CodeReviewData> dataList = new ArrayList<>();
        List<FileChange> fileChanges = new ArrayList<>();

        List<CodeViewAnalysis> codeViewAnalyses = codeViewAnalysisService.getBySourceVid(itemId, vid);
        List<CodeView> codeViews = covertCodeView(codeViewAnalyses);

        if (CollectionUtils.isNotEmpty(codeViews)) {
            int fromLine = 1;
            int buildLine = 1;
            boolean checkDel = false;
            Integer firstDelId = null;
            int lineId = 0;
            int oldId = 0;
            for (CodeView codeView : codeViews) {
                CodeReviewData data = new CodeReviewData();
                LineView lineView = new LineView();
                if (codeView.getType() == DiffTypeEnum.SAM.getCode()) {
                    data.setDiffType(DiffTypeEnum.SAM.getMsg());
                    data.setDestination(fromLine++);
                    data.setSource(buildLine++);
                } else if (codeView.getType() == DiffTypeEnum.DEL.getCode()) {
                    continue;
                } else if (codeView.getType() == DiffTypeEnum.CHECKDEL.getCode()) {
                    addFileChanges(fileChanges, codeView.getId(), buildLine);
                    data.setDestination(fromLine++);
                    data.setDiffType(DiffTypeEnum.DEL.getMsg());
                    checkDel = true;
                } else if (codeView.getType() == DiffTypeEnum.ADD.getCode()) {
                    data.setDiffType(DiffTypeEnum.SAM.getMsg());
                    data.setDestination(fromLine++);
                    data.setSource(buildLine++);
                } else if (codeView.getType() == DiffTypeEnum.CHECKADD.getCode()) {
                    addFileChanges(fileChanges, codeView.getId(), buildLine);
                    data.setDiffType(DiffTypeEnum.ADD.getMsg());
                    data.setSource(buildLine++);
                } else if (codeView.getType() == DiffTypeEnum.CHECK.getCode()) {
                    addFileChanges(fileChanges, codeView.getId(), buildLine);
                    data.setDestination(fromLine++);
                    data.setDiffType(DiffTypeEnum.ADD.getMsg());
                    data.setSource(buildLine++);
                }
                if (checkDel) {
                    if (firstDelId == null) {
                        firstDelId = lineId;
                    }
                    if (codeView.getType() != DiffTypeEnum.CHECKDEL.getCode()) {
                        oldId++;
                        if (oldId == oldLineId) {
                            if (null != firstDelId) {
                                res.setClickLineId(firstDelId);
                            } else {
                                res.setClickLineId(lineId);
                            }
                        }
                        checkDel = false;
                        firstDelId = null;
                        oldId++;
                        if (oldId == oldLineId) {
                            res.setClickLineId(lineId);
                        }
                    }
                } else {
                    oldId++;
                    if (oldId == oldLineId) {
                        res.setClickLineId(lineId);
                    }
                }

                List<LineView> lineViews = new ArrayList<>();
                lineView.setType("line");
                lineView.setView(codeView.getView());
                lineViews.add(lineView);
                data.setLineViews(lineViews);

                data.setId(lineId);
                dataList.add(data);
                lineId++;
            }
        }
        res.setData(dataList);
        croppFileChanges(fileChanges);
        res.setFileChanges(fileChanges);
        return res;
    }


    public void croppFileChanges(List<FileChange> fileChanges) {
        for (int i = fileChanges.size() - 1; i > 0; i--) {
            if (fileChanges.get(i).getId() == (fileChanges.get(i - 1).getId() + 1)) {
                fileChanges.remove(i);
            }
        }
    }

    public void addFileChanges(List<FileChange> fileChanges, Integer id, Integer line) {
        if (line == -1) {
            return;
        }
        FileChange fileChange = new FileChange();
        fileChange.setId(id);
        fileChange.setLine(line);
        fileChanges.add(fileChange);
    }


    public List<CodeView> covertCodeView(List<CodeViewAnalysis> codeViewAnalyses) {
        List<CodeView> codeViews = new ArrayList<>();
        for (CodeViewAnalysis codeViewAnalysis : codeViewAnalyses) {
            CodeView codeView = new CodeView();
            codeView.setId(codeViewAnalysis.getCodeId());
            codeView.setLine(codeViewAnalysis.getCodeLine());
            codeView.setType(codeViewAnalysis.getCodeType());
            codeView.setView(codeViewAnalysis.getCodeView());
            codeViews.add(codeView);
        }
        return codeViews;
    }


    public AllFilesRes getAllFiles(long itemId, boolean diff) {
        AllFilesRes allFilesRes = new AllFilesRes();
        List<FileData> allFileDataList = new ArrayList<>();
        List<FileData> diffFileDataList = new ArrayList<>();
        List<CodeClassAnalysis> codeClassAnalyses = codeClassAnalysisService.getByItemId(itemId);
        List<CodeFileAnalysis> codeFileAnalyses = codeFileAnalysisService.getByItemId(itemId);
        Map<String, List<FileData>> allMap = new HashMap<>();
        Map<String, List<FileData>> diffMap = new HashMap<>();
        for (CodeClassAnalysis codeClassAnalysis : codeClassAnalyses) {
            if (StringUtils.isEmpty(codeClassAnalysis.getClassName())) {
                continue;
            }
            if (StringUtils.isNotEmpty(codeClassAnalysis.getInClassName())) {
                continue;
            }
            FileData fileData = ConvertNode.convertFileData(codeClassAnalysis);

            if (diff) {
                if (fileData.getChangeType() > 0) {
                    if (diffMap.containsKey(fileData.getModuleName())) {
                        diffMap.get(fileData.getModuleName()).add(fileData);
                    } else {
                        List<FileData> list = new ArrayList<>();
                        list.add(fileData);
                        diffMap.put(fileData.getModuleName(), list);
                    }
                }
            }
            if (allMap.containsKey(fileData.getModuleName())) {
                allMap.get(fileData.getModuleName()).add(fileData);
            } else {
                List<FileData> list = new ArrayList<>();
                list.add(fileData);
                allMap.put(fileData.getModuleName(), list);
            }
        }

        for (CodeFileAnalysis codeFileAnalysis : codeFileAnalyses) {
            FileData fileData = ConvertNode.convertFileData(codeFileAnalysis);

            if (diff) {
                if (fileData.getChangeType() > 0) {
                    if (diffMap.containsKey(fileData.getModuleName())) {
                        diffMap.get(fileData.getModuleName()).add(fileData);
                    } else {
                        List<FileData> list = new ArrayList<>();
                        list.add(fileData);
                        diffMap.put(fileData.getModuleName(), list);
                    }
                }
            }
            if (allMap.containsKey(fileData.getModuleName())) {
                allMap.get(fileData.getModuleName()).add(fileData);
            } else {
                List<FileData> list = new ArrayList<>();
                list.add(fileData);
                allMap.put(fileData.getModuleName(), list);
            }
        }

        initFileData(diffFileDataList, diffMap);
        sortFileDataList(diffFileDataList);
        shortTitile(diffFileDataList, allFilesRes, diff);
        allFilesRes.setDiffFileDataList(diffFileDataList);


        initFileData(allFileDataList, allMap);
        sortFileDataList(allFileDataList);
        shortTitile(allFileDataList, allFilesRes, diff);
        allFilesRes.setAllFileDataList(allFileDataList);
        return allFilesRes;
    }

    public void initFileData(List<FileData> fileDataList, Map<String, List<FileData>> fileMap) {
        int countId = 0;
        if (!fileMap.isEmpty()) {
            for (Map.Entry<String, List<FileData>> entry : fileMap.entrySet()) {
                Map<String, FileData> pathMap = new HashMap();
                List<FileData> all = new ArrayList<>();
                if (StringUtils.isEmpty(entry.getKey())) {
                    all = fileMap.get("");
                    FileData rootData = new FileData();
                    rootData.setVid("path_" + countId);
                    rootData.setId(countId++);
                    rootData.setTitle("");
                    rootData.setPath("");
                    rootData.setModuleName("");
                    rootData.setChangeType(ChangeTypeEnum.DEFAULT.getCode());
                    rootData.setCheckType(ChangeTypeEnum.DEFAULT.getCode());
                    rootData.setCommitCount(0);
                    rootData.setEnd(false);
                    pathMap.put("", rootData);
                    fileDataList.add(rootData);
                } else {
                    all = fileMap.get(entry.getKey());
                }
                if (CollectionUtils.isNotEmpty(all)) {
                    for (FileData fileInfo : all) {
                        FileData fileFileData = null;
                        List<String> pathSplit = Arrays.asList((fileInfo.getPath()).split("/"));
                        for (int i = 0; i < pathSplit.size(); i++) {
                            if (i == pathSplit.size() - 1) {
                                String titlePath = StringUtils.join(Arrays.asList((fileInfo.getPath()).split("/")).subList(0, i), "/");
                                if (StringUtils.isEmpty(titlePath)) {
                                    titlePath = "";
                                }
                                FileData fileData = pathMap.get(titlePath);
                                FileData clildFileData = new FileData();
                                clildFileData.setVid(fileInfo.getVid());
                                clildFileData.setId(countId++);
                                clildFileData.setTitle(pathSplit.get(i));
                                clildFileData.setPath(fileInfo.getPath());
                                clildFileData.setModuleName(fileInfo.getModuleName());
                                clildFileData.setChangeType(fileInfo.getChangeType());
                                clildFileData.setCheckType(fileInfo.getCheckType());
                                clildFileData.setCommitCount(fileInfo.getCommitCount());
                                clildFileData.setEnd(true);
                                if (null == fileData) {
                                    if (null == fileFileData) {
                                        fileFileData = new FileData();
                                        fileFileData.setId(countId++);
                                        fileFileData.setVid("path_" + countId);
                                        fileFileData.setPath(fileInfo.getPath());
                                        fileFileData.setModuleName(fileInfo.getModuleName());
                                        fileFileData.setTitle(fileInfo.getTitle());
                                        fileFileData.setCommitCount(fileInfo.getCommitCount());
                                        pathMap.put(titlePath, fileFileData);
                                    } else {
                                        fileFileData.getChildren().add(clildFileData);
                                    }
                                } else {
                                    fileData.getChildren().add(clildFileData);
                                }
                            } else if (i == 0) {
                                String titlePath = StringUtils.join(Arrays.asList((fileInfo.getPath()).split("/")).subList(0, i + 1), "/");
                                if (StringUtils.isEmpty(titlePath)) {
                                    titlePath = "";
                                }
                                if (null == pathMap.get(titlePath)) {
                                    fileFileData = new FileData();
                                    fileFileData.setId(countId++);
                                    fileFileData.setVid("path_" + countId);
                                    fileFileData.setPath(titlePath);
                                    fileFileData.setModuleName(fileInfo.getModuleName());
                                    fileFileData.setTitle(titlePath);
                                    fileFileData.setCommitCount(fileInfo.getCommitCount());
                                    pathMap.put(titlePath, fileFileData);
                                }
                            } else {
                                String titlePath = StringUtils.join(Arrays.asList((fileInfo.getPath()).split("/")).subList(0, i), "/");
                                if (StringUtils.isEmpty(titlePath)) {
                                    titlePath = "";
                                }
                                String nextTitlePath = StringUtils.join(Arrays.asList((fileInfo.getPath()).split("/")).subList(0, i + 1), "/");
                                if (StringUtils.isEmpty(nextTitlePath)) {
                                    nextTitlePath = "";
                                }
                                if (null == pathMap.get(titlePath)) {
                                    FileData fileData = new FileData();
                                    fileData.setId(countId++);
                                    fileData.setVid("path_" + countId);
                                    fileData.setPath(titlePath);
                                    fileData.setModuleName(fileInfo.getModuleName());
                                    fileData.setTitle(pathSplit.get(i));
                                    fileData.setCommitCount(fileInfo.getCommitCount());
                                    pathMap.put(titlePath, fileData);
                                } else {
                                    if (null == pathMap.get(nextTitlePath)) {
                                        FileData fileData = new FileData();
                                        fileData.setId(countId++);
                                        fileData.setVid("path_" + countId);
                                        fileData.setPath(nextTitlePath);
                                        fileData.setModuleName(fileInfo.getModuleName());
                                        fileData.setTitle(pathSplit.get(i));
                                        fileData.setCommitCount(fileInfo.getCommitCount());
                                        pathMap.get(titlePath).getChildren().add(fileData);
                                        pathMap.put(nextTitlePath, fileData);
                                    }
                                }
                            }
                        }
                        if (null != fileFileData) {
                            if (StringUtils.isEmpty(entry.getKey())) {
                                fileDataList.add(fileFileData);
                            } else {
                                boolean addNew = true;
                                for (FileData fileData : fileDataList) {
                                    if (fileData.getPath().equals(entry.getKey())) {
                                        fileData.getChildren().add(fileFileData);
                                        addNew = false;
                                        break;
                                    }
                                }
                                if (addNew) {
                                    fileDataList.add(fileFileData);
                                }
                            }
                        }

                    }
                }
            }
        }

    }

    public void sortFileDataList(List<FileData> fileDataList) {
        if (CollectionUtils.isNotEmpty(fileDataList)) {
            for (FileData fileData : fileDataList) {
                if (CollectionUtils.isNotEmpty(fileData.getChildren())) {
                    sortFileDataList(fileData.getChildren());
                }
            }
            Collections.sort(fileDataList);
        }
    }

    public void shortTitile(List<FileData> fileDataList, AllFilesRes allFilesRes, boolean isDiff) {
        if (CollectionUtils.isNotEmpty(fileDataList)) {
            for (FileData fileData : fileDataList) {
                if (CollectionUtils.isNotEmpty(fileData.getChildren())) {
                    if (fileData.getChildren().size() == 1) {
                        if (fileData.getChildren().get(0).isEnd()) {
                            if (isDiff) {
                                if (StringUtils.isEmpty(allFilesRes.getDiffFirstVid())) {
                                    allFilesRes.setDiffFirstVid(fileData.getChildren().get(0).getVid());
                                }
                            } else {
                                if (StringUtils.isEmpty(allFilesRes.getAllFirstVid())) {
                                    allFilesRes.setAllFirstVid(fileData.getChildren().get(0).getVid());
                                }
                            }
                        } else {
                            fileData.setTitle(fileData.getTitle() + "/" + fileData.getChildren().get(0).getTitle());
                            fileData.setChildren(fileData.getChildren().get(0).getChildren());
                            shortTitile(fileDataList, allFilesRes, isDiff);
                        }
                    } else {
                        for (FileData data : fileData.getChildren()) {
                            if (data.isEnd()) {
                                if (isDiff) {
                                    if (StringUtils.isEmpty(allFilesRes.getDiffFirstVid())) {
                                        allFilesRes.setDiffFirstVid(fileData.getChildren().get(0).getVid());
                                    }
                                } else {
                                    if (StringUtils.isEmpty(allFilesRes.getAllFirstVid())) {
                                        allFilesRes.setAllFirstVid(fileData.getChildren().get(0).getVid());
                                    }
                                }
                            } else {
                                shortTitile(data.getChildren(), allFilesRes, isDiff);
                                if (data.getChildren().size() == 1) {
                                    if (!data.getChildren().get(0).isEnd()) {
                                        data.setTitle(data.getTitle() + "/" + data.getChildren().get(0).getTitle());
                                        data.setChildren(data.getChildren().get(0).getChildren());
                                    }
                                }
                            }
                        }
                    }
                } else if (fileData.isEnd()) {
                    if (isDiff) {
                        if (StringUtils.isEmpty(allFilesRes.getDiffFirstVid())) {
                            allFilesRes.setDiffFirstVid(fileData.getVid());
                        }
                    } else {
                        if (StringUtils.isEmpty(allFilesRes.getAllFirstVid())) {
                            allFilesRes.setAllFirstVid(fileData.getVid());
                        }
                    }
                }
            }
        }
    }

    public ClassNode getClassInfoByVid(long itemId, String vid, String type) {
        ClassNode res = new ClassNode();
        if (StringUtils.equals(type, "method")) {
            CodeMethodAnalysis codeMethodAnalysis = codeMethodAnalysisService.getByMethodVid(itemId, vid);
            if (null != codeMethodAnalysis) {
                CodeClassAnalysis codeClassAnalysis = codeClassAnalysisService.getByClassName(itemId, codeMethodAnalysis.getClassName());
                if (null != codeClassAnalysis) {
                    ClassNode classNode = ConvertNode.convertClassNode(codeClassAnalysis);
                    return classNode;
                }
            }
        } else {
            CodeFieldAnalysis codeFieldAnalysis = codeFieldAnalysisService.getByFieldVid(itemId, vid);
            if (null != codeFieldAnalysis) {
                CodeClassAnalysis codeClassAnalysis = codeClassAnalysisService.getByClassName(itemId, codeFieldAnalysis.getClassName());
                if (null != codeClassAnalysis) {
                    ClassNode classNode = ConvertNode.convertClassNode(codeClassAnalysis);
                    return classNode;
                }
            }
        }
        return res;
    }

}
