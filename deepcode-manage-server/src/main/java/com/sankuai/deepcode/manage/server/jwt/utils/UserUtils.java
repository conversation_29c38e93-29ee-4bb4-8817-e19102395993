package com.sankuai.deepcode.manage.server.jwt.utils;

import com.alibaba.ttl.TransmittableThreadLocal;
import com.sankuai.deepcode.commons.ex.BizException;
import com.sankuai.deepcode.dao.domain.SysUser;
import com.sankuai.deepcode.manage.server.jwt.domain.LoginUser;

import java.util.Optional;

/**
 * Package: com.sankuai.deepcode.manage.server.jwt.utils
 * Description:
 *
 * <AUTHOR>
 * @since 2025/3/5 18:01
 */
public class UserUtils {
    private static final TransmittableThreadLocal<LoginUser> TRANSMITTABLE_THREAD_LOCAL = new TransmittableThreadLocal<>();

    public static Optional<LoginUser> getUser() {
        return Optional.ofNullable(TRANSMITTABLE_THREAD_LOCAL.get());
    }

    /**
     * 获取用户ID
     * FIXME: 注意此处不再判定用户是否存在，不存在会直接抛异常
     * 【不要在 未登录的接口中使用此方法】
     *
     * @return 用户ID
     */
    public static Long getUserId() {
        return getUser().map(LoginUser::getUser).map(SysUser::getId).orElseThrow(() -> new BizException("用户未登录"));
    }

    public static void setUser(LoginUser user) {
        TRANSMITTABLE_THREAD_LOCAL.set(user);
    }
}
