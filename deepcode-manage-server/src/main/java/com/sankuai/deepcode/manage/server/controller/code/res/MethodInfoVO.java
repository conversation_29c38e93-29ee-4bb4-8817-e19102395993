package com.sankuai.deepcode.manage.server.controller.code.res;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * Package: com.sankuai.deepcode.manage.server.controller.code.res
 * Description:
 *
 * <AUTHOR>
 * @since 2025/3/26 10:21
 */
@Setter
@Getter
@ToString
@Accessors(chain = true)
public class MethodInfoVO {
    List<MethodVO> methods;
    @Schema(description = "文件vid")
    String fileVid;
    @Schema(description = "文件路径")
    String filePath;
    @Schema(description = "文件类型")
    String fileType;
    @Schema(description = "文件名")
    String fileName;


    /**
     * 方法信息
     * 包括方法定义、方法调用
     */
    @ToString
    @Setter
    @Getter
    public static class MethodVO {
        @Schema(description = "方法全名")
        private String fullMethodName;
        @Schema(description = "方法vid")
        private String methodVid;
        @Schema(description = "方法名所在行", example = "1")
        private Integer line;
        @Schema(description = "方法来源, local-工程内方法，unknown-未知", allowableValues = {"local", "unknown"}, example = "local")
        private String source;
        @Schema(description = "方法类型, 1-方法定义，2-方法调用", allowableValues = {"1", "2"}, example = "1")
        private Integer type;
    }
}
