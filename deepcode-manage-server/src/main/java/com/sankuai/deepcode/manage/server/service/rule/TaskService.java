package com.sankuai.deepcode.manage.server.service.rule;

import com.sankuai.deepcode.dao.domain.RuleTaskDetail;
import com.sankuai.deepcode.dao.domain.RuleTaskInfo;
import com.sankuai.deepcode.dao.service.RuleTaskDetailService;
import com.sankuai.deepcode.dao.service.RuleTaskInfoService;
import com.sankuai.deepcode.manage.server.controller.rule.res.TaskInfoVo;
import com.sankuai.deepcode.manage.server.enums.RuleType;
import com.sankuai.deepcode.manage.server.model.rule.RuleInfoDetailWithBindInfo;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class TaskService {

    @Autowired
    private RuleTaskDetailService ruleTaskDetailService;
    @Autowired
    private RuleTaskInfoService ruleTaskInfoService;
    @Autowired
    private RuleService ruleService;


    //新增代码审核任务
    public void addTask(long itemId) {

        int projectId = 123; // todo 需要通过工程 查规则，itemId 需转换
        List<RuleInfoDetailWithBindInfo> ruleInfoList = ruleService.getRuleInfoByProjectId(projectId);
        if (CollectionUtils.isEmpty(ruleInfoList)) {
            return;
        }
        long taskId = ruleTaskInfoService.insert(itemId);

        //todo 遍历 url 和 变更代码，生成评审结果。 是否合并规则

        RuleTaskDetail ruleTaskDetail = new RuleTaskDetail();
        ruleTaskDetail.setRuleId(taskId);
        ruleTaskDetail.setFileVid("asdasdzxcvzfcasdasdasd");
        ruleTaskDetail.setValue("风险详情");
        ruleTaskDetail.setType(RuleType.HARD_CODE.getCode());
        ruleTaskDetail.setStartLine(123);
        ruleTaskDetailService.insert(ruleTaskDetail);

        //更新状态
        ruleTaskInfoService.updateStatus(taskId, 1);
        //todo结束

    }

    //获取任务详情
    public TaskInfoVo getTask(long itemId) {
        TaskInfoVo taskInfoVo = new TaskInfoVo();
        RuleTaskInfo task = ruleTaskInfoService.getTaskLastByItemId(itemId);
        if (task == null) {
            return taskInfoVo;
        }
        long taskId = task.getId();
        RuleTaskDetail searchParam = new RuleTaskDetail();
        searchParam.setRuleId(taskId);
        List<RuleTaskDetail> list = ruleTaskDetailService.getRuleList(searchParam);
        taskInfoVo.setRuleTaskInfo(task);
        taskInfoVo.setRuleTaskDetails(list);
        return taskInfoVo;
    }

}
