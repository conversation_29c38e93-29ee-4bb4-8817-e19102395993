package com.sankuai.deepcode.manage.server.controller.project.req;

import com.sankuai.deepcode.manage.server.deserializer.TrimString;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Pattern;

/**
 * Package: com.sankuai.deepcode.manage.server.model.project
 * Description:
 *
 * <AUTHOR>
 * @since 2025/3/3 18:37
 */
@Setter
@Getter
@ToString
public class ProjectAnalyseRequest {
    /**
     * Git仓库地址
     */
    @Schema(title = "Git仓库地址", requiredMode = Schema.RequiredMode.REQUIRED, example = "ssh://*******************/xxx.git")
    @NotEmpty(message = "Git仓库地址不能为空")
    @TrimString
    @Pattern(regexp = "^(git@|https?://|ssh://).*\\.git$", message = "Git仓库地址格式不正确")
    String gitUrl;
    /**
     * 源分支名称
     */
    @Schema(title = "源分支名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "feat/xxx")
    @NotBlank(message = "源分支名称不能为空")
    @TrimString
    String sourceBranch;
    /**
     * 目标分支名称
     */
    @Schema(title = "目标分支名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "master")
    @NotEmpty(message = "目标分支名称不能为空")
    @TrimString
    String targetBranch;
    /**
     * 构建分支名称
     */
    @Schema(title = "构建分支名称", requiredMode = Schema.RequiredMode.NOT_REQUIRED, example = "qa", defaultValue = "master")
    String buildBranch;
}
