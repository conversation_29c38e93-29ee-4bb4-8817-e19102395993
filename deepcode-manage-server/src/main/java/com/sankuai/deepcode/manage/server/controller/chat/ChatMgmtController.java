package com.sankuai.deepcode.manage.server.controller.chat;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.sankuai.deepcode.commons.CommonResult;
import com.sankuai.deepcode.commons.DeepCodePreconditions;
import com.sankuai.deepcode.dao.domain.CodeAnalysisItem;
import com.sankuai.deepcode.dao.domain.CodeAnalysisItemDetail;
import com.sankuai.deepcode.dao.domain.DcChat;
import com.sankuai.deepcode.dao.domain.DcProject;
import com.sankuai.deepcode.dao.service.CodeAnalysisItemDetailService;
import com.sankuai.deepcode.dao.service.DcChatService;
import com.sankuai.deepcode.manage.server.controller.chat.req.CreateChatRequest;
import com.sankuai.deepcode.manage.server.controller.chat.res.ChatMsgVO;
import com.sankuai.deepcode.manage.server.controller.chat.res.ChatProjectInfoVO;
import com.sankuai.deepcode.manage.server.controller.chat.res.ChatVO;
import com.sankuai.deepcode.manage.server.enums.ErrorCodeEnum;
import com.sankuai.deepcode.manage.server.jwt.utils.UserUtils;
import com.sankuai.deepcode.manage.server.mapper.chat.ChatConvertor;
import com.sankuai.deepcode.manage.server.service.chat.ChatMsgService;
import com.sankuai.deepcode.manage.server.service.chat.ProjectChatService;
import com.sankuai.deepcode.manage.server.service.project.ProjectAnalysisService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.UUID;

/**
 * Package: com.sankuai.deepcode.manage.server.controller.chat
 * Description:
 *
 * <AUTHOR>
 * @since 2025/3/14 15:15
 */
@Tag(name = "会话管理", description = "会话的增删改查等操作")
@RestController
@RequestMapping("/chats")
@Slf4j
public class ChatMgmtController {
    private final DcChatService dcChatService;
    private final ChatConvertor chatConvertor;
    private final ProjectAnalysisService projectAnalysisService;
    private final ChatMsgService chatMsgService;
    private final CodeAnalysisItemDetailService codeAnalysisItemDetailService;
    private final ProjectChatService projectChatService;

    public ChatMgmtController(
            DcChatService dcChatService,
            ChatConvertor chatConvertor, ProjectAnalysisService projectAnalysisService,
            ChatMsgService chatMsgService,
            CodeAnalysisItemDetailService codeAnalysisItemDetailService, ProjectChatService projectChatService) {
        this.dcChatService = dcChatService;
        this.chatConvertor = chatConvertor;
        this.projectAnalysisService = projectAnalysisService;
        this.chatMsgService = chatMsgService;
        this.codeAnalysisItemDetailService = codeAnalysisItemDetailService;
        this.projectChatService = projectChatService;
    }


    @Operation(summary = "会话列表", parameters = {
            @Parameter(name = "pageNo", description = "页码", required = true, example = "1"),
            @Parameter(name = "pageSize", description = "每页大小", required = true, example = "10"),
            @Parameter(name = "projectId", description = "项目ID", required = true, example = "1")
    })
    @GetMapping
    public CommonResult<IPage<ChatVO>> getChatList(
            @RequestParam(value = "pageNo", required = false, defaultValue = "1") Integer pageNo,
            @RequestParam(value = "pageSize", required = false, defaultValue = "10") Integer pageSize,
            @RequestParam(value = "projectId") Long projectId
    ) {
        DcProject project = projectAnalysisService.getProject(projectId);
        return CommonResult.success(
                dcChatService.getUserChatByPage(pageNo, pageSize, projectId, UserUtils.getUserId()).convert(chat -> chatConvertor.convert(chat, project))
        );
    }

    @Operation(summary = "创建会话")
    @PostMapping()
    public CommonResult<ChatVO> createChat(
            @RequestBody CreateChatRequest request
    ) {
        DcProject project = projectAnalysisService.getProject(request.getProjectId());
        CodeAnalysisItem analysisItem = projectAnalysisService.getAnalysisItemAndValidation(project);
        DcChat chat = new DcChat();
        chat.setUserId(UserUtils.getUserId())
                .setProjectId(request.getProjectId())
                .setItemId(analysisItem.getId())
                .setTitle(request.getContent().trim())
                .setValid(true)
                .setCreateTime(LocalDateTime.now())
                .setUpdateTime(LocalDateTime.now())
                .setUuid(UUID.randomUUID().toString());

        dcChatService.save(chat);
        return CommonResult.success(chatConvertor.convert(chat, project));
    }

    @Operation(summary = "删除会话", parameters = {
            @Parameter(name = "chatId", description = "会话ID", required = true, example = "1")
    })
    @DeleteMapping("/{chatUuid}")
    public CommonResult<Boolean> deleteChat(@PathVariable("chatUuid") String chatUuid) {
        DcChat chat = dcChatService.getChatByUuid(chatUuid);
        DeepCodePreconditions.checkBizArgument(chat != null, ErrorCodeEnum.DATA_NOT_EXISTS, "会话不存在");
        DeepCodePreconditions.checkBizArgument(Objects.equals(chat.getUserId(), UserUtils.getUserId()), ErrorCodeEnum.NOAUTH, "无权限删除该会话");

        return CommonResult.success(dcChatService.removeById(chat));
    }

    @Operation(summary = "获取会话中的对话信息", parameters = {
            @Parameter(name = "chatUuid", description = "会话UUID", required = true, example = "1")
    })
    @GetMapping("/{chatUuid}/messages")
    public CommonResult<ChatMsgVO> getChat(@PathVariable("chatUuid") String chatUuid) {
        DcChat chat = dcChatService.getChatByUuid(chatUuid);
        DeepCodePreconditions.checkBizArgument(chat != null, ErrorCodeEnum.DATA_NOT_EXISTS, "会话不存在");

        return CommonResult.success(chatMsgService.constructChatMsg(chat, UserUtils.getUserId()));
    }

    @Operation(summary = "获取项目会话", parameters = {
            @Parameter(name = "projectId", description = "项目ID", required = true, example = "1")
    })
    @GetMapping("/{projectId}/infos")
    public CommonResult<ChatProjectInfoVO> getChatInfos(@PathVariable("projectId") Long projectId) {
        DcProject project = projectAnalysisService.getProject(projectId);
        CodeAnalysisItem analysisItem = project.getItemId() != null && project.getItemId() > 0 ? projectAnalysisService.getAnalysisItemAndValidation(project) : null;
        List<CodeAnalysisItemDetail> analysisItemDetails = codeAnalysisItemDetailService.getByItemId(Optional.ofNullable(analysisItem).map(CodeAnalysisItem::getId).orElse(null));

        return CommonResult.success(projectChatService.constructChatInfos(project, analysisItem, analysisItemDetails));
    }
}
