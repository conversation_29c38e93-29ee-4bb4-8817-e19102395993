package com.sankuai.deepcode.manage.server.util;

import com.sankuai.deepcode.dao.domain.CodeFileAnalysis;
import com.sankuai.deepcode.manage.server.controller.code.res.TreeNodeVO;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.*;

/**
 * Package: com.sankuai.deepcode.manage.server.util
 * Description:
 *
 * <AUTHOR>
 * @since 2025/3/13 20:37
 */
public class FileTreeBuilder {
    private static final Comparator<TreeNodeVO> NODE_COMPARATOR = (a, b) -> {
        // 首先比较是否为目录（通过判断是否有children）
        boolean aIsDir = CollectionUtils.isNotEmpty(a.getChildren());
        boolean bIsDir = CollectionUtils.isNotEmpty(b.getChildren());

        if (aIsDir != bIsDir) {
            return aIsDir ? -1 : 1;
        }

        // 同为目录或同为文件时，按名称排序
        return a.getFileName().compareTo(b.getFileName());
    };

    public static List<TreeNodeVO> buildFileTree(List<CodeFileAnalysis> filePaths, Boolean compact) {
        Map<String, TreeNodeVO> nodeMap = new HashMap<>();
        List<TreeNodeVO> rootNodes = new ArrayList<>();

        // 第一步：构建树形结构
        for (CodeFileAnalysis filePath : filePaths) {
            String[] parts = filePath.getFilePath().split("/");
            String currentPath = "";
            TreeNodeVO parentNode = null;

            for (int i = 0; i < parts.length; i++) {
                String part = parts[i];
                if (StringUtils.isNotEmpty(currentPath)) {
                    currentPath += "/";
                }
                currentPath += part;

                if (!nodeMap.containsKey(currentPath)) {
                    TreeNodeVO node = new TreeNodeVO(part);

                    // 如果是叶子节点（最后一个部分），设置fullFilePath
                    if (i == parts.length - 1) {
                        node.setFullFilePath(filePath.getFilePath());
                        node.setId(filePath.getId());
                        node.setFileType(filePath.getFileType());
                        node.setFileVid(filePath.getFileVid());
                        if (Objects.equals(filePath.getChangeType(), 0)) {
                            node.setHaveDiff(false);
                        } else if (Objects.equals(filePath.getChangeType(), 1) || Objects.equals(filePath.getChangeType(), 2)) {
                            node.setHaveDiff(true);
                        }
                    } else {
                        node.setFileType("directory");
                    }

                    nodeMap.put(currentPath, node);

                    if (parentNode != null) {
                        if (parentNode.getChildren() == null) {
                            parentNode.setChildren(new ArrayList<>());
                        }
                        parentNode.getChildren().add(node);
                    } else {
                        rootNodes.add(node);
                    }
                }

                parentNode = nodeMap.get(currentPath);
            }
        }

        // 第二步：递归排序
        sortNodes(rootNodes);

        // 第三步：如果需要压缩，进行树的压缩处理
        if (Boolean.TRUE.equals(compact)) {
            compactTree(rootNodes);
        }

        return rootNodes;
    }

    private static void sortNodes(List<TreeNodeVO> nodes) {
        if (CollectionUtils.isEmpty(nodes)) {
            return;
        }

        // 对当前层级节点排序
        nodes.sort(NODE_COMPARATOR);

        // 递归排序子节点
        for (TreeNodeVO node : nodes) {
            if (CollectionUtils.isNotEmpty(node.getChildren())) {
                sortNodes(node.getChildren());
            }
        }
    }

    // 添加新的压缩处理方法
    private static void compactTree(List<TreeNodeVO> nodes) {
        if (CollectionUtils.isEmpty(nodes)) {
            return;
        }

        for (int i = 0; i < nodes.size(); i++) {
            TreeNodeVO node = nodes.get(i);
            if (node.getChildren() != null) {
                // 递归处理子节点
                compactTree(node.getChildren());

                // 如果节点只有一个子节点，且当前节点和子节点都是目录，则进行压缩
                while (node.getChildren() != null && node.getChildren().size() == 1
                        && "directory".equals(node.getFileType())
                        // 新增判断：子节点也必须是目录
                        && "directory".equals(node.getChildren().get(0).getFileType())) {
                    TreeNodeVO child = node.getChildren().get(0);
                    // 合并文件名
                    node.setFileName(node.getFileName() + "/" + child.getFileName());
                    // 继承子节点的属性
                    node.setChildren(child.getChildren());
                    node.setFullFilePath(child.getFullFilePath());
                    node.setId(child.getId());
                    node.setFileType(child.getFileType());
                    node.setFileVid(child.getFileVid());
                    node.setHaveDiff(child.getHaveDiff());
                }
            }
        }
    }
}
