package com.sankuai.deepcode.manage.server.model.codeReview;

import com.sankuai.deepcode.ast.common.model.java.FieldNode;
import com.sankuai.deepcode.ast.common.model.java.MethodNode;
import lombok.Data;
import org.apache.commons.lang.StringUtils;

@Data
public class LineView {
    private String type;
    private String view;
    private MethodNode methodNode;
    private FieldNode fieldNode;

    public void setView(String view) {
        if (StringUtils.isEmpty(view)) {
            this.view = "\u00a0";
        } else if (StringUtils.isBlank(view)) {
            this.view = view.replaceAll(" ", "\u00a0");
        } else {
            this.view = view;
        }
    }
}
