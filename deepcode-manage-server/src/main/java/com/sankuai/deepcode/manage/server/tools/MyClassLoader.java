package com.sankuai.deepcode.manage.server.tools;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.tools.*;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.Arrays;

public class MyClassLoader extends ClassLoader {
    private static final Logger LOGGER = LoggerFactory.getLogger(MyClassLoader.class);


    private final JavaCompiler compiler;
    private final StandardJavaFileManager fileManager;
    private String classPath;

    public void setClassPath(String classPath) {
        this.classPath = classPath;
    }

    public MyClassLoader(ClassLoader parent) {
        super(parent);
        this.compiler = ToolProvider.getSystemJavaCompiler();
        this.fileManager = compiler.getStandardFileManager(null, null, null);
    }

    public Class<?> compileAndLoad(String className, String javaCode) throws Exception {
        JavaFileObject source = new StringJavaFileObject(className, javaCode);
        DiagnosticCollector<JavaFileObject> diagnostics = new DiagnosticCollector<>();
        Iterable<? extends JavaFileObject> compilationUnits = Arrays.asList(source);
        Iterable<String> options = null;
        if (StringUtils.isEmpty(classPath)) {
            options = Arrays.asList("-proc:none");
        } else {
            options = Arrays.asList("-classpath", classPath, "-proc:none");
        }

        JavaCompiler.CompilationTask task = compiler.getTask(null, fileManager, diagnostics, options, null, compilationUnits);
        if (!task.call()) {
            for (Diagnostic<? extends JavaFileObject> diagnostic : diagnostics.getDiagnostics()) {
                LOGGER.error("Error: {}", diagnostic.toString());
            }
            throw new RuntimeException("Compilation failed.");
        }

        JavaFileObject classFile = fileManager.getJavaFileForOutput(StandardLocation.CLASS_OUTPUT, className, JavaFileObject.Kind.CLASS, null);
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        try (InputStream input = classFile.openInputStream()) {
            byte[] buffer = new byte[1024];
            int bytesRead;
            while ((bytesRead = input.read(buffer)) != -1) {
                baos.write(buffer, 0, bytesRead);
            }
        }

        byte[] classBytes = baos.toByteArray();
        Class<?> clazz = defineClass(className, classBytes, 0, classBytes.length);
        return clazz;
    }

    public void clear() {
        try {
            fileManager.close();
        } catch (IOException e) {
            e.printStackTrace();
        }
    }
}
