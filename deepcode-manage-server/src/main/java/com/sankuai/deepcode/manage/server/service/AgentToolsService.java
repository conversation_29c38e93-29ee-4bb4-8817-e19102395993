package com.sankuai.deepcode.manage.server.service;

import com.alibaba.fastjson.JSON;
import com.google.common.reflect.TypeToken;
import com.sankuai.deepcode.ast.common.model.java.MethodNode;
import com.sankuai.deepcode.dao.domain.CodeClassAnalysis;
import com.sankuai.deepcode.dao.domain.CodeMethodAnalysis;
import com.sankuai.deepcode.dao.domain.MethodInvokeMethod;
import com.sankuai.deepcode.dao.service.CodeClassAnalysisService;
import com.sankuai.deepcode.dao.service.CodeMethodAnalysisService;
import com.sankuai.deepcode.dao.service.MethodInvokeMethodService;
import com.sankuai.deepcode.manage.server.util.ConvertNode;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

@Service
public class AgentToolsService {
    @Autowired
    private CodeMethodAnalysisService codeMethodAnalysisService;
    @Autowired
    private MethodInvokeMethodService methodInvokeMethodService;
    @Autowired
    private CodeClassAnalysisService codeClassAnalysisService;

    public List<MethodNode> getAllMethodNode(Long ItemId) {
        List<MethodNode> methodNodes = new ArrayList<>();
        List<CodeMethodAnalysis> codeMethodAnalyses = codeMethodAnalysisService.getByItemId(ItemId);
        for (CodeMethodAnalysis codeMethodAnalysis : codeMethodAnalyses) {
            MethodNode methodNode = ConvertNode.convertMethodNode(codeMethodAnalysis);
            methodNodes.add(methodNode);
        }
        return methodNodes;
    }

    public MethodNode getMethodNode(Long ItemId, String methodVid) {
        CodeMethodAnalysis codeMethodAnalysis = codeMethodAnalysisService.getByMethodVid(ItemId, methodVid);
        if (null != codeMethodAnalysis) {
            MethodNode methodNode = ConvertNode.convertMethodNode(codeMethodAnalysis);
            return methodNode;
        } else {
            return null;
        }
    }

    public List<MethodNode> getUpMethodNodes(Long itemId, String methodVid, Integer step) {
        List<MethodNode> methodNodes = new ArrayList<>();
        getLinkNodes(itemId, true, methodVid, step, methodNodes, new HashSet<>());
        return methodNodes;
    }

    public List<MethodNode> getDownMethodNodes(Long itemId, String methodVid, Integer step) {
        List<MethodNode> methodNodes = new ArrayList<>();
        getLinkNodes(itemId, false, methodVid, step, methodNodes, new HashSet<>());
        return methodNodes;
    }

    public String methodVidShowView(Long itemId, String methodVid) {
        String classVid = "noFind";
        String className = "noFind";
        int startLineId = 0;
        CodeMethodAnalysis codeMethodAnalysis = codeMethodAnalysisService.getByMethodVid(itemId, methodVid);
        if (null != codeMethodAnalysis) {
            startLineId = codeMethodAnalysis.getStartLine();
            CodeClassAnalysis codeClassAnalysis = codeClassAnalysisService.getByClassName(itemId, codeMethodAnalysis.getClassName());
            if (null != codeClassAnalysis) {
                classVid = codeClassAnalysis.getClassVid();
                if (codeClassAnalysis.getClassName().contains(".")) {
                    className = codeClassAnalysis.getClassName().substring(codeClassAnalysis.getClassName().lastIndexOf(".") + 1);
                } else {
                    className = codeClassAnalysis.getClassName();
                }
            }
        }
        String result = "{@CodeJumper@classVid=" + classVid + "@className=" + className + "@methodVid=" + methodVid + "@methodName=" + codeMethodAnalysis.getMethodName() + "@startLineId=" + startLineId + "@}";
        return result;
    }

    public String classVidShowView(Long itemId, String classVid) {
        String className = "noFind";
        int startLineId = 0;
        CodeClassAnalysis codeClassAnalysis = codeClassAnalysisService.getByClassVid(itemId, classVid);
        if (null != codeClassAnalysis.getChangeLines()) {
            Type changeLinesType = new TypeToken<List<Integer>>() {
            }.getType();
            List<Integer> changeLines = JSON.parseObject(codeClassAnalysis.getChangeLines().toString(), changeLinesType);
            if (changeLines.size() > 0) {
                startLineId = changeLines.get(0);
            }
            if (codeClassAnalysis.getClassName().contains(".")) {
                className = codeClassAnalysis.getClassName().substring(codeClassAnalysis.getClassName().lastIndexOf(".") + 1);
            } else {
                className = codeClassAnalysis.getClassName();
            }
        }
        String result = "{@CodeJumper@classVid=" + classVid + "@className=" + className + "@methodVid=noFind@methodName=noFind@startLineId=" + startLineId + "@}";
        return result;
    }


    public void getLinkNodes(long itemId, boolean up, String methodVid, int step, List<MethodNode> methodNodes, Set<String> setVids) {
        if (step <= 0) {
            return;
        }
        Set<String> methodVids = new HashSet<>();
        if (up) {
            List<MethodInvokeMethod> methodInvokeMethods = methodInvokeMethodService.getByTarget(itemId, methodVid);
            for (MethodInvokeMethod methodInvokeMethod : methodInvokeMethods) {
                if (!setVids.contains(methodInvokeMethod.getSource())) {
                    methodVids.add(methodInvokeMethod.getSource());
                    setVids.add(methodInvokeMethod.getSource());
                }
            }
        } else {
            List<MethodInvokeMethod> methodInvokeMethods = methodInvokeMethodService.getBySource(itemId, methodVid);
            for (MethodInvokeMethod methodInvokeMethod : methodInvokeMethods) {
                if (!setVids.contains(methodInvokeMethod.getTarget())) {
                    methodVids.add(methodInvokeMethod.getTarget());
                    setVids.add(methodInvokeMethod.getTarget());
                }
            }
        }
        if (methodVids.size() > 0) {
            List<CodeMethodAnalysis> codeMethodAnalyses = codeMethodAnalysisService.getByVids(itemId, methodVids);
            for (CodeMethodAnalysis codeMethodAnalysis : codeMethodAnalyses) {
                MethodNode methodNode = ConvertNode.convertMethodNode(codeMethodAnalysis);
                methodNodes.add(methodNode);
            }

            step = step - 1;
            for (String vid : methodVids) {
                getLinkNodes(itemId, up, vid, step, methodNodes, setVids);
            }
        }
    }

}
