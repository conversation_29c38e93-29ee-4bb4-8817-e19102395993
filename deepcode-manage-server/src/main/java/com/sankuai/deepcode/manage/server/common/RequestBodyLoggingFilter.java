package com.sankuai.deepcode.manage.server.common;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import org.springframework.web.filter.OncePerRequestFilter;
import org.springframework.web.util.ContentCachingRequestWrapper;

import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.nio.charset.StandardCharsets;

/**
 * Package: com.sankuai.deepcode.manage.server.common
 * Description:
 *
 * <AUTHOR>
 * @since 2025/3/20 10:13
 */
@Component
@Slf4j
public class RequestBodyLoggingFilter extends OncePerRequestFilter {


    @Override
    protected void doFilterInternal(
            HttpServletRequest request, HttpServletResponse response,
                                    Filter<PERSON>hain filterChain) throws ServletException, IOException {

        ContentCachingRequestWrapper wrappedRequest = new ContentCachingRequestWrapper(request);

        filterChain.doFilter(wrappedRequest, response);

        // 打印请求体
        String requestBody = new String(wrappedRequest.getContentAsByteArray(), StandardCharsets.UTF_8);
        if (StringUtils.hasText(requestBody)) {
            log.info("Request Body: {}", requestBody);
        }
    }
}
