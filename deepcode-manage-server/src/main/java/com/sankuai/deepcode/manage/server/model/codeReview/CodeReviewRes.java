package com.sankuai.deepcode.manage.server.model.codeReview;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
public class CodeReviewRes {
    private int devAdd = 0;
    private int devDel = 0;
    private int integrationAdd = 0;
    private int integrationDel = 0;
    private List<CodeReviewData> codeReviewDataList = new ArrayList<>();
    private List<FileListData> changeMethodInfos = new ArrayList<>();
    private List<FileListData> changeFieldInfos = new ArrayList<>();
    private List<FileChange> fileChanges = new ArrayList<>();
    private String fileWarnShow;
}
