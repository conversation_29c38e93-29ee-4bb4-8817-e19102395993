package com.sankuai.deepcode.manage.server.deserializer;

import com.fasterxml.jackson.annotation.JacksonAnnotationsInside;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;

import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;

/**
 * <AUTHOR>
 * @since 2025/3/11 18:37
 */
@Retention(RetentionPolicy.RUNTIME)
@JacksonAnnotationsInside
@JsonDeserialize(using = TrimDeserializer.class)
public @interface TrimString {
}
