package com.sankuai.deepcode.manage.server.controller.chat;

import com.sankuai.deepcode.commons.CommonResult;
import com.sankuai.deepcode.manage.server.controller.chat.req.AdviceQuestionRequest;
import com.sankuai.deepcode.manage.server.controller.chat.req.ProjectChatRequestVO;
import com.sankuai.deepcode.manage.server.controller.chat.res.AdviceQuestionVO;
import com.sankuai.deepcode.manage.server.controller.chat.res.ModelVO;
import com.sankuai.deepcode.manage.server.service.chat.AdviceQuestionsService;
import com.sankuai.deepcode.manage.server.service.chat.ProjectChatService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.util.List;

/**
 * Package: com.sankuai.deepcode.manage.server.controller.chat
 * Description:
 *
 * <AUTHOR>
 * @since 2025/3/20 14:41
 */
@Tag(name = "DeepCode 会话接口")
@RestController
@RequestMapping("/deepcode")
@Slf4j
public class DeepCodeChatController {
    private final ProjectChatService projectChatService;
    private final AdviceQuestionsService adviceQuestionsService;

    public DeepCodeChatController(
            ProjectChatService projectChatService, AdviceQuestionsService adviceQuestionsService
    ) {
        this.projectChatService = projectChatService;
        this.adviceQuestionsService = adviceQuestionsService;
    }


    @Operation(summary = "生成式会话")
    @PostMapping("/chat/completions")
    public SseEmitter chatCompletions(@RequestBody ProjectChatRequestVO request) {
        return projectChatService.chatCompletions(request);
    }


    @Operation(summary = "获取建议问题")
    @PostMapping(value = {"/chat/advice_questions", "/chat/adviceQuestions"})
    public CommonResult<AdviceQuestionVO> adviceQuestions(@RequestBody @Validated AdviceQuestionRequest request) {
        return CommonResult.success(adviceQuestionsService.adviceQuestions(request));
    }

    @Operation(summary = "获取模型列表")
    @GetMapping("/models")
    public CommonResult<List<ModelVO>> getModels() {
        return CommonResult.success(projectChatService.getModels());
    }
}
