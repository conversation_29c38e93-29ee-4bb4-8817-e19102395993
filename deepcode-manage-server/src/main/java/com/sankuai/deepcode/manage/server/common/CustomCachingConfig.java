package com.sankuai.deepcode.manage.server.common;

import com.github.benmanes.caffeine.cache.Caffeine;
import lombok.Data;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cache.Cache;
import org.springframework.cache.CacheManager;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.cache.caffeine.CaffeineCache;
import org.springframework.cache.interceptor.CacheErrorHandler;
import org.springframework.cache.support.SimpleCacheManager;
import org.springframework.context.annotation.*;
import org.springframework.lang.NonNull;

import java.time.Duration;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Package: com.sankuai.deepcode.manage.server.common
 * Description:
 *
 * <AUTHOR>
 * @since 2025-03-15 11:55:22
 */
@Setter
@Configuration
@EnableCaching(proxyTargetClass = true)
@EnableAspectJAutoProxy(exposeProxy = true, proxyTargetClass = true)
@Slf4j
@ConfigurationProperties(prefix = "caching")
public class CustomCachingConfig {
    private Map<String, CacheSpec> specs;

    @Bean
    @Role(BeanDefinition.ROLE_INFRASTRUCTURE)
    @Primary
    public CacheManager cacheManager() {
        SimpleCacheManager manager = new SimpleCacheManager();
        if (specs != null) {
            List<CaffeineCache> caches = specs.entrySet().stream()
                    .map(e -> buildCache(e.getKey(), e.getValue()))
                    .collect(Collectors.toList());
            manager.setCaches(caches);
        }
        return manager;
    }

    @Bean
    public CacheErrorHandler errorHandler() {
        return new CustomCacheErrorHandler();
    }

    private CaffeineCache buildCache(String name, CacheSpec spec) {
        log.info("Cache {} specified timeout of {} min, max of {}", name, spec.getExpireAfterWrite(), spec.getMaximumSize());
        final Caffeine<Object, Object> caffeineBuilder = Caffeine.newBuilder()
                .expireAfterWrite(spec.getExpireAfterWrite())
                .maximumSize(spec.getMaximumSize())
                .recordStats();
        return new CaffeineCache(name, caffeineBuilder.build());
    }

    @Data
    private static class CacheSpec {
        private Duration expireAfterWrite = Duration.parse("PT1H");
        private Integer maximumSize = 10000;
    }

    public static class CustomCacheErrorHandler implements CacheErrorHandler {
        @Override
        public void handleCacheGetError(@NonNull RuntimeException exception, Cache cache, @NonNull Object key) {
            log.error("Cache get error - cacheName:{}, cacheKey:{}", cache.getName(), key, exception);
        }

        @Override
        public void handleCachePutError(@NonNull RuntimeException exception, Cache cache, @NonNull Object key, Object value) {
            log.error("Cache put error - cacheName:{}, cacheKey:{}, cacheValue:{}", cache.getName(), key, value, exception);
        }

        @Override
        public void handleCacheEvictError(@NonNull RuntimeException exception, Cache cache, @NonNull Object key) {
            log.error("Cache evict error - cacheName:{}, cacheKey:{}", cache.getName(), key, exception);
        }

        @Override
        public void handleCacheClearError(@NonNull RuntimeException exception, Cache cache) {
            log.error("Cache clear error - cacheKey:{}", cache.getName(), exception);
        }
    }
}
