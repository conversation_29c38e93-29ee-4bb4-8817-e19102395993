package com.sankuai.deepcode.manage.server.deserializer;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;

import java.io.IOException;

/**
 * Package: com.sankuai.deepcode.manage.server.deserializer
 * Description:
 *
 * <AUTHOR>
 * @since 2025/3/12 14:17
 */
public class TrimDeserializer extends JsonDeserializer<String> {
    @Override
    public String deserialize(JsonParser jsonParser, DeserializationContext deserializationContext) throws IOException {
        return jsonParser.getValueAsString().trim();
    }
}
