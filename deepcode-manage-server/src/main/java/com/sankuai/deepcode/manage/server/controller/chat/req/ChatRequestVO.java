package com.sankuai.deepcode.manage.server.controller.chat.req;

import com.fasterxml.jackson.annotation.JsonAlias;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.common.collect.Lists;
import com.sankuai.deepcode.ai.llm.openai.chat.Role;
import io.swagger.v3.oas.annotations.Hidden;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * Package: com.sankuai.deepcode.manage.server.controller.chat.req
 * Description:
 *
 * <AUTHOR>
 * @since 2025/3/18 19:30
 */
@Setter
@Getter
@ToString
public class ChatRequestVO {
    @Schema(description = "模型名称", example = "gpt-4o-mini")
    private String model;

    @Schema(description = "消息列表")
    private List<MessageVO> messages;

    @Schema(description = "温度", example = "0.6")
    private Double temperature;

    @Schema(description = "topP")
    private Double topP;

    @Schema(description = "n")
    private Integer n;

    @Schema(description = "流式输出", example = "true")
    private Boolean stream;

    @Schema(description = "停止词")
    private List<String> stop;

    @Schema(description = "最大tokens", example = "4096")
    @JsonProperty("maxTokens")
    @JsonAlias("max_tokens")
    private Integer maxTokens;

    @Schema(description = "重复惩罚")
    private Double presencePenalty;

    @Schema(description = "频率惩罚")
    private Double frequencyPenalty;


    @Hidden
    public Optional<MessageVO> getLastUserMessage() {
        if (CollectionUtils.isEmpty(messages)) {
            return Optional.empty();
        }
        return messages.stream()
                .filter(message -> Role.USER.name().equalsIgnoreCase(message.getRole()))
                .reduce((first, second) -> second);
    }


    /**
     * 获取 最后一条的用户查询
     *
     * @return 用户查询
     */
    @Hidden
    public String getUserQuery() {
        return getLastUserMessage().map(MessageVO::getContent).orElse(Lists.newArrayList()).stream().filter(
                c -> MessageType.TEXT == c.getType()
        ).findFirst().map(MessageVO.ContentVO::getText).orElse(StringUtils.EMPTY);
    }

    /**
     * 获取 最后一条的用户输入文件列表
     *
     * @return 文件列表
     */
    @Hidden
    public List<MessageVO.FilePayLoad> getFileContent() {
        return getLastUserMessage()
                .map(MessageVO::getContent)
                .orElse(Collections.emptyList())
                .stream()
                .filter(c -> MessageType.FILE == c.getType())
                .map(msg -> (MessageVO.FilePayLoad) msg.getMedia())
                .collect(Collectors.toList());
    }

    /**
     * 获取 最后一条用户输入的图片列表
     *
     * @return 图片列表
     */
    @Hidden
    public List<MessageVO.ImagePayload> getImageContent() {
        return getLastUserMessage()
                .map(MessageVO::getContent)
                .orElse(Collections.emptyList())
                .stream()
                .filter(c -> MessageType.IMAGE == c.getType())
                .map(msg -> (MessageVO.ImagePayload) msg.getMedia())
                .collect(Collectors.toList());
    }
}
