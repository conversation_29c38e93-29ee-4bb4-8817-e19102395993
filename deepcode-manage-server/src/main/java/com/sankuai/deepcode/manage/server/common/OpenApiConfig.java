package com.sankuai.deepcode.manage.server.common;

import io.swagger.v3.oas.models.Components;
import io.swagger.v3.oas.models.ExternalDocumentation;
import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Contact;
import io.swagger.v3.oas.models.info.Info;
import io.swagger.v3.oas.models.security.SecurityRequirement;
import io.swagger.v3.oas.models.security.SecurityScheme;
import io.swagger.v3.oas.models.servers.Server;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.env.Environment;

import java.util.Arrays;

/**
 * Package: com.sankuai.deepcode.manage.server.common
 * Description:
 *
 * <AUTHOR>
 * @since 2025/3/3 14:57
 */
@Configuration
public class OpenApiConfig {
    @Autowired
    Environment environment;

    @Bean
    public OpenAPI customOpenAPI() {
        OpenAPI openAPI = new OpenAPI()
                .info(new Info()
                        .title("DeepCode 代码知识图谱API文档")
                        .version("1.0.0")
                        .description("构建代码知识图谱；通过增强检索（RAG）、基础查询、图查询、文本倒排检索等多路索引查询结合的方式完成基于知识图谱的问答检索")
                        .contact(new Contact().name("技术大拿：陈军").email("<EMAIL>"))
                ).externalDocs(new ExternalDocumentation()
                        .description("《DeepCode产品文档》")
                        .url("https://km.sankuai.com/collabpage/2675879340")
                ).components(
                        new Components()
                                .addSecuritySchemes(
                                        "tokenAuth", new SecurityScheme()
                                                .type(SecurityScheme.Type.APIKEY)
                                                .in(SecurityScheme.In.HEADER).name("token")
                                )
                ).addSecurityItem(new SecurityRequirement().addList("tokenAuth"));
        if (environment.getActiveProfiles().length > 0) {
            String[] envs = environment.getActiveProfiles();
            if (!Arrays.asList(envs).contains("local")) {
                // 如果不包含local，则默认是 有部署的，是有反向代理的，需要添加 /api 前缀
                openAPI.addServersItem(new Server().url("/api"));
            }
        }

        return openAPI;
    }
}
