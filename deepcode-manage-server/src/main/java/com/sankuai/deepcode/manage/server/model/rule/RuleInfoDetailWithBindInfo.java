package com.sankuai.deepcode.manage.server.model.rule;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class RuleInfoDetailWithBindInfo {

    private Long id;

    private Long userId;

    private String userName;

    private String value;

    private int type;

    private long ruleId;

    private long projectId;

    private LocalDateTime createTime;

    private LocalDateTime updateTime;

}
