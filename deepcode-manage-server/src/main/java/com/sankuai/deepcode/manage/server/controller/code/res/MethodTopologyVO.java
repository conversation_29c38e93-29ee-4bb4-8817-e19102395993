package com.sankuai.deepcode.manage.server.controller.code.res;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * Package: com.sankuai.deepcode.manage.server.controller.code.res
 * Description:
 *
 * <AUTHOR>
 * @since 2025/6/3 21:17
 */
@Setter
@Getter
@ToString
@Accessors(chain = true)
public class MethodTopologyVO {
    @Schema(description = "节点列表")
    List<TopologyNode> nodes;
    @Schema(description = "边列表")
    List<TopologyEdge> edges;

    @Setter
    @Getter
    @ToString
    @Accessors(chain = true)
    public static class TopologyNode {
        String id;
        Long itemId;
        String moduleName;
        String className;
        String innerClassName;
        String methodName;
        String access;
        Integer startLine;
        Integer endLine;
        String source;
        Integer changeType;

        String fileVid;
        String filePath;
    }

    @Getter
    @Setter
    @ToString
    @Accessors(chain = true)
    public static class TopologyEdge {
        @Schema(description = "源节点")
        private String source;
        @Schema(description = "目标节点")
        private String target;
        @Schema(description = "调用行，以逗号分隔")
        private String invokeLines;
    }
}
