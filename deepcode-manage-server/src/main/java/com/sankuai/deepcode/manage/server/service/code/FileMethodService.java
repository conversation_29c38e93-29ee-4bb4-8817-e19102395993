package com.sankuai.deepcode.manage.server.service.code;

import com.sankuai.deepcode.commons.MapUtils;
import com.sankuai.deepcode.dao.domain.*;
import com.sankuai.deepcode.dao.service.*;
import com.sankuai.deepcode.manage.server.constants.CachingKeys;
import com.sankuai.deepcode.manage.server.controller.code.res.AiReviewsVO;
import com.sankuai.deepcode.manage.server.controller.code.res.MethodInfoVO;
import com.sankuai.deepcode.manage.server.mapper.code.FileContentConvertor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * Package: com.sankuai.deepcode.manage.server.service.code
 * Description:
 *
 * <AUTHOR>
 * @since 2025/3/26 12:15
 */
@Service
@Slf4j
public class FileMethodService {
    private final CodeFileAnalysisService codeFileAnalysisService;
    private final CodeClassAnalysisService codeClassAnalysisService;
    private final CodeMethodAnalysisService codeMethodAnalysisService;
    private final MethodInvokeMethodService methodInvokeMethodService;
    private final RuleTaskDetailService ruleTaskDetailService;
    private final RuleInfoDetailService ruleInfoDetailService;
    private final FileContentConvertor fileContentConvertor;

    public FileMethodService(
            CodeFileAnalysisService codeFileAnalysisService,
            CodeClassAnalysisService codeClassAnalysisService,
            CodeMethodAnalysisService codeMethodAnalysisService,
            MethodInvokeMethodService methodInvokeMethodService,
            RuleTaskDetailService ruleTaskDetailService,
            RuleInfoDetailService ruleInfoDetailService,
            FileContentConvertor fileContentConvertor
    ) {
        this.codeFileAnalysisService = codeFileAnalysisService;
        this.codeClassAnalysisService = codeClassAnalysisService;
        this.codeMethodAnalysisService = codeMethodAnalysisService;
        this.methodInvokeMethodService = methodInvokeMethodService;
        this.ruleTaskDetailService = ruleTaskDetailService;
        this.ruleInfoDetailService = ruleInfoDetailService;
        this.fileContentConvertor = fileContentConvertor;
    }

    /**
     * 获取文件方法信息
     *
     * @param analysisItem 分析项目
     * @param fileVid      文件vid
     * @return 方法信息
     */
    @Cacheable(
            value = CachingKeys.FILE_METHODS,
            key = "'itemId:' + #analysisItem.getId() + ':fileVid:' + #fileVid"
    )
    public MethodInfoVO getMethodInfo(CodeAnalysisItem analysisItem, String fileVid) {
        Optional<CodeFileAnalysis> codeFileAnalysis = codeFileAnalysisService.getByItemAndVid(analysisItem.getId(), fileVid);
        if (!codeFileAnalysis.isPresent()) {
            return null;
        }

        // FIXME: 当前按JAVA语言，先找文件中的类，再找关联的方法，后续需要适配其他语言
        List<CodeClassAnalysis> fileClassList = codeClassAnalysisService.getByItemIdAndFileVid(analysisItem.getId(), fileVid);
        // 获取文件中的方法列表
        List<CodeMethodAnalysis> fileMethodList = codeMethodAnalysisService.getByItemAndClassNames(
                analysisItem.getId(), fileClassList.stream().map(CodeClassAnalysis::getClassName).collect(Collectors.toList())
        );

        // 获取文件中，方法调用的其他方法
        List<MethodInvokeMethod> fileMethodInvokeList = methodInvokeMethodService.getByItemAndMethodVids(
                analysisItem.getId(), fileMethodList.stream().map(CodeMethodAnalysis::getMethodVid).collect(Collectors.toList())
        );

        // 文件中的方法列表，都需要转换成 methodVO
        List<MethodInfoVO.MethodVO> methodVOList = fileMethodList.stream().map(method -> {
            MethodInfoVO.MethodVO methodVO = new MethodInfoVO.MethodVO();
            methodVO.setMethodVid(method.getMethodVid());
            methodVO.setFullMethodName(method.getMethodName());
            methodVO.setLine(method.getStartLine());
            methodVO.setSource(method.getSource());
            methodVO.setType(1);
            return methodVO;

        }).collect(Collectors.toList());

        // 文件中调用的 targetMethod 方法，也需要按照调用行号转换成 methodVO
        List<CodeMethodAnalysis> targetMethodList = codeMethodAnalysisService.getByMethodVids(
                analysisItem.getId(), fileMethodInvokeList.stream().map(MethodInvokeMethod::getTarget).collect(Collectors.toList())
        );
        Map<String, CodeMethodAnalysis> targetMethodMap = MapUtils.toMap(targetMethodList, CodeMethodAnalysis::getMethodVid);
        List<MethodInfoVO.MethodVO> invokeMethodVoList = fileMethodInvokeList.stream()
                .map(invoke -> {
                    return invoke.getInvokeParams().stream().map(line -> {
                        CodeMethodAnalysis targetMethod = targetMethodMap.get(invoke.getTarget());
                        if (targetMethod == null) {
                            return null;
                        }

                        MethodInfoVO.MethodVO methodVO = new MethodInfoVO.MethodVO();
                        methodVO.setMethodVid(invoke.getTarget());
                        methodVO.setFullMethodName(targetMethod.getMethodName());
                        methodVO.setLine(line.getInvokeLine());
                        methodVO.setSource(targetMethod.getSource());
                        methodVO.setType(2);
                        return methodVO;
                    }).collect(Collectors.toList());
                })
                .flatMap(List::stream)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

        return new MethodInfoVO()
                .setFileName(codeFileAnalysis.get().getFileName())
                .setFileVid(fileVid)
                .setFilePath(codeFileAnalysis.get().getFilePath())
                .setFileType(codeFileAnalysis.get().getFileType())
                .setMethods(
                        ListUtils.union(methodVOList, invokeMethodVoList)
                );
    }

    public AiReviewsVO getAiReviews(CodeAnalysisItem analysisItem, String fileVid) {
        CodeFileAnalysis fileAnalysis = codeFileAnalysisService.getByItemAndVid(analysisItem.getId(), fileVid).orElse(null);
        if (fileAnalysis == null) {
            return null;
        }
        List<RuleTaskDetail> ruleTaskDetails = ruleTaskDetailService.getByItemAndFileVid(analysisItem.getId(), fileVid);
        if (CollectionUtils.isEmpty(ruleTaskDetails)) {
            return null;
        }
        List<RuleInfoDetail> ruleInfoDetails = ruleInfoDetailService.getRuleInfoDetailsByRuleIds(ruleTaskDetails.stream().map(RuleTaskDetail::getRuleId).collect(Collectors.toList()));
        if (CollectionUtils.isEmpty(ruleInfoDetails)) {
            return null;
        }
        Map<Long, RuleInfoDetail> ruleInfoDetailMap = MapUtils.toMap(ruleInfoDetails, RuleInfoDetail::getId);

        return fileContentConvertor.convertReviewVO(ruleTaskDetails, ruleInfoDetailMap, fileAnalysis);
    }

    @CacheEvict(value = CachingKeys.FILE_METHODS, allEntries = true)
    public void clearMethodCache() {
        log.info(">>> clearMethodCache: {}", CachingKeys.FILE_METHODS);
    }
}
