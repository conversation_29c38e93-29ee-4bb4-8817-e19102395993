package com.sankuai.deepcode.manage.server.util;

import com.alibaba.fastjson.JSON;
import com.google.common.reflect.TypeToken;
import com.sankuai.deepcode.ast.common.model.java.*;
import com.sankuai.deepcode.dao.domain.CodeClassAnalysis;
import com.sankuai.deepcode.dao.domain.CodeFieldAnalysis;
import com.sankuai.deepcode.dao.domain.CodeFileAnalysis;
import com.sankuai.deepcode.dao.domain.CodeMethodAnalysis;
import com.sankuai.deepcode.manage.server.model.file.FileData;

import java.lang.reflect.Type;
import java.util.List;

public class ConvertNode {

    public static ClassNode convertClassNode(CodeClassAnalysis codeClassAnalysis) {
        ClassNode classNode = new ClassNode();
        classNode.setVid(codeClassAnalysis.getClassVid());
        classNode.setClassName(codeClassAnalysis.getClassName());
        classNode.setInClassName(codeClassAnalysis.getInClassName());
        Type javaExtendsType = new TypeToken<JavaExtends>() {
        }.getType();
        JavaExtends javaExtends = JSON.parseObject(codeClassAnalysis.getSuperClass().toString(), javaExtendsType);
        classNode.setSuperClass(javaExtends);
        Type javaImplementsType = new TypeToken<List<JavaImplements>>() {
        }.getType();
        List<JavaImplements> javaImplements = JSON.parseObject(codeClassAnalysis.getInterfaces().toString(), javaImplementsType);
        classNode.setInterfaces(javaImplements);
        classNode.setAccess(codeClassAnalysis.getAccess());
        Type javaAnnotationReturnType = new TypeToken<List<JavaAnnotation>>() {
        }.getType();
        List<JavaAnnotation> javaAnnotations = JSON.parseObject(codeClassAnalysis.getAnnotations().toString(), javaAnnotationReturnType);
        classNode.setAnnotations(javaAnnotations);
        classNode.setStartLine(codeClassAnalysis.getStartLine());
        classNode.setEndLine(codeClassAnalysis.getEndLine());
        classNode.setChangeType(codeClassAnalysis.getChangeType());
        Type changeLinesType = new TypeToken<List<Integer>>() {
        }.getType();
        List<Integer> changeLines = JSON.parseObject(codeClassAnalysis.getChangeLines().toString(), changeLinesType);
        classNode.setChangeLines(changeLines);
        Type commentLinesType = new TypeToken<List<Integer>>() {
        }.getType();
        List<Integer> commentLines = JSON.parseObject(codeClassAnalysis.getCommentLines().toString(), commentLinesType);
        classNode.setCommentLines(commentLines);
        return classNode;
    }

    public static MethodNode convertMethodNode(CodeMethodAnalysis codeMethodAnalysis) {
        MethodNode methodNode = new MethodNode();
        methodNode.setVid(codeMethodAnalysis.getMethodVid());
        methodNode.setSource(codeMethodAnalysis.getSource());
        methodNode.setClassName(codeMethodAnalysis.getClassName());
        methodNode.setInClassName(codeMethodAnalysis.getInClassName());
        methodNode.setMethodName(codeMethodAnalysis.getMethodName());
        methodNode.setAccess(codeMethodAnalysis.getAccess());
        Type javaParamType = new TypeToken<List<JavaParam>>() {
        }.getType();
        List<JavaParam> javaParams = JSON.parseObject(codeMethodAnalysis.getParams().toString(), javaParamType);
        methodNode.setParams(javaParams);
        Type javaReturnType = new TypeToken<JavaReturn>() {
        }.getType();
        JavaReturn javaReturn = JSON.parseObject(codeMethodAnalysis.getReturnInfo().toString(), javaReturnType);
        methodNode.setReturnInfo(javaReturn);
        Type javaAnnotationReturnType = new TypeToken<List<JavaAnnotation>>() {
        }.getType();
        List<JavaAnnotation> javaAnnotations = JSON.parseObject(codeMethodAnalysis.getAnnotations().toString(), javaAnnotationReturnType);
        methodNode.setAnnotations(javaAnnotations);
        methodNode.setExceptions(codeMethodAnalysis.getExceptions());
        methodNode.setStartLine(codeMethodAnalysis.getStartLine());
        methodNode.setEndLine(codeMethodAnalysis.getEndLine());
        methodNode.setChangeType(codeMethodAnalysis.getChangeType());
        methodNode.setComplexity(codeMethodAnalysis.getComplexity());
        Type changeLinesType = new TypeToken<List<Integer>>() {
        }.getType();
        List<Integer> changeLines = JSON.parseObject(codeMethodAnalysis.getChangeLines().toString(), changeLinesType);
        methodNode.setChangeLines(changeLines);
        Type commentLinesType = new TypeToken<List<Integer>>() {
        }.getType();
        List<Integer> commentLines = JSON.parseObject(codeMethodAnalysis.getCommentLines().toString(), commentLinesType);
        methodNode.setCommentLines(commentLines);
        return methodNode;
    }

    public static FieldNode convertFieldNode(CodeFieldAnalysis codeFieldAnalysis) {
        FieldNode fieldNode = new FieldNode();
        fieldNode.setVid(codeFieldAnalysis.getFieldVid());
        fieldNode.setSource(codeFieldAnalysis.getSource());
        fieldNode.setClassName(codeFieldAnalysis.getClassName());
        fieldNode.setInClassName(codeFieldAnalysis.getInClassName());
        fieldNode.setFieldName(codeFieldAnalysis.getFieldName());
        fieldNode.setAccess(codeFieldAnalysis.getAccess());
        Type javaAnnotationReturnType = new TypeToken<List<JavaAnnotation>>() {
        }.getType();
        List<JavaAnnotation> javaAnnotations = JSON.parseObject(codeFieldAnalysis.getAnnotations().toString(), javaAnnotationReturnType);
        fieldNode.setAnnotations(javaAnnotations);
        Type signaturesType = new TypeToken<List<String>>() {
        }.getType();
        List<String> signatures = JSON.parseObject(codeFieldAnalysis.getSignatures().toString(), signaturesType);
        fieldNode.setSignatures(signatures);
        fieldNode.setFieldType(codeFieldAnalysis.getFieldType());
        fieldNode.setValue(codeFieldAnalysis.getFieldValue());
        fieldNode.setChangeType(codeFieldAnalysis.getChangeType());
        Type changeLinesType = new TypeToken<List<Integer>>() {
        }.getType();
        List<Integer> changeLines = JSON.parseObject(codeFieldAnalysis.getChangeLines().toString(), changeLinesType);
        fieldNode.setChangeLines(changeLines);
        fieldNode.setStartLine(codeFieldAnalysis.getStartLine());
        fieldNode.setEndLine(codeFieldAnalysis.getEndLine());
        Type commentLinesType = new TypeToken<List<Integer>>() {
        }.getType();
        List<Integer> commentLines = JSON.parseObject(codeFieldAnalysis.getChangeLines().toString(), commentLinesType);
        fieldNode.setCommentLines(commentLines);
        return fieldNode;
    }

    public static FileData convertFileData(CodeClassAnalysis codeClassAnalysis) {
        FileData fileData = new FileData();
        fileData.setVid(codeClassAnalysis.getClassVid());
        fileData.setModuleName(codeClassAnalysis.getModuleName());
        fileData.setChangeType(codeClassAnalysis.getChangeType());
        fileData.setCheckType(codeClassAnalysis.getCheckType());
        fileData.setPath(codeClassAnalysis.getClassPath());
        fileData.setModuleName(codeClassAnalysis.getModuleName());
        fileData.setCommitCount(codeClassAnalysis.getCommitCount());
        return fileData;
    }

    public static FileData convertFileData(CodeFileAnalysis codeFileAnalysis) {
        FileData fileData = new FileData();
        fileData.setVid(codeFileAnalysis.getFileVid());
        fileData.setModuleName(codeFileAnalysis.getModuleName());
        fileData.setChangeType(codeFileAnalysis.getChangeType());
        fileData.setCheckType(codeFileAnalysis.getCheckType());
        fileData.setPath(codeFileAnalysis.getFilePath());
        fileData.setModuleName(codeFileAnalysis.getModuleName());
        return fileData;
    }
}
