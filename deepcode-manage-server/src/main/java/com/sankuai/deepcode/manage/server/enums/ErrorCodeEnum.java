package com.sankuai.deepcode.manage.server.enums;

import com.sankuai.deepcode.commons.ICodeEnum;
import lombok.Getter;

/**
 * 错误代码枚举类
 *
 * <AUTHOR>
 */
@Getter
public enum ErrorCodeEnum implements ICodeEnum {

    NOAUTH(401, "无权限"),
    DATA_NOT_EXISTS(404, "数据不存在"),

    FAILURE(500, "失败"),
    LIMIT(503, "限流"),
    SUCCESS(200, "成功"),

    DATA_EXISTS(301, "数据已存在"),

    COMING_LATTER(302, "数据正在处理中，请稍后重试"),

    NO_REFRESH(201, "不刷新");

    private int code;
    private String desc;

    ErrorCodeEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @Override
    public String toString() {
        return "ErrorCodeEnum{" +
                "code=" + code +
                ", desc='" + desc + '\'' +
                '}';
    }
}