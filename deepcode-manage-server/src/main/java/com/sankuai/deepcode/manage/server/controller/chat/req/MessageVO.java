package com.sankuai.deepcode.manage.server.controller.chat.req;

import com.fasterxml.jackson.annotation.*;
import com.sankuai.deepcode.ai.llm.openai.chat.Role;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * Package: com.sankuai.deepcode.manage.server.controller.chat.req
 * Description:
 *
 * <AUTHOR>
 * @since 2025/3/18 19:31
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
@Setter
@Getter
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class MessageVO {
    @NotBlank(message = "role不能为空")
    @Schema(description = "消息角色", example = "user", implementation = Role.class)
    @JsonProperty("role")
    private String role;

    @NotBlank(message = "content不能为空")
    @Schema(description = "消息内容")
    @JsonProperty("content")
    private List<ContentVO> content;

    @Schema(description = "消息名称")
    private String name;

    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonIgnoreProperties(ignoreUnknown = true)
    @Getter
    @Setter
    @ToString
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ContentVO {
        @Schema(description = "消息类型", example = "text", implementation = MessageType.class)
        @JsonProperty("type")
        MessageType type;
        @Schema(description = "消息文本", example = "你好")
        @JsonProperty("text")
        String text;


        @Schema(description = "媒体内容")
        @JsonProperty("media")
        @JsonTypeInfo(use = JsonTypeInfo.Id.NAME, property = "type")
        @JsonSubTypes({
                @JsonSubTypes.Type(value = ImagePayload.class, name = "IMAGE"),
                @JsonSubTypes.Type(value = FilePayLoad.class, name = "FILE"),
                @JsonSubTypes.Type(value = TextPayLoad.class, name = "TEXT")
        })
        private MediaContent media;

    }

    @JsonTypeInfo(use = JsonTypeInfo.Id.NAME, property = "type")
    public interface MediaContent {
        MessageType getType();

        ContentVO toContentVO();
    }

    @Setter
    @ToString
    public static class TextPayLoad implements MediaContent {
        @Schema(description = "文本内容")
        private final String text;

        @JsonCreator
        public TextPayLoad(@JsonProperty("text") String text) {
            this.text = text;
        }

        @Override
        public MessageType getType() {
            return MessageType.TEXT;
        }

        @Override
        public ContentVO toContentVO() {
            return new ContentVO(MessageType.TEXT, text, null);
        }
    }

    @Getter
    @ToString
    public static class ImagePayload implements MediaContent {
        @Schema(description = "图片URL")
        private final String url;
        @Schema(description = "图片名称")
        private final String name;
        @Schema(description = "Base64编码的图片数据")
        private final String base64Data;

        @JsonCreator
        public ImagePayload(
                @JsonProperty("url") String url,
                @JsonProperty("name") String name,
                @JsonProperty("base64Data") String base64Data
        ) {
            this.url = url;
            this.name = name;
            this.base64Data = base64Data;
        }

        @Override
        public MessageType getType() {
            return MessageType.IMAGE;
        }

        @Override
        public ContentVO toContentVO() {
            return new ContentVO(MessageType.IMAGE, null, this);
        }
    }

    @Getter
    @ToString
    public static class FilePayLoad implements MediaContent {
        @Schema(description = "文件ID")
        private final Long fileId;
        @Schema(description = "文件VID")
        private final String vid;
        @Schema(description = "文件名称")
        private final String name;
        @Schema(description = "文件路径")
        private final String path;
        @Schema(description = "文件URL")
        private final String url;
        @Schema(description = "开始位置")
        private final LineInfo start;
        @Schema(description = "结束位置")
        private final LineInfo end;

        @JsonCreator
        public FilePayLoad(
                @JsonProperty("fileId") Long fileId,
                @JsonProperty("vid") String vid,
                @JsonProperty("name") String name,
                @JsonProperty("path") String path,
                @JsonProperty("url") String url,
                @JsonProperty("start") LineInfo start,
                @JsonProperty("end") LineInfo end
        ) {
            this.fileId = fileId;
            this.vid = vid;
            this.name = name;
            this.path = path;
            this.url = url;
            this.start = start;
            this.end = end;
        }

        @Override
        public MessageType getType() {
            return MessageType.FILE;
        }

        @Override
        public ContentVO toContentVO() {
            return new ContentVO(MessageType.FILE, null, this);
        }
    }


    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonIgnoreProperties(ignoreUnknown = true)
    @ToString
    @Getter
    @Setter
    @NoArgsConstructor
    @AllArgsConstructor
    public static class LineInfo {
        @Schema(description = "行号", example = "1")
        Integer line;
        @Schema(description = "列号", example = "1")
        Integer column;
    }

}
