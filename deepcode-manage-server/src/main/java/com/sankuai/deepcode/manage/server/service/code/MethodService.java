package com.sankuai.deepcode.manage.server.service.code;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.deepcode.commons.DeepCodePreconditions;
import com.sankuai.deepcode.commons.MapUtils;
import com.sankuai.deepcode.commons.function.OverrideBinaryOperator;
import com.sankuai.deepcode.dao.domain.*;
import com.sankuai.deepcode.dao.po.InvokeLinePO;
import com.sankuai.deepcode.dao.service.CodeClassAnalysisService;
import com.sankuai.deepcode.dao.service.CodeFileAnalysisService;
import com.sankuai.deepcode.dao.service.CodeMethodAnalysisService;
import com.sankuai.deepcode.dao.service.MethodInvokeMethodService;
import com.sankuai.deepcode.manage.server.constants.CachingKeys;
import com.sankuai.deepcode.manage.server.controller.code.res.MethodDefinitionVO;
import com.sankuai.deepcode.manage.server.controller.code.res.MethodTopologyVO;
import com.sankuai.deepcode.manage.server.enums.ErrorCodeEnum;
import com.sankuai.deepcode.manage.server.mapper.code.MethodConvertor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.commons.lang3.tuple.Triple;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Package: com.sankuai.deepcode.manage.server.service.code
 * Description:
 *
 * <AUTHOR>
 * @since 2025/3/17 19:04
 */
@Service
@Slf4j
public class MethodService {
    private final CodeFileAnalysisService codeFileAnalysisService;
    private final CodeClassAnalysisService codeClassAnalysisService;
    private final CodeMethodAnalysisService codeMethodAnalysisService;
    private final MethodConvertor methodConvertor;
    private final MethodInvokeMethodService methodInvokeMethodService;

    public MethodService(
            CodeFileAnalysisService codeFileAnalysisService,
            CodeClassAnalysisService codeClassAnalysisService,
            CodeMethodAnalysisService codeMethodAnalysisService,
            MethodConvertor methodConvertor,
            MethodInvokeMethodService methodInvokeMethodService
    ) {
        this.codeFileAnalysisService = codeFileAnalysisService;
        this.codeClassAnalysisService = codeClassAnalysisService;
        this.codeMethodAnalysisService = codeMethodAnalysisService;
        this.methodConvertor = methodConvertor;
        this.methodInvokeMethodService = methodInvokeMethodService;
    }

    /**
     * 获取方法定义
     *
     * @param dcProject        项目信息
     * @param analysisItem     项目分析结果
     * @param codeFileAnalysis 调用方法的文件路径
     * @param line             调用方法的行号
     * @param methodName       调用方法的名称
     * @return 方法定义列表
     */
    @Cacheable(
            value = CachingKeys.METHOD_DEFINITIONS,
            key = "'project:' + #dcProject.id + ':itemId:' + #analysisItem.id + ':fileVid:' + #codeFileAnalysis.getFileVid() + ':line:' + #line + ':methodName:' + #methodName"
    )
    public List<MethodDefinitionVO> getMethodDefinitions(DcProject dcProject, CodeAnalysisItem analysisItem, CodeFileAnalysis codeFileAnalysis, Integer line, String methodName) {


        // 2. 查询文件对应的 类信息
        // FIXME: 目前仅按Java适配，其他语言还需要改造
        List<CodeClassAnalysis> classAnalysis = codeClassAnalysisService.getByItemIdAndFileVid(analysisItem.getId(), codeFileAnalysis.getFileVid());
        DeepCodePreconditions.checkBizArgument(CollectionUtils.isNotEmpty(classAnalysis), ErrorCodeEnum.DATA_NOT_EXISTS, "类路径不存在，请确认类路径是否正确");

        // 3. 查找该类中，所有的方法定义
        List<CodeMethodAnalysis> codeMethodAnalyses = codeMethodAnalysisService.getByItemAndClassNames(analysisItem.getId(), classAnalysis.stream().map(CodeClassAnalysis::getClassName).collect(Collectors.toList()));
        List<MethodInvokeMethod> methodInvokeMethods = methodInvokeMethodService.getByItemAndMethodVids(analysisItem.getId(), codeMethodAnalyses.stream().map(CodeMethodAnalysis::getMethodVid).collect(Collectors.toList()));
        List<MethodInvokeMethod> exactlyInvokeMethods = methodInvokeMethods.stream().filter(method -> {
            List<InvokeLinePO> invokeLinePos = method.getInvokeParams();
            return invokeLinePos.stream().anyMatch(i -> Objects.equals(i.getInvokeLine(), line));
        }).collect(Collectors.toList());
        DeepCodePreconditions.checkBizArgument(CollectionUtils.isNotEmpty(exactlyInvokeMethods), ErrorCodeEnum.DATA_NOT_EXISTS, "方法调用信息不存在，请确认方法调用信息是否正确");

        // 4. 查询被调用方法定义
        List<String> targetMethodVids = exactlyInvokeMethods.stream().map(MethodInvokeMethod::getTarget).collect(Collectors.toList());
        List<CodeMethodAnalysis> targetMethods = codeMethodAnalysisService.getByMethodVids(analysisItem.getId(), targetMethodVids);

        // 5. 组装被调用方法、类、文件之间的关系
        List<Triple<CodeMethodAnalysis, CodeClassAnalysis, CodeFileAnalysis>> methodClassFileInfo = getMethodClassFileInfo(analysisItem, targetMethods);

        return methodClassFileInfo.stream().map(t -> methodConvertor.convert2DefinitionVO(t.getLeft(), t.getMiddle(), t.getRight())).collect(Collectors.toList());
    }

    private List<Triple<CodeMethodAnalysis, CodeClassAnalysis, CodeFileAnalysis>> getMethodClassFileInfo(CodeAnalysisItem item, List<CodeMethodAnalysis> targetMethods) {
        List<Triple<CodeMethodAnalysis, CodeClassAnalysis, CodeFileAnalysis>> methodClassFileInfo = Lists.newArrayList();
        if (CollectionUtils.isEmpty(targetMethods)) {
            return methodClassFileInfo;
        }
        List<CodeClassAnalysis> codeClassAnalyses = codeClassAnalysisService.getByClassNames(item.getId(), targetMethods.stream().map(CodeMethodAnalysis::getClassName).collect(Collectors.toList()));
        List<CodeFileAnalysis> codeFileAnalyses = codeFileAnalysisService.getByItemAndVids(item.getId(), codeClassAnalyses.stream().map(CodeClassAnalysis::getFileVid).collect(Collectors.toList()));

        Map<String, CodeClassAnalysis> classAnalysisMap = MapUtils.toMap(codeClassAnalyses, CodeClassAnalysis::getClassName, Function.identity(), OverrideBinaryOperator::keepFirst);
        Map<String, CodeFileAnalysis> fileAnalysisMap = MapUtils.toMap(codeFileAnalyses, CodeFileAnalysis::getFileVid, Function.identity(), OverrideBinaryOperator::keepFirst);
        for (CodeMethodAnalysis method : targetMethods) {
            CodeClassAnalysis classAnalysis = classAnalysisMap.get(method.getClassName());
            CodeFileAnalysis fileAnalysis = classAnalysis == null ? null : fileAnalysisMap.get(classAnalysis.getFileVid());
            methodClassFileInfo.add(Triple.of(method, classAnalysis, fileAnalysis));
        }
        return methodClassFileInfo;
    }

    @CacheEvict(value = CachingKeys.METHOD_DEFINITIONS, allEntries = true)
    public void clearMethodDefinitionCache() {
        log.info(">>> clearMethodDefinitionCache: {}", CachingKeys.METHOD_DEFINITIONS);
    }



    /**
     * 获取方法【上游】调用信息
     *
     * @param dcProject    项目信息
     * @param analysisItem 项目分析结果
     * @param fileAnalysis 调用方法的文件路径
     * @param line         方法定义的行号
     * @param methodName   调用方法的名称
     * @return 方法调用列表
     */
    @Cacheable(
            value = CachingKeys.METHOD_INVOCATIONS,
            key = "'project:' + #dcProject.id + ':itemId:' + #analysisItem.id + ':fileVid:' + #fileAnalysis.getFileVid() + ':line:' + #line + ':methodName:' + #methodName"
    )
    public List<MethodDefinitionVO> getInvokedMethods(DcProject dcProject, CodeAnalysisItem analysisItem, CodeFileAnalysis fileAnalysis, Integer line, String methodName) {
        // 1. 查询文件分析
        Optional<CodeFileAnalysis> codeFileAnalysis = codeFileAnalysisService.getByItemIdAndFilePath(analysisItem.getId(), fileAnalysis.getFilePath());
        DeepCodePreconditions.checkBizArgument(codeFileAnalysis.isPresent(), ErrorCodeEnum.DATA_NOT_EXISTS, "文件路径不存在，请确认文件路径是否正确");

        // 2. 查询文件对应的 类信息
        // FIXME: 目前仅按Java适配，其他语言还需要改造
        List<CodeClassAnalysis> classAnalysis = codeClassAnalysisService.getByItemIdAndFileVid(analysisItem.getId(), codeFileAnalysis.get().getFileVid());
        DeepCodePreconditions.checkBizArgument(CollectionUtils.isNotEmpty(classAnalysis), ErrorCodeEnum.DATA_NOT_EXISTS, "类路径不存在，请确认类路径是否正确");

        // 3. 查找该类中，所有的方法定义
        List<CodeMethodAnalysis> codeMethodAnalyses = codeMethodAnalysisService.getByItemAndClassNames(analysisItem.getId(), classAnalysis.stream().map(CodeClassAnalysis::getClassName).collect(Collectors.toList()));
        Optional<CodeMethodAnalysis> targetMethod = codeMethodAnalyses.stream()
                // 过滤出 方法名、行号 完全匹配的方法
                .filter(m -> Objects.equals(m.getMethodName(), methodName) && Objects.equals(m.getStartLine(), line))
                .findFirst();
        DeepCodePreconditions.checkBizArgument(targetMethod.isPresent(), ErrorCodeEnum.DATA_NOT_EXISTS, "方法定义不存在，请确认方法定义是否正确");

        List<MethodInvokeMethod> methodInvokeMethods = methodInvokeMethodService.getByTargetMethodVid(analysisItem.getId(), targetMethod.get().getMethodVid());
        if (CollectionUtils.isEmpty(methodInvokeMethods)) {
            // 不存在调用关系时，返回空列表
            return Lists.newArrayList();
        }

        // 4. 找到调用传入方法的方法定义
        List<String> sourceMethodVids = methodInvokeMethods.stream().map(MethodInvokeMethod::getSource).collect(Collectors.toList());
        Map<String, Pair<CodeMethodAnalysis, CodeFileAnalysis>> methodFileMap = getCodeFileAnalysisByMethodVids(analysisItem, sourceMethodVids);

        List<MethodDefinitionVO> methodDefinitionVos = Lists.newArrayList();
        for (MethodInvokeMethod methodInvokeMethod : methodInvokeMethods) {
            for (InvokeLinePO invokeLinePo : methodInvokeMethod.getInvokeParams()) {
                // 按调用行，组织跳转信息
                methodDefinitionVos.add(
                        methodConvertor.convert2InvokedVO(
                                methodFileMap.get(methodInvokeMethod.getSource()).getRight(),
                                invokeLinePo,
                                methodFileMap.get(methodInvokeMethod.getSource()).getLeft()
                        )
                );
            }
        }

        return methodDefinitionVos;
    }

    /**
     * 根据方法VID列表, 获取文件对应文件分析列表
     *
     * @param item       项目分析结果
     * @param methodVids 方法VID列表
     * @return 方法VID -> 文件分析
     */
    public Map<String, Pair<CodeMethodAnalysis, CodeFileAnalysis>> getCodeFileAnalysisByMethodVids(CodeAnalysisItem item, List<String> methodVids) {
        if (CollectionUtils.isEmpty(methodVids)) {
            return Maps.newHashMap();
        }
        List<CodeMethodAnalysis> codeMethodAnalyses = codeMethodAnalysisService.getByMethodVids(item.getId(), methodVids);
        if (CollectionUtils.isEmpty(codeMethodAnalyses)) {
            return Maps.newHashMap();
        }
        List<CodeClassAnalysis> codeClassAnalyses = codeClassAnalysisService.getByClassNames(item.getId(), codeMethodAnalyses.stream().map(CodeMethodAnalysis::getClassName).collect(Collectors.toList()));
        if (CollectionUtils.isEmpty(codeClassAnalyses)) {
            return Maps.newHashMap();
        }
        List<CodeFileAnalysis> codeFileAnalyses = codeFileAnalysisService.getByItemAndVids(item.getId(), codeClassAnalyses.stream().map(CodeClassAnalysis::getFileVid).collect(Collectors.toList()));
        if (CollectionUtils.isEmpty(codeFileAnalyses)) {
            return Maps.newHashMap();
        }

        Map<String, CodeClassAnalysis> codeClassAnalysisMap = codeClassAnalyses.stream().collect(Collectors.toMap(CodeClassAnalysis::getClassName, Function.identity(), OverrideBinaryOperator::keepFirst));
        Map<String, CodeFileAnalysis> codeFileAnalysisMap = codeFileAnalyses.stream().collect(Collectors.toMap(CodeFileAnalysis::getFileVid, Function.identity(), OverrideBinaryOperator::keepFirst));

        // 完成方法-》方法所在文件的映射
        return codeMethodAnalyses.stream().collect(Collectors.toMap(CodeMethodAnalysis::getMethodVid, m -> {
            CodeClassAnalysis codeClassAnalysis = codeClassAnalysisMap.get(m.getClassName());
            if (codeClassAnalysis == null) {
                // 未找到方法定义的文件
                log.warn("方法未找到方法定义的文件: {}", m);
                return Pair.of(m, null);
            }
            return Pair.of(m, codeFileAnalysisMap.get(codeClassAnalysis.getFileVid()));
        }, OverrideBinaryOperator::keepFirst));
    }

    @CacheEvict(value = CachingKeys.METHOD_INVOCATIONS, allEntries = true)
    public void clearMethodInvocationCache() {
        log.info(">>> clearMethodInvocationCache: {}", CachingKeys.METHOD_INVOCATIONS);
    }


    @Cacheable(
            value = CachingKeys.METHOD_TOPOLOGY,
            key = "'itemId:' + #analysisItem.id + ':methodVid:' + #methodAnalysis.getMethodVid() + ':type:' + #type"
    )
    public MethodTopologyVO getMethodInvokeTopology(CodeAnalysisItem analysisItem, CodeMethodAnalysis methodAnalysis, Integer type) {
        List<MethodInvokeMethod> methodInvokeMethods = methodInvokeMethodService.recursivelyGetMethodInvokes(analysisItem.getId(), methodAnalysis.getMethodVid(), type);
        if (CollectionUtils.isEmpty(methodInvokeMethods)) {
            // 没有调用关系（孤岛方法），只有当前节点，没有边
            // 获取当前方法对应的类分析信息
            List<CodeClassAnalysis> classAnalyses = codeClassAnalysisService.getByClassNames(analysisItem.getId(),
                    Collections.singletonList(methodAnalysis.getClassName()));
            Map<String, CodeClassAnalysis> classAnalysisMap = classAnalyses.stream()
                    .collect(Collectors.toMap(CodeClassAnalysis::getClassName, Function.identity(), OverrideBinaryOperator::keepFirst));

            return new MethodTopologyVO().setNodes(
                    methodConvertor.convertNodeList(Collections.singletonList(methodAnalysis), classAnalysisMap)
            );
        }
        List<CodeMethodAnalysis> allMethodAnalyses = codeMethodAnalysisService.getByMethodVids(
                analysisItem.getId(),
                Lists.newArrayList(
                        methodInvokeMethods.stream().map(m -> Arrays.asList(m.getSource(), m.getTarget())).flatMap(List::stream).collect(Collectors.toSet())
                )
        );

        // 获取所有方法对应的类分析信息
        List<String> classNames = allMethodAnalyses.stream()
                .map(CodeMethodAnalysis::getClassName)
                .distinct()
                .collect(Collectors.toList());
        List<CodeClassAnalysis> classAnalyses = codeClassAnalysisService.getByClassNames(analysisItem.getId(), classNames);
        Map<String, CodeClassAnalysis> classAnalysisMap = classAnalyses.stream()
                .collect(Collectors.toMap(CodeClassAnalysis::getClassName, Function.identity(), OverrideBinaryOperator::keepFirst));

        return new MethodTopologyVO()
                .setEdges(methodConvertor.convertEdgeList(methodInvokeMethods))
                .setNodes(methodConvertor.convertNodeList(allMethodAnalyses, classAnalysisMap));
    }

    @CacheEvict(value = CachingKeys.METHOD_TOPOLOGY, allEntries = true)
    public void clearMethodTopologyCache() {
        log.info(">>> clearMethodTopologyCache: {}", CachingKeys.METHOD_TOPOLOGY);
    }
}
