package com.sankuai.deepcode.manage.server.service;

import com.github.javaparser.StaticJavaParser;
import com.github.javaparser.ast.CompilationUnit;
import com.sankuai.deepcode.manage.server.model.code.CodeRunRes;
import com.sankuai.deepcode.manage.server.tools.MyClassLoader;
import com.sankuai.inf.octo.mns.util.ProcessInfoUtil;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.io.PrintWriter;
import java.io.StringWriter;
import java.lang.reflect.Method;

@Service
public class CodeRunService {

    private static final Logger LOGGER = LoggerFactory.getLogger(CodeRunService.class);

    public CodeRunRes run(String javaCode, String methodVid) {
        CodeRunRes res = new CodeRunRes();
        MyClassLoader classLoader = null;
        String result = "";
        try {
            long id = System.currentTimeMillis();
            CompilationUnit cu = StaticJavaParser.parse(javaCode);
            cu.setPackageDeclaration("com.sankuai.deepcode.manage.server.tools.tools");
            cu.getType(0).setName("Task" + id);
            javaCode = cu.toString();

            ClassLoader currentClassLoader = CodeRunService.class.getClassLoader();
            classLoader = new MyClassLoader( currentClassLoader);
            classLoader.loadClass("com.sankuai.deepcode.ast.common.model.java.MethodNode");
            classLoader.loadClass("com.sankuai.deepcode.manage.server.tools.AgentTools");
            String classpath = "";
            if (!ProcessInfoUtil.isMac()) {
                classpath = System.getProperty("java.class.path") + ":/opt/meituan/deepcode_ast_common";
            }
            classLoader.setClassPath(classpath);

            Class<?> compileAndLoad = classLoader.compileAndLoad("com.sankuai.deepcode.manage.server.tools.tools.Task" + id, javaCode);
            Object instance = compileAndLoad.getDeclaredConstructor().newInstance();
            Method method = compileAndLoad.getMethod("runJava", String.class);
            if (StringUtils.isEmpty(methodVid)) {
                methodVid = "";
            }
            result = (String) method.invoke(instance, methodVid);
        } catch (Exception e) {
            res.setException(true);
            StringWriter sw = new StringWriter();
            PrintWriter pw = new PrintWriter(sw);
            e.printStackTrace(pw);
            String stackTrace = sw.toString();
            if (StringUtils.isNotEmpty(stackTrace) && stackTrace.length() > 512) {
                res.setExceptionStr(stackTrace.substring(0, 512));
            } else {
                res.setExceptionStr(stackTrace);
            }
        } finally {
            classLoader.clear();
        }
        res.setRes(result);
        return res;
    }

}
