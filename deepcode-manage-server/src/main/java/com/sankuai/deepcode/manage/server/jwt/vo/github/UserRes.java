package com.sankuai.deepcode.manage.server.jwt.vo.github;

import lombok.Data;

@Data
public class UserRes {

    private String login;
    private int id;
    private String node_id;
    private String avatar_url;
    private String gravatar_id;
    private String url;
    private String html_url;
    private String followers_url;
    private String following_url;
    private String gists_url;
    private String starred_url;
    private String subscriptions_url;
    private String organizations_url;
    private String repos_url;
    private String events_url;
    private String received_events_url;
    private String type;
    private String user_view_type;
    private boolean site_admin;
    private String name;
    private String company;
    private String blog;
    private String location;
    private String email;
    private String hireable;
    private String bio;
    private String twitter_username;
    private String notification_email;
    private int public_repos;
    private int public_gists;
    private int followers;
    private int following;
    private String created_at;
    private String updated_at;

    //{
    //    "login": "shimine",
    //    "id": 8346357,
    //    "node_id": "MDQ6VXNlcjgzNDYzNTc=",
    //    "avatar_url": "https://avatars.githubusercontent.com/u/8346357?v=4",
    //    "gravatar_id": "",
    //    "url": "https://api.github.com/users/shimine",
    //    "html_url": "https://github.com/shimine",
    //    "followers_url": "https://api.github.com/users/shimine/followers",
    //    "following_url": "https://api.github.com/users/shimine/following{/other_user}",
    //    "gists_url": "https://api.github.com/users/shimine/gists{/gist_id}",
    //    "starred_url": "https://api.github.com/users/shimine/starred{/owner}{/repo}",
    //    "subscriptions_url": "https://api.github.com/users/shimine/subscriptions",
    //    "organizations_url": "https://api.github.com/users/shimine/orgs",
    //    "repos_url": "https://api.github.com/users/shimine/repos",
    //    "events_url": "https://api.github.com/users/shimine/events{/privacy}",
    //    "received_events_url": "https://api.github.com/users/shimine/received_events",
    //    "type": "User",
    //    "user_view_type": "public",
    //    "site_admin": false,
    //    "name": null,
    //    "company": null,
    //    "blog": "",
    //    "location": null,
    //    "email": null,
    //    "hireable": null,
    //    "bio": null,
    //    "twitter_username": null,
    //    "notification_email": null,
    //    "public_repos": 11,
    //    "public_gists": 0,
    //    "followers": 3,
    //    "following": 0,
    //    "created_at": "2014-08-04T01:13:06Z",
    //    "updated_at": "2025-01-20T03:22:55Z"
    //}
}
