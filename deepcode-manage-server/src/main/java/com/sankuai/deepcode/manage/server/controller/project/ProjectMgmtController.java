package com.sankuai.deepcode.manage.server.controller.project;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sankuai.deepcode.commons.CommonResult;
import com.sankuai.deepcode.commons.DeepCodePreconditions;
import com.sankuai.deepcode.dao.domain.CodeAnalysisItem;
import com.sankuai.deepcode.dao.domain.DcProject;
import com.sankuai.deepcode.dao.service.CodeAnalysisItemService;
import com.sankuai.deepcode.dao.service.DcProjectService;
import com.sankuai.deepcode.manage.server.controller.project.req.ProjectAnalyseRequest;
import com.sankuai.deepcode.manage.server.controller.project.res.CodeAnalysisItemVO;
import com.sankuai.deepcode.manage.server.controller.project.res.ProjectAnalysisVO;
import com.sankuai.deepcode.manage.server.controller.project.res.ProjectVO;
import com.sankuai.deepcode.manage.server.enums.ErrorCodeEnum;
import com.sankuai.deepcode.manage.server.jwt.utils.UserUtils;
import com.sankuai.deepcode.manage.server.mapper.project.ProjectConvertor;
import com.sankuai.deepcode.manage.server.service.project.ProjectInitService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * Package: com.sankuai.deepcode.manage.server.controller.project
 * Description:
 *
 * <AUTHOR>
 * @since 2025/3/3 16:30
 */
@Tag(name = "项目管理")
@RestController
@RequestMapping("/project")
@Slf4j
public class ProjectMgmtController {
    private final DcProjectService dcProjectService;
    private final ProjectConvertor projectConvertor;
    private final ProjectInitService projectInitService;
    private final CodeAnalysisItemService codeAnalysisItemService;

    public ProjectMgmtController(
            DcProjectService dcProjectService,
            ProjectConvertor projectConvertor,
            ProjectInitService projectInitService,
            CodeAnalysisItemService codeAnalysisItemService
    ) {
        this.dcProjectService = dcProjectService;
        this.projectConvertor = projectConvertor;
        this.projectInitService = projectInitService;
        this.codeAnalysisItemService = codeAnalysisItemService;
    }


    @Operation(
            summary = "项目分析",
            description = "新导入git项目进行分析"
    )
    @PostMapping("/analyse")
    CommonResult<ProjectAnalysisVO> analyseProject(
            @RequestBody @Validated ProjectAnalyseRequest analyseRequest
    ) {
        DcProject project = new DcProject();
        project.setProjectName(analyseProjectName(analyseRequest.getGitUrl()));
        project.setGitUrl(analyseRequest.getGitUrl());
        project.setUserId(UserUtils.getUserId());
        project.setSourceBranch(analyseRequest.getSourceBranch());
        project.setTargetBranch(analyseRequest.getTargetBranch());
        project.setCreateTime(LocalDateTime.now());
        project.setUpdateTime(LocalDateTime.now());
        project.setValid(true);

        dcProjectService.save(project);

        List<CodeAnalysisItem> existsItems = codeAnalysisItemService.getItemsByGitUrlAndBranch(
                analyseRequest.getGitUrl(), analyseRequest.getSourceBranch(), analyseRequest.getTargetBranch());

        if (CollectionUtils.isEmpty(existsItems)) {
            // 如果不存在同工程，已经分析的分支时，直接初始化一个
            projectInitService.initializeProjectTasks(project);
            dcProjectService.updateById(project);
            return CommonResult.success(
                    new ProjectAnalysisVO().setProject(
                            projectConvertor.convertProject(project, codeAnalysisItemService.getById(project.getItemId()))
                    ));
        }

        // 如果存在已经分析过的历史数据，需要针对列表中给定的数据进行绑定
        return CommonResult.fail(
                new ProjectAnalysisVO().setProject(projectConvertor.convertProject(project, null))
                        .setAnalysisItems(projectConvertor.convertAnalysisItemList(existsItems)),
                ErrorCodeEnum.DATA_EXISTS, "存在相同工程、分支已分析的结果"
        );
    }

    private String analyseProjectName(String gitUrl) {
        if (gitUrl == null || gitUrl.trim().isEmpty()) {
            return "unknown";
        }

        String url = gitUrl.trim();

        // 移除http(s)://或git://前缀
        if (url.contains("://")) {
            url = url.substring(url.indexOf("://") + 3);
        }

        // 移除用户认证信息
        if (url.contains("@")) {
            url = url.substring(url.indexOf("@") + 1);
        }

        // 移除域名部分
        int firstSlash = url.indexOf("/");
        if (firstSlash >= 0) {
            url = url.substring(firstSlash + 1);
        }

        // 移除.git后缀
        if (url.toLowerCase().endsWith(".git")) {
            url = url.substring(0, url.length() - 4);
        }

        // 获取最后一段作为项目名
        int lastSlash = url.lastIndexOf("/");
        if (lastSlash >= 0) {
            url = url.substring(lastSlash + 1);
        }

        return url.isEmpty() ? "unknown" : url;
    }


    @Operation(
            summary = "项目列表查询", description = "个人近期上传分析的项目列表",
            parameters = {
                    @Parameter(name = "pageNum", description = "页码", required = true, example = "1"),
                    @Parameter(name = "pageSize", description = "每页数量", example = "10")
            }
    )
    @GetMapping("/list")
    CommonResult<IPage<ProjectVO>> queryProjectList(
            @RequestParam(value = "pageNum", required = false, defaultValue = "1") Integer pageNum,
            @RequestParam(value = "pageSize", required = false, defaultValue = "10") Integer pageSize
    ) {
        log.info("queryProjectList: {}", UserUtils.getUser());
        Page<DcProject> projectList = dcProjectService.getProjectListByUser(UserUtils.getUserId(), pageNum, pageSize);
        return CommonResult.success(projectList.convert(p -> projectConvertor.convertProject(p, codeAnalysisItemService.getById(p.getItemId()))));
    }

    @Operation(summary = "项目删除", description = "删除个人上传的项目", parameters = {
            @Parameter(name = "projectId", description = "需要删除的项目ID", required = true, example = "1")
    })
    @DeleteMapping("/{projectId}")
    CommonResult<String> deleteProject(@PathVariable("projectId") Long projectId) {
        DcProject project = dcProjectService.getById(projectId);
        DeepCodePreconditions.checkBizArgument(project != null, "项目不存在");

        // TODO: 需要验证用户身份，是否有删除权限, 或者有权限组
        if (!Objects.equals(project.getUserId(), UserUtils.getUserId())) {
            return CommonResult.fail("没有权限删除该项目");
        }

        dcProjectService.removeById(projectId);
        return CommonResult.success();
    }

    @Operation(summary = "项目详情", description = "查询项目详情", parameters = {
            @Parameter(name = "projectId", description = "需要查询的项目ID", required = true, example = "1")
    })
    @GetMapping("/{projectId}")
    CommonResult<ProjectVO> queryProjectDetail(@PathVariable("projectId") Long projectId) {
        DcProject project = dcProjectService.getById(projectId);
        DeepCodePreconditions.checkBizArgument(project != null, "项目不存在");

        return CommonResult.success(projectConvertor.convertProject(project, codeAnalysisItemService.getById(project.getItemId())));
    }

    @Operation(summary = "项目分析项列表", description = "如果项目没有关联分析任务，可以依据项目查询可关联的分析项列表", parameters = {
            @Parameter(name = "projectId", description = "需要查询的项目ID", required = true, example = "1")
    })
    @GetMapping("/{projectId}/analysisItems")
    CommonResult<List<CodeAnalysisItemVO>> queryAnalysisItems(@PathVariable("projectId") Long projectId) {
        DcProject project = dcProjectService.getById(projectId);
        DeepCodePreconditions.checkBizArgument(project != null, "项目不存在");
        List<CodeAnalysisItem> analysisItemList = codeAnalysisItemService.getItemsByGitUrlAndBranch(
                project.getGitUrl(), project.getSourceBranch(), project.getTargetBranch());

        return CommonResult.success(analysisItemList.stream().map(projectConvertor::convertAnalysisItem).collect(Collectors.toList()));
    }

    @Operation(summary = "强制创建项目的分析条目")
    @PostMapping("/{projectId}/analysisItems")
    CommonResult<ProjectVO> associateAnalysisItem(
            @PathVariable("projectId") Long projectId
    ) {
        DcProject project = dcProjectService.getById(projectId);
        DeepCodePreconditions.checkBizArgument(project != null, "项目不存在");
        DeepCodePreconditions.checkBizArgument(Objects.equals(project.getUserId(), UserUtils.getUserId()), "没有权限关联项目");

        // DeepCodePreconditions.checkBizArgument(project.getItemId() == null || project.getItemId() <= 0, "项目已经关联过分析项了");
        projectInitService.initializeProjectTasks(project);
        dcProjectService.updateById(project);
        return CommonResult.success(
                projectConvertor.convertProject(project, codeAnalysisItemService.getById(project.getItemId()))
        );
    }

    @Operation(summary = "项目关联分析项", description = "将项目关联到指定的分析项上", parameters = {
            @Parameter(name = "projectId", description = "需要查询的项目ID", required = true, example = "1"),
            @Parameter(name = "analysisItemId", description = "需要关联的分析项ID", required = true, example = "1")
    })
    @PostMapping("/{projectId}/analysisItems/{analysisItemId}")
    CommonResult<ProjectVO> associateAnalysisItem(
            @PathVariable("projectId") Long projectId,
            @PathVariable("analysisItemId") Long analysisItemId) {
        DcProject project = dcProjectService.getById(projectId);
        DeepCodePreconditions.checkBizArgument(project != null, "项目不存在");
        DeepCodePreconditions.checkBizArgument(Objects.equals(project.getUserId(), UserUtils.getUserId()), "没有权限关联项目");

        DeepCodePreconditions.checkBizArgument(project.getItemId() == null || project.getItemId() <= 0, "项目已经关联过分析项了");

        CodeAnalysisItem analysisItem = codeAnalysisItemService.getById(analysisItemId);
        DeepCodePreconditions.checkBizArgument(analysisItem != null, "分析项不存在");

        DeepCodePreconditions.checkBizArgument(Objects.equals(analysisItem.getGitUrl(), project.getGitUrl()), "当前项目与分析项目仓库地址不匹配");
        DeepCodePreconditions.checkBizArgument(Objects.equals(analysisItem.getFromBranch(), project.getSourceBranch()), "当前项目与分析项目源分支不匹配");
        DeepCodePreconditions.checkBizArgument(Objects.equals(analysisItem.getToBranch(), project.getTargetBranch()), "当前项目与分析项目目标分支不匹配");

        boolean result = dcProjectService.updateProjectItem(projectId, analysisItemId);
        return result ? CommonResult.success(
                projectConvertor.convertProject(project, codeAnalysisItemService.getById(analysisItemId)))
                : CommonResult.fail("关联失败");
    }
}
