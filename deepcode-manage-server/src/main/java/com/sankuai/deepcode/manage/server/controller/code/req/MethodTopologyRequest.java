package com.sankuai.deepcode.manage.server.controller.code.req;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

/**
 * Package: com.sankuai.deepcode.manage.server.controller.code.req
 * Description:
 *
 * <AUTHOR>
 * @since 2025/6/3 21:16
 */
@Setter
@Getter
@ToString
public class MethodTopologyRequest {
    @Schema(description = "项目ID", example = "1")
    @NotNull(message = "项目ID不能为空")
    private Long projectId;

    @Schema(description = "文件Vid", example = "123456")
    @NotEmpty(message = "文件Vid不能为空")
    private String fileVid;

    @Schema(description = "方法Vid", example = "123456")
    @NotEmpty(message = "方法Vid不能为空")
    private String methodVid;

    @Schema(description = "类型, 1-调用关系（向下），2-引用关系（向上）", example = "1")
    @NotNull(message = "类型不能为空")
    private Integer type;
}
