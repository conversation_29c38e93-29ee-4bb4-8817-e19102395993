package com.sankuai.deepcode.manage.server.controller;


import com.sankuai.deepcode.ast.common.model.java.ClassNode;
import com.sankuai.deepcode.ast.common.model.java.FieldNode;
import com.sankuai.deepcode.ast.common.model.java.MethodNode;
import com.sankuai.deepcode.commons.CommonResult;
import com.sankuai.deepcode.dao.domain.*;
import com.sankuai.deepcode.dao.mapper.CodeAnalysisItemMapper;
import com.sankuai.deepcode.dao.service.*;
import com.sankuai.deepcode.manage.server.enums.ErrorCodeEnum;
import com.sankuai.deepcode.manage.server.model.codeReview.CheckShowRes;
import com.sankuai.deepcode.manage.server.model.codeReview.CodeReviewRes;
import com.sankuai.deepcode.manage.server.model.file.*;
import com.sankuai.deepcode.manage.server.service.CodeViewService;
import com.sankuai.deepcode.manage.server.service.GraphService;
import com.sankuai.deepcode.manage.server.util.ConvertNode;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/file")
public class FileControllor {

    private static final Logger LOGGER = LoggerFactory.getLogger(FileControllor.class);

    @Autowired
    private CodeMethodAnalysisService codeMethodAnalysisService;
    @Autowired
    private CodeAnalysisItemService codeAnalysisItemService;
    @Autowired
    private CodeClassAnalysisService codeClassAnalysisService;
    @Autowired
    private CodeFieldAnalysisService codeFieldAnalysisService;
    @Autowired
    private GraphService graphService;
    @Autowired
    private CodeViewService codeViewService;

    @GetMapping("/getAllFiles")
    public CommonResult getAllFiles(@RequestParam long itemId) {
        CodeAnalysisItem codeAnalysisItem = codeAnalysisItemService.getById(itemId);
        if (null == codeAnalysisItem) {
            return CommonResult.fail(ErrorCodeEnum.FAILURE.getCode(), "未找到对应item");
        }

        boolean isDiff = false;
        if (codeAnalysisItem.isDiff()) {
            isDiff = true;
        }
        AllFilesRes allFilesRes = codeViewService.getAllFiles(codeAnalysisItem.getId(), isDiff);
        return CommonResult.success(allFilesRes);
    }


    @GetMapping("/getGraphByFile")
    public CommonResult getGraphByFile(@RequestParam long itemId, @RequestParam String vid) {
        FileGraphRes fileGraphRes = null;
        try {
            fileGraphRes = new FileGraphRes();
            graphService.getGraphByFile(itemId, vid, fileGraphRes, new HashSet<>());
        } catch (Exception e) {
            LOGGER.error("getGraphByFile error", e);
            return CommonResult.fail(ErrorCodeEnum.FAILURE, "getGraphByFile error:" + e.getMessage());
        }
        return CommonResult.success(fileGraphRes);
    }


    @GetMapping("/getInfoByFile")
    public CommonResult getInfoByFile(@RequestParam long itemId, @RequestParam String vid) {
        GetInfoByFileRes getInfoByFileRes = new GetInfoByFileRes();
        try {
            CodeClassAnalysis codeClassAnalysis = codeClassAnalysisService.getByClassVid(itemId, vid);
            if (null != codeClassAnalysis) {
                List<CodeMethodAnalysis> codeMethodAnalyses = codeMethodAnalysisService.getByItemAndClassName(itemId, codeClassAnalysis.getClassName());
                for (CodeMethodAnalysis codeMethodAnalysis : codeMethodAnalyses) {
                    MethodNode methodNode = ConvertNode.convertMethodNode(codeMethodAnalysis);
                    getInfoByFileRes.getMethodNodes().add(methodNode);
                }
                List<CodeFieldAnalysis> codeFieldAnalyses = codeFieldAnalysisService.getByClassName(itemId, codeClassAnalysis.getClassName());
                for (CodeFieldAnalysis codeFieldAnalysis : codeFieldAnalyses) {
                    FieldNode fieldNode = ConvertNode.convertFieldNode(codeFieldAnalysis);
                    getInfoByFileRes.getFieldNodes().add(fieldNode);
                }
            }
            return CommonResult.success(getInfoByFileRes);
        } catch (Exception e) {
            LOGGER.error("getGraphByFile error", e);
            return CommonResult.fail(ErrorCodeEnum.FAILURE, "getInfoByFile error:" + e.getMessage());
        }
    }


    @GetMapping("/getCodeViewByVid")
    public CommonResult getCodeViewByVid(@RequestParam long itemId, @RequestParam String vid) {
        try {
            CodeReviewRes res = codeViewService.initCodeView(itemId, vid);
            return CommonResult.success(res);
        } catch (Exception e) {
            LOGGER.error("getCodeViewByVid e:", e);
            return CommonResult.fail(ErrorCodeEnum.FAILURE, "查询es失败，请联系管理员");
        }
    }


    @PostMapping("/getTreeByVid")
    public CommonResult getTreeByVid(@RequestBody TreeByVidParam treeByVidParam) {
        if (null == treeByVidParam.getItemId() || 0 == treeByVidParam.getItemId()) {
            return CommonResult.fail(ErrorCodeEnum.FAILURE, "itemId不能为空");
        }
        if (StringUtils.isEmpty(treeByVidParam.getVid())) {
            return CommonResult.fail(ErrorCodeEnum.FAILURE, "vid不能为空");
        }
        if (treeByVidParam.getStep() > 30) {
            return CommonResult.fail(ErrorCodeEnum.FAILURE, "step不能超过30");
        }
        if (!"method".equals(treeByVidParam.getVidType())) {
            return CommonResult.fail(ErrorCodeEnum.FAILURE, "当前仅支持method");
        }
        try {
            TreeByVidInfo treeByVidInfo = graphService.getTreeByVid(treeByVidParam);
            return CommonResult.success(treeByVidInfo);
        } catch (Exception e) {
            LOGGER.error("getTreeByVid e:", e);
            return CommonResult.fail(ErrorCodeEnum.FAILURE, "getTreeByVid失败，请联系管理员");
        }
    }

    @GetMapping("/getLinkMethodInfoByVid")
    public CommonResult getLinkMethodInfoByVid(@RequestParam String lineViewType, @RequestParam long itemId, @RequestParam String vid) throws IOException {
        try {
            List<MethodNode> res = codeViewService.getLinkMethodInfoByVid(lineViewType, itemId, vid);
            return CommonResult.success(res);
        } catch (Exception e) {
            LOGGER.error("getLinkMethodInfoByVid e:", e);
            return CommonResult.fail(ErrorCodeEnum.FAILURE, "查询getLinkMethodInfoByVid失败，请联系管理员");
        }
    }

    @GetMapping("/getClassInfoByVid")
    public CommonResult getClassInfoByVid(@RequestParam long itemId, @RequestParam String vid, @RequestParam String type) throws IOException {
        try {
            ClassNode res = codeViewService.getClassInfoByVid(itemId, vid, type);
            return CommonResult.success(res);
        } catch (Exception e) {
            LOGGER.error("getClassInfoByVid e:", e);
            return CommonResult.fail(ErrorCodeEnum.FAILURE, "查询getClassInfoByVid失败，请联系管理员");
        }
    }

    @GetMapping("/getCheckCodeViewByItemId")
    public CommonResult getCheckCodeViewByItemId(@RequestParam long itemId, @RequestParam String vid, @RequestParam(required = false, defaultValue = "1") Integer oldLineId) {
        try {
            CheckShowRes res = codeViewService.initCheckCodeView(itemId, vid, oldLineId);
            return CommonResult.success(res);
        } catch (Exception e) {
            LOGGER.error("getCheckCodeViewByItemId e:", e);
            return CommonResult.fail(ErrorCodeEnum.FAILURE, "getCheckCodeViewByItemId失败，请联系管理员");
        }
    }

    @GetMapping("/getReposGraphByItemId")
    public CommonResult getReposGraphByItemId(@RequestParam long itemId) {
        try {
            ReposGraphByItemIdRes res = graphService.getReposGraphByItemIdV2(itemId);
            return CommonResult.success(res);
        } catch (Exception e) {
            LOGGER.error("getRepoGraphByItemId e:", e);
            return CommonResult.fail(ErrorCodeEnum.FAILURE, "getRepoGraphByItemId失败，请联系管理员");
        }
    }
}
