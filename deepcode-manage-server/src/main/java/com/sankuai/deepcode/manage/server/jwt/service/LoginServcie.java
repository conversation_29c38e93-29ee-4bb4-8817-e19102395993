package com.sankuai.deepcode.manage.server.jwt.service;


import com.sankuai.deepcode.dao.domain.SysUser;
import com.sankuai.deepcode.manage.server.jwt.domain.LoginUser;
import com.sankuai.deepcode.manage.server.jwt.domain.ResponseResult;

public interface LoginServcie {
    ResponseResult bindUser(LoginUser loginUser);

    ResponseResult login(SysUser user);
    ResponseResult  getInfo(Long userId);


    ResponseResult logout();

    ResponseResult loginByGithub(String code);
    ResponseResult loginByMt(String code);

}
