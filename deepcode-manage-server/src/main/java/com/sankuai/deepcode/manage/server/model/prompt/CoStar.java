package com.sankuai.deepcode.manage.server.model.prompt;

import lombok.Data;

@Data
public class CoStar {
    private String context; //上下文
    private String objective; //目标
    private String style; //风格
    private String tone; //语气
    private String audience;  //受众
    private String reponse;  //回复

    public String initPrompt(CoStar coStar) {
        StringBuffer sb = new StringBuffer();
        sb.append("#").append(coStar.context).append("#\n");
        sb.append("#").append(coStar.objective).append("#\n");
        sb.append("#").append(coStar.style).append("#\n");
        sb.append("#").append(coStar.tone).append("#\n");
        sb.append("#").append(coStar.audience).append("#\n");
        sb.append("#").append(coStar.reponse).append("#\n");
        return sb.toString();
    }
}
