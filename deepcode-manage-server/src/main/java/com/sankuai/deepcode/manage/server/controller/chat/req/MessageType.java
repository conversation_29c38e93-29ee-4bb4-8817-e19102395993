package com.sankuai.deepcode.manage.server.controller.chat.req;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.stream.Stream;

/**
 * Package: com.sankuai.deepcode.manage.server.controller.chat.req
 * Description:
 *
 * <AUTHOR>
 * @since 2025/3/24 18:25
 */
@Getter
@AllArgsConstructor
public enum MessageType {
    TEXT("text"),
    IMAGE("image"),
    FILE("file"),
    ;

    @JsonProperty("type")
    private final String type;


    @JsonCreator(mode = JsonCreator.Mode.DELEGATING)
    public static MessageType from(@JsonProperty("type")String type) {
        return Stream.of(values()).filter(v -> v.type.equals(type)).findFirst().orElseThrow(() -> new IllegalArgumentException("Invalid message type: " + type));
    }
}
