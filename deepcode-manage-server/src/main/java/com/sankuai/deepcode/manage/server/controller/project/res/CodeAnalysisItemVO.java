package com.sankuai.deepcode.manage.server.controller.project.res;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.time.LocalDateTime;

/**
 * Package: com.sankuai.deepcode.manage.server.controller.project.res
 * Description:
 *
 * <AUTHOR>
 * @since 2025/3/12 15:14
 */
@Getter
@Setter
@ToString
public class CodeAnalysisItemVO {
    private Long id;
    @Schema(description = "仓库地址")
    private String gitUrl;

    @Schema(description = "源分支名称")
    private String sourceBranch;

    @Schema(description = "源提交的commit id")
    private String sourceCommit;

    @Schema(description = "源提交的commit信息")
    private String sourceCommitMsg;

    @Schema(description = "目标分支名称")
    private String targetBranch;

    @Schema(description = "目标提交的commit id")
    private String targetCommit;

    @Schema(description = "分析状态")
    private String status;

    @Schema(description = "代码分析时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime analysisTime;
}
