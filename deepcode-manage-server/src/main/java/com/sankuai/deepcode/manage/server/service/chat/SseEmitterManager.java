package com.sankuai.deepcode.manage.server.service.chat;

import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.util.Map;

/**
 * Package: com.sankuai.deepcode.manage.server.service.chat
 * Description:
 *
 * <AUTHOR>
 * @since 2025/3/21 13:58
 */
@Component
@Slf4j
public class SseEmitterManager {
    private final static Map<String, SseEmitter> SSE_EMITTER_MAP = Maps.newConcurrentMap();

    /**
     * 创建一个新的 SseEmitter 实例并添加到映射表中
     *
     * @param uid 用户唯一标识
     * @return 新创建的 SseEmitter 实例
     */
    public static SseEmitter instance(String uid) {
        SseEmitter sseEmitter = new SseEmitter(-1L);

        // 设置回调函数，在连接完成、超时或发生错误时自动从映射表中移除
        sseEmitter.onCompletion(() -> remove(uid));
        sseEmitter.onTimeout(() -> remove(uid));
        sseEmitter.onError(throwable -> remove(uid));

        // 将 SseEmitter 实例添加到映射表中
        add(uid, sseEmitter);

        return sseEmitter;
    }

    /**
     * 创建一个新的 SseEmitter 实例
     *
     * @return 新创建的 SseEmitter 实例
     */
    public static Pair<String, SseEmitter> instance() {
        String uid = java.util.UUID.randomUUID().toString();
        SseEmitter sseEmitter = instance(uid);
        return Pair.of(uid, sseEmitter);
    }

    /**
     * 添加 SseEmitter 实例到映射表中
     *
     * @param uid        用户唯一标识
     * @param sseEmitter SseEmitter 实例
     */
    public static void add(String uid, SseEmitter sseEmitter) {
        SSE_EMITTER_MAP.put(uid, sseEmitter);
    }

    /**
     * 从映射表中获取 SseEmitter 实例
     *
     * @param uid 用户唯一标识
     * @return SseEmitter 实例
     */
    public static SseEmitter get(String uid) {
        return SSE_EMITTER_MAP.get(uid);
    }

    /**
     * 从映射表中移除 SseEmitter 实例
     *
     * @param uid 用户唯一标识
     */
    public static void remove(String uid) {
        SSE_EMITTER_MAP.remove(uid);
    }
}
