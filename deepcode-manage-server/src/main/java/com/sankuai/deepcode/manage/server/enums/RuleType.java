package com.sankuai.deepcode.manage.server.enums;

public enum RuleType {
    HARD_CODE(1, "硬编码"),
    FEATURE_RISK(2, "特征风险"),
    DEFECT_DETECTION(3, "缺陷检测");

    private final int code;
    private final String desc;

    RuleType(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static RuleType getByCode(int code) {
        for (RuleType type : values()) {
            if (type.getCode() == code) {
                return type;
            }
        }
        return null;
    }
}