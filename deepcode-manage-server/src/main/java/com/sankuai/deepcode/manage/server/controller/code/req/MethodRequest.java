package com.sankuai.deepcode.manage.server.controller.code.req;

import com.sankuai.deepcode.manage.server.deserializer.TrimString;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotNull;

/**
 * Package: com.sankuai.deepcode.manage.server.controller.code.req
 * Description:
 *
 * <AUTHOR>
 * @since 2025/3/17 16:15
 */
@Setter
@Getter
@ToString
public class MethodRequest {
    @Schema(description = "项目ID", example = "123")
    @NotNull(message = "项目ID不能为空")
    private Long projectId;

    @Schema(description = "文件路径", example = "com/sankuai/deepcode/manage/controller/CodeController.java")
    @NotNull(message = "文件路径不能为空")
    @TrimString
    private String filePath;

    @Schema(description = "行号", example = "10")
    private Integer line;

    @Schema(description = "方法名", example = "getCodeList")
    @NotNull(message = "方法名不能为空")
    private String methodName;

}
