package com.sankuai.deepcode.manage.server.mapper.chat;

import com.sankuai.deepcode.dao.domain.DcChat;
import com.sankuai.deepcode.dao.domain.DcChatMessage;
import com.sankuai.deepcode.dao.domain.DcProject;
import com.sankuai.deepcode.dao.enums.ItemStatusEnum;
import com.sankuai.deepcode.dao.po.ChatMsgChildrenPO;
import com.sankuai.deepcode.dao.service.CodeAnalysisItemService;
import com.sankuai.deepcode.manage.server.controller.chat.res.ChatVO;
import com.sankuai.deepcode.manage.server.controller.chat.res.MsgVO;
import com.sankuai.deepcode.manage.server.controller.chat.res.ProjectStatusEnum;
import org.mapstruct.*;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.Objects;

/**
 * Package: com.sankuai.deepcode.manage.server.mapper.chat
 * Description:
 *
 * <AUTHOR>
 * @since 2025/3/14 15:27
 */
@Mapper(
        componentModel = "spring",
        imports = {
                ItemStatusEnum.class,
                ProjectStatusEnum.class,
                Objects.class
        }
)
public abstract class ChatConvertor {

    @Autowired
    protected CodeAnalysisItemService codeAnalysisItemService;

    @Mappings({
            @Mapping(target = "id", source = "chat.uuid"),
            @Mapping(target = "projectId", source = "project.id"),
            @Mapping(target = "itemId", source = "project.itemId"),
            @Mapping(target = "title", source = "chat.title"),
            @Mapping(target = "createTime", source = "chat.createTime"),
            @Mapping(target = "updateTime", source = "chat.updateTime")
    })
    public abstract ChatVO convert(DcChat chat, DcProject project);

    /**
     * 在映射完成后处理 chatAvailable 和 chatUnavailableReason 字段
     */
    @AfterMapping
    protected void processChatAvailability(DcChat chat, DcProject project, @MappingTarget ChatVO chatVO) {
        determineChatAvailability(chat, project, chatVO);
    }

    @Mappings({
            @Mapping(target = "id", source = "uuid"),
            @Mapping(target = "chatUuid", source = "chatUuid"),
            @Mapping(target = "prevMsgId", source = "prevMsgId"),
            @Mapping(target = "content", source = "content"),
            @Mapping(target = "role", source = "role"),
            @Mapping(target = "childrenIds", expression = "java(convertChildrenMsg(chatMessage.getChildrenIds()))"),
            @Mapping(target = "createTime", source = "createTime"),
            @Mapping(target = "updateTime", source = "updateTime"),
    })
    public abstract MsgVO convertMsg(DcChatMessage chatMessage);

    @Mappings({
            @Mapping(target = "ids", source = "childrenIds"),
    })
    public abstract MsgVO.ChildrenVO convertChildrenMsg(ChatMsgChildrenPO childrenPo);


    public abstract List<MsgVO> convertMsgList(List<DcChatMessage> chatMessages);

    /**
     * 确定聊天是否可用及不可用原因，并直接设置到 ChatVO 对象中
     *
     * @param chat    聊天信息
     * @param project 项目信息
     * @param chatVO  聊天VO对象，用于设置可用性和不可用原因
     */
    protected void determineChatAvailability(DcChat chat, DcProject project, ChatVO chatVO) {
        if (project == null) {
            chatVO.setChatAvailable(false);
            chatVO.setChatUnavailableReason("哎呀，这个项目好像不见了呢~ 要不要检查一下项目是否存在呀？");
            return;
        }
        if (project.getItemId() == null || project.getItemId() <= 0) {
            chatVO.setChatAvailable(false);
            chatVO.setChatUnavailableReason("咦，这个项目还没有关联分析任务呢，请先去关联一下吧~");
            return;
        }

        if (!Objects.equals(chat.getItemId(), project.getItemId())) {
            chatVO.setChatAvailable(false);
            chatVO.setChatUnavailableReason("项目分析报告有新变化啦！为了给你最准确的结果，请开启个新会话吧~");
            return;
        }

        // 如果聊天可用，设置为true和null
        chatVO.setChatAvailable(true);
        chatVO.setChatUnavailableReason(null);
    }
}
