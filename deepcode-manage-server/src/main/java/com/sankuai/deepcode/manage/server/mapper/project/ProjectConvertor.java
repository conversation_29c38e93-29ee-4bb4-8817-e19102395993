package com.sankuai.deepcode.manage.server.mapper.project;

import com.sankuai.deepcode.dao.domain.CodeAnalysisItem;
import com.sankuai.deepcode.dao.domain.DcProject;
import com.sankuai.deepcode.dao.enums.ItemStatusEnum;
import com.sankuai.deepcode.manage.server.controller.project.res.CodeAnalysisItemVO;
import com.sankuai.deepcode.manage.server.controller.project.res.ProjectVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;

import java.util.EnumSet;
import java.util.List;
import java.util.Objects;

/**
 * Package: com.sankuai.deepcode.manage.server.mapper.project
 * Description:
 *
 * <AUTHOR>
 * @since 2025/3/6 20:10
 */
@Mapper(
        componentModel = "spring",
        imports = {
                ItemStatusEnum.class,
                Objects.class
        }
)
public interface ProjectConvertor {
    @Mappings({
            @Mapping(target = "id", source = "project.id"),
            @Mapping(target = "name", source = "project.projectName"),
            @Mapping(target = "createTime", source = "project.createTime", dateFormat = "yyyy-MM-dd HH:mm:ss"),
            @Mapping(target = "updateTime", source = "project.updateTime", dateFormat = "yyyy-MM-dd HH:mm:ss"),
            @Mapping(target = "status", source = "item.status"),
            @Mapping(target = "statusDesc", expression = "java(richStatusDesc(item))"),
            @Mapping(target = "needPolling", expression = "java(processNeedPoll(item))"),
            @Mapping(target = "userId", source = "project.userId"),
            @Mapping(target = "gitUrl", source = "project.gitUrl"),
            @Mapping(target = "isDiff", expression = "java(!Objects.equals(project.getSourceBranch(), project.getTargetBranch()))"),
            @Mapping(target = "projectName", source = "project.projectName"),
            @Mapping(target = "sourceBranch", source = "project.sourceBranch"),
            @Mapping(target = "targetBranch", source = "project.targetBranch"),
            @Mapping(target = "analysisItem", expression = "java(convertAnalysisItem(item))")
    })
    ProjectVO convertProject(DcProject project, CodeAnalysisItem item);

    @Mappings({
            @Mapping(source = "id", target = "id"),
            @Mapping(source = "gitUrl", target = "gitUrl"),
            @Mapping(source = "fromBranch", target = "sourceBranch"),
            @Mapping(source = "fromCommitId", target = "sourceCommit"),
            @Mapping(source = "lastCommitMsg", target = "sourceCommitMsg"),
            @Mapping(source = "toBranch", target = "targetBranch"),
            @Mapping(source = "toCommitId", target = "targetCommit"),
            @Mapping(target = "status", expression = "java(simpleStatus(item.getStatus()))"),
            @Mapping(source = "utime", target = "analysisTime", dateFormat = "yyyy-MM-dd HH:mm:ss")
    })
    CodeAnalysisItemVO convertAnalysisItem(CodeAnalysisItem item);

    List<CodeAnalysisItemVO> convertAnalysisItemList(List<CodeAnalysisItem> itemList);

    default String simpleStatus(Integer status) {
        return ItemStatusEnum.getByCode(status).getDesc();
    }

    default String richStatusDesc(CodeAnalysisItem item) {
        if (item == null) {
            return "项目未创建分析项目，请手动创建分析项目或是关联已有报告~";
        }
        ItemStatusEnum statusEnum = ItemStatusEnum.getByCode(item.getStatus());
        switch (statusEnum) {
            case INITIATING:
                return "项目正在热身中，马上就要开始啦~";
            case PROCESSING:
                return "代码分析师正在卖力工作，请给Ta一点时间哦~";
            case SUCCESS:
                return "太棒啦！项目分析圆满完成~";
            case FAILED:
                return "糟糕，分析过程中遇到了一点小问题，快找管理员来帮忙吧~";
            default:
                return "哎呀，项目分析状态迷失在外太空啦~";
        }
    }

    default Boolean processNeedPoll(CodeAnalysisItem item) {
        if (item == null) {
            return false;
        }
        EnumSet<ItemStatusEnum> pollStatus = EnumSet.of(
                ItemStatusEnum.INITIATING, ItemStatusEnum.PROCESSING
        );
        return pollStatus.contains(ItemStatusEnum.getByCode(item.getStatus()));
    }
}
