package com.sankuai.deepcode.manage.server.controller.rule;

import com.sankuai.deepcode.commons.CommonResult;
import com.sankuai.deepcode.dao.domain.*;
import com.sankuai.deepcode.manage.server.enums.ErrorCodeEnum;
import com.sankuai.deepcode.manage.server.model.rule.RuleInfoDetailWithBindInfo;
import com.sankuai.deepcode.manage.server.model.rule.RuleInfoDetailWithUserInfo;
import com.sankuai.deepcode.manage.server.service.rule.RuleService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import java.util.ArrayList;
import java.util.List;

@RestController
@RequestMapping("/rule")
public class RuleControllor {

    private static final Logger LOGGER = LoggerFactory.getLogger(RuleControllor.class);

    @Autowired
    private RuleService ruleService;

    @PostMapping("/getRules")
    public CommonResult getRules(@RequestBody RuleInfoDetail ruleInfoDetail) {
        List<RuleInfoDetailWithUserInfo> ruleList = ruleService.getRuleList(ruleInfoDetail);
        return CommonResult.success(ruleList);
    }

    @PostMapping("/addRule")
    public CommonResult addRule(@RequestBody RuleInfoDetail ruleInfoDetail) {
        int res = ruleService.addRule(ruleInfoDetail);
        return CommonResult.success(res);
    }

    @PostMapping("/editRule")
    public CommonResult editRule(@RequestBody RuleInfoDetail ruleInfoDetail) {
        boolean res = ruleService.editRule(ruleInfoDetail);
        return CommonResult.success(res);
    }

    @GetMapping("/delRule")
    public CommonResult delRule(@RequestParam long ruleId) {
        boolean res = false;
        try {
            res = ruleService.delRule(ruleId);
        } catch (Exception e) {
            LOGGER.error("delRule error", e);
            return CommonResult.fail(ErrorCodeEnum.FAILURE, "delRule error:" + e.getMessage());
        }
        return CommonResult.success(res);
    }

    @GetMapping("/bindRule")
    public CommonResult bindRule(@RequestParam long ruleId, @RequestParam long userId, @RequestParam int projectId) {
        int res = 0;
        try {
            res = ruleService.bindRule(ruleId, userId, projectId);
        } catch (Exception e) {
            LOGGER.error("bindRule error", e);
            return CommonResult.fail(ErrorCodeEnum.FAILURE, "bindRule error:" + e.getMessage());
        }
        return CommonResult.success(res);
    }


    @GetMapping("/unbindRule")
    public CommonResult unbindRule(@RequestParam long bindId, @RequestParam long userId) {
        boolean res = false;
        try {
            res = ruleService.unbindRule(bindId, userId);
        } catch (Exception e) {
            LOGGER.error("unbindRule error", e);
            return CommonResult.fail(ErrorCodeEnum.FAILURE, "unbindRule error:" + e.getMessage());
        }
        return CommonResult.success(res);
    }

    @GetMapping("/getBindInfoByProjectId")
    public CommonResult getBindInfoByProjectId(@RequestParam long projectId) {
        List<RuleInfoDetailWithBindInfo> res = new ArrayList<>();
        try {
            res = ruleService.getRuleInfoByProjectId(projectId);
        } catch (Exception e) {
            LOGGER.error("getBindInfoByProjectId error", e);
            return CommonResult.fail(ErrorCodeEnum.FAILURE, "getBindInfoByProjectId error:" + e.getMessage());
        }
        return CommonResult.success(res);
    }

}
