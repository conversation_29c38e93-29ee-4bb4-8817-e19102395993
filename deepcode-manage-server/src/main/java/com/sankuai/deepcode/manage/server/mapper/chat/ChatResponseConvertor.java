package com.sankuai.deepcode.manage.server.mapper.chat;

import com.google.common.collect.Lists;
import com.sankuai.deepcode.ai.llm.openai.chat.AssistantMessage;
import com.sankuai.deepcode.ai.llm.openai.chat.ChatCompletionResponse;
import com.sankuai.deepcode.ai.llm.openai.chat.Message;
import com.sankuai.deepcode.ai.llm.openai.chat.Role;
import com.sankuai.deepcode.ai.llm.openai.shared.CompletionTokensDetails;
import com.sankuai.deepcode.ai.llm.openai.shared.PromptTokensDetails;
import com.sankuai.deepcode.ai.llm.openai.shared.Usage;
import com.sankuai.deepcode.dao.po.ChatMsgContentPO;
import com.sankuai.deepcode.manage.server.controller.chat.req.MessageVO;
import com.sankuai.deepcode.manage.server.controller.chat.req.ProjectChatRequestVO;
import org.apache.commons.collections4.CollectionUtils;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Package: com.sankuai.deepcode.manage.server.mapper.chat
 * Description:
 *
 * <AUTHOR>
 * @since 2025/3/25 14:00
 */
@Mapper(
        componentModel = "spring"
)
public interface ChatResponseConvertor {
    /*************************************************** 助手消息体转换 ***************************************************/
    default ChatMsgContentPO convertAssistant(ChatCompletionResponse completionResponse) {
        ChatMsgContentPO assistantMsgPo = new ChatMsgContentPO();
        assistantMsgPo.setId(completionResponse.id());
        assistantMsgPo.setRole(Role.ASSISTANT.name().toLowerCase());
        assistantMsgPo.setModel(completionResponse.model());

        processContent(assistantMsgPo, completionResponse);

        assistantMsgPo.setUsage(convertUsage(completionResponse.usage()));
        assistantMsgPo.setTimestamp(completionResponse.created() != null ? completionResponse.created().longValue() : LocalDateTime.now().atZone(ZoneId.systemDefault()).toEpochSecond());
        return assistantMsgPo;
    }

    default void processContent(ChatMsgContentPO assistantMsgPo, ChatCompletionResponse completionResponse) {
        if (CollectionUtils.isEmpty(completionResponse.choices())) {
            return;
        }
        AssistantMessage message = completionResponse.choices().get(0).message();
        if (message == null) {
            return;
        }
        assistantMsgPo.setContent(message.content());
        assistantMsgPo.setReasoningContent(message.reasoningContent());
        assistantMsgPo.setProcessingContent(message.processingContent());
    }

    @Mappings({
            @Mapping(target = "promptTokens", expression = "java(usage.promptTokens())"),
            @Mapping(target = "completionTokens", expression = "java(usage.completionTokens())"),
            @Mapping(target = "totalTokens", expression = "java(usage.totalTokens())"),
            @Mapping(target = "promptTokensDetails", expression = "java(convertPromptTokensDetails(usage.promptTokensDetails()))"),
            @Mapping(target = "completionTokensDetails", expression = "java(convertCompletionTokensDetails(usage.completionTokensDetails()))"),
    })
    ChatMsgContentPO.Usage convertUsage(Usage usage);

    @Mappings({
            @Mapping(target = "cachedTokens", expression = "java(promptTokensDetails.cachedTokens())")
    })
    ChatMsgContentPO.PromptTokensDetails convertPromptTokensDetails(PromptTokensDetails promptTokensDetails);

    @Mappings({
            @Mapping(target = "reasoningTokens", expression = "java(completionTokensDetails.reasoningTokens())")
    })
    ChatMsgContentPO.CompletionTokensDetails convertCompletionTokensDetails(CompletionTokensDetails completionTokensDetails);


    /***************************************************** 用户消息体转换 *****************************************************/
    default ChatMsgContentPO convertUser(ProjectChatRequestVO requestVO) {
        ChatMsgContentPO userMsgPo = new ChatMsgContentPO();
        userMsgPo.setRole(Role.USER.name().toLowerCase());
        userMsgPo.setModel(requestVO.getModel());
        userMsgPo.setContent(requestVO.getUserQuery());
        userMsgPo.setTimestamp(System.currentTimeMillis());

        userMsgPo.setMediaInfos(convertRequestMedia(requestVO));
        return userMsgPo;
    }

    default List<ChatMsgContentPO.MediaInfo> convertRequestMedia(ProjectChatRequestVO requestVO) {
        List<MessageVO.FilePayLoad> filePayLoadList = requestVO.getFileContent();
        List<MessageVO.ImagePayload> imagePayloadList = requestVO.getImageContent();

        List<ChatMsgContentPO.MediaInfo> mediaInfoList = Lists.newArrayList();
        mediaInfoList.addAll(convertFileMedia(filePayLoadList));
        mediaInfoList.addAll(convertImageMedia(imagePayloadList));
        return mediaInfoList;
    }

    default List<ChatMsgContentPO.MediaInfo> convertImageMedia(List<MessageVO.ImagePayload> imagePayloadList) {
        List<ChatMsgContentPO.ImageInfo> mediaInfoList = convertImageInfo(imagePayloadList);
        return mediaInfoList.stream()
                .map(f -> ChatMsgContentPO.MediaInfo.builder().media(f).type("image").build())
                .collect(Collectors.toList());
    }

    default List<ChatMsgContentPO.MediaInfo> convertFileMedia(List<MessageVO.FilePayLoad> filePayLoadList) {
        List<ChatMsgContentPO.FileInfo> mediaInfoList = convertFileInfo(filePayLoadList);
        return mediaInfoList.stream()
                .map(f -> ChatMsgContentPO.MediaInfo.builder().media(f).type("file").build())
                .collect(Collectors.toList());
    }

    @Mappings({
            @Mapping(target = "fileId", source = "fileId"),
            @Mapping(target = "vid", source = "vid"),
            @Mapping(target = "path", source = "path"),
            @Mapping(target = "name", source = "name"),
            @Mapping(target = "url", source = "url"),
            @Mapping(target = "start", source = "start"),
            @Mapping(target = "end", source = "end"),
    })
    ChatMsgContentPO.FileInfo convertFileInfo(MessageVO.FilePayLoad filePayLoad);

    List<ChatMsgContentPO.FileInfo> convertFileInfo(List<MessageVO.FilePayLoad> filePayLoad);

    @Mappings({
            @Mapping(target = "line", source = "line"),
            @Mapping(target = "column", source = "column")
    })
    ChatMsgContentPO.LineInfo convertLineInfo(MessageVO.LineInfo lineInfo);

    @Mappings({
            @Mapping(target = "url", source = "url"),
            @Mapping(target = "name", source = "name"),
            @Mapping(target = "base64Data", source = "base64Data")
    })
    ChatMsgContentPO.ImageInfo convertImageInfo(MessageVO.ImagePayload imagePayload);

    List<ChatMsgContentPO.ImageInfo> convertImageInfo(List<MessageVO.ImagePayload> imagePayload);
}
