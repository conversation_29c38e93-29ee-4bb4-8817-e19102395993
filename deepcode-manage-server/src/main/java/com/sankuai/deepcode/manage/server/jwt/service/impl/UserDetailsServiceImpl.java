package com.sankuai.deepcode.manage.server.jwt.service.impl;

import com.sankuai.deepcode.commons.DeepCodePreconditions;
import com.sankuai.deepcode.dao.domain.SysUser;
import com.sankuai.deepcode.dao.domain.SysUserDetail;
import com.sankuai.deepcode.dao.mapper.SysUserDetailMapper;
import com.sankuai.deepcode.dao.service.SysUserDetailService;
import com.sankuai.deepcode.dao.service.SysUserService;
import com.sankuai.deepcode.manage.server.jwt.domain.LoginUser;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Service;

import java.util.*;

@Service
public class UserDetailsServiceImpl implements UserDetailsService {

    @Autowired
    private SysUserService sysUserService;
    @Autowired
    private SysUserDetailService sysUserDetailService;


    @Autowired
    private SysUserDetailMapper userDetailMapper;

    @Override
    public UserDetails loadUserByUsername(String username) throws UsernameNotFoundException {

        //查询用户信息
//        LambdaQueryWrapper<User> queryWrapper = new LambdaQueryWrapper<>();
//        queryWrapper.eq(User::getUserName,username);

        Optional<SysUser> user = sysUserService.getUserByUserName(username);
        //如果没有查询到用户就抛出异常
        DeepCodePreconditions.checkBizArgument(user.isPresent(), "用户名或者密码错误");

        List<SysUserDetail> userDetails = sysUserDetailService.getDetailsByUserId(user.get().getId());
        List<String> list = new ArrayList<>(Arrays.asList("test", "admin", "anonymous"));
        //把数据封装成UserDetails返回
        return new LoginUser(user.get(), list, userDetails);
    }


    public UserDetails loadUserByUserId(Long userId) throws UsernameNotFoundException {

        //查询用户信息

        SysUser user = sysUserService.getById(userId);
        //如果没有查询到用户就抛出异常
        if (Objects.isNull(user)) {
            throw new RuntimeException("用户名或者密码错误");
        }
        //userDetailMapper.selectOne(Wrappers.<UserDetail>lambdaQuery().eq(UserDetail::getUserName, userRes.getLogin()))
        List<SysUserDetail> userDetails = sysUserDetailService.getDetailsByUserId(user.getId());
        List<String> list = new ArrayList<>(Arrays.asList("test", "admin", "anonymous"));
        //List<String> list = menuMapper.selectPermsByUserId(user.getId());
        //把数据封装成UserDetails返回
        return new LoginUser(user, list, userDetails);
    }
}
