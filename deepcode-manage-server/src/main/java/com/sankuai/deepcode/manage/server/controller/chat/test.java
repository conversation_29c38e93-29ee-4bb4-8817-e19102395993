package com.sankuai.deepcode.manage.server.controller.chat;

import com.sankuai.deepcode.ai.agnets.callback.PlanCompleteCallback;
import com.sankuai.deepcode.ai.agnets.model.InitPlanParam;
import com.sankuai.deepcode.ai.agnets.service.PlanFlowService;
import com.sankuai.deepcode.ai.enums.ChatEnum;
import com.sankuai.deepcode.ai.llm.model.chat.ChatMessage;
import com.sankuai.deepcode.ai.llm.openai.chat.ChatCompletionResponse;
import com.sankuai.deepcode.ai.llm.service.OneApiService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.util.ArrayList;
import java.util.List;

@RestController
@RequestMapping("/test1")
public class test {

    @Autowired
    private OneApiService oneApiService;
    @Autowired
    private PlanFlowService planFlowService;

    @GetMapping("/test1")
    public SseEmitter test() {
        //必须设置超时时间
        SseEmitter sseEmitter = new SseEmitter(-1L);
        List<ChatMessage> chatMessages = new ArrayList<>();
        ChatMessage system = new ChatMessage();
        system.setRole("system");
        system.setContent(PlanFlowService.SystemPrompt);
        ChatMessage chatMessage = new ChatMessage();

        chatMessage.setRole("user");
        chatMessage.setContent("这个工程是做什么的");
        chatMessages.add(system);
        chatMessages.add(chatMessage);

        ChatCompletionResponse chatCompletionResponse = ChatCompletionResponse.builder()
                .choices(new ArrayList<>())
                .build();

        InitPlanParam initPlanParam = new InitPlanParam();
        initPlanParam.setItemId(16L);
        initPlanParam.setChatMessages(chatMessages);
        initPlanParam.setModelName(ChatEnum.GPT_4_1_MINI.getName());
        initPlanParam.setSseEmitter(sseEmitter);
        initPlanParam.setChatCompletionResponse(chatCompletionResponse);

        //回调函数
        initPlanParam.setPlanCompleteCallback(new PlanCompleteCallback() {
            @Override
            public void onComplete(SseEmitter emitter, ChatCompletionResponse chatCompletionResponse) {
                System.out.println("onComplete");

                //处理数据存储
                emitter.complete();
            }
        });

        //调用
        planFlowService.initPlanFlow(initPlanParam);
        return sseEmitter;
    }

}
