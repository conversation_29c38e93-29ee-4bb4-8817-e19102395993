package com.sankuai.deepcode.manage.server.controller.code.req;

import com.sankuai.deepcode.manage.server.deserializer.TrimString;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * Package: com.sankuai.deepcode.manage.server.controller.code.req
 * Description:
 *
 * <AUTHOR>
 * @since 2025/3/13 21:54
 */
@Setter
@Getter
@ToString
public class FileContentReq {
    @Schema(description = "项目ID", example = "1")
    @NotNull(message = "项目ID不能为空")
    private Long projectId;

    @Schema(description = "文件路径", example = "/src/main/java/com/sankuai/deepcode/manage/DeepCodeManageApplication.java")
    @NotBlank(message = "文件路径不能为空")
    @TrimString
    private String filePath;
}
