package com.sankuai.deepcode.manage.server.service.code;

import com.google.common.collect.Lists;
import com.sankuai.deepcode.dao.domain.CodeFileAnalysis;
import com.sankuai.deepcode.dao.service.CodeViewAnalysisService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * Package: com.sankuai.deepcode.manage.server.service.code
 * Description:
 *
 * <AUTHOR>
 * @since 2025/3/25 19:26
 */
@Service
@Slf4j
public class FileCodeService {
    private final CodeViewAnalysisService codeViewAnalysisService;

    public FileCodeService(CodeViewAnalysisService codeViewAnalysisService) {
        this.codeViewAnalysisService = codeViewAnalysisService;
    }

    public List<String> getFileCodeByLines(CodeFileAnalysis codeFileAnalysis, Integer startLine, Integer endLine) {
        if (codeFileAnalysis == null) {
            return Lists.newArrayList();
        }
        if (startLine == null) {
            startLine = 1;
        }
        return codeViewAnalysisService.getContentByLines(codeFileAnalysis.getItemId(), codeFileAnalysis.getFileVid(), startLine, endLine);
    }
}
