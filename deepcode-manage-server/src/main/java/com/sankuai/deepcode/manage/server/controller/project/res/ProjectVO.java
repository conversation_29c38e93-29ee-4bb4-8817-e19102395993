package com.sankuai.deepcode.manage.server.controller.project.res;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.time.LocalDateTime;

/**
 * Package: com.sankuai.deepcode.manage.server.model.project
 * Description:
 *
 * <AUTHOR>
 * @since 2025/3/3 19:49
 */
@Setter
@Getter
@ToString
public class ProjectVO {

    @Schema(description = "项目ID")
    private Long id;
    @Schema(description = "项目名称")
    private String name;
    @Schema(description = "创建人")
    private Integer userId;

    @Schema(description = "项目状态")
    private Integer status;
    @Schema(description = "项目状态描述")
    private String statusDesc;
    @Schema(description = "是否需要轮询")
    private Boolean needPolling;

    @Schema(description = "git地址")
    private String gitUrl;

    @Schema(description = "是否差异模式")
    Boolean isDiff;

    @Schema(description = "源分支")
    private String sourceBranch;
    @Schema(description = "目标分支")
    private String targetBranch;
    @Schema(description = "项目名称")
    private String projectName;
    @Schema(description = "创建时间")
    private LocalDateTime createTime;
    @Schema(description = "更新时间")
    private LocalDateTime updateTime;
    @Schema(description = "代码分析项")
    private CodeAnalysisItemVO analysisItem;
}
