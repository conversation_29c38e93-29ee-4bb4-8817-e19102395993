package com.sankuai.deepcode.manage.server.service.chat;

import com.sankuai.deepcode.ai.llm.oneapi.OneApiClient;
import com.sankuai.deepcode.ai.llm.openai.chat.*;
import com.sankuai.deepcode.ai.prompt.CodeLanguage;
import com.sankuai.deepcode.ai.prompt.PromptBuilder;
import com.sankuai.deepcode.ai.prompt.PromptTemplate;
import com.sankuai.deepcode.dao.domain.CodeAnalysisItem;
import com.sankuai.deepcode.dao.domain.CodeFileAnalysis;
import com.sankuai.deepcode.dao.domain.DcChat;
import com.sankuai.deepcode.dao.domain.DcChatMessage;
import com.sankuai.deepcode.dao.service.CodeFileAnalysisService;
import com.sankuai.deepcode.dao.service.DcChatMessageService;
import com.sankuai.deepcode.dao.service.DcChatService;
import com.sankuai.deepcode.manage.server.controller.chat.req.AdviceQuestionRequest;
import com.sankuai.deepcode.manage.server.controller.chat.res.AdviceQuestionVO;
import com.sankuai.deepcode.manage.server.jwt.utils.UserUtils;
import com.sankuai.deepcode.ai.embedding.service.EmbeddingSearchService;
import com.sankuai.deepcode.manage.server.service.project.ProjectAnalysisService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * Package: com.sankuai.deepcode.manage.server.service.chat
 * Description:
 *
 * <AUTHOR>
 * @since 2025/3/27 11:39
 */
@Service
@Slf4j
public class AdviceQuestionsService {

    private final EmbeddingSearchService embeddingSearchService;
    private final DcChatMessageService dcChatMessageService;
    private final ProjectAnalysisService projectAnalysisService;
    private final ChatMsgService chatMsgService;
    private final DcChatService dcChatService;
    private final CodeFileAnalysisService codeFileAnalysisService;
    private final OneApiClient oneApiClient;

    public AdviceQuestionsService(
            EmbeddingSearchService embeddingSearchService,
            DcChatMessageService dcChatMessageService,
            ProjectAnalysisService projectAnalysisService,
            ChatMsgService chatMsgService,
            DcChatService dcChatService,
            CodeFileAnalysisService codeFileAnalysisService,
            OneApiClient oneApiClient
    ) {
        this.embeddingSearchService = embeddingSearchService;
        this.dcChatMessageService = dcChatMessageService;
        this.projectAnalysisService = projectAnalysisService;
        this.chatMsgService = chatMsgService;
        this.dcChatService = dcChatService;
        this.codeFileAnalysisService = codeFileAnalysisService;
        this.oneApiClient = oneApiClient;
    }

    public AdviceQuestionVO adviceQuestions(AdviceQuestionRequest request) {
        CodeAnalysisItem analysisItem = projectAnalysisService.getAnalysisItemAndValidation(request.getProjectId());

        PromptTemplate questionAdvicePromptTemplate = adviceQuestionsPromptTemplate();

        ChatCompletionRequest.Builder completionRequestBuilder = ChatCompletionRequest.builder();
        completionRequestBuilder.model(request.getModel())
                .temperature(request.getTemperature())
        ;

        // 优先使用传入的 message 列表作为用户历史问题
        if (CollectionUtils.isNotEmpty(request.getMessages())) {
            completionRequestBuilder.messages(
                    UserMessage.from(request.getMessages())
            );
        } else {
            // 未传入消息列表，则通过 消息ID 查找用户消息
            Optional<DcChatMessage> lastMsg = dcChatMessageService.getMsgByUuId(request.getLastMsgId(), UserUtils.getUserId());
            lastMsg.ifPresent(msg -> {
                DcChat chat = dcChatService.getChatByUuid(msg.getChatUuid());
                List<DcChatMessage> historyMsgList = chatMsgService.getChainedMsgList(chat, request.getLastMsgId());
                List<Message> historyUserMsg = historyMsgList.stream()
                        .filter(m -> m.getRole().equalsIgnoreCase(Role.USER.name()))
                        .map(m -> m.getContent().getContent())
                        .map(UserMessage::from)
                        .collect(Collectors.toList());
                // 将所有历史消息插入上下文
                completionRequestBuilder.messages(historyUserMsg);
            });
        }

        if (StringUtils.isNotBlank(request.getFileVid())) {
            // 传入文件不为空时，读取文件内容
            Optional<CodeFileAnalysis> fileAnalysis = codeFileAnalysisService.getByItemAndVid(analysisItem.getId(), request.getFileVid());
            fileAnalysis.ifPresent(f -> {
                String fileContent = embeddingSearchService.getCodeDescByFileVid(analysisItem.getId(), f.getFileVid());

                if (StringUtils.isNotBlank(fileContent)) {
                    questionAdvicePromptTemplate.withVariable(
                            "contextData",
                            PromptBuilder.instance().bold("文件名：").append(f.getFilePath()).newLine()
                                    .append("文件内容:").newLine()
                                    .codeBlock(CodeLanguage.PLAINTEXT, fileContent)
                    );
                }
            });
        }
        if (request.getN() != null && request.getN() > 0) {
            questionAdvicePromptTemplate.withVariable("questionCount", request.getN());
        }
        completionRequestBuilder.systemMessage(
                SystemMessage.from(questionAdvicePromptTemplate.format().toString())
        );

        ChatCompletionRequest completionRequest = completionRequestBuilder.build();
        ChatCompletionResponse response = oneApiClient.syncChatCompletion(completionRequest).execute();

        return convertQuestionVo(response);
    }

    private AdviceQuestionVO convertQuestionVo(ChatCompletionResponse response) {
        String content = response.choices().get(0).message().content();
        return new AdviceQuestionVO().setQuestions(
                Stream.of(content.split("\n"))
                        .map(s -> s.replaceAll("^-\\s*", "").trim())
                        .collect(Collectors.toList())
        );
    }

    private PromptTemplate adviceQuestionsPromptTemplate() {
        PromptBuilder promptBuilder = PromptBuilder.instance();
        promptBuilder.h1(PromptBuilder.instance().bold("---Role---"));
        promptBuilder.append("You are a helpful assistant generating a bulleted list of ")
                .variable("questionCount", 5)
                .append(" questions about data in the contents provided.")
                .newLine();

        promptBuilder.h1(PromptBuilder.instance().bold("---Reference Contents---"))
                .variable("contextData")
                .newLine();

        promptBuilder.h1(PromptBuilder.instance().bold("---Goal---"))
                .append("Generate a bulleted list of ")
                .variable("questionCount", 5)
                .append(" candidates for the next question. Use - marks as bullet points.")
                .newLine();
        promptBuilder.lines(
                "These candidate questions should represent the most important or urgent information content or themes in the reference contents.",
                "The candidate questions should be answerable using the reference contents provided, but should not mention any specific data fields or data tables in the question text.",
//                "If the user's questions reference several named entities, then each candidate question should reference all named entities."
                "All candidate questions should in proper Chinese"
        );

        promptBuilder.h1(PromptBuilder.instance().bold("---Example questions---"));

        return promptBuilder.toTemplate();
    }
}
