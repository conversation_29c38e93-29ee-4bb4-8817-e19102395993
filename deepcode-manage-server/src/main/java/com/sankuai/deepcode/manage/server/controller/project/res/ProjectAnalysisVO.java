package com.sankuai.deepcode.manage.server.controller.project.res;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * Package: com.sankuai.deepcode.manage.server.controller.project.res
 * Description:
 *
 * <AUTHOR>
 * @since 2025/3/13 16:54
 */
@Setter
@Getter
@ToString
@Accessors(chain = true)
public class ProjectAnalysisVO {
    ProjectVO project;
    List<CodeAnalysisItemVO> analysisItems;
}
