package com.sankuai.deepcode.manage.server.service;

import com.dianping.lion.client.log.Logger;
import com.dianping.lion.client.log.LoggerFactory;
import com.sankuai.deepcode.ast.common.enums.ChangeTypeEnum;
import com.sankuai.deepcode.dao.domain.*;
import com.sankuai.deepcode.dao.service.*;
import com.sankuai.deepcode.manage.server.model.file.*;
import com.sankuai.deepcode.manage.server.util.ConvertNode;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

@Service
public class GraphService {
    private static final Logger LOGGER = LoggerFactory.getLogger(GraphService.class);

    @Autowired
    private CodeClassAnalysisService codeClassAnalysisService;
    @Autowired
    private ClassImportClassService classImportClassService;
    @Autowired
    private CodeMethodAnalysisService codeMethodAnalysisService;
    @Autowired
    private MethodInvokeMethodService methodInvokeMethodService;
    @Autowired
    private CodeFileAnalysisService codeFileAnalysisService;
    @Autowired
    private CodeAnalysisItemService codeAnalysisItemService;

    @Autowired
    private CodeViewService codeViewService;

    public void getGraphByFile(long itemId, String vid, FileGraphRes fileGraphRes, Set<String> vidSet) {
        CodeClassAnalysis codeClassAnalysis = codeClassAnalysisService.getByClassVid(itemId, vid);
        if (null != codeClassAnalysis) {
            if (vidSet.contains(codeClassAnalysis.getClassVid())) {
                return;
            } else {
                vidSet.add(codeClassAnalysis.getClassVid());
            }
            FileShowNode fileShowNode = new FileShowNode();
            fileShowNode.setVid(codeClassAnalysis.getClassVid());
            fileShowNode.setType("class");
            fileShowNode.setDescription(codeClassAnalysis.getClassName());
            fileShowNode.setLabel(codeClassAnalysis.getClassName().split("\\.")[codeClassAnalysis.getClassName().split("\\.").length - 1]);
            fileGraphRes.getNodes().add(fileShowNode);

            List<ClassImportClass> classImportClasses = selectClassImportByVid(itemId, codeClassAnalysis.getClassVid());
            if (CollectionUtils.isNotEmpty(classImportClasses)) {
                for (ClassImportClass classImportClass : classImportClasses) {
                    FileShowEdge fileShowEdge = new FileShowEdge();
                    fileShowEdge.setSourceVid(classImportClass.getSource());
                    fileShowEdge.setTargetVid(classImportClass.getTarget());
                    fileGraphRes.getEdges().add(fileShowEdge);
                    if (!vidSet.contains(classImportClass.getTarget())) {
                        getGraphByFile(itemId, classImportClass.getTarget(), fileGraphRes, vidSet);
                    }
                }
            }
        }
    }


    public List<ClassImportClass> selectClassImportByVid(long itemId, String vid) {
        return classImportClassService.getByItemAndSourceClassVid(itemId, vid);
    }


    public TreeByVidInfo getTreeByVid(TreeByVidParam treeByVidParam) {
        TreeByVidInfo treeByVidInfo = new TreeByVidInfo();
        CodeMethodAnalysis codeMethodAnalysis = codeMethodAnalysisService.getByMethodVid(treeByVidParam.getItemId(), treeByVidParam.getVid());
        if (null != codeMethodAnalysis) {
            treeByVidInfo.setId(0);
            CodeClassAnalysis codeClassAnalysis = codeClassAnalysisService.getByClassName(treeByVidParam.getItemId(), codeMethodAnalysis.getClassName());
            if (null != codeClassAnalysis) {
                treeByVidInfo.setClassVid(codeClassAnalysis.getClassVid());
                treeByVidInfo.setClassName(codeClassAnalysis.getClassName());
            }
            treeByVidInfo.setMethodVid(codeMethodAnalysis.getMethodVid());
            treeByVidInfo.setMethodName(codeMethodAnalysis.getMethodName());
            treeByVidInfo.setType("method");
            treeByVidInfo.setStartLine(codeMethodAnalysis.getStartLine());
        }
        TreeCount treeCount = new TreeCount();
        treeCount.setId(1);
        List<TreeByVidInfo> treeByVidInfos = new ArrayList<>();
        if (treeByVidParam.isDown()) {
            treeByVidInfos = getTreeGraphByVid(treeByVidParam.getItemId(), treeByVidParam.getVid(), true, treeCount);
        } else {
            treeByVidInfos = getTreeGraphByVid(treeByVidParam.getItemId(), treeByVidParam.getVid(), false, treeCount);
        }
        treeByVidInfo.setChildren(treeByVidInfos);
        return treeByVidInfo;
    }

    public List<TreeByVidInfo> getTreeGraphByVid(long itemId, String startVid, boolean down, TreeCount treeCount) {
        List<TreeByVidInfo> treeByVidInfos = new ArrayList<>();
        if (treeCount.getVidSet().contains(startVid)) {
            return treeByVidInfos;
        } else {
            treeCount.getVidSet().add(startVid);
            if (down) {
                List<MethodInvokeMethod> methodInvokeMethods = methodInvokeMethodService.getBySource(itemId, startVid);
                for (MethodInvokeMethod methodInvokeMethod : methodInvokeMethods) {
                    if (!treeCount.getVidSet().contains(methodInvokeMethod.getTarget())) {
                        treeCount.addId();
                        TreeByVidInfo treeByVidInfo = new TreeByVidInfo();
                        CodeMethodAnalysis codeMethodAnalysis = codeMethodAnalysisService.getByMethodVid(itemId, methodInvokeMethod.getTarget());
                        if (null != codeMethodAnalysis) {
                            treeByVidInfo.setId(treeCount.getId());
                            CodeClassAnalysis codeClassAnalysis = codeClassAnalysisService.getByClassName(itemId, codeMethodAnalysis.getClassName());
                            if (null != codeClassAnalysis) {
                                treeByVidInfo.setClassVid(codeClassAnalysis.getClassVid());
                                treeByVidInfo.setClassName(codeClassAnalysis.getClassName());
                            }
                            treeByVidInfo.setMethodVid(codeMethodAnalysis.getMethodVid());
                            treeByVidInfo.setMethodName(codeMethodAnalysis.getMethodName());
                            treeByVidInfo.setType("method");
                            treeByVidInfo.setStartLine(codeMethodAnalysis.getStartLine());
                            treeByVidInfos.add(treeByVidInfo);
                        }
                        List<TreeByVidInfo> children = getTreeGraphByVid(itemId, methodInvokeMethod.getTarget(), down, treeCount);
                        if (CollectionUtils.isNotEmpty(children)) {
                            treeByVidInfo.setChildren(children);
                        }
                    }
                }
            } else {
                List<MethodInvokeMethod> methodInvokeMethods = methodInvokeMethodService.getByTarget(itemId, startVid);
                for (MethodInvokeMethod methodInvokeMethod : methodInvokeMethods) {
                    if (!treeCount.getVidSet().contains(methodInvokeMethod.getSource())) {
                        treeCount.addId();
                        TreeByVidInfo treeByVidInfo = new TreeByVidInfo();
                        CodeMethodAnalysis codeMethodAnalysis = codeMethodAnalysisService.getByMethodVid(itemId, methodInvokeMethod.getSource());
                        if (null != codeMethodAnalysis) {
                            treeByVidInfo.setId(treeCount.getId());
                            CodeClassAnalysis codeClassAnalysis = codeClassAnalysisService.getByClassName(itemId, codeMethodAnalysis.getClassName());
                            if (null != codeClassAnalysis) {
                                treeByVidInfo.setClassVid(codeClassAnalysis.getClassVid());
                                treeByVidInfo.setClassName(codeClassAnalysis.getClassName());
                            }
                            treeByVidInfo.setMethodVid(codeMethodAnalysis.getMethodVid());
                            treeByVidInfo.setMethodName(codeMethodAnalysis.getMethodName());
                            treeByVidInfo.setType("method");
                            treeByVidInfo.setStartLine(codeMethodAnalysis.getStartLine());
                            treeByVidInfos.add(treeByVidInfo);
                        }
                        List<TreeByVidInfo> children = getTreeGraphByVid(itemId, methodInvokeMethod.getSource(), down, treeCount);
                        if (CollectionUtils.isNotEmpty(children)) {
                            treeByVidInfo.setChildren(children);
                        }
                    }
                }
            }
            return treeByVidInfos;
        }
    }

    public ReposGraphByItemIdRes getReposGraphByItemId(long itemId) {
        CodeAnalysisItem codeAnalysisItem = codeAnalysisItemService.getById(itemId);
//        String projects = codeAnalysisItem.getGitUrl().split("/")[3];
        String repos = codeAnalysisItem.getGitUrl().split("/")[4].split("\\.git")[0];
        GraphCombos rootCombos = new GraphCombos();
        rootCombos.setId(repos);
        rootCombos.setLabel(repos);
        ReposGraphByItemIdRes result = new ReposGraphByItemIdRes();
        result.getCombos().add(rootCombos);
        List<ClassImportClass> classImportClasses = classImportClassService.getByItemId(itemId);
        Map<String, ClassImportClass> vidAndClassImportClass = new HashMap<>();
        for (ClassImportClass classImportClass : classImportClasses) {
            vidAndClassImportClass.put(classImportClass.getSource(), classImportClass);
        }

        List<CodeClassAnalysis> codeClassAnalyses = codeClassAnalysisService.getByItemId(itemId);
        Map<String, GraphNode> vidAndGraphNode = new HashMap<>();
        Map<String, List<GraphNode>> moduleAndGraphNode = new HashMap<>();
        for (CodeClassAnalysis codeClassAnalysis : codeClassAnalyses) {
            GraphNode graphNode = new GraphNode();
            graphNode.setVid(codeClassAnalysis.getClassVid());
            graphNode.setId(codeClassAnalysis.getClassPath());
            String label = codeClassAnalysis.getClassPath().split("/")[codeClassAnalysis.getClassPath().split("/").length - 1];
            graphNode.setPath(codeClassAnalysis.getClassPath().split("/" + label)[0]);
            graphNode.setLabel(label);
            graphNode.setNodeType("file");
            graphNode.setComboId(codeClassAnalysis.getModuleName());
            graphNode.setChangeType(codeClassAnalysis.getChangeType());
            result.getNodes().add(graphNode);
            vidAndGraphNode.put(graphNode.getVid(), graphNode);
            if (moduleAndGraphNode.containsKey(codeClassAnalysis.getModuleName())) {
                moduleAndGraphNode.get(codeClassAnalysis.getModuleName()).add(graphNode);
            } else {
                List<GraphNode> graphNodes = new ArrayList<>();
                graphNodes.add(graphNode);
                moduleAndGraphNode.put(codeClassAnalysis.getModuleName(), graphNodes);
            }
        }

        for (Map.Entry<String, ClassImportClass> entry : vidAndClassImportClass.entrySet()) {
            String sourceVid = entry.getKey();
            GraphNode sourceNode = vidAndGraphNode.get(sourceVid);
            GraphNode targetNode = vidAndGraphNode.get(entry.getValue().getTarget());
            if (null != sourceNode && null != targetNode) {
                GraphEdge graphEdge = new GraphEdge();
                graphEdge.setSource(sourceNode.getId());
                graphEdge.setTarget(targetNode.getId());
                result.getEdges().add(graphEdge);
            }
        }

        List<CodeFileAnalysis> codeFileAnalyses = codeFileAnalysisService.getByItemId(itemId);
        for (CodeFileAnalysis codeFileAnalysis : codeFileAnalyses) {
            GraphNode graphNode = new GraphNode();
            graphNode.setVid(codeFileAnalysis.getFileVid());
            graphNode.setId(codeFileAnalysis.getFilePath());
            String label = codeFileAnalysis.getFilePath().split("/")[codeFileAnalysis.getFilePath().split("/").length - 1];
            graphNode.setPath(codeFileAnalysis.getFilePath().split("/" + label)[0]);
            graphNode.setLabel(label);
            graphNode.setNodeType("file");
            graphNode.setChangeType(codeFileAnalysis.getChangeType());
            graphNode.setComboId(codeFileAnalysis.getModuleName());
            result.getNodes().add(graphNode);
            if (moduleAndGraphNode.containsKey(codeFileAnalysis.getModuleName())) {
                moduleAndGraphNode.get(codeFileAnalysis.getModuleName()).add(graphNode);
            } else {
                List<GraphNode> graphNodes = new ArrayList<>();
                graphNodes.add(graphNode);
                moduleAndGraphNode.put(codeFileAnalysis.getModuleName(), graphNodes);
            }
        }

        for (Map.Entry<String, List<GraphNode>> entry : moduleAndGraphNode.entrySet()) {
            Set<String> uniqVidSet = new HashSet<>();
            GraphCombos graphCombos = new GraphCombos();
            graphCombos.setId(entry.getKey());
            graphCombos.setLabel(entry.getKey());
            graphCombos.setParentId(repos);
            result.getCombos().add(graphCombos);

            Map<String, List<GraphNode>> pathSet = new HashMap<>();
            for (GraphNode graphNode : entry.getValue()) {
                if (pathSet.containsKey(graphNode.getPath())) {
                    pathSet.get(graphNode.getPath()).add(graphNode);
                } else {
                    List<GraphNode> graphNodes = new ArrayList<>();
                    graphNodes.add(graphNode);
                    pathSet.put(graphNode.getPath(), graphNodes);
                }
            }
            for (Map.Entry<String, List<GraphNode>> pathEntry : pathSet.entrySet()) {
                String path = pathEntry.getKey();
                int count = 0;
                String tmpPath = "";
                String parentPath = "";
                for (String sp : path.split("/")) {
                    if (count == 0) {
                        tmpPath = sp;
                    } else {
                        parentPath = tmpPath;
                        tmpPath += "/" + sp;
                    }
                    count++;
                    if (tmpPath.equals(path)) {
                        for (GraphNode graphNode : pathEntry.getValue()) {
                            if (!tmpPath.equals(graphNode.getId())) {
                                GraphEdge graphEdge = new GraphEdge();
                                graphEdge.setSource(tmpPath);
                                graphEdge.setTarget(graphNode.getId());
                                result.getEdges().add(graphEdge);
                            }
                        }
                    }

                    if (uniqVidSet.contains(tmpPath)) {
                        continue;
                    } else {
                        uniqVidSet.add(tmpPath);
                    }

                    if (tmpPath.equals(entry.getKey())) {
                        continue;
                    }

                    GraphNode graphNode = new GraphNode();
                    graphNode.setVid(tmpPath);
                    graphNode.setId(tmpPath);
                    if (tmpPath.split(parentPath + "/").length > 1) {
                        graphNode.setLabel(tmpPath.split(parentPath + "/")[1]);
                    } else {
                        graphNode.setLabel(parentPath);
                    }
                    graphNode.setNodeType("directory");
                    graphNode.setChangeType(ChangeTypeEnum.DEFAULT.getCode());
                    graphNode.setComboId(entry.getKey());
                    result.getNodes().add(graphNode);

                    if (uniqVidSet.contains(parentPath + "->" + tmpPath)) {
                        continue;
                    } else {
                        uniqVidSet.add(parentPath + "->" + tmpPath);
                    }

                    if (parentPath.equals(tmpPath)) {
                        continue;
                    }

                    if (parentPath.equals(entry.getKey())) {
                        continue;
                    }

                    GraphEdge graphEdge = new GraphEdge();
                    graphEdge.setSource(parentPath);
                    graphEdge.setTarget(tmpPath);
                    result.getEdges().add(graphEdge);
                }
            }
        }
        return result;
    }


    public ReposGraphByItemIdRes getReposGraphByItemIdV2(long itemId) {
        ReposGraphByItemIdRes result = new ReposGraphByItemIdRes();
        AllFilesRes allFilesRes = new AllFilesRes();
        List<FileData> allFileDataList = new ArrayList<>();
        List<CodeClassAnalysis> codeClassAnalyses = codeClassAnalysisService.getByItemId(itemId);
        List<CodeFileAnalysis> codeFileAnalyses = codeFileAnalysisService.getByItemId(itemId);
        Map<String, List<FileData>> allMap = new HashMap<>();
        for (CodeClassAnalysis codeClassAnalysis : codeClassAnalyses) {
            if (StringUtils.isEmpty(codeClassAnalysis.getClassName())) {
                continue;
            }
            if (StringUtils.isNotEmpty(codeClassAnalysis.getInClassName())) {
                continue;
            }
            FileData fileData = ConvertNode.convertFileData(codeClassAnalysis);
            if (allMap.containsKey(fileData.getModuleName())) {
                allMap.get(fileData.getModuleName()).add(fileData);
            } else {
                List<FileData> list = new ArrayList<>();
                list.add(fileData);
                allMap.put(fileData.getModuleName(), list);
            }
        }

        for (CodeFileAnalysis codeFileAnalysis : codeFileAnalyses) {
            FileData fileData = ConvertNode.convertFileData(codeFileAnalysis);
            if (allMap.containsKey(fileData.getModuleName())) {
                allMap.get(fileData.getModuleName()).add(fileData);
            } else {
                List<FileData> list = new ArrayList<>();
                list.add(fileData);
                allMap.put(fileData.getModuleName(), list);
            }
        }

        codeViewService.initFileData(allFileDataList, allMap);
        codeViewService.sortFileDataList(allFileDataList);
        codeViewService.shortTitile(allFileDataList, allFilesRes, false);

        CodeAnalysisItem codeAnalysisItem = codeAnalysisItemService.getById(itemId);
//        String projects = codeAnalysisItem.getGitUrl().split("/")[3];
        String repos = codeAnalysisItem.getGitUrl().split("/")[4].split("\\.git")[0];
        GraphCombos rootCombos = new GraphCombos();
        rootCombos.setId(repos);
        rootCombos.setLabel(repos);
        result.getCombos().add(rootCombos);

        Map<String, GraphNode> vidAndGraphNode = new HashMap<>();
        Set<String> modelNameSet = new HashSet<>();
        initReposGraphByItemIdRes(null, result, allFileDataList, vidAndGraphNode, modelNameSet, repos);

        List<ClassImportClass> classImportClasses = classImportClassService.getByItemId(itemId);
        Map<String, ClassImportClass> vidAndClassImportClass = new HashMap<>();
        for (ClassImportClass classImportClass : classImportClasses) {
            vidAndClassImportClass.put(classImportClass.getSource(), classImportClass);
        }

        for (Map.Entry<String, ClassImportClass> entry : vidAndClassImportClass.entrySet()) {
            String sourceVid = entry.getKey();
            GraphNode sourceNode = vidAndGraphNode.get(sourceVid);
            GraphNode targetNode = vidAndGraphNode.get(entry.getValue().getTarget());
            if (null != sourceNode && null != targetNode) {
                GraphEdge graphEdge = new GraphEdge();
                graphEdge.setSource(sourceNode.getId());
                graphEdge.setTarget(targetNode.getId());
                graphEdge.setFileImport(true);
                result.getEdges().add(graphEdge);
            }
        }
        return result;
    }

    public void initReposGraphByItemIdRes(GraphNode parentNode, ReposGraphByItemIdRes result, List<FileData> allFileDataList, Map<String, GraphNode> vidAndGraphNode, Set<String> modelNameSet, String repos) {
        for (FileData fileData : allFileDataList) {
            if (!modelNameSet.contains(fileData.getModuleName())) {
                modelNameSet.add(fileData.getModuleName());
                GraphCombos graphCombos = new GraphCombos();
                graphCombos.setId(fileData.getModuleName());
                graphCombos.setLabel(fileData.getModuleName());
                graphCombos.setParentId(repos);
                result.getCombos().add(graphCombos);
            }


            GraphNode children = new GraphNode();
            children.setId(fileData.getPath());
            children.setLabel(fileData.getTitle());
            children.setPath(fileData.getPath());
            if (fileData.isEnd()) {
                children.setVid(fileData.getVid());
                children.setNodeType("file");
            } else {
                children.setVid(fileData.getPath());
                children.setNodeType("directory");
            }
            children.setChangeType(fileData.getChangeType());
            children.setComboId(fileData.getModuleName());
            if (null == parentNode) {
                result.getNodes().add(children);
            } else {
                parentNode.getChildren().add(children);
                GraphEdge graphEdge = new GraphEdge();
                graphEdge.setSource(parentNode.getId());
                graphEdge.setTarget(children.getId());
                graphEdge.setFileImport(false);
                result.getEdges().add(graphEdge);
            }
            vidAndGraphNode.put(children.getVid(), children);
            if (CollectionUtils.isNotEmpty(fileData.getChildren())) {
                initReposGraphByItemIdRes(children, result, fileData.getChildren(), vidAndGraphNode, modelNameSet, repos);
            }
        }
    }


}
