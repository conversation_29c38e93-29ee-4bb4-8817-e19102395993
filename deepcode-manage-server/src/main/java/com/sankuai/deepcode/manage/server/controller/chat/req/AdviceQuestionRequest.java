package com.sankuai.deepcode.manage.server.controller.chat.req;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * Package: com.sankuai.deepcode.manage.server.controller.chat.req
 * Description:
 *
 * <AUTHOR>
 * @since 2025/3/27 10:41
 */
@Setter
@Getter
@ToString
public class AdviceQuestionRequest {
    @NotBlank
    @Schema(description = "模型名称", example = "gpt-4o-mini")
    String model;

    @Schema(description = "项目ID", example = "1")
    @NotNull(message = "项目ID不能为空")
    Long projectId;

    @Schema(description = "上一条消息ID(UUID)", example = "53fde8e8-992c-41a1-9b07-ee1f0d6d5223")
    String lastMsgId;

    @Schema(description = "消息列表, 优先用于问题生成，次要使用 chat 的最后几次会话")
    List<String> messages;

    @Schema(description = "文件vid")
    String fileVid;

    @Schema(description = "需要返回的推荐问题数量", example = "5")
    Integer n;

    @Schema(description = "温度", example = "0.6")
    Double temperature = Double.valueOf("0.6");
}
