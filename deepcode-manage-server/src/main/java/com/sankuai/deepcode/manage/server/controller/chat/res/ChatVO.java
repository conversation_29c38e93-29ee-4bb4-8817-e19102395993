package com.sankuai.deepcode.manage.server.controller.chat.res;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.time.LocalDateTime;

/**
 * Package: com.sankuai.deepcode.manage.server.controller.chat.res
 * Description:
 *
 * <AUTHOR>
 * @since 2025/3/14 15:24
 */
@Setter
@Getter
@ToString
public class ChatVO {
    @Schema(description = "对话id")
    private String id;
    @Schema(description = "项目id")
    private Long projectId;
    @Schema(description = "分析项id")
    private Long itemId;
    @Schema(description = "对话标题")
    private String title;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;
    @Schema(description = "更新时间")
    private LocalDateTime updateTime;

    @Schema(description = "是否开启对话")
    private Boolean chatAvailable;
    @Schema(description = "对话不可用原因")
    private String chatUnavailableReason;
}
