package com.sankuai.deepcode.manage.server.controller.chat.req;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * Package: com.sankuai.deepcode.manage.server.controller.chat.req
 * Description:
 *
 * <AUTHOR>
 * @since 2025/3/20 15:21
 */
@Setter
@Getter
@ToString
public class CreateChatRequest {
    @NotNull(message = "projectId不能为空")
    @Schema(description = "项目ID")
    private Long projectId;

    @NotBlank(message = "content不能为空")
    @Schema(description = "首条消息内容")
    private String content;
}
