package com.sankuai.deepcode.manage.server.model.file;

import lombok.Data;

import java.text.Collator;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Locale;

@Data
public class FileData implements Comparable<FileData> {
    private long id;
    private String vid;
    private int changeType;
    private int checkType;
    private String title = "";
    private String moduleName = "";
    private String path;
    private boolean end = false;
    private int commitCount = 0;
    private List<FileData> children = new ArrayList<>();

    @Override
    public int compareTo(FileData fileData) {
        if (this.end && !fileData.end) {
            return 1;
        } else if (!this.end && fileData.end) {
            return -1;
        }
        boolean thisTitleEmpty = this.title.isEmpty();
        boolean otherTitleEmpty = fileData.getTitle().isEmpty();
        if (thisTitleEmpty && !otherTitleEmpty) {
            return 1;
        } else if (!thisTitleEmpty && otherTitleEmpty) {
            return -1;
        }
        Collator comparator = Collator.getInstance(Locale.ENGLISH);
        return comparator.compare(this.title, fileData.getTitle());
    }
}
