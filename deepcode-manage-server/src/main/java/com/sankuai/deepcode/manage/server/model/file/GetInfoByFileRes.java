package com.sankuai.deepcode.manage.server.model.file;

import com.sankuai.deepcode.ast.common.model.java.FieldNode;
import com.sankuai.deepcode.ast.common.model.java.MethodNode;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
public class GetInfoByFileRes {
    private List<MethodNode> methodNodes = new ArrayList<>();
    private List<FieldNode> fieldNodes = new ArrayList<>();
}
