package com.sankuai.deepcode.manage.server.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.dianping.cat.util.StringUtils;
import com.google.common.reflect.TypeToken;
import com.sankuai.deepcode.ai.embedding.service.EmbeddingService;
import com.sankuai.deepcode.ai.llm.model.chat.*;
import com.sankuai.deepcode.ast.common.enums.DiffTypeEnum;
import com.sankuai.deepcode.ast.common.model.base.CodeView;
import com.sankuai.deepcode.ast.common.util.DeepCopy;
import com.sankuai.deepcode.dao.domain.CodeClassAnalysis;
import com.sankuai.deepcode.dao.domain.CodeMethodAnalysis;
import com.sankuai.deepcode.dao.domain.CodeViewAnalysis;
import com.sankuai.deepcode.dao.milvus.MilvusService;
import com.sankuai.deepcode.dao.service.CodeClassAnalysisService;
import com.sankuai.deepcode.dao.service.CodeMethodAnalysisService;
import com.sankuai.deepcode.dao.service.CodeViewAnalysisService;
import com.sankuai.deepcode.manage.server.common.ThreadPoolConfig;
import com.sankuai.deepcode.manage.server.model.code.CodeRunRes;
import com.sankuai.deepcode.manage.server.model.prompt.CoStar;
import io.milvus.v2.service.vector.request.AnnSearchReq;
import io.milvus.v2.service.vector.request.data.FloatVec;
import io.milvus.v2.service.vector.response.SearchResp;
import io.restassured.response.Response;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.io.BufferedReader;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.lang.reflect.Type;
import java.util.*;

import static io.restassured.RestAssured.given;

@Service
public class GptApiService {

    @Autowired
    private ThreadPoolConfig threadPoolConfig;

    @Autowired
    private EmbeddingService embeddingService;
    @Autowired
    private MilvusService milvusService;
    @Autowired
    private CodeMethodAnalysisService codeMethodAnalysisService;
    @Autowired
    private CodeClassAnalysisService codeClassAnalysisService;
    @Autowired
    private CodeRunService codeRunService;
    @Autowired
    private AgentToolsService agentToolsService;
    @Autowired
    private CodeViewAnalysisService codeViewAnalysisService;

    private static final Logger LOGGER = LoggerFactory.getLogger(GptApiService.class);
    private static final String URL = "https://smartapi.sankuai.com";
    private static final String MODEL = "gpt-4.1-mini";
//    private static final String MODEL = "LongCat-Prime-8K-Chat";

//    private static String DEFAULT_PROMPT = "";

    private static List<ChatTool> tools = new ArrayList<>();


    static {
//        CoStar coStar = new CoStar();
//        coStar.setContext("你将扮演一个ai智能助手");
//        coStar.setObjective("可以做代码知识图谱检索");
//        coStar.setStyle("尽量保证回答的准确性和可读性");
//        coStar.setTone("作为专业的编程人员");
//        coStar.setAudience("面向的是java服务的开发、测试、产品人员");
//        coStar.setReponse("回复内容尽量使用中文");
//
//        DEFAULT_PROMPT = coStar.initPrompt(coStar);
//
        initTools();
    }

    @Autowired
    private CodeViewService codeViewService;

    public void initHeaders(Map<String, String> headersMap) {
        headersMap.put("Content-Type", "application/json");
        headersMap.put("chatgptToken", "ef012de58924ca15a11129a3c1419c47");
        headersMap.put("appkey", "com.sankuai.houyi.manage.server");
    }


    public SseEmitter chatStream(long itemId, List<ChatMessage> chatMessages) {
        SseEmitter emitter = new SseEmitter();
        Map<String, String> headersMap = new HashMap<>();
        initHeaders(headersMap);

//        ChatMessage chatMessage = new ChatMessage();
//        chatMessage.setRole("system");
//        chatMessage.setContent(DEFAULT_PROMPT);
//        chatMessages.add(0, chatMessage);

        Map<String, Object> paramsMap = new HashMap<>();
        paramsMap.put("model", MODEL);
        paramsMap.put("messages", chatMessages);
        paramsMap.put("stream", true);
        paramsMap.put("tools", tools);
        paramsMap.put("operator", "deepcode");
        paramsMap.put("useCache", false);

        threadPoolConfig.streamExecutor().execute(() -> {
            try {
                Response response = given().headers(headersMap).body(JSONObject.toJSONString(paramsMap))
                        .when().post(URL + "/open/v1/aigc/originChatCompletions");

                if (response.statusCode() == 200) {
                    InputStream inputStream = response.getBody().asInputStream();
                    try (BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream))) {
                        String line;
                        boolean callTools = false;
                        List<ToolCall> tool_calls = new ArrayList<>();
                        while ((line = reader.readLine()) != null) {
                            if (!line.startsWith("data:")) {
                                continue;
                            }
                            String tmpStr = line.split("data: ")[1];
                            if (tmpStr.equals("[DONE]")) {
                                break;
                            }
                            Type type = new TypeToken<ChatMsgRes>() {
                            }.getType();
                            ChatMsgRes chatMsgRes = JSON.parseObject(tmpStr, type);
                            if (CollectionUtils.isNotEmpty(chatMsgRes.getChoices().get(0).getDelta().getTool_calls())) {
                                callTools = true;
                                if (CollectionUtils.isEmpty(tool_calls)) {
                                    tool_calls = chatMsgRes.getChoices().get(0).getDelta().getTool_calls();
                                }
                                for (int i = 0; i < chatMsgRes.getChoices().get(0).getDelta().getTool_calls().size(); i++) {
                                    ToolCallFunction toolCallFunction = chatMsgRes.getChoices().get(0).getDelta().getTool_calls().get(i).getFunction();
                                    tool_calls.get(i).getFunction().setArguments(tool_calls.get(i).getFunction().getArguments() + toolCallFunction.getArguments());
                                }
                            } else {
                                if (chatMsgRes.getLastOne() == true) {
                                    break;
                                }
                                StreamRes streamRes = new StreamRes();
                                streamRes.setContent(chatMsgRes.getChoices().get(0).getDelta().getContent());
                                if (StringUtils.isBlank(streamRes.getContent())) {
                                    continue;
                                }
                                streamRes.setFullContent(chatMsgRes.getContent());
                                emitter.send(SseEmitter.event().data(streamRes));
                            }
                        }
                        if (callTools) {
                            runToolCall(itemId, tool_calls, emitter, chatMessages);
                        }
                    }
                } else {
                    StreamRes streamRes = new StreamRes();
                    streamRes.setContent("请求失败:" + response.getBody().asString());
                    streamRes.setFullContent("请求失败:" + response.getBody().asString());
                    emitter.send(SseEmitter.event().data(streamRes));
                }
                emitter.complete();
            } catch (Exception e) {
                emitter.completeWithError(e);
            }
        });

        return emitter;
    }


    public void chatStreamBySseEmitter(List<ChatMessage> chatMessages, SseEmitter emitter) {
        Map<String, String> headersMap = new HashMap<>();
        initHeaders(headersMap);

//        ChatMessage chatMessage = new ChatMessage();
//        chatMessage.setRole("system");
//        chatMessage.setContent(DEFAULT_PROMPT);
//        chatMessages.add(0, chatMessage);

        Map<String, Object> paramsMap = new HashMap<>();
        paramsMap.put("model", MODEL);
        paramsMap.put("messages", chatMessages);
        paramsMap.put("stream", true);
        paramsMap.put("operator", "deepcode");
        paramsMap.put("useCache", false);

        try {
            Response response = given().headers(headersMap).body(JSONObject.toJSONString(paramsMap))
                    .when().post(URL + "/open/v1/aigc/originChatCompletions");

            if (response.statusCode() == 200) {
                InputStream inputStream = response.getBody().asInputStream();
                try (BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream))) {
                    String line;
                    while ((line = reader.readLine()) != null) {
                        if (!line.startsWith("data:")) {
                            continue;
                        }
                        String tmpStr = line.split("data: ")[1];
                        if (tmpStr.equals("[DONE]")) {
                            break;
                        }
                        Type type = new TypeToken<ChatMsgRes>() {
                        }.getType();
                        ChatMsgRes chatMsgRes = JSON.parseObject(tmpStr, type);
                        if (chatMsgRes.getLastOne() == true) {
                            break;
                        }
                        StreamRes streamRes = new StreamRes();
                        streamRes.setContent(chatMsgRes.getChoices().get(0).getDelta().getContent());
                        if (StringUtils.isBlank(streamRes.getContent())) {
                            continue;
                        }
                        streamRes.setFullContent(chatMsgRes.getContent());
                        emitter.send(SseEmitter.event().data(streamRes));
                    }
                }
            } else {
                StreamRes streamRes = new StreamRes();
                streamRes.setContent("请求失败:" + response.getBody().asString());
                streamRes.setFullContent("请求失败:" + response.getBody().asString());
                emitter.send(SseEmitter.event().data(streamRes));
            }
            emitter.complete();
        } catch (Exception e) {
            emitter.completeWithError(e);
        }

    }


    public void runToolCall(long itemId, List<ToolCall> tool_calls, SseEmitter emitter, List<ChatMessage> chatMessages) throws Exception {
        for (ToolCall toolCall : tool_calls) {
            if (toolCall.getFunction().getName().equals("getMethodByDesc")) {
                StreamRes streamRes = new StreamRes();
                streamRes.setContent("开始查询向量知识库...\n");
                streamRes.setFullContent("开始查询向量知识库...\n");
                emitter.send(SseEmitter.event().data(streamRes));
                List<String> texts = new ArrayList<>();
                Type mapType = new TypeToken<Map<String, String>>() {
                }.getType();
                Map<String, String> arguments = JSON.parseObject(toolCall.getFunction().getArguments(), mapType);
                texts.add(arguments.get("desc"));
                List<List<Float>> lists = embeddingService.getEmbeddings(texts);
                List<AnnSearchReq> searchRequests = new ArrayList<>();
                List<Float> queryVector = lists.get(0);
                List<Float> floatDataVector = new ArrayList<>();
                for (Float value : queryVector) {
                    floatDataVector.add(value);
                }
                FloatVec floatVectorField = new FloatVec(floatDataVector);
                AnnSearchReq chatDescReq = AnnSearchReq
                        .builder()
                        .vectorFieldName("chat_desc")
                        .vectors(Collections.singletonList(floatVectorField))
                        .topK(5)
                        .build();
                searchRequests.add(chatDescReq);
                AnnSearchReq descReq = AnnSearchReq
                        .builder()
                        .vectorFieldName("desc")
                        .vectors(Collections.singletonList(floatVectorField))
                        .topK(5)
                        .build();
                searchRequests.add(descReq);
                SearchResp searchResp = milvusService.hybridSearch("item_" + itemId + "_method", "partition0", searchRequests);
                if (CollectionUtils.isNotEmpty(searchResp.getSearchResults())
                        && CollectionUtils.isNotEmpty(searchResp.getSearchResults().get(0))) {
                    SearchResp.SearchResult searchResult = searchResp.getSearchResults().get(0).get(0);
                    if (searchResult.getScore() > 0.4) {
                        streamRes = new StreamRes();
                        streamRes.setContent("向量检索重排...\n");
                        streamRes.setFullContent("开始查询向量知识库...\n\n向量检索重排...\n");
                        emitter.send(SseEmitter.event().data(streamRes));
                        Map<String, String> stringMap = choiceSearch(searchResp.getSearchResults().get(0), chatMessages);
//                        Map<String, String> stringMap = new HashMap<>();
//                        Type type = new TypeToken<Map<String, String>>() {
//                        }.getType();
//                        stringMap = JSON.parseObject(JSON.toJSONString(searchResult.getEntity()), type);
                        String vid = stringMap.get("vid");
                        String desc_view = stringMap.get("desc_view");
                        String chatDescView = stringMap.get("chat_desc_view");

                        String content = "";
                        content += "查询到方法:" + agentToolsService.methodVidShowView(itemId, vid) + "\n\n";
                        CodeMethodAnalysis codeMethodAnalysis = codeMethodAnalysisService.getByMethodVid(itemId, vid);

                        String glorifyContent = chatMessages.get(chatMessages.size() - 1).getContent();
                        String res = glorifySearch(glorifyContent, itemId, vid, "method", desc_view, chatDescView);
                        if (StringUtils.isNotEmpty(res)) {
                            content += res;
                        } else {
                            if (null != codeMethodAnalysis) {
                                content += "方法名:" + codeMethodAnalysis.getMethodName() + "\n\n";
                            }
                            if (StringUtils.isNotEmpty(desc_view)) {
                                content += "方法注释:" + desc_view + "\n\n";
                            }
                            if (StringUtils.isNotEmpty(chatDescView)) {
                                content += "方法描述:" + chatDescView + "\n\n";
                            }
                        }
                        streamRes = new StreamRes();
                        streamRes.setContent(content);
                        streamRes.setFullContent(content);
                        emitter.send(SseEmitter.event().data(streamRes));
                    } else {
                        streamRes = new StreamRes();
                        streamRes.setContent("很抱歉，知识库内没有检索到，如果还需查询请增加描述信息");
                        streamRes.setFullContent("很抱歉，知识库内没有检索到，如果还需查询请增加描述信息");
                        emitter.send(SseEmitter.event().data(streamRes));
                    }
                } else {
                    streamRes = new StreamRes();
                    streamRes.setContent("很抱歉，知识库内检索为空，如果还需查询请增加描述信息");
                    streamRes.setFullContent("很抱歉，知识库内检索为空，如果还需查询请增加描述信息");
                    emitter.send(SseEmitter.event().data(streamRes));
                }
            } else if (toolCall.getFunction().getName().equals("getClassyDesc")) {
                StreamRes streamRes = new StreamRes();
                streamRes.setContent("开始查询向量知识库...\n");
                streamRes.setFullContent("开始查询向量知识库...\n\n向量检索重排...\n");
                emitter.send(SseEmitter.event().data(streamRes));
                List<String> texts = new ArrayList<>();
                texts.add(toolCall.getFunction().getArguments());
                List<List<Float>> lists = embeddingService.getEmbeddings(texts);
                List<AnnSearchReq> searchRequests = new ArrayList<>();
                List<Float> queryVector = lists.get(0);
                List<Float> floatDataVector = new ArrayList<>();
                for (Float value : queryVector) {
                    floatDataVector.add(value);
                }
                FloatVec floatVectorField = new FloatVec(floatDataVector);
                AnnSearchReq chatDescReq = AnnSearchReq
                        .builder()
                        .vectorFieldName("chat_desc")
                        .vectors(Collections.singletonList(floatVectorField))
                        .topK(5)
                        .build();
                searchRequests.add(chatDescReq);
                AnnSearchReq descReq = AnnSearchReq
                        .builder()
                        .vectorFieldName("desc")
                        .vectors(Collections.singletonList(floatVectorField))
                        .topK(5)
                        .build();
                searchRequests.add(descReq);
                SearchResp searchResp = milvusService.hybridSearch("item_" + itemId + "_class", "partition0", searchRequests);
                if (CollectionUtils.isNotEmpty(searchResp.getSearchResults())
                        && CollectionUtils.isNotEmpty(searchResp.getSearchResults().get(0))) {
                    SearchResp.SearchResult searchResult = searchResp.getSearchResults().get(0).get(0);
                    if (searchResult.getScore() > 0.4) {
                        streamRes = new StreamRes();
                        streamRes.setContent("向量检索重排...\n");
                        streamRes.setFullContent("向量检索重排...\n");
                        emitter.send(SseEmitter.event().data(streamRes));
                        Map<String, String> stringMap = choiceSearch(searchResp.getSearchResults().get(0), chatMessages);
//                        Map<String, String> stringMap = new HashMap<>();
//                        Type type = new TypeToken<Map<String, String>>() {
//                        }.getType();
//                        stringMap = JSON.parseObject(JSON.toJSONString(searchResult.getEntity()), type);
                        String vid = stringMap.get("vid");
                        String desc_view = stringMap.get("desc_view");
                        String chatDescView = stringMap.get("chat_desc_view");

                        String content = "";
                        content += "查询到类:" + agentToolsService.classVidShowView(itemId, vid) + "\n\n";
                        CodeClassAnalysis codeMethodAnalysis = codeClassAnalysisService.getByClassVid(itemId, vid);

                        String glorifyContent = chatMessages.get(chatMessages.size() - 1).getContent();
                        String res = glorifySearch(glorifyContent, itemId, vid, "class", desc_view, chatDescView);
                        if (StringUtils.isNotEmpty(res)) {
                            content += res;
                        } else {
                            if (null != codeMethodAnalysis) {
                                content += "类名:" + codeMethodAnalysis.getClassName() + "\n\n";
                            }
                            if (StringUtils.isNotEmpty(desc_view)) {
                                content += "类注释:" + desc_view + "\n\n";
                            }
                            if (StringUtils.isNotEmpty(chatDescView)) {
                                content += "类描述:" + chatDescView + "\n\n";
                            }
                        }
                        streamRes = new StreamRes();
                        streamRes.setContent(content);
                        streamRes.setFullContent(content);
                        emitter.send(SseEmitter.event().data(streamRes));
                    } else {
                        streamRes = new StreamRes();
                        streamRes.setContent("很抱歉，知识库内没有检索到，如果还需查询请增加描述信息");
                        streamRes.setFullContent("很抱歉，知识库内没有检索到，如果还需查询请增加描述信息");
                        emitter.send(SseEmitter.event().data(streamRes));
                    }
                } else {
                    streamRes = new StreamRes();
                    streamRes.setContent("很抱歉，知识库内检索为空，如果还需查询请增加描述信息");
                    streamRes.setFullContent("很抱歉，知识库内检索为空，如果还需查询请增加描述信息");
                    emitter.send(SseEmitter.event().data(streamRes));
                }

            } else if (toolCall.getFunction().getName().equals("getInfoByJavaCode")) {
                StreamRes streamRes = new StreamRes();
                streamRes.setContent("开始执行复杂查询...\n");
                streamRes.setFullContent("开始执行复杂查询...\n");
                emitter.send(SseEmitter.event().data(streamRes));


                CoStar coStar = new CoStar();
                coStar.setContext("你将扮演代码知识图谱检索的智能助手");
                coStar.setObjective("1、已知定义了java方法实体为：" +
                        "package com.sankuai.deepcode.ast.common.model.java;\n" +
                        "\n" +
                        "import com.sankuai.deepcode.ast.common.model.base.BaseNode;\n" +
                        "import lombok.Data;\n" +
                        "\n" +
                        "import java.util.ArrayList;\n" +
                        "import java.util.List;\n" +
                        "\n" +
                        "@Data\n" +
                        "public class MethodNode extends BaseNode {\n" +
                        "    private String vid; //方法唯一id\n" +
                        "    private String className; //方法类名称\n" +
                        "    private String inClassName = \"\"; //方法内部类名称\n" +
                        "    private String methodName; //方法名称\n" +
                        "    private JavaExtends superClass; //父类\n" +
                        "    private List<JavaImplements> interfaces = new ArrayList<>(); //接口类\n" +
                        "    private String access; //修饰符\n" +
                        "    private List<JavaParam> params = new ArrayList<>(); //参数\n" +
                        "    private JavaReturn returnInfo; //返回值\n" +
                        "    private List<JavaAnnotation> annotations = new ArrayList<>(); //注解\n" +
                        "    private String exceptions; //异常\n" +
                        "    private int startLine = -1; //开始行号\n" +
                        "    private int endLine = -1; //截止行号\n" +
                        "    private int changeType = 0; //  0无变更  1新增 2变更\n" +
                        "    private int complexity = 1; //圈复杂度\n" +
                        "    private List<Integer> changeLines = new ArrayList<>(); //所有变更行\n" +
                        "    private List<Integer> commentLines = new ArrayList<>(); //注释行\n" +
                        "  }\n\n" +
                        "2、com.sankuai.deepcode.manage.server.tools.AgentTools下有四个静态方法分别为：\n" +
                        "List<MethodNode> getAllMethodNode(long ItemId) 查询所有方法\n" +
                        "MethodNode getMethodNode(long ItemId, String methodVid)  根据指定vid查询方法\n" +
                        "List<MethodNode> getUpMethodNodes(long itemId, String methodVid, int step) 根据指定vid指定跳数查询方法上游\n" +
                        "List<MethodNode> getDownMethodNodes(long itemId, String methodVid, int step) 根据指定vid指定跳数查询方法下游\n" +
                        "String methodVidShowView(long itemId, String methodVid) 根据指定方法vid渲染展示内容\n" +
                        "3、如果需要使用ItemId，itemId=" + itemId + "L\n" +
                        "4、如果需要step又没有明确指出数量，默认值为5");

                coStar.setStyle("根据问题返回一段完整的java代码，包含import和类名，代码执行时会解决此问题\n" +
                        "1、方法必须是String runJava(String methodVid)" +
                        "2、根据问题拼接一段String返回，返回内容至少包含被methodVidShowView方法渲染后的vid展示内容" +
                        "3、如果返回的内容是多个，先返回总数，每个方法展示内容使用英文逗号加换行符拼接" +
                        "3、方法直接throws Exception");
                coStar.setTone("作为专业的编程人员");
                coStar.setAudience("面向的是java服务的开发、测试、产品人员");
                coStar.setReponse("1、先返回代码执行步骤的简要描述\n" +
                        "2、然后返回java代码块\n" +
                        "3、除此之外不要做其他描述\n");

                ChatMessage chatMessage = new ChatMessage();
                chatMessage.setRole("system");
                chatMessage.setContent(coStar.initPrompt(coStar));
                chatMessages.add(0, chatMessage);

                ChatMsgRes chatMsgRes = completions(chatMessages);
                String content = chatMsgRes.getChoices().get(0).getMessage().getContent();
                StringBuffer stepStr = new StringBuffer();
                StringBuffer javaStr = new StringBuffer();
                boolean isJava = false;
                for (String line : content.split("\n")) {
                    if (line.startsWith("```java")) {
                        isJava = true;
                        continue;
                    } else if (line.equals("```")) {
                        break;
                    }
                    if (isJava) {
                        javaStr.append(line).append("\n");
                    } else {
                        stepStr.append(line).append("\n");
                    }
                }


                String methodVid = "";
                try {
                    Type mapType = new TypeToken<Map<String, String>>() {
                    }.getType();
                    Map<String, String> arguments = JSON.parseObject(toolCall.getFunction().getArguments(), mapType);
                    if (arguments != null) {
                        methodVid = arguments.get("methodVid");
                    }
                } catch (Exception e) {
//                    throw new RuntimeException(e);
                }

                streamRes = new StreamRes();
                streamRes.setContent(stepStr + "\n");
                streamRes.setFullContent(stepStr + "\n");
                emitter.send(SseEmitter.event().data(streamRes));
                CodeRunRes codeRunRes = codeRunService.run(javaStr.toString(), methodVid);


                if (codeRunRes.isException()) {
                    streamRes = new StreamRes();
                    streamRes.setContent("执行代码异常，e:" + codeRunRes.getExceptionStr());
                    streamRes.setFullContent("执行代码异常，e:" + codeRunRes.getExceptionStr());
                } else {
                    streamRes = new StreamRes();
                    streamRes.setContent(stepStr + "\n" + codeRunRes.getRes());
                    streamRes.setFullContent(stepStr + "\n" + codeRunRes.getRes());
                }
                emitter.send(SseEmitter.event().data(streamRes));
            }
        }
    }

    public ChatMsgRes completions(List<ChatMessage> chatMessages) {
        ChatMsgRes res = null;
        Map<String, String> headersMap = new HashMap<>();
        initHeaders(headersMap);

//        ChatMessage chatMessage = new ChatMessage();
//        chatMessage.setRole("system");
//        chatMessage.setContent(DEFAULT_PROMPT);
//        chatMessages.add(0, chatMessage);

        Map<String, Object> paramsMap = new HashMap<>();
        paramsMap.put("model", MODEL);
        paramsMap.put("messages", chatMessages);
        paramsMap.put("stream", false);
        paramsMap.put("operator", "deepcode");
        paramsMap.put("useCache", false);

        try {
            Response response = given().headers(headersMap).body(JSONObject.toJSONString(paramsMap))
                    .when().post(URL + "/open/v1/aigc/originChatCompletions");
            Type type = new TypeToken<ChatMsgResponse>() {
            }.getType();
            ChatMsgResponse chatMsgResponse = JSON.parseObject(response.getBody().asString(), type);
            if (chatMsgResponse.getCode() == 0) {
                res = chatMsgResponse.getData();
            } else {
                LOGGER.error("调用completions接口失败 code:{} msg:{}", chatMsgResponse.getCode(), chatMsgResponse.getMsg());
            }
        } catch (Exception e) {
            LOGGER.error("调用completions接口异常e:", e);
        }
        return res;
    }

    public static void initTools() {
        ChatTool chatTool1 = new ChatTool();
        chatTool1.setId("getMethodByDesc");
        ChatFunction chatFunction1 = new ChatFunction();
        chatFunction1.setName("getMethodByDesc");
        chatFunction1.setDescription("查询单个方法详细信息，若查询多个方法则可以调用多次。一般的表述有：“有什么方法能做到”、“有什么方法能实现”等");
        ChatParameter chatParameter1 = new ChatParameter();
        List<String> requireds1 = new ArrayList<String>();
        requireds1.add("desc");
        chatParameter1.setRequired(requireds1);
        Map<String, ChatPropertie> properties1 = new HashMap<String, ChatPropertie>();
        ChatPropertie chatPropertie1 = new ChatPropertie();
        properties1.put("desc", chatPropertie1);
        chatPropertie1.setType("string");
        chatPropertie1.setDescription("方法描述，一般都是中文的方法功能描述。也可能是方法名称，一般都是纯英文或拼音的标准驼峰格式。");
        chatParameter1.setProperties(properties1);
        chatFunction1.setParameters(chatParameter1);
        chatTool1.setFunction(chatFunction1);
        tools.add(chatTool1);

        ChatTool chatTool2 = new ChatTool();
        chatTool2.setId("getClassyDesc");
        ChatFunction chatFunction2 = new ChatFunction();
        chatFunction2.setName("getClassyDesc");
        chatFunction2.setDescription("查询单个类详细信息，若查询多个类则可以调用多次。一般的表述有：“有什么类能做到”、“有什么类能实现”等");
        ChatParameter chatParameter2 = new ChatParameter();
        List<String> requireds2 = new ArrayList<String>();
        requireds2.add("desc");
        chatParameter2.setRequired(requireds2);
        Map<String, ChatPropertie> properties2 = new HashMap<String, ChatPropertie>();
        ChatPropertie chatPropertie2 = new ChatPropertie();
        properties2.put("desc", chatPropertie2);
        chatPropertie2.setType("string");
        chatPropertie2.setDescription("方法描述，一般都是中文的类描述。也可能是类名称，一般都是纯英文、或英文.拼接或拼音的标准驼峰格式。");
        chatParameter2.setProperties(properties2);
        chatFunction2.setParameters(chatParameter2);
        chatTool2.setFunction(chatFunction2);
        tools.add(chatTool2);

        ChatTool chatTool3 = new ChatTool();
        chatTool3.setId("getInfoByJavaCode");
        ChatFunction chatFunction3 = new ChatFunction();
        chatFunction3.setName("getInfoByJavaCode");
        chatFunction3.setDescription("复杂的条件查询。一般的表述有：“这个方法上游/下游，入口/出口都有什么方法”、“改动/变更/新增方法有哪些”、“xxx属性最高/大/多的方法”、“方法名带xxx的有哪些”等");
        ChatParameter chatParameter3 = new ChatParameter();
        Map<String, ChatPropertie> properties3 = new HashMap<String, ChatPropertie>();
        ChatPropertie chatPropertie3 = new ChatPropertie();
        properties3.put("methodVid", chatPropertie3);
        chatPropertie3.setType("string");
        chatPropertie3.setDescription("如果是根据某个方法做链路追踪methodVid作为起点。也可以为空，作为全遍历的场景，例如查询所有没有返回值的方法。");
        chatParameter3.setProperties(properties3);
        chatFunction3.setParameters(chatParameter3);
        chatTool3.setFunction(chatFunction3);
        tools.add(chatTool3);
    }


    public String glorifySearch(String content, long itemId, String vid, String type, String desc, String chatDesc) {
        String code = "";
        String res = null;
        try {
            if (type.equals("method")) {
                CodeMethodAnalysis codeMethodAnalysis = codeMethodAnalysisService.getByMethodVid(itemId, vid);
                if (null != codeMethodAnalysis) {
                    CodeClassAnalysis codeClassAnalysis = codeClassAnalysisService.getByClassName(itemId, codeMethodAnalysis.getClassName());
                    if (null != codeClassAnalysis) {
                        List<CodeViewAnalysis> codeViewAnalyses = codeViewAnalysisService.getByStartAndEnd(itemId, codeClassAnalysis.getClassVid(), codeMethodAnalysis.getStartLine(), codeMethodAnalysis.getEndLine());
                        List<CodeView> codeViews = codeViewService.covertCodeView(codeViewAnalyses);
                        for (CodeView codeView : codeViews) {
                            if (codeView.getType() == DiffTypeEnum.SAM.getCode()) {
                                code += "    " + codeView.getView() + "\n";
                            } else if (codeView.getType() == DiffTypeEnum.ADD.getCode()) {
                                code += "+    " + codeView.getView() + "\n";
                            }
                        }
                    }
                }
            } else {
                List<CodeViewAnalysis> codeViewAnalyses = codeViewAnalysisService.getBySourceVid(itemId, vid);
                List<CodeView> codeViews = codeViewService.covertCodeView(codeViewAnalyses);
                for (CodeView codeView : codeViews) {
                    if (codeView.getType() == DiffTypeEnum.SAM.getCode()) {
                        code += "    " + codeView.getView() + "\n";
                    } else if (codeView.getType() == DiffTypeEnum.ADD.getCode()) {
                        code += "+    " + codeView.getView() + "\n";
                    }
                }
            }
            CoStar coStar = new CoStar();
            coStar.setContext("你将扮演代码知识图谱检索的智能助手");
            coStar.setObjective("根据类或者方法源码、注释、描述等信息，根据问题返回答案");
            coStar.setStyle("尽量保证回答的准确性和可读性");
            coStar.setTone("作为专业的编程人员");
            coStar.setAudience("面向的是java服务的开发、测试、产品人员");
            coStar.setReponse("答案尽量使用中文回复");

            ChatMessage chatMessage = new ChatMessage();
            chatMessage.setRole("system");
            chatMessage.setContent(coStar.initPrompt(coStar));

            List<ChatMessage> chatMessages = new ArrayList<>();
            chatMessages.add(chatMessage);

            ChatMessage chatMessage1 = new ChatMessage();
            chatMessage1.setRole("user");
            String str = "";
            if (StringUtils.isNotEmpty(code)) {
                str += "已知源码如下：\n" + code + "\n";
            }
            if (StringUtils.isNotEmpty(chatDesc)) {
                str += "描述如下：" + chatDesc + "\n";
            }
            str += "问题为：" + content + "\n";
            str += "请结合已知信息重新回答问题";
            chatMessage1.setContent(str);
            chatMessages.add(chatMessage1);

            ChatMsgRes chatMsgRes = completions(chatMessages);

            res = chatMsgRes.getChoices().get(0).getMessage().getContent();
        } catch (Exception e) {
//            throw new RuntimeException(e);
        }
        return res;
    }

    public Map<String, String> choiceSearch(List<SearchResp.SearchResult> searchResults, List<ChatMessage> chatMessages) {
        List<ChatMessage> chatMessagesNew = new ArrayList<>();
        for (ChatMessage chatMessage : chatMessages) {
            ChatMessage chat = DeepCopy.deepCopy(chatMessage, ChatMessage.class);
            chatMessagesNew.add(chat);
        }
        Map<String, String> res = new HashMap<>();
        Map<String, String> first = new HashMap<>();
        try {
            Map<String, Map<String, String>> map = new HashMap<>();
            int count = 0;
            for (SearchResp.SearchResult searchResult : searchResults) {
                Type type = new TypeToken<Map<String, String>>() {
                }.getType();
                Map<String, String> stringMap = JSON.parseObject(JSON.toJSONString(searchResult.getEntity()), type);
                map.put(stringMap.get("vid"), stringMap);
                if (count == 0) {
                    first = stringMap;
                }
                count++;
            }

            ChatMessage chatMessage1 = new ChatMessage();
            chatMessage1.setRole("user");
            String str = "查询到:";
            for (Map.Entry<String, Map<String, String>> entry : map.entrySet()) {
                str += "vid：" + entry.getValue().get("vid") + "\n,注释:" + entry.getValue().get("desc_view") + "\n,描述:" + entry.getValue().get("chat_desc_view") + "\n";
            }
            chatMessage1.setContent(str);
            chatMessagesNew.add(chatMessage1);


            ChatMessage chatMessage2 = new ChatMessage();
            chatMessage2.setRole("user");
            chatMessage2.setContent("查询到的哪个vid最符合问题？，请直接回复vid，不要做其他任何描述");
            chatMessagesNew.add(chatMessage2);

            ChatMsgRes chatMsgRes = completions(chatMessagesNew);
            String vid = chatMsgRes.getChoices().get(0).getMessage().getContent();
            res = map.get(vid);
        } catch (Exception e) {
            return first;
        }
        if (null == res) {
            return first;
        } else {
            return res;
        }
    }


    public SseEmitter apiChatStream(List<ChatMessage> chatMessages) {
        SseEmitter emitter = new SseEmitter();
        Map<String, String> headersMap = new HashMap<>();
        initHeaders(headersMap);

        Map<String, Object> paramsMap = new HashMap<>();
        paramsMap.put("model", MODEL);
        paramsMap.put("messages", chatMessages);
        paramsMap.put("stream", true);
        paramsMap.put("operator", "deepcode");
        paramsMap.put("useCache", false);

        String content = chatMessages.get(chatMessages.size() - 1).getContent();

        threadPoolConfig.streamExecutor().execute(() -> {
            try {
                StreamRes streamRes = new StreamRes();
                streamRes.setContent("开始查询向量知识库...\n");
                streamRes.setFullContent("开始查询向量知识库...\n\n向量检索重排...\n");
                emitter.send(SseEmitter.event().data(streamRes));
                List<String> texts = new ArrayList<>();
                texts.add(content);
                List<List<Float>> lists = embeddingService.getEmbeddings(texts);
                List<Float> queryVector = lists.get(0);
                List<Float> floatDataVector = new ArrayList<>();
                for (Float value : queryVector) {
                    floatDataVector.add(value);
                }
                FloatVec floatVectorField = new FloatVec(floatDataVector);
                SearchResp searchResp = milvusService.searchApiChat("api_desc", "partition0", Collections.singletonList(floatVectorField), "chat_by_qwen_desc");
                if (CollectionUtils.isNotEmpty(searchResp.getSearchResults())
                        && CollectionUtils.isNotEmpty(searchResp.getSearchResults().get(0))) {
                    SearchResp.SearchResult searchResult = searchResp.getSearchResults().get(0).get(0);
                    if (searchResult.getScore() > 0.4) {
                        streamRes = new StreamRes();
                        streamRes.setContent("向量检索重排...\n");
                        streamRes.setFullContent("向量检索重排...\n");
                        emitter.send(SseEmitter.event().data(streamRes));
                        Map<String, Map<String, String>> stringMap = choiceApiChatSearch(searchResp.getSearchResults().get(0), chatMessages);

                        StringBuilder sb = new StringBuilder();
                        sb.append("根据问题检索到的知识为：\n\n");
                        int count = 1;
                        for (Map.Entry<String, Map<String, String>> entry : stringMap.entrySet()) {
                            sb.append(count + "、appkey:" + entry.getValue().get("appkey") + "\n\n");
                            sb.append("className:" + entry.getValue().get("class_name") + "\n\n");
                            sb.append("methodName:" + entry.getValue().get("method_name") + "\n\n");
                            sb.append("接口描述:" + entry.getValue().get("desc_by_qwen") + "\n\n");
                            sb.append("余弦相似分数:" + entry.getValue().get("score") + "\n\n");
                            count++;
                        }

                        streamRes = new StreamRes();
                        streamRes.setContent(sb.toString());
                        streamRes.setFullContent(sb.toString());
                        emitter.send(SseEmitter.event().data(streamRes));
                    } else {
                        streamRes = new StreamRes();
                        streamRes.setContent("很抱歉，知识库内没有检索到，如果还需查询请增加描述信息");
                        streamRes.setFullContent("很抱歉，知识库内没有检索到，如果还需查询请增加描述信息");
                        emitter.send(SseEmitter.event().data(streamRes));
                    }
                } else {
                    streamRes = new StreamRes();
                    streamRes.setContent("很抱歉，知识库内检索为空，如果还需查询请增加描述信息");
                    streamRes.setFullContent("很抱歉，知识库内检索为空，如果还需查询请增加描述信息");
                    emitter.send(SseEmitter.event().data(streamRes));
                }
                emitter.complete();
            } catch (Exception e) {
                emitter.completeWithError(e);
            }
        });

        return emitter;
    }

    public Map<String, Map<String, String>> choiceApiChatSearch(List<SearchResp.SearchResult> searchResults, List<ChatMessage> chatMessages) {
        Map<String, Map<String, String>> result = new HashMap<>();
        List<ChatMessage> chatMessagesNew = new ArrayList<>();
        for (ChatMessage chatMessage : chatMessages) {
            ChatMessage chat = DeepCopy.deepCopy(chatMessage, ChatMessage.class);
            chatMessagesNew.add(chat);
        }
        Map<String, Map<String, String>> first = new HashMap<>();
        try {
            Map<String, Map<String, String>> map = new HashMap<>();
            int count = 0;
            for (SearchResp.SearchResult searchResult : searchResults) {
                Type type = new TypeToken<Map<String, String>>() {
                }.getType();
                Map<String, String> stringMap = JSON.parseObject(JSON.toJSONString(searchResult.getEntity()), type);
                stringMap.put("score", String.valueOf(searchResult.getScore()));
                map.put(stringMap.get("id"), stringMap);
                if (count < 5) {
                    first.put(stringMap.get("id"), stringMap);
                }
                count++;
            }

            ChatMessage chatMessage1 = new ChatMessage();
            chatMessage1.setRole("System");
            String str = "你将扮演一个接口描述查询工具，已知:\n";
            for (Map.Entry<String, Map<String, String>> entry : map.entrySet()) {
                str += "id：" + entry.getValue().get("id") + "\n,接口描述:" + entry.getValue().get("desc_by_qwen") + "\n";
            }
            chatMessage1.setContent(str);
            chatMessagesNew.add(chatMessage1);


            ChatMessage chatMessage2 = new ChatMessage();
            chatMessage2.setRole("user");
            chatMessage2.setContent("找到到最符合问题的5个id，使用英文逗号分隔，不要有其他任何描述");
            chatMessagesNew.add(chatMessage2);

            ChatMsgRes chatMsgRes = completions(chatMessagesNew);
            for (String tmp : chatMsgRes.getChoices().get(0).getMessage().getContent().split(",")) {
                result.put(tmp, map.get(tmp.trim()));
            }
            return result;
        } catch (Exception e) {
            return first;
        }

    }

}