package com.sankuai.deepcode.manage.server.common;

import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.ThreadPoolExecutor;

@Configuration
public class ThreadPoolConfig {

    private ThreadPoolTaskExecutor executor;

    public ThreadPoolTaskExecutor streamExecutor() {
        if (executor == null) {
            executor = new ThreadPoolTaskExecutor();
            // 设置核心线程数
            executor.setCorePoolSize(12);
            // 设置最大线程数
            executor.setMaxPoolSize(50);
            // 设置队列容量
            executor.setQueueCapacity(100);
            // 设置线程活跃时间（秒）
            executor.setKeepAliveSeconds(60);
            // 设置默认线程名称
            executor.setThreadNamePrefix("stream-run-");
            // 设置拒绝策略
            executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
            // 等待所有任务结束后再关闭线程池
            executor.setWaitForTasksToCompleteOnShutdown(true);
            executor.initialize();
            return executor;
        } else {
            return executor;
        }
    }
}