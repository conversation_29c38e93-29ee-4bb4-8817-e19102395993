package com.sankuai.deepcode.manage.server;

import com.sankuai.inf.octo.mns.util.ProcessInfoUtil;
import org.mybatis.spring.boot.autoconfigure.MybatisAutoConfiguration;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.context.annotation.ComponentScan;


/**
 * <AUTHOR>
 */
@SpringBootApplication(exclude = {DataSourceAutoConfiguration.class, MybatisAutoConfiguration.class})
@ComponentScan("com.sankuai.deepcode")
public class DeepCodePlatformStartApp {
    private static final Logger log = LoggerFactory.getLogger(DeepCodePlatformStartApp.class);

    public static void main(String[] args) {
        if (ProcessInfoUtil.isMac()) {
            System.setProperty("spring.profiles.active", "local");
        }
        SpringApplication.run(DeepCodePlatformStartApp.class, args);
        log.info("服务启动成功！");
    }
}