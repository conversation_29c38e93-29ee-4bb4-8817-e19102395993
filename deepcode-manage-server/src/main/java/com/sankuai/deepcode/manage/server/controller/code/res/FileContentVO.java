package com.sankuai.deepcode.manage.server.controller.code.res;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * Package: com.sankuai.deepcode.manage.server.controller.code.res
 * Description:
 *
 * <AUTHOR>
 * @since 2025/3/13 22:00
 */
@Getter
@Setter
@ToString
public class FileContentVO {
    @Schema(description = "文件路径")
    private String filePath;
    @Schema(description = "sourceBranch分支的文件内容")
    private String sourceBranchFileContent;
    @Schema(description = "targetBranch分支的文件内容")
    private String targetBranchFileContent;
    @Schema(description = "是否有差异")
    Boolean haveDiff;
    @Schema(description = "文件类型")
    String fileType;
}
