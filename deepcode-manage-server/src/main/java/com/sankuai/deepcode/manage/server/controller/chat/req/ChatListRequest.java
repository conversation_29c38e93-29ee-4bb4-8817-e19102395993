package com.sankuai.deepcode.manage.server.controller.chat.req;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * Package: com.sankuai.deepcode.manage.server.controller.chat.req
 * Description:
 *
 * <AUTHOR>
 * @since 2025/3/14 15:20
 */
@Setter
@Getter
@ToString
public class ChatListRequest {
    @Schema(description = "页码", example = "1")
    private Integer pageNo = 1;
    @Schema(description = "每页大小", example = "10")
    private Integer pageSize = 10;
}
