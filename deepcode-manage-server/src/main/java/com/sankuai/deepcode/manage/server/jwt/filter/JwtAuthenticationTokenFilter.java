package com.sankuai.deepcode.manage.server.jwt.filter;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.sankuai.deepcode.dao.domain.SysToken;
import com.sankuai.deepcode.dao.mapper.SysTokenMapper;
import com.sankuai.deepcode.dao.service.SysTokenService;
import com.sankuai.deepcode.manage.server.jwt.domain.LoginUser;
import com.sankuai.deepcode.manage.server.jwt.utils.JwtUtil;
import com.sankuai.deepcode.manage.server.jwt.utils.UserUtils;
import io.jsonwebtoken.Claims;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import org.springframework.web.filter.OncePerRequestFilter;

import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

@Component
@Slf4j
public class JwtAuthenticationTokenFilter extends OncePerRequestFilter {
    @Autowired
    private SysTokenService sysTokenService;

    @Autowired
    private SysTokenMapper tokenMapper;

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain) throws ServletException, IOException {
        //获取token
        String token = request.getHeader("token");
        if (!StringUtils.hasText(token)) {
            //放行
            filterChain.doFilter(request, response);
            return;
        }
        //解析token
        String userid;
        LoginUser loginUserInfo = null;
        try {
            Claims claims = JwtUtil.parseJWT(token);
            String userInfo = claims.getSubject();
            loginUserInfo = new Gson().fromJson(userInfo, LoginUser.class);
            userid = String.valueOf(loginUserInfo.getUser().getId());
        } catch (Exception e) {
            log.warn("token非法: {}", e.getMessage());
            throw new RuntimeException("token非法");
        }
        //从redis中获取用户信息
        String redisKey = "login:" + userid;
        //new QueryWrapper<Token>().eq("user_id", userid)

        Optional<SysToken> latestToken = sysTokenService.getLatestTokenByUserId(Long.valueOf(userid));
        if (!latestToken.isPresent()) {
            SysToken tokenInfo = new SysToken();
            tokenInfo.setUserId(Long.valueOf(userid));
            tokenInfo.setToken(token);
            Gson gson = new GsonBuilder().setDateFormat("yyyy HH:mm:ss").create();
            tokenInfo.setTokenInfo(gson.toJson(loginUserInfo));
            tokenInfo.setValid(1);
            tokenInfo.setCreateTime(LocalDateTime.now());
            tokenInfo.setUpdateTime(LocalDateTime.now());
//         jwt, userid, new Gson().toJson(loginUser), 1, new Date(), new Date()
            tokenMapper.insert(tokenInfo);
        } else {
            Gson gson = new GsonBuilder().setDateFormat("yyyy HH:mm:ss").create();
            loginUserInfo = gson.fromJson(latestToken.get().getTokenInfo(), LoginUser.class);
        }
//        LoginUser loginUser = redisCache.getCacheObject(redisKey);
        if (Objects.isNull(loginUserInfo)) {
            throw new RuntimeException("用户未登录");
        }
        UserUtils.setUser(loginUserInfo);
        //存入SecurityContextHolder
        //TODO 获取权限信息封装到Authentication中
        UsernamePasswordAuthenticationToken authenticationToken =
                new UsernamePasswordAuthenticationToken(loginUserInfo, null, loginUserInfo.getAuthorities());
        SecurityContextHolder.getContext().setAuthentication(authenticationToken);
        // todo 放行
        filterChain.doFilter(request, response);
    }
}
