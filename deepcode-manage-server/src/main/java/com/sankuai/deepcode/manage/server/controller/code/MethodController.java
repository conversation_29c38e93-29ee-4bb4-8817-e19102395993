package com.sankuai.deepcode.manage.server.controller.code;

import com.sankuai.deepcode.commons.CommonResult;
import com.sankuai.deepcode.commons.DeepCodePreconditions;
import com.sankuai.deepcode.dao.domain.CodeAnalysisItem;
import com.sankuai.deepcode.dao.domain.CodeFileAnalysis;
import com.sankuai.deepcode.dao.domain.CodeMethodAnalysis;
import com.sankuai.deepcode.dao.domain.DcProject;
import com.sankuai.deepcode.dao.service.CodeFileAnalysisService;
import com.sankuai.deepcode.dao.service.CodeMethodAnalysisService;
import com.sankuai.deepcode.dao.service.DcProjectService;
import com.sankuai.deepcode.manage.server.controller.code.req.MethodRequest;
import com.sankuai.deepcode.manage.server.controller.code.req.MethodTopologyRequest;
import com.sankuai.deepcode.manage.server.controller.code.res.MethodDefinitionVO;
import com.sankuai.deepcode.manage.server.controller.code.res.MethodTopologyVO;
import com.sankuai.deepcode.manage.server.enums.ErrorCodeEnum;
import com.sankuai.deepcode.manage.server.service.code.MethodService;
import com.sankuai.deepcode.manage.server.service.project.ProjectAnalysisService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Optional;

/**
 * Package: com.sankuai.deepcode.manage.server.controller.code
 * Description:
 *
 * <AUTHOR>
 * @since 2025/3/17 16:12
 */
@Tag(name = "方法相关接口")
@RestController
@RequestMapping("/method")
@Slf4j
public class MethodController {
    private final ProjectAnalysisService projectAnalysisService;
    private final DcProjectService dcProjectService;
    private final MethodService methodService;
    private final CodeFileAnalysisService codeFileAnalysisService;
    private final CodeMethodAnalysisService codeMethodAnalysisService;

    public MethodController(
            ProjectAnalysisService projectAnalysisService,
            DcProjectService dcProjectService,
            MethodService methodService,
            CodeFileAnalysisService codeFileAnalysisService,
            CodeMethodAnalysisService codeMethodAnalysisService

    ) {
        this.projectAnalysisService = projectAnalysisService;
        this.dcProjectService = dcProjectService;

        this.methodService = methodService;
        this.codeFileAnalysisService = codeFileAnalysisService;
        this.codeMethodAnalysisService = codeMethodAnalysisService;
    }


    @Operation(summary = "获取方法定义")
    @PostMapping("/definitions")
    public CommonResult<List<MethodDefinitionVO>> definitions(@RequestBody @Validated MethodRequest methodRequest) {
        DcProject dcProject = dcProjectService.getById(methodRequest.getProjectId());
        CodeAnalysisItem analysisItem = projectAnalysisService.getAnalysisItemAndValidation(dcProject);

        // 1. 查询文件分析
        Optional<CodeFileAnalysis> codeFileAnalysis = codeFileAnalysisService.getByItemIdAndFilePath(analysisItem.getId(), methodRequest.getFilePath());
        DeepCodePreconditions.checkBizArgument(codeFileAnalysis.isPresent(), ErrorCodeEnum.DATA_NOT_EXISTS, "文件路径不存在，请确认文件路径是否正确");

        return CommonResult.success(methodService.getMethodDefinitions(
                dcProject, analysisItem, codeFileAnalysis.get(), methodRequest.getLine(), methodRequest.getMethodName()
        ));
    }

    @Operation(summary = "方法被调用列表")
    @PostMapping("/invoked")
    public CommonResult<List<MethodDefinitionVO>> invocations(@RequestBody @Validated MethodRequest methodRequest) {
        DcProject dcProject = dcProjectService.getById(methodRequest.getProjectId());
        CodeAnalysisItem analysisItem = projectAnalysisService.getAnalysisItemAndValidation(dcProject);
        // 1. 查询文件分析
        Optional<CodeFileAnalysis> codeFileAnalysis = codeFileAnalysisService.getByItemIdAndFilePath(analysisItem.getId(), methodRequest.getFilePath());
        DeepCodePreconditions.checkBizArgument(codeFileAnalysis.isPresent(), ErrorCodeEnum.DATA_NOT_EXISTS, "文件路径不存在，请确认文件路径是否正确");
        return CommonResult.success(
                methodService.getInvokedMethods(dcProject, analysisItem, codeFileAnalysis.get(), methodRequest.getLine(), methodRequest.getMethodName())
        );
    }


    @Operation(summary = "方法调用拓扑图")
    @PostMapping("/invoke/topology")
    public CommonResult<MethodTopologyVO> invokedQuery(@RequestBody @Validated MethodTopologyRequest methodTopologyRequest) {
        CodeAnalysisItem analysisItem = projectAnalysisService.getAnalysisItemAndValidation(methodTopologyRequest.getProjectId());

        CodeMethodAnalysis methodAnalysis = codeMethodAnalysisService.getByMethodVid(analysisItem.getId(), methodTopologyRequest.getMethodVid());
        DeepCodePreconditions.checkBizArgument(methodAnalysis != null, ErrorCodeEnum.DATA_NOT_EXISTS, "方法不存在，请确认方法vid是否正确");
        return CommonResult.success(
                methodService.getMethodInvokeTopology(analysisItem, methodAnalysis, methodTopologyRequest.getType())
        );
    }
}
