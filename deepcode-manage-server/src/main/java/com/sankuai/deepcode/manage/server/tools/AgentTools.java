package com.sankuai.deepcode.manage.server.tools;

import com.sankuai.deepcode.ast.common.model.java.MethodNode;
import com.sankuai.deepcode.manage.server.util.SpringUtil;

import java.lang.reflect.Method;
import java.util.List;

public class AgentTools {

    public static List<MethodNode> getAllMethodNode(long ItemId) throws Exception {
        Object bean = SpringUtil.getBean("agentToolsService");
        Method method = bean.getClass().getMethod("getAllMethodNode", Long.class);
        List<MethodNode> methodNodes = (List<MethodNode>) method.invoke(bean, ItemId);
        return methodNodes;
    }

    public static MethodNode getMethodNode(long ItemId, String methodVid) throws Exception {
        Object bean = SpringUtil.getBean("agentToolsService");
        Method method = bean.getClass().getMethod("getMethodNode", Long.class, String.class);
        MethodNode methodNode = (MethodNode) method.invoke(bean, ItemId, methodVid);
        return methodNode;
    }

    public static List<MethodNode> getUpMethodNodes(long itemId, String methodVid, int step) throws Exception {
        Object bean = SpringUtil.getBean("agentToolsService");
        Method method = bean.getClass().getMethod("getUpMethodNodes", Long.class, String.class, Integer.class);
        List<MethodNode> methodNodes = (List<MethodNode>) method.invoke(bean, itemId, methodVid, step);
        return methodNodes;
    }

    public static List<MethodNode> getDownMethodNodes(long itemId, String methodVid, int step) throws Exception {
        Object bean = SpringUtil.getBean("agentToolsService");
        Method method = bean.getClass().getMethod("getDownMethodNodes", Long.class, String.class, Integer.class);
        List<MethodNode> methodNodes = (List<MethodNode>) method.invoke(bean, itemId, methodVid, step);
        return methodNodes;
    }

    public static String methodVidShowView(long itemId, String methodVid) throws Exception {
        Object bean = SpringUtil.getBean("agentToolsService");
        Method method = bean.getClass().getMethod("methodVidShowView", Long.class, String.class);
        String result = (String) method.invoke(bean, itemId, methodVid);
        return result;
    }

}
