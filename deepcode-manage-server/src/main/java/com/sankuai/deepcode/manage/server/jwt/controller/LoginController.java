package com.sankuai.deepcode.manage.server.jwt.controller;

import com.google.gson.Gson;

import com.sankuai.deepcode.dao.domain.SysUser;
import com.sankuai.deepcode.manage.server.jwt.domain.LoginUser;
import com.sankuai.deepcode.manage.server.jwt.domain.ResponseResult;
import com.sankuai.deepcode.manage.server.jwt.service.LoginServcie;
import com.sankuai.deepcode.manage.server.jwt.utils.JwtUtil;
import io.jsonwebtoken.Claims;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@RestController
public class LoginController {

    @Autowired
    private LoginServcie loginServcie;

    @GetMapping("/user/auth2/github/loginByGithub")
    public ResponseResult loginByGithub(@RequestParam String code){
        return loginServcie.loginByGithub(code);
    }


    @GetMapping("/user/auth2/mt/loginByMt")
    public ResponseResult loginByMt(@RequestParam String code){
        return loginServcie.loginByMt(code);
    }


    @PostMapping("/user/login")
    public ResponseResult login(@RequestBody SysUser user){
        //登录
        return loginServcie.login(user);
    }


    @GetMapping("/user/test")
    @ResponseBody
    public String login(){
        //登录
        return "test";
    }

    @PostMapping("/user/getInfo")
    public ResponseResult getInfo(@RequestHeader("token") String authorization) throws Exception {
        String token = authorization.replace("Bearer ", "");
        Claims cl = JwtUtil.parseJWT(token);
        String sub = cl.getSubject();
        LoginUser loginUserInfo= new Gson().fromJson(sub, LoginUser.class);
        return new ResponseResult(0, "查询成功", loginUserInfo);
    }


    @PostMapping("/user/bindUser")
    public ResponseResult bindUser(@RequestBody LoginUser loginUser){
        //bindUser
        return loginServcie.bindUser(loginUser);
    }

//    @RequestMapping("/user/logout")
//    public ResponseResult logout(){
//        return loginServcie.logout();
//    }
}
