package com.sankuai.deepcode.manage.server.enums;

public enum CodeReviewNameEnum {

    LINE("line"),

    METHOD("method"),

    INVOKEMETHOD("invokeMethod"),

    FIELD("field"),

    INVOKEFIELD("invokeField");


    private String code;

    CodeReviewNameEnum(String code) {
        this.code = code;
    }

    public String getCode() {
        return this.code;
    }


    @Override
    public String toString() {
        return "ErrorCodeEnum{" +
                "code=" + code +
                '}';
    }
}