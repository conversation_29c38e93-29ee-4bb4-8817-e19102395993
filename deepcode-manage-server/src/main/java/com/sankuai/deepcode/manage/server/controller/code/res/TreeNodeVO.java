package com.sankuai.deepcode.manage.server.controller.code.res;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.common.collect.Lists;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

/**
 * Package: com.sankuai.deepcode.manage.server.controller.code.res
 * Description:
 *
 * <AUTHOR>
 * @since 2025/3/13 20:25
 */
@Setter
@Getter
@ToString
@JsonInclude(JsonInclude.Include.NON_NULL)
public class TreeNodeVO {
    @Schema(description = "节点名称", example = "Demo.java")
    @Getter(onMethod_ = @__(@JsonProperty("label")))
    String fileName;

    @Schema(description = "节点全路径, 中间目录没有这个属性", example = "demo-project/src/main/java/com/sankuai/meituan/demo/Demo.java")
    String fullFilePath;

    @Schema(description = "节点类型, 1-文件, 2-目录")
    String fileType;

    @Schema(description = "文件ID")
    Long id;

    @Schema(description = "文件Vid")
    String fileVid;

    @Schema(description = "子节点列表")
    List<TreeNodeVO> children;

    @Schema(description = "是否有差异")
    Boolean haveDiff;


    public TreeNodeVO(String fileName) {
        this.fileName = fileName;
        this.children = Lists.newArrayList();
    }
}
