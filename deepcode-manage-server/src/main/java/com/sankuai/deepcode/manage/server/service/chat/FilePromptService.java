package com.sankuai.deepcode.manage.server.service.chat;

import com.sankuai.deepcode.ai.embedding.model.SearchFile;
import com.sankuai.deepcode.ai.embedding.service.EmbeddingSearchService;
import com.sankuai.deepcode.ai.prompt.CodeLanguage;
import com.sankuai.deepcode.ai.prompt.PromptBuilder;
import com.sankuai.deepcode.commons.MapUtils;
import com.sankuai.deepcode.dao.domain.CodeAnalysisItem;
import com.sankuai.deepcode.dao.domain.CodeFileAnalysis;
import com.sankuai.deepcode.dao.service.CodeFileAnalysisService;
import com.sankuai.deepcode.manage.server.controller.chat.req.MessageVO;
import com.sankuai.deepcode.manage.server.service.code.FileCodeService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Package: com.sankuai.deepcode.manage.server.service.chat
 * Description:
 *
 * <AUTHOR>
 * @since 2025/3/20 19:51
 */
@Service
@Slf4j
public class FilePromptService {
    private static final int MAX_FILE_COUNT = 10;

    private final CodeFileAnalysisService codeFileAnalysisService;
    private final FileCodeService fileCodeService;
    private final EmbeddingSearchService embeddingSearchService;


    public FilePromptService(CodeFileAnalysisService codeFileAnalysisService, FileCodeService fileCodeService, EmbeddingSearchService embeddingSearchService) {
        this.codeFileAnalysisService = codeFileAnalysisService;
        this.fileCodeService = fileCodeService;
        this.embeddingSearchService = embeddingSearchService;
    }


    public String buildSearchFilePrompt(List<SearchFile> searchFiles, CodeAnalysisItem analysisItem) {
        PromptBuilder promptBuilder = PromptBuilder.instance();
        promptBuilder.h1("文件列表");

        if (CollectionUtils.isEmpty(searchFiles)) {
            return promptBuilder.line("[]").toString();
        }

        // 最多处理10个文件
        List<SearchFile> handleSearchFiles = searchFiles.subList(0, Math.min(searchFiles.size(), MAX_FILE_COUNT));
        List<CodeFileAnalysis> codeFileAnalysisList = codeFileAnalysisService.getByItemAndVids(
                analysisItem.getId(),
                handleSearchFiles.stream().map(SearchFile::getFileVid).collect(Collectors.toList())
        );
        if (CollectionUtils.isEmpty(codeFileAnalysisList)) {
            log.warn("#ERROR#召回文件，未找到文件分析记录: {}", handleSearchFiles.stream().map(SearchFile::getFileVid).collect(Collectors.joining(",")));
            return promptBuilder.line("[](召回文件本地未找到)").toString();
        }

        Map<String, SearchFile> searchFileMap = MapUtils.toMap(handleSearchFiles, SearchFile::getFileVid);
        Map<String, CodeFileAnalysis> codeFileAnalysisMap = MapUtils.toMap(codeFileAnalysisList, CodeFileAnalysis::getFileVid);

        Map<String, Pair<SearchFile, CodeFileAnalysis>> fileMap = searchFileMap.entrySet().stream()
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        entry -> Pair.of(entry.getValue(), codeFileAnalysisMap.get(entry.getKey()))
                ));

        fileMap.forEach((key, value) -> {
            SearchFile searchFile = value.getLeft();
            CodeFileAnalysis codeFileAnalysis = value.getRight();
            if (codeFileAnalysis == null) {
                log.warn("#ERROR#召回文件，未找到文件分析记录, vid: {}, searchFile: {}", key, searchFile);
                return;
            }
            PromptBuilder tempBuilder = PromptBuilder.instance();
            tempBuilder.line("项目文件路径: " + codeFileAnalysis.getFilePath());
            if (StringUtils.isNotBlank(searchFile.getDesc())) {
                tempBuilder.line("该文件中对应功能描述: ")
                        .codeBlock(CodeLanguage.PLAINTEXT, searchFile.getDesc());
            }
            tempBuilder.newLine();
            if (StringUtils.isNotBlank(searchFile.getCode())) {
                tempBuilder.append("文件总行数: ")
                        .append(Math.max(codeFileAnalysis.getStartLine(), codeFileAnalysis.getEndLine()))
                        .append(", 代码片段起止行：").append(searchFile.getStartLine()).append(" - ").append(searchFile.getEndLine())
                        .append(", 代码片段: ").newLine()
                        .codeBlock(CodeLanguage.from(codeFileAnalysis.getFileType()), searchFile.getCode());
            }

            promptBuilder.indent(tempBuilder);
        });


        return promptBuilder.toString();
    }

    /**
     * 构建输入文件提示词
     *
     * @param analysisItem    代码分析项
     * @param filePayLoadList 输入文件内容列表
     * @return 输入文件提示词字符串
     */
    public String buildInputFilePrompt(CodeAnalysisItem analysisItem, List<MessageVO.FilePayLoad> filePayLoadList) {
        if (CollectionUtils.isEmpty(filePayLoadList)) {
            return null;
        }
        PromptBuilder promptBuilder = PromptBuilder.instance();

        promptBuilder.newLine();
        promptBuilder.h1("以下文件内容是和用户问题有关联的重点关注内容，需要*优先考虑*：");

        List<CodeFileAnalysis> exactlyFileList = codeFileAnalysisService.getByItemAndVids(
                analysisItem.getId(),
                filePayLoadList.stream().map(MessageVO.FilePayLoad::getVid).collect(Collectors.toList())
        );

        Map<String, MessageVO.FilePayLoad> filePayLoadMap = MapUtils.toMap(filePayLoadList, MessageVO.FilePayLoad::getVid);
        Map<String, CodeFileAnalysis> exactlyFileMap = MapUtils.toMap(exactlyFileList, CodeFileAnalysis::getFileVid);

        exactlyFileMap.forEach((vid, fileAnalysis) -> {
            promptBuilder.line("项目文件路径: " + fileAnalysis.getFilePath());
            String fileDesc = embeddingSearchService.getCodeDescByFileVid(analysisItem.getId(), vid);
            if (StringUtils.isNotBlank(fileDesc)) {
                promptBuilder.line("该文件包含的对应功能简述: ")
                        .codeBlock(CodeLanguage.PLAINTEXT, fileDesc);
            }
            MessageVO.FilePayLoad payLoad = filePayLoadMap.get(vid);
            if (payLoad.getStart().getLine() != null && payLoad.getStart().getLine() > 0) {
                List<String> codeLines = fileCodeService.getFileCodeByLines(fileAnalysis, payLoad.getStart().getLine(), payLoad.getEnd().getLine());
                promptBuilder.append("文件总行数：").append(Math.max(fileAnalysis.getStartLine(), fileAnalysis.getEndLine()))
                        .append(", 代码片段起止行：").append(payLoad.getStart().getLine()).append(" - ").append(payLoad.getEnd().getLine())
                        .append("代码片段: ").newLine()
                        .codeBlock(CodeLanguage.from(fileAnalysis.getFileType()), codeLines);
            }
        });

        return promptBuilder.toString();
    }
}
