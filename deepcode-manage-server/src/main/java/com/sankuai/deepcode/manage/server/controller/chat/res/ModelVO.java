package com.sankuai.deepcode.manage.server.controller.chat.res;

import com.sankuai.deepcode.ai.llm.openai.models.Model;
import io.swagger.v3.oas.annotations.Hidden;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * Package: com.sankuai.deepcode.manage.server.controller.chat.res
 * Description:
 *
 * <AUTHOR>
 * @since 2025/3/27 10:55
 */
@Setter
@Getter
@ToString
public class ModelVO extends Model {

    @Hidden
    public static ModelVO from(Model model) {
        if (model == null) {
            return null;
        }
        ModelVO modelVO = new ModelVO();
        modelVO.setId(model.getId());
        modelVO.setModelObject(model.getModelObject());
        modelVO.setOwnedBy(model.getOwnedBy());

        return modelVO;
    }
}
