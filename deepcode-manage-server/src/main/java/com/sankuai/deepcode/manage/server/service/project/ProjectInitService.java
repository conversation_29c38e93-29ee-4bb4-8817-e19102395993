package com.sankuai.deepcode.manage.server.service.project;

import com.sankuai.deepcode.dao.domain.CodeAnalysisItem;
import com.sankuai.deepcode.dao.domain.DcProject;
import com.sankuai.deepcode.dao.enums.ItemStatusEnum;
import com.sankuai.deepcode.dao.enums.ItemStepEnum;
import com.sankuai.deepcode.dao.service.CodeAnalysisItemService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.EnumSet;
import java.util.Objects;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * Package: com.sankuai.deepcode.manage.server.service.project
 * Description:
 *
 * <AUTHOR>
 * @since 2025/3/7 10:47
 */
@Service
@Slf4j
public class ProjectInitService {
    private final CodeAnalysisItemService codeAnalysisItemService;

    private final EnumSet<ItemStepEnum> asyncSteps = EnumSet.of(ItemStepEnum.EMBEDDING, ItemStepEnum.KNOWLEDGE);

    public ProjectInitService(
            CodeAnalysisItemService codeAnalysisItemService
    ) {
        this.codeAnalysisItemService = codeAnalysisItemService;
    }

    public void initializeProjectTasks(DcProject project) {
        if (project == null || project.getId() == null) {
            return;
        }

        CodeAnalysisItem analysisItem = new CodeAnalysisItem();
        analysisItem.setGitUrl(project.getGitUrl());
        analysisItem.setFromBranch(project.getSourceBranch());
        analysisItem.setToBranch(project.getTargetBranch());
        analysisItem.setBuildBranch(project.getSourceBranch());

        analysisItem.setDiff(!Objects.equals(project.getSourceBranch(), project.getTargetBranch()));

        analysisItem.setStatus(ItemStatusEnum.INITIATING.getCode());

        analysisItem.setAsyncTypes(analysisItem.isDiff() ?
            Stream.of(ItemStepEnum.EMBEDDING, ItemStepEnum.KNOWLEDGE, ItemStepEnum.AI_REVIEW).map(s -> String.valueOf(s.getCode())).collect(Collectors.joining(",")) :
            asyncSteps.stream().map(s -> String.valueOf(s.getCode())).collect(Collectors.joining(",")));
        analysisItem.setAsyncStatus(ItemStatusEnum.INITIATING.getCode());

        codeAnalysisItemService.save(analysisItem);
        project.setItemId(analysisItem.getId());
    }
}
