package com.sankuai.deepcode.manage.server.service.chat;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.sankuai.deepcode.ai.agnets.callback.PlanCompleteCallback;
import com.sankuai.deepcode.ai.agnets.model.InitPlanParam;
import com.sankuai.deepcode.ai.agnets.service.PlanFlowService;
import com.sankuai.deepcode.ai.embedding.service.EmbeddingSearchService;
import com.sankuai.deepcode.ai.llm.model.chat.ChatMessage;
import com.sankuai.deepcode.ai.llm.oneapi.OneApiClient;
import com.sankuai.deepcode.ai.llm.openai.chat.*;
import com.sankuai.deepcode.ai.llm.openai.models.Model;
import com.sankuai.deepcode.commons.ex.BizException;
import com.sankuai.deepcode.dao.domain.*;
import com.sankuai.deepcode.dao.enums.ItemStatusEnum;
import com.sankuai.deepcode.dao.enums.ItemStepEnum;
import com.sankuai.deepcode.dao.po.ChatMsgChildrenPO;
import com.sankuai.deepcode.dao.po.ChatMsgContentPO;
import com.sankuai.deepcode.dao.service.CodeFileAnalysisService;
import com.sankuai.deepcode.dao.service.DcChatMessageService;
import com.sankuai.deepcode.dao.service.DcChatService;
import com.sankuai.deepcode.manage.server.controller.chat.req.MessageType;
import com.sankuai.deepcode.manage.server.controller.chat.req.MessageVO;
import com.sankuai.deepcode.manage.server.controller.chat.req.ProjectChatRequestVO;
import com.sankuai.deepcode.manage.server.controller.chat.res.ChatCompletionsVO;
import com.sankuai.deepcode.manage.server.controller.chat.res.ChatProjectInfoVO;
import com.sankuai.deepcode.manage.server.controller.chat.res.ModelVO;
import com.sankuai.deepcode.manage.server.controller.chat.res.ProjectStatusEnum;
import com.sankuai.deepcode.manage.server.jwt.utils.UserUtils;
import com.sankuai.deepcode.manage.server.mapper.chat.ChatResponseConvertor;
import com.sankuai.deepcode.manage.server.mapper.chat.MediaContentConvertor;
import com.sankuai.deepcode.manage.server.service.project.ProjectAnalysisService;
import com.sankuai.deepcode.manage.server.util.ResponseHelper;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.ListUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Duration;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;


/**
 * Package: com.sankuai.deepcode.manage.server.controller.service
 * Description:
 *
 * <AUTHOR>
 * @since 2025/3/20 15:37
 */
@Service
@Slf4j
public class ProjectChatService {
    private final OneApiClient oneApiClient;
    private final DcChatService dcChatService;
    private final EmbeddingSearchService embeddingSearchService;
    private final FilePromptService filePromptService;
    private final DcChatMessageService dcChatMessageService;
    private final ChatResponseConvertor chatResponseConvertor;
    private final ProjectAnalysisService projectAnalysisService;
    private final ChatMsgService chatMsgService;
    private final MediaContentConvertor mediaContentConvertor;
    private final CodeFileAnalysisService codeFileAnalysisService;
    private final PlanFlowService planFlowService;

    // 单个文件 知识总结耗时预估 单位：秒
    private final Double singleFileKnowledgeCost = Double.valueOf("5.0");
    // 单个文件 文本向量化耗时预估 单位：秒
    private final Double singleFileEmbeddingCost = Double.valueOf("0.4");

    public ProjectChatService(
            OneApiClient oneApiClient,
            DcChatService dcChatService,
            EmbeddingSearchService embeddingSearchService,
            FilePromptService filePromptService,
            DcChatMessageService dcChatMessageService,
            ChatResponseConvertor chatResponseConvertor,
            ProjectAnalysisService projectAnalysisService,
            ChatMsgService chatMsgService,
            MediaContentConvertor mediaContentConvertor,
            CodeFileAnalysisService codeFileAnalysisService,
            PlanFlowService planFlowService
    ) {
        this.oneApiClient = oneApiClient;
        this.dcChatService = dcChatService;
        this.embeddingSearchService = embeddingSearchService;
        this.filePromptService = filePromptService;
        this.dcChatMessageService = dcChatMessageService;
        this.chatResponseConvertor = chatResponseConvertor;
        this.projectAnalysisService = projectAnalysisService;
        this.chatMsgService = chatMsgService;
        this.mediaContentConvertor = mediaContentConvertor;
        this.codeFileAnalysisService = codeFileAnalysisService;
        this.planFlowService = planFlowService;
    }

    @SneakyThrows
    public SseEmitter chatCompletions(ProjectChatRequestVO request) {
        // 生成 assistant 消息的唯一标识符
        String assistantMsgUuid = UUID.randomUUID().toString();
        SseEmitter emitter = SseEmitterManager.instance(assistantMsgUuid);
        CodeAnalysisItem analysisItem;
        try {
            analysisItem = projectAnalysisService.getAnalysisItemAndValidation(request.getProjectId());
        } catch (BizException e) {
            emitter.send(ResponseHelper.partialContent(null, null, e.getMessage()));
            emitter.complete();
            return emitter;
        }

        DcChat chat = dcChatService.getChatByUuid(request.getChatUuid());
        if (chat == null || !Objects.equals(chat.getUserId(), UserUtils.getUserId())) {
            emitter.send(ResponseHelper.partialContent(null, null, "对话不存在, 请重新发起对话"));
            log.warn("出现对话不存在的请求：{}", request);
            emitter.complete();
            return emitter;
        }
        DcChatMessage userMessage = null;
        // 获取到用户查询
        AtomicReference<String> query = new AtomicReference<>(request.getUserQuery());
        if (StringUtils.isBlank(query.get()) && StringUtils.isBlank(request.getRegenerateMsgId())) {
            emitter.send(ResponseHelper.partialContent(null, null, "请输入正确的内容~"));
            log.warn("获取到的用户输入内容为空：{}", request);
            emitter.complete();
            return emitter;
        }

        // 重新生成的逻辑
        if (StringUtils.isNotBlank(request.getRegenerateMsgId())) {
            Optional<DcChatMessage> existUserMsg = dcChatMessageService.getMsgByUuId(request.getRegenerateMsgId(), UserUtils.getUserId());
            if (!existUserMsg.isPresent()) {
                emitter.send(ResponseHelper.partialContent(null, null, "重新生成的源消息不存在, 请重新发起对话"));
                log.warn("重新生成时，传入的 userMsgId 不存在：{}", request);
                emitter.complete();
                return emitter;
            }
            if (!Objects.equals(existUserMsg.get().getRole(), Role.USER.name().toLowerCase())) {
                emitter.send(ResponseHelper.partialContent(null, null, "重新生成的源消息不是用户消息, 请重新发起对话"));
                log.warn("重新生成的源消息不是用户消息， request：{}", request);
                emitter.complete();
                return emitter;
            }
            userMessage = existUserMsg.get();
            // 重新生成时，需要将 assistant 消息添加到 childrenIds 中
            userMessage.getChildrenIds().getChildrenIds().add(assistantMsgUuid);
            userMessage.setUpdateTime(LocalDateTime.now());

            // 重新生成时，需要将上一次用户的 文件信息，回填到请求中
            List<ChatMsgContentPO.MediaInfo> mediaInfos = userMessage.getContent().getMediaInfos();
            if (CollectionUtils.isNotEmpty(mediaInfos)) {
                request.getLastUserMessage().ifPresent(m -> {
                    query.set(existUserMsg.get().getContent().getContent());
                    m.setContent(
                            ListUtils.union(
                                    Collections.singletonList(new MessageVO.ContentVO(MessageType.TEXT, existUserMsg.get().getContent().getContent(), null)),
                                    mediaContentConvertor.convert(mediaInfos).stream()
                                            .map(MessageVO.MediaContent::toContentVO).collect(Collectors.toList())
                            )
                    );
                });
            }

            dcChatMessageService.updateById(userMessage);
        } else {
            // 生成用户消息
            userMessage = new DcChatMessage();
            String userMsgUuid = UUID.randomUUID().toString();
            // 查询前一条消息，正常来说，前一条消息应该是 null 或是 assistant 的一个消息
            // 传入了 prevMsgId 时，必须保证传入是正确的
            if (StringUtils.isNotBlank(request.getPrevMsgId())) {
                Optional<DcChatMessage> prevMsg = dcChatMessageService.getMsgByUuId(request.getPrevMsgId(), UserUtils.getUserId());
                if (!prevMsg.isPresent()) {
                    emitter.send(ResponseHelper.partialContent(null, null, "前置消息不存在, 请重新发起对话"));
                    log.warn("传入的 prevMsgId 不存在：{}", request);
                    emitter.complete();
                    return emitter;
                }
                prevMsg.get().addChildrenId(userMsgUuid);
            }

            userMessage.setChatUuid(chat.getUuid())
                    .setUserId(chat.getUserId())
                    .setProjectId(chat.getProjectId())
                    .setItemId(analysisItem.getId())
                    .setUuid(userMsgUuid)
                    .setChatUuid(chat.getUuid())
                    .setPrevMsgId(request.getPrevMsgId())
                    .setRole(Role.USER.name().toLowerCase())
                    .setChildrenIds(ChatMsgChildrenPO.of(assistantMsgUuid))
                    .setContent(chatResponseConvertor.convertUser(request))
                    .setValid(true)
            ;
            dcChatMessageService.save(userMessage);
        }
        DcChatMessage assistantMessage = new DcChatMessage();
        assistantMessage.setUuid(assistantMsgUuid);
        assistantMessage.setChatUuid(chat.getUuid());
        assistantMessage.setUserId(chat.getUserId());
        assistantMessage.setPrevMsgId(userMessage.getUuid());
        assistantMessage.setProjectId(chat.getProjectId());
        assistantMessage.setItemId(analysisItem.getId());
        assistantMessage.setValid(true);
        assistantMessage.setRole(Role.ASSISTANT.name().toLowerCase());


        List<DcChatMessage> historyMsg = chatMsgService.getChainedMsgList(chat, userMessage.getPrevMsgId());
        // TODO 此处可以依据 historyMsg 组装出 Tokens 总量，后续可添加上下文长度拦截提示
        List<Message> historyMsgList = convertFromDcChatMessage(historyMsg);
        List<ChatMessage> chatMessages = convertChatMsgFromDcChatMessage(historyMsg);
        ChatMessage system = new ChatMessage();
        system.setRole("system");
        system.setContent(PlanFlowService.SystemPrompt);
        chatMessages.add(0, system);

        if (CollectionUtils.isNotEmpty(request.getFileContent())) {
            ChatMessage fileMsg = new ChatMessage();
            fileMsg.setRole(Role.USER.name().toLowerCase());
            fileMsg.setContent(filePromptService.buildInputFilePrompt(analysisItem, request.getFileContent()));
            chatMessages.add(fileMsg);
        }
        ChatMessage lastUserMsg = new ChatMessage();
        lastUserMsg.setRole(Role.USER.name().toLowerCase());
        lastUserMsg.setContent("用户问题：" + query.get());
        chatMessages.add(lastUserMsg);

        DcChatMessage finalUserMessage = userMessage;
        assistantMessage.setCreateTime(LocalDateTime.now());

        InitPlanParam initPlanParam = new InitPlanParam();
        initPlanParam.setItemId(analysisItem.getId());
        initPlanParam.setUserId(UserUtils.getUserId());
        initPlanParam.setModelName(request.getModel());
        initPlanParam.setSseEmitter(emitter);
        initPlanParam.setChatCompletionResponse(ChatCompletionsVO.builder().msgId(assistantMsgUuid).prevMsgId(userMessage.getUuid()).build());
        initPlanParam.setChatMessages(chatMessages);
        initPlanParam.setPlanCompleteCallback(new PlanCompleteCallback() {
            @SneakyThrows
            @Override
            public void onComplete(SseEmitter emitter, ChatCompletionResponse chatCompletionResponse) {
                assistantMessage.setContent(chatResponseConvertor.convertAssistant(chatCompletionResponse));
                assistantMessage.setUpdateTime(LocalDateTime.now());

                dcChatMessageService.save(assistantMessage);
                dcChatService.touchUpdateTime(chat, assistantMessage.getUpdateTime());
                emitter.complete();
            }
        });
        planFlowService.initPlanFlow(initPlanParam);
        return emitter;
        /**
         * emitter.send(ResponseHelper.partialProcessingContent(null, null, "正在检索相关代码...\n"));
         *                     //2. 调用向量检索
         *                     SearchByTextRes searchResult = embeddingSearchService.embeddingSearchService(analysisItem.getId(), query.get());
         *                     emitter.send(
         *                             ResponseHelper.partialProcessingContent(null, null, "检索到" + searchResult.getSearchFiles().size() + "个相关文件: \n")
         *                     );
         *                     if (CollectionUtils.isNotEmpty(searchResult.getSearchFiles())) {
         *                         List<CodeFileAnalysis> files = codeFileAnalysisService.getByItemAndVids(analysisItem.getId(), searchResult.getSearchFiles().stream().map(SearchFile::getFileVid).collect(Collectors.toList()));
         *                         if (CollectionUtils.isNotEmpty(files)) {
         *                             files.forEach(f -> {
         *                                 try {
         *                                     emitter.send(ResponseHelper.partialProcessingContent(null, null, PromptBuilder.instance().numberedList(f.getFilePath()).build()));
         *                                 } catch (IOException ignore) {
         *                                 }
         *                             });
         *                         }
         *                     }
         *
         *                     //3. 构建对应的代码相关知识
         *                     PromptBuilder promptBuilder = PromptBuilder.instance()
         *                             .h2("Restrictions")
         *                             .listItem(
         *                                     "业务逻辑描述相关的答案需要与给定的文件内容强制相关",
         *                                     "问题中若涉及到无法完成的内容时，请直接回复：抱歉小主，当前系统能力暂时无法支持此操作~"
         *                             )
         *                             .line("与用户问题可能相关代码信息如下：");
         *                     promptBuilder.line(filePromptService.buildSearchFilePrompt(searchResult.getSearchFiles(), analysisItem));
         *
         *                     if (CollectionUtils.isNotEmpty(request.getFileContent())) {
         *                         promptBuilder.line(filePromptService.buildInputFilePrompt(analysisItem, request.getFileContent()));
         *                     }
         *
         *                     //4. 调用大模型进行生成
         *                     ChatCompletionRequest chatRequest = ChatCompletionRequest.builder()
         *                             .model(request.getModel())
         *                             .maxTokens(request.getMaxTokens())
         *                             .addMessages(historyMsgList)
         *                             .addUserMessage(promptBuilder.build())
         *                             .addUserMessage(PromptBuilder.instance("请根据以上信息回答用户问题：").newLine().line(query).build())
         *                             .build();
         *                     AtomicReference<ChatCompletionsVO> lastStreamResponse = new AtomicReference<>();
         *                     StringBuilder reasoningContentBuilder = new StringBuilder();
         *                     // 发消息前，设置一下创建时间，方便统计整体时长
         *                     assistantMessage.setCreateTime(LocalDateTime.now());
         *
         *                     oneApiClient.chatCompletion(chatRequest)
         *                             .onPartialResponse(r -> {
         *                                 try {
         *                                     ChatCompletionsVO chatCompletionsVO = ChatCompletionsVO.from(r).build();
         *                                     chatCompletionsVO.prevMsgId(finalUserMessage.getUuid());
         *                                     chatCompletionsVO.msgId(assistantMessage.getUuid());
         *                                     if (BooleanUtils.isTrue(r.lastOne())) {
         *                                         lastStreamResponse.set(chatCompletionsVO);
         *                                     }
         *                                     // 当前推理内容并不会在 lastMessage 的响应内容中，需要在发送过程中手动进行收集
         *                                     String reasoningContent = chatCompletionsVO.deltaReasoningContent();
         *                                     if (StringUtils.isNotBlank(reasoningContent)) {
         *                                         reasoningContentBuilder.append(reasoningContent);
         *                                     }
         *                                     emitter.send(chatCompletionsVO);
         *                                 } catch (Exception e) {
         *                                     log.error("发送消息失败", e);
         *                                     emitter.completeWithError(e);
         *                                 }
         *                             })
         *                             .onComplete(() -> {
         *                                 // 如果有推理模型，回写完整推理结果
         *                                 lastStreamResponse.get().reasoningContent(reasoningContentBuilder.toString());
         *                                 assistantMessage.setContent(chatResponseConvertor.convertAssistant(lastStreamResponse.get()));
         *                                 assistantMessage.setUpdateTime(LocalDateTime.now());
         *                                 // 完成后，进行消息的一次性写入
         *                                 dcChatMessageService.save(assistantMessage);
         *                                 dcChatService.touchUpdateTime(chat, assistantMessage.getUpdateTime());
         *                                 emitter.complete();
         *                             })
         *                             .onError(emitter::completeWithError)
         *                             .execute();
         */
    }

    private List<ChatMessage> convertChatMsgFromDcChatMessage(List<DcChatMessage> historyMsg) {
        List<ChatMessage> chatMessages = Lists.newArrayList();
        if (CollectionUtils.isEmpty(historyMsg)) {
            return chatMessages;
        }
        for (DcChatMessage dcChatMessage : historyMsg) {
            Role role = Role.from(dcChatMessage.getRole());
            switch (role) {
                case USER:
                case ASSISTANT:
                    ChatMessage msg = new ChatMessage();
                    msg.setRole(dcChatMessage.getRole());
                    msg.setContent(dcChatMessage.getContent().getContent());
                    chatMessages.add(msg);
                    break;
                default:
                    break;
            }
        }
        return chatMessages;
    }


    public List<Message> convertFromDcChatMessage(List<DcChatMessage> dcChatMessages) {
        List<Message> messages = Lists.newArrayList();
        if (CollectionUtils.isEmpty(dcChatMessages)) {
            return messages;
        }
        for (DcChatMessage dcChatMessage : dcChatMessages) {
            Role role = Role.from(dcChatMessage.getRole());
            switch (role) {
                case USER:
                    messages.add(UserMessage.from(dcChatMessage.getContent().getContent()));
                    break;
                case ASSISTANT:
                    messages.add(AssistantMessage.from(dcChatMessage.getContent().getContent()));
                    break;
                default:
                    break;
            }
        }
        return messages;
    }

    public List<ModelVO> getModels() {
        List<Model> models = oneApiClient.models().getData();
        return models.stream().map(ModelVO::from).collect(Collectors.toList());
    }

    /**
     * 构造项目相关信息
     *
     * @param project             项目信息
     * @param analysisItem        分析项信息
     * @param analysisItemDetails 分析项详情信息
     * @return 项目相关信息对象
     */
    public ChatProjectInfoVO constructChatInfos(
            DcProject project, CodeAnalysisItem analysisItem, List<CodeAnalysisItemDetail> analysisItemDetails
    ) {
        Set<String> asyncItemNames = EnumSet.of(ItemStepEnum.EMBEDDING, ItemStepEnum.KNOWLEDGE).stream().map(ItemStepEnum::getTypeName).collect(Collectors.toSet());

        if (project == null) {
            return null;
        }
        ChatProjectInfoVO chatProjectInfoVO = new ChatProjectInfoVO();
        //FIXME: 应该要给出整体的项目简介的
        chatProjectInfoVO.setProjectDesc("");
        chatProjectInfoVO.setNeedPolling(Boolean.FALSE);
        chatProjectInfoVO.setChatEnabled(Boolean.FALSE);
        chatProjectInfoVO.setShowCode(Boolean.FALSE);

        chatProjectInfoVO.setProjectInfo(buildProjectInfo(project, analysisItem));

        if (analysisItem == null) {
            chatProjectInfoVO.setStatus(ProjectStatusEnum.HAVE_NOT_ANALYSED);
            return chatProjectInfoVO;
        }


        // 将分析项详情按同步/异步分组
        Map<Boolean, List<CodeAnalysisItemDetail>> detailsByAsync = analysisItemDetails.stream()
                .collect(Collectors.partitioningBy(CodeAnalysisItemDetail::isAsync));
        // 获取同步的详情列表
        List<CodeAnalysisItemDetail> syncedDetails = detailsByAsync.get(false);
        // 获取异步的详情列表
        List<CodeAnalysisItemDetail> asyncDetails = detailsByAsync.get(true);
        if (CollectionUtils.isNotEmpty(syncedDetails)) {
            // 同步分析中的任务，只要存在「失败的情况」整体状态为「分析失败」
            if (syncedDetails.stream().anyMatch(detail -> Objects.equals(detail.getStatus(), ItemStatusEnum.FAILED.getCode()))) {
                chatProjectInfoVO.setStatus(ProjectStatusEnum.ANALYSE_FAILED);
            } else if (syncedDetails.stream().anyMatch(detail -> Objects.equals(detail.getStatus(), ItemStatusEnum.INITIATING.getCode()) || Objects.equals(detail.getStatus(), ItemStatusEnum.PROCESSING.getCode()))) {
                //  存在「初始化中或处理中的任务」整体状态为「分析中」
                chatProjectInfoVO.setStatus(ProjectStatusEnum.ANALYSING);
                chatProjectInfoVO.setNeedPolling(Boolean.TRUE);
            } else if (syncedDetails.stream().allMatch(detail -> Objects.equals(detail.getStatus(), ItemStatusEnum.SUCCESS.getCode()))) {
                // 同步全部成功处理完成后，就可以显示代码啦
                chatProjectInfoVO.setShowCode(Boolean.TRUE);
                // 全部完成的情况，检查异步任务状态
                if (CollectionUtils.isNotEmpty(asyncDetails)) {
                    if (asyncDetails.stream().filter(d -> asyncItemNames.contains(d.getType())).anyMatch(detail -> Objects.equals(detail.getStatus(), ItemStatusEnum.FAILED.getCode()))) {
                        // 异步任务存在失败，则项目状态为「生成知识失败」
                        chatProjectInfoVO.setStatus(ProjectStatusEnum.KNOWLEDGE_GENERATE_FAILED);
                    } else if (asyncDetails.stream().anyMatch(detail -> Objects.equals(detail.getStatus(), ItemStatusEnum.INITIATING.getCode()) || Objects.equals(detail.getStatus(), ItemStatusEnum.PROCESSING.getCode()))) {
                        // 异步任务存在初始化或处理中，则项目状态为「生成知识中」
                        chatProjectInfoVO.setStatus(ProjectStatusEnum.KNOWLEDGE_GENERATING);
                        chatProjectInfoVO.setNeedPolling(Boolean.TRUE);
                    } else if (asyncDetails.stream().filter(
                            // 只判定 向量化、文本语义的状态
                            d -> asyncItemNames.contains(d.getType())
                    ).allMatch(detail -> Objects.equals(detail.getStatus(), ItemStatusEnum.SUCCESS.getCode()))) {
                        // 异步任务全部完成，项目状态为「成功」
                        chatProjectInfoVO.setStatus(ProjectStatusEnum.SUCCESS);
                        //  启用聊天功能
                        chatProjectInfoVO.setChatEnabled(Boolean.TRUE);
                    }
                } else {
                    chatProjectInfoVO.setStatus(ProjectStatusEnum.KNOWLEDGE_GENERATING);
                    chatProjectInfoVO.setNeedPolling(Boolean.TRUE);
                }
            }
        } else {
            // 如果没有分析条目，可能是 分析任务 当前还没开始，默认显示 分析中
            chatProjectInfoVO.setStatus(ProjectStatusEnum.ANALYSING);
            chatProjectInfoVO.setNeedPolling(Boolean.TRUE);
        }

        chatProjectInfoVO.setProcessInfoList(buildProcessInfoList(analysisItem, analysisItemDetails));
        return chatProjectInfoVO;
    }

    private ChatProjectInfoVO.ProjectInfo buildProjectInfo(DcProject project, CodeAnalysisItem analysisItem) {
        ChatProjectInfoVO.ProjectInfo projectInfo = new ChatProjectInfoVO.ProjectInfo();
        if (project == null) {
            return null;
        }
        projectInfo.setProjectId(project.getId());
        projectInfo.setProjectName(project.getProjectName());
        projectInfo.setGitUrl(project.getGitUrl());

        projectInfo.setIsDiff(!Objects.equals(project.getSourceBranch(), project.getTargetBranch()));
        projectInfo.setModeDesc(BooleanUtils.isTrue(projectInfo.getIsDiff()) ? "差异模式" : "全量模式");
        projectInfo.setSourceBranch(project.getSourceBranch());
        projectInfo.setTargetBranch(project.getTargetBranch());

        if (analysisItem != null) {
            projectInfo.setSourceCommit(analysisItem.getFromCommitId());
            projectInfo.setSourceCommitMessage(analysisItem.getLastCommitMsg());
            projectInfo.setTargetCommit(analysisItem.getToCommitId());
        } else {
            projectInfo.setSameAnalysedItemCount(projectAnalysisService.getSameAnalysedItemCount(project));
        }

        return projectInfo;
    }

    private List<ChatProjectInfoVO.ProcessInfo> buildProcessInfoList(CodeAnalysisItem analysisItem, List<CodeAnalysisItemDetail> analysisItemDetails) {
        Double oneHandleProgress = 100.0D;
        if (analysisItem == null || CollectionUtils.isEmpty(analysisItemDetails)) {
            return Collections.emptyList();
        }
        // 兜底进行一次排序
        analysisItemDetails.sort(Comparator.comparing(CodeAnalysisItemDetail::getUtime));
        List<ChatProjectInfoVO.ProcessInfo> processInfoList = new ArrayList<>();
        for (CodeAnalysisItemDetail detail : analysisItemDetails) {
            ItemStepEnum handleType = ItemStepEnum.fromType(detail.getType());
            if (handleType == null) {
                continue;
            }
            ChatProjectInfoVO.ProcessInfo processInfo = new ChatProjectInfoVO.ProcessInfo();
            processInfo.setHandleType(handleType.getTypeName());
            processInfo.setName(handleType.getDesc());
            processInfo.setDesc(detail.getContentStart());
            processInfo.setResult(detail.getContentEnd());

            Optional<ItemStatusEnum> status = ItemStatusEnum.of(detail.getStatus());
            processInfo.setStatus(status.map(ItemStatusEnum::getCode).orElse(null));
            processInfo.setStatusDesc(status.map(ItemStatusEnum::getDesc).orElse("状态未知"));
            processInfo.setStartTime(detail.getCtime());
            processInfo.setEndTime(detail.getUtime());
            processInfo.setProgress(0.0D);

            switch (handleType) {
                case DOWNLOAD:
                    if (status.isPresent()) {
                        if (status.get() == ItemStatusEnum.SUCCESS) {
                            processInfo.setProgress(oneHandleProgress);
                        } else if (status.get() == ItemStatusEnum.FAILED) {
                            break;
                        }
                    } else {
                        processInfo.setStatusDesc("状态未知");
                        break;
                    }
                    JSONObject jsonObject = JSON.parseObject(detail.getExtern());
                    Long projectSize = jsonObject.getLong("size");
                    if (projectSize != null && projectSize > 0) {
                        String sizeDesc;
                        if (projectSize < 1024) {
                            sizeDesc = String.format("%.2f KB", projectSize.doubleValue());
                        } else if (projectSize < 1024 * 1024) {
                            sizeDesc = String.format("%.2f MB", projectSize / 1024.0);
                        } else {
                            sizeDesc = String.format("%.2f GB", projectSize / (1024.0 * 1024));
                        }
                        processInfo.setResultDetail("项目文件大小：" + sizeDesc);
                    }
                    break;
                case FILE:
                    if (status.isPresent()) {
                        if (status.get() == ItemStatusEnum.SUCCESS) {
                            processInfo.setProgress(oneHandleProgress);
                        } else if (status.get() == ItemStatusEnum.FAILED) {
                            break;
                        }
                    } else {
                        processInfo.setStatusDesc("状态未知");
                        break;
                    }
                    JSONObject fileObject = Optional.ofNullable(detail.getExtern())
                            .map(JSON::parseObject)
                            .orElse(new JSONObject());
                    Long feCount = fileObject.getLong("frontEnd");
                    Long javaCount = fileObject.getLong("java");
                    Long pythonCount = fileObject.getLong("python");
                    Long otherCount = fileObject.getLong("noSource");
                    StringBuilder resultDetail = new StringBuilder("【工程代码结构】");
                    if (feCount != null && feCount > 0) {
                        resultDetail.append("前端：").append(feCount).append("个，");
                    }
                    if (javaCount != null && javaCount > 0) {
                        resultDetail.append("Java：").append(javaCount).append("个，");
                    }
                    if (pythonCount != null && pythonCount > 0) {
                        resultDetail.append("Python：").append(pythonCount).append("个，");
                    }
                    if (otherCount != null && otherCount > 0) {
                        resultDetail.append("其他：").append(otherCount).append("个，");
                    }
                    String detailStr = resultDetail.toString();
                    if (detailStr.endsWith("，")) {
                        detailStr = detailStr.substring(0, detailStr.length() - 1);
                    }
                    processInfo.setResultDetail(detailStr);
                    break;
                case JAVA:
                case PYTHON:
                case FOREEND:
                case DATA:
                    if (status.isPresent()) {
                        if (status.get() == ItemStatusEnum.SUCCESS) {
                            processInfo.setProgress(oneHandleProgress);
                        } else if (status.get() == ItemStatusEnum.FAILED) {
                            break;
                        }
                    } else {
                        processInfo.setStatusDesc("状态未知");
                        break;
                    }
                    break;
                case EMBEDDING:
                case KNOWLEDGE:
                case AI_REVIEW:
                    if (status.isPresent()) {
                        if (status.get() == ItemStatusEnum.SUCCESS) {
                            processInfo.setProgress(oneHandleProgress);
                        } else if (status.get() == ItemStatusEnum.FAILED) {
                            break;
                        }
                    } else {
                        processInfo.setStatusDesc("状态未知");
                        break;
                    }
                    JSONObject externObject = Optional.ofNullable(detail.getExtern())
                            .map(JSON::parseObject)
                            .orElse(new JSONObject());
                    // 总文件数量
                    Long totalNum = externObject.getLong("totalNum");
                    // 当前处理数量
                    Long runNum = externObject.getLong("runNum");
                    Double percent = externObject.getDouble("percent");
                    if (percent != null) {
                        processInfo.setProgress(percent);
                    } else if (totalNum != null && runNum != null && totalNum > 0 && runNum > 0) {
                        processInfo.setProgress(BigDecimal.valueOf(runNum.doubleValue() / totalNum.doubleValue()).setScale(2, RoundingMode.HALF_UP).doubleValue());
                    }
                    if (totalNum != null && totalNum > 0) {
                        processInfo.setResultDetail("文件总数：" + totalNum);
                    }

                    if (handleType == ItemStepEnum.AI_REVIEW) {
                        int issueFiles = externObject.getIntValue("issueFiles");
                        if (issueFiles > 0) {
                            processInfo.setResultDetail(processInfo.getResultDetail() + ", 问题文件数：" + issueFiles);
                        }
                    }

                    Long costTime = externObject.getLong("costTime");
                    if (costTime != null) {
                        String existingDetail = processInfo.getResultDetail();

                        String timeStart = "共花费时间";
                        String estimatedCompletionTime = "";
                        if (status.get() == ItemStatusEnum.INITIATING || status.get() == ItemStatusEnum.PROCESSING) {
                            timeStart = "已花费时间";
                            if (runNum != null && totalNum != null && runNum > 0 && totalNum > 0) {
                                if (handleType == ItemStepEnum.EMBEDDING) {
                                    estimatedCompletionTime = String.format("预计完成时间：%s", getTimeString((long) ((totalNum - runNum) * singleFileEmbeddingCost * 1000)));
                                } else {
                                    estimatedCompletionTime = String.format("预计完成时间：%s", getTimeString((long) ((totalNum - runNum) * singleFileKnowledgeCost * 1000)));
                                }
                            }
                        }

                        String timeSpentDesc = String.format("%s：%s", timeStart, handleTimeStr(costTime, detail.getCtime()));

                        String finalDetail;
                        if (StringUtils.isNotBlank(existingDetail)) {
                            if (StringUtils.isNotBlank(estimatedCompletionTime)) {
                                finalDetail = String.format("%s，%s，%s", existingDetail, timeSpentDesc, estimatedCompletionTime);
                            } else {
                                finalDetail = String.format("%s，%s", existingDetail, timeSpentDesc);
                            }
                        } else {
                            if (StringUtils.isNotBlank(estimatedCompletionTime)) {
                                finalDetail = String.format("%s，%s", timeSpentDesc, estimatedCompletionTime);
                            } else {
                                finalDetail = timeSpentDesc;
                            }
                        }
                        processInfo.setResultDetail(finalDetail);
                    }
                    break;
                default:
                    break;
            }
            processInfoList.add(processInfo);
        }
        return processInfoList;
    }

    private String handleTimeStr(Long costTime, LocalDateTime ctime) {
        if (costTime == null || costTime <= 0) {
            costTime = Duration.between(ctime, LocalDateTime.now()).toMillis();
        }
        return getTimeString(costTime);
    }

    private String getTimeString(Long costTime) {
        String timeDesc;
        long seconds = costTime / 1000;
        if (seconds < 60) {
            timeDesc = seconds + " 秒";
        } else if (seconds < 600) {
            long minutes = seconds / 60;
            long remainSeconds = seconds % 60;
            timeDesc = minutes + " 分 " + remainSeconds + " 秒";
        } else if (seconds < 3600) {
            timeDesc = (seconds / 60) + " 分钟";
        } else {
            long hours = seconds / 3600;
            long minutes = (seconds % 3600) / 60;
            timeDesc = hours + " 小时 " + minutes + " 分钟";
        }
        return timeDesc;
    }
}