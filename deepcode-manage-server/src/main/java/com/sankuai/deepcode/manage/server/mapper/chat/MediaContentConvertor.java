package com.sankuai.deepcode.manage.server.mapper.chat;

import com.sankuai.deepcode.dao.po.ChatMsgContentPO;
import com.sankuai.deepcode.manage.server.controller.chat.req.MessageVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;

import java.util.List;

/**
 * Package: com.sankuai.deepcode.manage.server.mapper.chat
 * Description:
 *
 * <AUTHOR>
 * @since 2025/3/27 15:31
 */
@Mapper(
        componentModel = "spring"
)
public interface MediaContentConvertor {
    default MessageVO.MediaContent convert(ChatMsgContentPO.MediaInfo mediaInfo) {
        if (mediaInfo == null || mediaInfo.media() == null) {
            return null;
        }

        ChatMsgContentPO.Media media = mediaInfo.media();
        if (media instanceof ChatMsgContentPO.ImageInfo) {
            ChatMsgContentPO.ImageInfo imageInfo = (ChatMsgContentPO.ImageInfo) media;
            return new MessageVO.ImagePayload(
                    imageInfo.getUrl(),
                    imageInfo.getName(),
                    imageInfo.getBase64Data()
            );
        } else if (media instanceof ChatMsgContentPO.FileInfo) {
            ChatMsgContentPO.FileInfo fileInfo = (ChatMsgContentPO.FileInfo) media;
            return new MessageVO.FilePayLoad(
                    fileInfo.getFileId(),
                    fileInfo.getVid(),
                    fileInfo.getName(),
                    fileInfo.getPath(),
                    fileInfo.getUrl(),
                    convertLineInfo(fileInfo.getStart()),
                    convertLineInfo(fileInfo.getEnd())
            );
        }
        return null;
    }

    @Mappings({
            @Mapping(target = "line", source = "lineInfo.line"),
            @Mapping(target = "column", source = "lineInfo.column")
    })
    MessageVO.LineInfo convertLineInfo(ChatMsgContentPO.LineInfo lineInfo);


    List<MessageVO.MediaContent> convert(List<ChatMsgContentPO.MediaInfo> mediaInfoList);
}
