package com.sankuai.deepcode.manage.server.controller;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.sankuai.deepcode.commons.CommonResult;
import com.sankuai.deepcode.dao.domain.CodeAnalysisItem;
import com.sankuai.deepcode.dao.service.CodeAnalysisItemService;
import com.sankuai.deepcode.manage.server.model.item.SearchParam;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/item")
public class ItemControllor {

    private static final Logger LOGGER = LoggerFactory.getLogger(ItemControllor.class);

    @Autowired
    private CodeAnalysisItemService codeAnalysisItemService;

    @PostMapping("/searchItem")
    public CommonResult<IPage<CodeAnalysisItem>> searchItem(@RequestBody SearchParam searchParam) {
        return CommonResult.success(
                codeAnalysisItemService.getPageItemsByGitUrl(searchParam.getGitUrl(), searchParam.getCurrentPage(), searchParam.getPageSize())
        );
    }

}
