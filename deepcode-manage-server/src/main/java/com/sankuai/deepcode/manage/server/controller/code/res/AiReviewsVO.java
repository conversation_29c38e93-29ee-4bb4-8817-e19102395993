package com.sankuai.deepcode.manage.server.controller.code.res;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Package: com.sankuai.deepcode.manage.server.controller.code.res
 * Description:
 *
 * <AUTHOR>
 * @since 2025/6/4 16:33
 */
@Setter
@Getter
@ToString
public class AiReviewsVO {
    @Schema(description = "文件vid")
    private String fileVid;
    @Schema(description = "文件路径")
    private String filePath;
    @Schema(description = "ai评审信息")
    List<AiReviewInfo> aiReviewInfos;

    @ToString
    @Getter
    @Setter
    public static class AiReviewInfo {
        @Schema(description = "规则信息")
        private RuleInfo ruleInfo;

        @Schema(description = "开始行")
        private Integer startLine;

        @Schema(description = "评审详情内容")
        private String reviewContent;

        @Schema(description = "评审时间")
        private LocalDateTime reviewTime;
    }

    @Setter
    @Getter
    @ToString
    public static class RuleInfo {
        @Schema(description = "规则ID")
        private Long ruleId;
        @Schema(description = "规则类型")
        private Integer ruleType;
        @Schema(description = "规则内容")
        private String content;
        @Schema(description = "语言")
        private String languages;
    }
}
