package com.sankuai.deepcode.manage.server.controller;

import com.sankuai.deepcode.commons.CommonResult;
import com.sankuai.deepcode.manage.server.constants.CachingKeys;
import com.sankuai.deepcode.manage.server.service.code.FileMethodService;
import com.sankuai.deepcode.manage.server.service.code.MethodService;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * Package: com.sankuai.deepcode.manage.server.controller
 * Description:
 *
 * <AUTHOR>
 * @since 2025/6/5 09:58
 */
@RestController
@RequestMapping("/common")
public class CommonController {
    final FileMethodService fileMethodService;
    final MethodService methodService;

    public CommonController(FileMethodService fileMethodService, MethodService methodService) {
        this.fileMethodService = fileMethodService;
        this.methodService = methodService;
    }

    @GetMapping("/clearCache")
    public CommonResult<Boolean> clearCache(
            @RequestParam("cacheKey") String cacheKey
    ) {
        switch (cacheKey) {
            case CachingKeys.FILE_METHODS:
                fileMethodService.clearMethodCache();
                return CommonResult.success(true);
            case CachingKeys.METHOD_INVOCATIONS:
                methodService.clearMethodInvocationCache();
                return CommonResult.success(true);
            case CachingKeys.METHOD_DEFINITIONS:
                methodService.clearMethodDefinitionCache();
                return CommonResult.success(true);
            case CachingKeys.METHOD_TOPOLOGY:
                methodService.clearMethodTopologyCache();
                return CommonResult.success(true);
            default:
                return CommonResult.fail("Invalid cache key");
        }
    }
}
