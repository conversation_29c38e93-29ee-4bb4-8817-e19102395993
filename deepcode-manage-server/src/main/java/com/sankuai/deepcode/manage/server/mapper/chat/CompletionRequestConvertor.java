package com.sankuai.deepcode.manage.server.mapper.chat;

import com.sankuai.deepcode.ai.llm.openai.chat.*;
import com.sankuai.deepcode.manage.server.controller.chat.req.ChatRequestVO;
import com.sankuai.deepcode.manage.server.controller.chat.req.MessageVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;

import java.util.List;

/**
 * Package: com.sankuai.deepcode.manage.server.mapper.chat
 * Description:
 *
 * <AUTHOR>
 * @since 2025/3/20 13:49
 */
@Mapper(
        componentModel = "spring"
)
public interface CompletionRequestConvertor {
    default ChatCompletionRequest convert(ChatRequestVO requestVO) {
        if (requestVO == null) {
            return null;
        }
        return ChatCompletionRequest.builder()
                .model(requestVO.getModel())
                .messages(convertMessage(requestVO.getMessages()))
                .temperature(requestVO.getTemperature())
                .topP(requestVO.getTopP())
                .n(requestVO.getN())
                .stream(requestVO.getStream())
                .stop(requestVO.getStop())
                .maxTokens(requestVO.getMaxTokens())
                .presencePenalty(requestVO.getPresencePenalty())
                .frequencyPenalty(requestVO.getFrequencyPenalty())
                .build();
    }

//    Message convertMessage(MessageVO messageVO);


    // 忽略自动映射
//    @Mapping(target = "content", ignore = true)
//    @Mapping(target = "role", expression = "java(Role.from())")
    @Mapping(target = "role", source = "role")
    default Message convertMessage(MessageVO messageVO) {
        if (messageVO == null) {
            return null;
        }

        switch (messageVO.getRole().toLowerCase()) {
            case "user":
                return convertToUserMessage(messageVO);
            case "assistant":
                return convertToAssistantMessage(messageVO);
            case "system":
                return convertToSystemMessage(messageVO);
            case "tool":
                return convertToToolMessage(messageVO);
            default:
                throw new IllegalArgumentException("Unknown role: " + messageVO.getRole());
        }
    }

    @Named("messageToUser")
    default UserMessage convertToUserMessage(MessageVO messageVO) {
        String content = messageVO.getContent().stream()
                .filter(c -> "text".equals(c.getType()))
                .map(MessageVO.ContentVO::getText)
                .findFirst()
                .orElse("");

        return UserMessage.builder()
                .content(content)
                .name(messageVO.getName())
                .build();

        /*
        UserMessage.Builder builder = UserMessage.builder()
                .name(messageVO.getName());

        for (MessageVO.ContentVO content : messageVO.getContent()) {
            if ("text".equals(content.getType())) {
                builder.addText(content.getText());
            }
        }

        return builder.build();
        */
    }

    @Named("messageToAssistant")
    default AssistantMessage convertToAssistantMessage(MessageVO messageVO) {
        String content = messageVO.getContent().stream()
                .filter(c -> "text".equals(c.getType()))
                .map(MessageVO.ContentVO::getText)
                .findFirst()
                .orElse("");

        return AssistantMessage.builder()
                .content(content)
                .name(messageVO.getName())
                .build();
    }

    @Named("messageToSystem")
    default SystemMessage convertToSystemMessage(MessageVO messageVO) {
        String content = messageVO.getContent().stream()
                .filter(c -> "text".equals(c.getType()))
                .map(MessageVO.ContentVO::getText)
                .findFirst()
                .orElse("");

        return SystemMessage.builder()
                .content(content)
                .name(messageVO.getName())
                .build();
    }

    @Named("messageToTool")
    default ToolMessage convertToToolMessage(MessageVO messageVO) {
        String content = messageVO.getContent().stream()
                .filter(c -> "text".equals(c.getType()))
                .map(MessageVO.ContentVO::getText)
                .findFirst()
                .orElse("");

        return ToolMessage.builder()
                .content(content)
                .build();
    }

    List<Message> convertMessage(List<MessageVO> messageVO);
}
