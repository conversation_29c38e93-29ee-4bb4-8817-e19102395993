package com.sankuai.deepcode.manage.server.controller.chat.res;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.sankuai.deepcode.ai.llm.openai.chat.ChatCompletionChoice;
import com.sankuai.deepcode.ai.llm.openai.chat.ChatCompletionResponse;
import com.sankuai.deepcode.ai.llm.openai.shared.Usage;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * Package: com.sankuai.deepcode.manage.server.controller.chat.res
 * Description:
 *
 * <AUTHOR>
 * @since 2025/3/18 19:33
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
@Setter
@Getter
@ToString(callSuper = true)
@Accessors(fluent = true)
public class ChatCompletionsVO extends ChatCompletionResponse {
    @Schema(description = "消息id")
    @JsonProperty
    String msgId;
    @JsonProperty
    @Schema(description = "前一条消息ID")
    String prevMsgId;

    public static Builder builder() {
        return new Builder();
    }

    public static class Builder extends ChatCompletionResponse.Builder {
        private String msgId;
        private String prevMsgId;

        public Builder msgId(String msgId) {
            this.msgId = msgId;
            return this;
        }

        public Builder prevMsgId(String prevMsgId) {
            this.prevMsgId = prevMsgId;
            return this;
        }

        @Override
        public ChatCompletionsVO build() {
            return new ChatCompletionsVO(
                    super.id,
                    super.created,
                    super.model,
                    super.choices,
                    super.content,
                    super.usage,
                    super.systemFingerprint,
                    super.serviceTier,
                    msgId,
                    prevMsgId
            );
        }
    }


    public ChatCompletionsVO(String id, Integer created, String model, List<ChatCompletionChoice> choices, String content, Usage usage, String systemFingerprint, String serviceTier, String msgId, String prevMsgId) {
        super(
                ChatCompletionResponse.builder()
                        .id(id)
                        .created(created)
                        .model(model)
                        .choices(choices)
                        .content(content)
                        .usage(usage)
                        .systemFingerprint(systemFingerprint)
                        .serviceTier(serviceTier)
        );
        this.msgId = msgId;
        this.prevMsgId = prevMsgId;
    }

    public static ChatCompletionsVO from(ChatCompletionResponse response, String msgId, String prevMsgId) {
        return new ChatCompletionsVO(
                response.id(),
                response.created(),
                response.model(),
                response.choices(),
                response.content(),
                response.usage(),
                response.systemFingerprint(),
                response.serviceTier(),
                msgId,
                prevMsgId
        );
    }

    public static Builder from(ChatCompletionResponse response) {
        Builder builder = builder();
        builder.id(response.id())
                .created(response.created())
                .model(response.model())
                .choices(response.choices())
                .usage(response.usage())
                .content(response.content())
                .systemFingerprint(response.systemFingerprint())
                .serviceTier(response.serviceTier());
        return builder;
    }

}
