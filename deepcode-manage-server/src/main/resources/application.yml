# web服务端口号
server:
  # 启用代理头识别
  port: 8080
#  forward-headers-strategy: framework
management:
  endpoints:
    web:
      base-path: /monitor
      path-mapping:
        health: /alive

spring:
  profiles:
    active: '@active-profile@'
    include: dao
  mvc:
    pathmatch:
      matching-strategy: ant_path_matcher

oneapi:
  llm:
    model: deepseek-r1-friday
    api-key: sk-ILfMb0ur7izU5W6r214b3dAeAa0a451cA
    log-responses: true
    log-requests: true
    log-level: info
  embedding:
    model: jina-embeddings-v3
    api-key: sk-xs9hzuwPwT4nafLP27A99e86685a4a9a8

auth:
  exclude-urls:
    - /user/login
    - /user/auth2/github/loginByGithub
    - /user/bindUser
    - /user/auth2/mt/loginByMt
    - /user/test
    - /api-docs/**
    - /swagger-ui/**
    - /webjars/**


#日志配置文件位置
logging:
  config: classpath:log4j2/log4j2-server.xml

# SpringDoc配置
springdoc:
  api-docs:
    path: /api-docs
    resolve-schema-properties: true
  swagger-ui:
    path: /swagger-ui.html
  default-produces-media-type: application/json

caching:
  specs:
    method_definitions:
      maximumSize: 10000
      expireAfterWrite: PT1H
    method_invocations:
      maximumSize: 10000
      expireAfterWrite: PT1H
    file_methods:
      maximumSize: 30000
      expireAfterWrite: PT1H
    method_topology:
      maximumSize: 10000
      expireAfterWrite: PT2H

---
spring:
  config:
    activate:
      on-profile: local


#日志配置文件位置
logging:
  config: classpath:log4j2/log4j2-local.xml
  level:
    com:
      sankuai:
        deepcode:
          dao:
            mapper: debug
        ai:
          llm:
            openai: debug
    org.apache.ibatis: trace

---

spring:
  config:
    activate:
      on-profile: test
#日志配置文件位置
logging:
  config: classpath:log4j2/log4j2-server.xml


---

spring:
  config:
    activate:
      on-profile: prod

#日志配置文件位置
logging:
  config: classpath:log4j2/log4j2-server.xml


---

spring:
  config:
    activate:
      on-profile: staging

#日志配置文件位置
logging:
  config: classpath:log4j2/log4j2-server.xml


---

spring:
  config:
    activate:
      on-profile: dev

#日志配置文件位置
logging:
  config: classpath:log4j2/log4j2-server.xml

