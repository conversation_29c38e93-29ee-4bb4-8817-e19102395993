<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE generatorConfiguration PUBLIC "-//mybatis.org//DTD MyBatis Generator Configuration 1.0//EN"
        "http://mybatis.org/dtd/mybatis-generator-config_1_0.dtd">

<generatorConfiguration>
    <context id="MybatisGenerator" targetRuntime="MyBatis3">

        <plugin type="org.mybatis.generator.plugins.EqualsHashCodePlugin"/>

        <commentGenerator>
            <property name="suppressDate" value="true"/>
            <property name="suppressAllComments" value="true"/>
            <property name="javaFileEncoding" value="UTF-8"/>
        </commentGenerator>

        <!--load from properties 替换为申请的数据库地址、用户名、密码-->
        <jdbcConnection driverClass="com.mysql.jdbc.Driver"
                        connectionURL="*****************************************"
                        userId="rds_deepcode"
                        password="$jJU4eOJyW*dJo">
        </jdbcConnection>

        <!--load targetProject from properties-->
        <javaModelGenerator targetPackage="com.sankuai.deepcode.dao.domain"
                            targetProject="src/main/java">
            <property name="enableSubPackages" value="true"/>
            <property name="trimStrings" value="true"/>
        </javaModelGenerator>

        <sqlMapGenerator targetPackage="mapping" targetProject="src/main/resources">
            <property name="enableSubPackages" value="true"/>
        </sqlMapGenerator>

        <!--load targetProject from properties-->
        <javaClientGenerator type="XMLMAPPER" targetPackage="com.sankuai.deepcode.dao.mapper"
                             targetProject="src/main/java">
            <property name="enableSubPackages" value="true"/>
        </javaClientGenerator>
                <table tableName="sys_user_detail">
                    <generatedKey column="id" sqlStatement="MySql" identity="true"/>
                </table>
        <!--        <table tableName="code_analysis_item">-->
        <!--            <generatedKey column="id" sqlStatement="MySql" identity="true"/>-->
        <!--        </table>-->
        <!--        <table tableName="code_class_analysis">-->
        <!--            <generatedKey column="id" sqlStatement="MySql" identity="true"/>-->
        <!--        </table>-->
        <!--        <table tableName="code_field_analysis">-->
        <!--            <generatedKey column="id" sqlStatement="MySql" identity="true"/>-->
        <!--        </table>-->
        <!--        <table tableName="code_file_analysis">-->
        <!--            <generatedKey column="id" sqlStatement="MySql" identity="true"/>-->
        <!--        </table>-->
        <!--        <table tableName="code_method_analysis">-->
        <!--            <generatedKey column="id" sqlStatement="MySql" identity="true"/>-->
        <!--        </table>-->
        <!--        <table tableName="method_invoke_method">-->
        <!--            <generatedKey column="id" sqlStatement="MySql" identity="true"/>-->
        <!--        </table>-->
        <!--        <table tableName="method_desc">-->
        <!--            <generatedKey column="id" sqlStatement="MySql" identity="true"/>-->
        <!--        </table>-->
        <!--        <table tableName="class_desc">-->
        <!--            <generatedKey column="id" sqlStatement="MySql" identity="true"/>-->
        <!--        </table>-->
        <!--        <table tableName="code_view_analysis">-->
        <!--            <generatedKey column="id" sqlStatement="MySql" identity="true"/>-->
        <!--        </table>-->
        <!--                <table tableName="method_quote_field">-->
        <!--                    <generatedKey column="id" sqlStatement="MySql" identity="true"/>-->
        <!--                </table>-->
<!--        <table tableName="check_ast_rate">-->
<!--            <generatedKey column="id" sqlStatement="MySql" identity="true"/>-->
<!--        </table>-->
    </context>
</generatorConfiguration>
