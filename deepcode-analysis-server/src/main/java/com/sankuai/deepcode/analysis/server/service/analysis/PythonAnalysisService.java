package com.sankuai.deepcode.analysis.server.service.analysis;

import com.sankuai.deepcode.analysis.server.struct.python.*;
import com.sankuai.deepcode.ast.model.python3.PythonAnalysesResult;
import com.sankuai.deepcode.ast.model.python3.PythonFunctionCallNode;
import com.sankuai.deepcode.ast.model.python3.PythonModuleNode;
import com.sankuai.deepcode.ast.model.python3.PythonParameterNode;
import com.sankuai.deepcode.ast.python3.analysis.PythonAnalyzer;
import com.sankuai.deepcode.commons.PaginationUtils;
import com.sankuai.deepcode.dao.domain.*;
import com.sankuai.deepcode.dao.service.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.map.MultiKeyMap;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.commons.lang3.tuple.Triple;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * Package: com.sankuai.deepcode.analysis.server.service.analysis
 * Description:
 *
 * <AUTHOR>
 * @since 2024/12/23 14:02
 */
@Service
@Slf4j
public class PythonAnalysisService {
    @Autowired
    private CodeAnalysisItemService codeAnalysisItemService;
    @Autowired
    private CodeClassAnalysisService codeClassAnalysisService;
    @Autowired
    private ClassImportClassService classImportClassService;
    @Autowired
    private CodeMethodAnalysisService codeMethodAnalysisService;
    @Autowired
    private MethodInvokeMethodService methodInvokeMethodService;
    @Autowired
    private CodeFileAnalysisService codeFileAnalysisService;
    @Autowired
    private CodeViewAnalysisService codeViewAnalysisService;
    @Autowired
    private CodeFieldAnalysisService codeFieldAnalysisService;

    @Autowired
    private PythonModuleClassConvertor pythonModuleClassConvertor;
    @Autowired
    private PythonMethodConvertor pythonMethodConvertor;
    @Autowired
    private PythonMethodCallConvertor pythonMethodCallConvertor;
    @Autowired
    private PythonImportConvertor pythonImportConvertor;
    @Autowired
    private PythonCodeFileViewConvertor pythonCodeFileViewConvertor;
    @Autowired
    private PythonFieldConvertor pythonFieldConvertor;

    /**
     * 批量插入大小
     */
    private final int batchSize = 500;


    public PythonAnalysesResult analysis(String gitUrl, String gitName, String fromBranch, String toBranch, String buildBranch, String buildCommit) {
        Long itemId = insertAnalyseItem(gitUrl, fromBranch, toBranch, buildBranch);

        PythonAnalysesResult analysesResult = PythonAnalyzer.analyzePythonProject(itemId, gitUrl, gitName, fromBranch, toBranch, buildBranch, buildCommit);

        insertClassAnalysis(itemId, analysesResult);

        return analysesResult;
    }

    public void insertClassAnalysis(Long itemId, PythonAnalysesResult analysesResult) {
        List<PythonModuleNode> moduleNodes = new ArrayList<>(analysesResult.getModuleMap().values());

        //1. 模块、类定义内容存储[code_class_analysis]
        batchInsertModuleAndClassAnalysis(itemId, moduleNodes);
        //2. 模块、类、方法、变量等引用存储 [class_import_class]
        batchInsertModuleImportModule(itemId, moduleNodes);

        //3. 模块定义字段、类静态字段存储[code_field_analysis]
        batchInsertField(itemId, moduleNodes);

        //4. 方法定义存储 [code_method_analysis]
        batchInsertModuleAndClassMethod(itemId, moduleNodes);
        //5. 方法调用方法存储 [method_invoke_method]
        batchInsertMethodCall(itemId, moduleNodes);

        //6. 原始文件信息存储 [code_file_analysis]
        batchInsertFileView(itemId, analysesResult);
        //7. 文本类文件内容存储[code_view_analysis]
        batchInsertCodeView(itemId, analysesResult);
    }

    /**
     * 批量插入代码视图
     *
     * @param itemId         项目ID
     * @param analysesResult 解析结果
     */
    private void batchInsertCodeView(Long itemId, PythonAnalysesResult analysesResult) {
        List<CodeViewAnalysis> codeViewList = analysesResult.getFileMap().values().stream()
                .map(fileNode -> pythonCodeFileViewConvertor.convertCode(itemId, fileNode))
                .flatMap(List::stream)
                .collect(Collectors.toList());

        PaginationUtils.processPages(codeViewList, batchSize, codeViewAnalysisService::batchInsert);
    }

    /**
     * 批量插入文件视图
     *
     * @param itemId         项目ID
     * @param analysesResult 解析结果
     */
    private void batchInsertFileView(Long itemId, PythonAnalysesResult analysesResult) {
        List<CodeFileAnalysis> fileList = analysesResult.getFileMap().values().stream()
                .map(fileNode -> pythonCodeFileViewConvertor.convertFile(itemId, fileNode))
                .collect(Collectors.toList());

        PaginationUtils.processPages(fileList, batchSize, codeFileAnalysisService::batchInsert);
    }

    /**
     * TODO 需要完成字段定义的格式确定和解析
     *
     * @param itemId      项目ID
     * @param moduleNodes 模块节点列表
     */
    private void batchInsertField(Long itemId, List<PythonModuleNode> moduleNodes) {
        // 1. 模块下的字段定义
        List<PythonParameterNode> moduleParamNodeList = moduleNodes.stream()
                .flatMap(moduleNode -> CollectionUtils.isNotEmpty(moduleNode.getModuleParamNodes()) ? moduleNode.getModuleParamNodes().stream() : Stream.empty())
                .collect(Collectors.toList());

        List<CodeFieldAnalysis> moduleFieldList = moduleParamNodeList.stream()
                .map(paramNode -> pythonFieldConvertor.convert(itemId, paramNode))
                .collect(Collectors.toList());

        PaginationUtils.processPages(moduleFieldList, batchSize, codeFieldAnalysisService::batchInsert);
    }

    /**
     * 批量插入方法调用
     *
     * @param itemId      项目ID
     * @param moduleNodes 模块节点列表
     */
    private void batchInsertMethodCall(Long itemId, List<PythonModuleNode> moduleNodes) {
        // 1.模块下的方法调用
        MultiKeyMap<String, List<PythonFunctionCallNode>> moduleFunctionCallMap = new MultiKeyMap<>();
        moduleNodes.forEach(moduleNode -> {
            if (CollectionUtils.isNotEmpty(moduleNode.getFunctionCallNodes())) {
                moduleNode.getFunctionCallNodes().forEach(callNode -> {
                    String moduleVid = moduleNode.getVid();
                    String callVid = callNode.getVid();
                    List<PythonFunctionCallNode> callLineList = moduleFunctionCallMap.get(moduleVid, callVid);
                    if (callLineList == null) {
                        callLineList = new ArrayList<>();
                        moduleFunctionCallMap.put(moduleVid, callVid, callLineList);
                    }
                    callLineList.add(callNode);
                });
            }
        });
        List<MethodInvokeMethod> moduleFunctionCallList = moduleFunctionCallMap.entrySet().stream()
                .map(entry -> pythonMethodCallConvertor.convertCallNode(itemId, entry.getKey().getKey(0), entry.getKey().getKey(1), entry.getValue()))
                .collect(Collectors.toList());

        PaginationUtils.processPages(moduleFunctionCallList, batchSize, methodInvokeMethodService::batchInsert);

        // 2.类方法下的方法调用
        MultiKeyMap<String, List<PythonFunctionCallNode>> classMethodCallMap = new MultiKeyMap<>();
        moduleNodes.stream()
                .flatMap(moduleNode -> CollectionUtils.isNotEmpty(moduleNode.getClassNodes()) ? moduleNode.getClassNodes().stream() : Stream.empty())
                .flatMap(classNode -> CollectionUtils.isNotEmpty(classNode.getMethods()) ? classNode.getMethods().stream() : Stream.empty())
                .forEach(classMethodNode -> {
                    if (CollectionUtils.isNotEmpty(classMethodNode.getCallNodes())) {
                        classMethodNode.getCallNodes().forEach(callNode -> {
                            String classMethodVid = classMethodNode.getVid();
                            String callVid = callNode.getVid();
                            List<PythonFunctionCallNode> callLineList = moduleFunctionCallMap.get(classMethodVid, callVid);
                            if (callLineList == null) {
                                callLineList = new ArrayList<>();
                                classMethodCallMap.put(classMethodVid, callVid, callLineList);
                            }
                            callLineList.add(callNode);
                        });
                    }
                });
        List<MethodInvokeMethod> classMethodCallList = classMethodCallMap.entrySet().stream()
                .map(entry -> pythonMethodCallConvertor.convertCallNode(itemId, entry.getKey().getKey(0), entry.getKey().getKey(1), entry.getValue()))
                .collect(Collectors.toList());
        PaginationUtils.processPages(classMethodCallList, batchSize, methodInvokeMethodService::batchInsert);

        // 3.模块中方法定义时「装饰器函数调用」
        MultiKeyMap<String, List<PythonFunctionCallNode>> moduleFunctionDecoreatorCallMap = new MultiKeyMap<>();
        moduleNodes.stream().flatMap(moduleNode -> CollectionUtils.isNotEmpty(moduleNode.getFunctionNodes()) ? moduleNode.getFunctionNodes().stream() : Stream.empty())
                .forEach(methodFunctionNode -> {
                    if (CollectionUtils.isEmpty(methodFunctionNode.getDecorators())) {
                        return;
                    }
                    String moduleMethodVid = methodFunctionNode.getVid();
                    methodFunctionNode.getDecorators().forEach(decorator -> {
                        String decoratorVid = decorator.getVid();
                        List<PythonFunctionCallNode> callLineList = moduleFunctionDecoreatorCallMap.get(moduleMethodVid, decoratorVid);
                        if (callLineList == null) {
                            callLineList = new ArrayList<>();
                            moduleFunctionDecoreatorCallMap.put(moduleMethodVid, decoratorVid, callLineList);
                        }
                        callLineList.add(decorator);
                    });
                });
        List<MethodInvokeMethod> moduleFunctionDecoreatorCallList = moduleFunctionDecoreatorCallMap.entrySet().stream()
                .map(entry -> pythonMethodCallConvertor.convertCallNode(itemId, entry.getKey().getKey(0), entry.getKey().getKey(1), entry.getValue()))
                .collect(Collectors.toList());
        PaginationUtils.processPages(moduleFunctionDecoreatorCallList, batchSize, methodInvokeMethodService::batchInsert);

        // 4.类中方法定义时「装饰器函数调用」
        MultiKeyMap<String, List<PythonFunctionCallNode>> classMethodDecoreatorCallMap = new MultiKeyMap<>();
        moduleNodes.stream()
                .flatMap(moduleNode -> CollectionUtils.isNotEmpty(moduleNode.getClassNodes()) ? moduleNode.getClassNodes().stream() : Stream.empty())
                .flatMap(classNode -> CollectionUtils.isNotEmpty(classNode.getMethods()) ? classNode.getMethods().stream() : Stream.empty())
                .forEach(classMethodNode -> {
                    if (CollectionUtils.isEmpty(classMethodNode.getDecorators())) {
                        return;
                    }
                    String classMethodVid = classMethodNode.getVid();
                    classMethodNode.getDecorators().forEach(decorator -> {
                        String decoratorVid = decorator.getVid();
                        List<PythonFunctionCallNode> callLineList = classMethodDecoreatorCallMap.get(classMethodVid, decoratorVid);
                        if (callLineList == null) {
                            callLineList = new ArrayList<>();
                            classMethodDecoreatorCallMap.put(classMethodVid, decoratorVid, callLineList);
                        }
                        callLineList.add(decorator);
                    });
                });
        List<MethodInvokeMethod> classMothodDecoreatorCallList = classMethodDecoreatorCallMap.entrySet().stream()
                .map(entry -> pythonMethodCallConvertor.convertCallNode(itemId, entry.getKey().getKey(0), entry.getKey().getKey(1), entry.getValue()))
                .collect(Collectors.toList());
        PaginationUtils.processPages(classMothodDecoreatorCallList, batchSize, methodInvokeMethodService::batchInsert);
    }

    /**
     * 批量插入模块和类的方法
     *
     * @param itemId      项目ID
     * @param moduleNodes 模块节点列表
     */
    private void batchInsertModuleAndClassMethod(Long itemId, List<PythonModuleNode> moduleNodes) {
        // 模块中的方法定义
        List<CodeMethodAnalysis> moduleFunctionList = moduleNodes.stream()
                .map(m -> Pair.of(m, m.getFunctionNodes()))
                .filter(p -> CollectionUtils.isNotEmpty(p.getRight()))
                .flatMap(p -> p.getRight().stream().map(f -> Pair.of(p.getLeft(), f)))
                .map(moduleMethod -> pythonMethodConvertor.convertFunctionNode(itemId, moduleMethod.getRight(), moduleMethod.getLeft()))
                .collect(Collectors.toList());

        // 类中的方法定义
        List<CodeMethodAnalysis> classMethodList = moduleNodes.stream()
                .flatMap(module -> module.getClassNodes() == null ? Stream.empty() : module.getClassNodes().stream().map(c -> Pair.of(module, c)))
                .flatMap(classNode -> classNode.getRight() == null ? Stream.empty() : classNode.getRight().getMethods().stream().map(m -> Triple.of(classNode.getLeft(), classNode.getRight(), m)))
                .map(moduleClassMethod -> pythonMethodConvertor.convertMethodNode(itemId, moduleClassMethod.getRight(), moduleClassMethod.getLeft(), moduleClassMethod.getMiddle()))
                .collect(Collectors.toList());

        List<CodeMethodAnalysis> methodList = Stream.concat(moduleFunctionList.stream(), classMethodList.stream()).collect(Collectors.toList());
        PaginationUtils.processPages(methodList, batchSize, codeMethodAnalysisService::batchInsert);
    }

    /**
     * 批量插入模块导入的模块
     *
     * @param itemId      项目ID
     * @param moduleNodes 模块节点列表
     */
    private void batchInsertModuleImportModule(Long itemId, List<PythonModuleNode> moduleNodes) {
        List<ClassImportClass> importClassList = moduleNodes.stream()
                .flatMap(module -> module.getImportNodes() == null ? Stream.empty() : module.getImportNodes().stream())
                .map(importNode -> pythonImportConvertor.convertImportNode(itemId, importNode))
                .flatMap(List::stream)
                .collect(Collectors.toList());

        PaginationUtils.processPages(importClassList, batchSize, classImportClassService::batchInsert);
    }

    /**
     * 批量插入模块和类
     *
     * @param itemId      项目ID
     * @param moduleNodes 模块节点列表
     */
    private void batchInsertModuleAndClassAnalysis(Long itemId, List<PythonModuleNode> moduleNodes) {
        List<CodeClassAnalysis> moduleList = pythonModuleClassConvertor.convertModuleNodeList(itemId, moduleNodes);
        // 分批插入模块信息
        PaginationUtils.processPages(moduleList, batchSize, codeClassAnalysisService::batchInsert);


        List<CodeClassAnalysis> classList = moduleNodes.stream()
                .filter(Objects::nonNull)
                .flatMap(module -> module.getClassNodes() == null ? Stream.empty() : module.getClassNodes().stream())
                .filter(Objects::nonNull)
                .map(classNode -> pythonModuleClassConvertor.convertClassNode(itemId, classNode))
                .collect(Collectors.toList());
        // 分批插入类信息
        PaginationUtils.processPages(classList, batchSize, codeClassAnalysisService::batchInsert);
    }

    /**
     * 分析任务的插入
     *
     * @param gitUrl      git 地址
     * @param fromBranch  分析的来源分支
     * @param toBranch    分析的目标分支
     * @param buildBranch 构建的分支
     * @return 插入的id
     */
    private Long insertAnalyseItem(String gitUrl, String fromBranch, String toBranch, String buildBranch) {
        CodeAnalysisItem codeAnalysisItem = new CodeAnalysisItem();
        LocalDateTime date = LocalDateTime.now();
        codeAnalysisItem.setGitUrl(gitUrl);
        codeAnalysisItem.setFromBranch(fromBranch);
        codeAnalysisItem.setToBranch(toBranch);
        codeAnalysisItem.setBuildBranch(buildBranch);
        codeAnalysisItem.setDiff(StringUtils.isNotEmpty(toBranch));
        codeAnalysisItem.setStatus(0);
        codeAnalysisItem.setUtime(date);
        codeAnalysisItem.setCtime(date);
        codeAnalysisItem.setValid(true);

        codeAnalysisItemService.save(codeAnalysisItem);
        return codeAnalysisItem.getId();
    }
}
