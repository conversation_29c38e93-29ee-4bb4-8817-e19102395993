package com.sankuai.deepcode.analysis.server.struct.python;

import com.sankuai.deepcode.ast.model.python3.PythonParameterNode;
import com.sankuai.deepcode.dao.domain.CodeFieldAnalysis;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;

/**
 * Package: com.sankuai.houyi.code.graph.server.struct.python
 * Description:
 *
 * <AUTHOR>
 * @since 2025/1/16 19:27
 */

@Mapper(
        componentModel = "spring"
)
public interface PythonFieldConvertor {
    @Mappings({
            @Mapping(target = "itemId", source = "itemId"),
            @Mapping(target = "fieldVid", expression = "java(pythonParameterNode.getVid())"),
            @Mapping(target = "source", constant = "local"),

            @Mapping(target = "fieldName", source = "pythonParameterNode.name"),
            @Mapping(target = "fieldType", source = "pythonParameterNode.type"),
            @Mapping(target = "fieldValue", source = "pythonParameterNode.defaultValue"),

            @Mapping(target = "className", source = "pythonParameterNode.modulePath"),

            @Mapping(target = "valid", constant = "true"),
    })
    CodeFieldAnalysis convert(Long itemId, PythonParameterNode pythonParameterNode);
}
