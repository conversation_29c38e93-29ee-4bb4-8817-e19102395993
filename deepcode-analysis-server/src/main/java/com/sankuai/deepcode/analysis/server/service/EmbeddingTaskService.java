package com.sankuai.deepcode.analysis.server.service;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.gson.JsonArray;
import com.google.gson.JsonObject;
import com.sankuai.deepcode.ai.embedding.model.EmbeddingsRes;
import com.sankuai.deepcode.ai.embedding.service.EmbeddingService;
import com.sankuai.deepcode.ai.enums.EmbeddingEnum;
import com.sankuai.deepcode.ast.enums.DiffTypeEnum;
import com.sankuai.deepcode.dao.domain.CodeAnalysisItem;
import com.sankuai.deepcode.dao.domain.CodeFileAnalysis;
import com.sankuai.deepcode.dao.domain.CodeViewAnalysis;
import com.sankuai.deepcode.dao.enums.ItemStatusEnum;
import com.sankuai.deepcode.dao.enums.ItemStepEnum;
import com.sankuai.deepcode.dao.milvus.EmbeddingInit;
import com.sankuai.deepcode.dao.milvus.MilvusService;
import com.sankuai.deepcode.dao.model.AsyncTaskExtern;
import com.sankuai.deepcode.dao.service.CodeFileAnalysisService;
import com.sankuai.deepcode.dao.service.CodeViewAnalysisService;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.AsyncResult;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.Future;

@Service
public class EmbeddingTaskService {

    @Autowired
    private CodeViewAnalysisService codeViewAnalysisService;
    @Autowired
    private CodeFileAnalysisService codeFileAnalysisService;
    @Autowired
    private EmbeddingService embeddingService;
    @Autowired
    private MilvusService milvusService;
    @Autowired
    private EmbeddingInit embeddingInit;
    @Autowired
    private ItemSatusService itemSatusService;

    private static final Logger LOGGER = LoggerFactory.getLogger(EmbeddingTaskService.class);

    int CHUNK_SIZE = 500;

    public Future<Boolean> embeddingByItem(CodeAnalysisItem codeAnalysisItem) {
        long start = System.currentTimeMillis();
        try {
            itemSatusService.updateItemAsyncStatus(codeAnalysisItem, ItemStatusEnum.PROCESSING);
            itemSatusService.insertDetail(codeAnalysisItem.getId(), ItemStepEnum.EMBEDDING, "开始原始文本向量化", "");
            List<CodeFileAnalysis> codeFileAnalyses = codeFileAnalysisService.getByItemId(codeAnalysisItem.getId());
            if (milvusService.isExist("item_code_" + codeAnalysisItem.getId())) {
                milvusService.drop("item_code_" + codeAnalysisItem.getId());
            }
            embeddingInit.createCollection(codeAnalysisItem.getId());

            AsyncTaskExtern embeddingExtern = new AsyncTaskExtern();
            embeddingExtern.setStatus(ItemStatusEnum.PROCESSING.getCode());
            embeddingExtern.setModel(EmbeddingEnum.JINA_V3.getName());
            embeddingExtern.setTotalNum(codeFileAnalyses.size());
            embeddingExtern.setRunNum(0);
            embeddingExtern.setPercent(0);

            for (CodeFileAnalysis codeFileAnalysis : codeFileAnalyses) {
                List<CodeViewAnalysis> codeViewAnalyses = codeViewAnalysisService.getAllContent(codeAnalysisItem.getId(), codeFileAnalysis.getFileVid());
                if (codeViewAnalyses.size() == 1) {
                    continue;
                }
                List<CodeViewAnalysis> allContent = new ArrayList<>();
                for (CodeViewAnalysis codeViewAnalysis : codeViewAnalyses) {
                    if (StringUtils.isNotEmpty(codeViewAnalysis.getCodeView())) {
                        allContent.add(codeViewAnalysis);
                    }
                }


                embeddingExtern.setRunNum(embeddingExtern.getRunNum() + 1);
                //500行切一个chunk
                List<JsonObject> data = new ArrayList<>();
                List<List<CodeViewAnalysis>> chunks = Lists.partition(allContent, CHUNK_SIZE);
                int chunkId = 0;
                for (List<CodeViewAnalysis> chunk : chunks) {
                    int startLine = chunk.get(0).getCodeLine();
                    int endLine = chunk.get(chunk.size() - 1).getCodeLine();
                    EmbeddingsRes embeddingsRes = embeddingService.embeddings(chunk.toString(), EmbeddingEnum.JINA_V3.getName());
                    JsonObject jsonObject = new JsonObject();
                    jsonObject.addProperty("file_vid", codeFileAnalysis.getFileVid());
                    jsonObject.addProperty("chunk_id", chunkId);
                    jsonObject.addProperty("start_line", startLine);
                    jsonObject.addProperty("end_line", endLine);
                    StringBuilder descView = new StringBuilder();
                    for (CodeViewAnalysis codeViewAnalysis : chunk) {
                        if (codeAnalysisItem.isDiff()) {
                            descView.append(codeViewAnalysis.getCodeView()).append("\n");
                        } else {
                            if (codeViewAnalysis.getCodeType() == DiffTypeEnum.ADD.getCode()) {
                                descView.append("+").append(codeViewAnalysis.getCodeView()).append("\n");
                            } else if (codeViewAnalysis.getCodeType() == DiffTypeEnum.DEL.getCode()) {
                                descView.append("-").append(codeViewAnalysis.getCodeView()).append("\n");
                            } else {
                                descView.append(" ").append(codeViewAnalysis.getCodeView()).append("\n");
                            }
                        }
                        jsonObject.addProperty("code", descView.toString());
                        JsonArray descArray = new JsonArray();
                        for (Float value : embeddingsRes.getData().get(0).getEmbedding()) {
                            descArray.add(value);
                        }
                        jsonObject.add("embedding", descArray);
                    }
                    chunkId++;
                    data.add(jsonObject);
                    milvusService.insert("item_code_" + codeAnalysisItem.getId(), "partition0", data);

                }

                if (embeddingExtern.getRunNum() % 20 == 0) {
                    double percent = Math.round((double) embeddingExtern.getRunNum() / embeddingExtern.getTotalNum() * 10000) / 100.0;
                    embeddingExtern.setPercent(percent);
                    itemSatusService.updateDetailAsyncExtern(codeAnalysisItem.getId(), ItemStepEnum.EMBEDDING, JSON.toJSONString(embeddingExtern));
                    LOGGER.info("analysisByItem item:{} embedding percent:{}", codeAnalysisItem.getId(), percent);
                }

            }


            embeddingExtern.setStatus(ItemStatusEnum.SUCCESS.getCode());
            embeddingExtern.setPercent(100.0);
            long cost = System.currentTimeMillis() - start;
            embeddingExtern.setCostTime(cost);
            itemSatusService.updateDetailSucess(codeAnalysisItem.getId(), ItemStepEnum.EMBEDDING, "原始文本向量化完成", JSON.toJSONString(embeddingExtern));
            itemSatusService.updateItemAsyncSucess(codeAnalysisItem, cost);
        } catch (Exception e) {
            LOGGER.error("analysisByItemId error e:", e);
            itemSatusService.updateDetailFail(codeAnalysisItem.getId(), ItemStepEnum.EMBEDDING, "原始文本向量化异常");
            itemSatusService.updateItemAsyncFail(codeAnalysisItem, System.currentTimeMillis() - start);
        } finally {
            return new AsyncResult<Boolean>(true);
        }
    }

}
