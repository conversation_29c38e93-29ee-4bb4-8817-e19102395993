package com.sankuai.deepcode.analysis.server.struct.python;


import com.alibaba.fastjson.JSON;
import com.sankuai.deepcode.ast.model.base.CodeView;
import com.sankuai.deepcode.ast.model.java.FileNode;
import com.sankuai.deepcode.ast.util.Md5Util;
import com.sankuai.deepcode.dao.domain.CodeFileAnalysis;
import com.sankuai.deepcode.dao.domain.CodeViewAnalysis;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Mapper(
        componentModel = "spring",
        imports = {
                JSON.class,
                Md5Util.class
        }
)
public interface PythonCodeFileViewConvertor {

    @Mappings({
            @Mapping(target = "itemId", source = "itemId"),
            @Mapping(target = "fileVid", expression = "java(Md5Util.fileNodeToMd5(fileNode))"),
            @Mapping(target = "moduleName", source = "fileNode.moduleName"),
            @Mapping(target = "fileName", source = "fileNode.fileName"),
            @Mapping(target = "filePath", source = "fileNode.path"),
            @Mapping(target = "fileType", source = "fileNode.fileType"),
            @Mapping(target = "changeType", source = "fileNode.changeType"),
            @Mapping(target = "changeLines", expression = "java(JSON.toJSONString(fileNode.getChangeLines()))"),
            @Mapping(target = "checkType", source = "fileNode.checkType"),
            @Mapping(target = "checkLines", expression = "java(JSON.toJSONString(fileNode.getCheckLines()))"),
            @Mapping(target = "startLine", source = "fileNode.startLine"),
            @Mapping(target = "endLine", source = "fileNode.endLine"),
            @Mapping(target = "commentLines", expression = "java(JSON.toJSONString(fileNode.getCommentLines()))"),
            @Mapping(target = "commitCount", source = "fileNode.commitCount"),

            @Mapping(target = "valid", constant = "true"),
    })
    CodeFileAnalysis convertFile(Long itemId, FileNode fileNode);

    default List<CodeViewAnalysis> convertCode(Long itemId, FileNode fileNode) {
        return fileNode.getCodeViews().stream().map(codeView -> convertCodeView(itemId, fileNode, codeView)).collect(Collectors.toList());
    }

    default CodeViewAnalysis convertCodeView(Long itemId, FileNode fileNode, CodeView codeView) {
        CodeViewAnalysis codeViewAnalysis = new CodeViewAnalysis();

        codeViewAnalysis.setItemId(itemId);
        codeViewAnalysis.setSourceVid(Md5Util.fileNodeToMd5(fileNode));
        codeViewAnalysis.setCodeId(codeView.getId());
        codeViewAnalysis.setCodeLine(codeView.getLine());
        codeViewAnalysis.setCodeType(codeView.getType());
        codeViewAnalysis.setCodeView(codeView.getView());
        codeViewAnalysis.setValid(true);

        return codeViewAnalysis;
    }
}
