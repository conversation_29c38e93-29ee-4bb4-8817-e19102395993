package com.sankuai.deepcode.analysis.server.crane;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/test")
public class Controller {

    @Autowired
    private RunItemByStatus runItemByStatus;

    @GetMapping("/test")
    public void getRunItemByStatus() throws Exception {
        runItemByStatus.runAsyncItemByStatus();
    }
}
