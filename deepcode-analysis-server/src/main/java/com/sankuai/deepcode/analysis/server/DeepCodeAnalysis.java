package com.sankuai.deepcode.analysis.server;

import com.sankuai.inf.octo.mns.util.ProcessInfoUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.scheduling.annotation.EnableAsync;


/**
 * <AUTHOR>
 */
@SpringBootApplication(exclude = DataSourceAutoConfiguration.class)
@ComponentScan("com.sankuai.deepcode")
@EnableAsync
public class DeepCodeAnalysis {
    private static final Logger log = LoggerFactory.getLogger(DeepCodeAnalysis.class);

    public static void main(String[] args) {
        if (ProcessInfoUtil.isMac()) {
            System.setProperty("spring.profiles.active", "local");
        }
        SpringApplication.run(DeepCodeAnalysis.class, args);
        log.info("服务启动成功！");
    }
}