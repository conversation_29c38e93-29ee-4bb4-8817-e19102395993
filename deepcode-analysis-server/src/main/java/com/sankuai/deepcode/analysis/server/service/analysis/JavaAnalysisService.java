package com.sankuai.deepcode.analysis.server.service.analysis;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.sankuai.deepcode.ast.model.base.CodeView;
import com.sankuai.deepcode.ast.model.java.*;
import com.sankuai.deepcode.dao.domain.*;
import com.sankuai.deepcode.dao.po.InvokeLinePO;
import com.sankuai.deepcode.dao.service.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;


@Service
@Slf4j
public class JavaAnalysisService {
    private static final Logger LOGGER = LoggerFactory.getLogger(JavaAnalysisService.class);

    @Autowired
    private CodeClassAnalysisService codeClassAnalysisService;
    @Autowired
    private CodeViewAnalysisService codeViewAnalysisService;
    @Autowired
    private CodeFieldAnalysisService codeFieldAnalysisService;
    @Autowired
    private CodeMethodAnalysisService codeMethodAnalysisService;
    @Autowired
    private CodeFileAnalysisService codeFileAnalysisService;
    @Autowired
    private MethodQuoteFieldService methodQuoteFieldService;
    @Autowired
    private ClassImportClassService classImportClassService;
    @Autowired
    private MethodInvokeMethodService methodInvokeMethodService;

    private int batchSize = 500;


    public JavaAnalysisRes insertInfo(JavaAnalysisRes javaAnalysisRes, CodeAnalysisItem codeAnalysisItem) {
        //class
        long start = System.currentTimeMillis();
        List<CodeClassAnalysis> codeClassAnalyses = new ArrayList<>();
        for (ClassNode classNode : javaAnalysisRes.getClassNodeMap().values()) {
            CodeClassAnalysis codeClassAnalysis = new CodeClassAnalysis();
            codeClassAnalysis.setItemId(codeAnalysisItem.getId());
            codeClassAnalysis.setModuleName(classNode.getModuleName());
            codeClassAnalysis.setClassVid(classNode.getVid());
            codeClassAnalysis.setFileVid(classNode.getFileVid());
            codeClassAnalysis.setClassPath(classNode.getClassPath());
            codeClassAnalysis.setClassName(classNode.getClassName());
            codeClassAnalysis.setInClassName(classNode.getInClassName());
            codeClassAnalysis.setClassType(classNode.getClassType());
            codeClassAnalysis.setGenerics(JSON.toJSONString(classNode.getGenerics()));
            codeClassAnalysis.setSuperClass(JSON.toJSONString(classNode.getSuperClass()));
            codeClassAnalysis.setInterfaces(JSON.toJSONString(classNode.getInterfaces()));
            codeClassAnalysis.setAccess(classNode.getAccess());
            codeClassAnalysis.setAnnotations(JSON.toJSONString(classNode.getAnnotations()));
            codeClassAnalysis.setChangeType(classNode.getChangeType());
            codeClassAnalysis.setChangeLines(JSON.toJSONString(classNode.getChangeLines()));
//            codeClassAnalysis.setCheckType(classNode.getCheckType());
//            codeClassAnalysis.setCheckLines(JSON.toJSONString(classNode.getCheckLines()));
            codeClassAnalysis.setStartLine(classNode.getStartLine());
            codeClassAnalysis.setEndLine(classNode.getEndLine());
            codeClassAnalysis.setCommentLines(JSON.toJSONString(classNode.getCommentLines()));
            codeClassAnalysis.setCommitCount(classNode.getCommitCount());
            codeClassAnalysis.setValid(true);
            codeClassAnalyses.add(codeClassAnalysis);
        }
        List<List<CodeClassAnalysis>> splitClass = Lists.partition(codeClassAnalyses, batchSize);
        if (CollectionUtils.isNotEmpty(splitClass)) {
            for (List<CodeClassAnalysis> classTagVOList : splitClass) {
                codeClassAnalysisService.batchInsert(classTagVOList);
            }
        }
        long end = System.currentTimeMillis();
        LOGGER.info("存储class耗时:" + (end - start) + "ms");
        start = System.currentTimeMillis();


        List<CodeViewAnalysis> codeViewAnalyses = new ArrayList<>();
        for (FileNode fileNode : javaAnalysisRes.getFileNodeMap().values()) {
            for (CodeView codeView : fileNode.getCodeViews()) {
                CodeViewAnalysis codeViewAnalysis = new CodeViewAnalysis();
                codeViewAnalysis.setItemId(codeAnalysisItem.getId());
                codeViewAnalysis.setSourceVid(fileNode.getVid());
                codeViewAnalysis.setCodeId(codeView.getId());
                codeViewAnalysis.setCodeLine(codeView.getLine());
                codeViewAnalysis.setCodeType(codeView.getType());
                codeViewAnalysis.setCodeView(codeView.getView());
                codeViewAnalysis.setValid(true);
                codeViewAnalyses.add(codeViewAnalysis);
            }
        }

        List<List<CodeViewAnalysis>> splitCodeView = Lists.partition(codeViewAnalyses, batchSize * 2);
        for (List<CodeViewAnalysis> codeViewList : splitCodeView) {
            codeViewAnalysisService.batchInsert(codeViewList);
        }

        end = System.currentTimeMillis();
        LOGGER.info("存储源码耗时:" + (end - start) + "ms");
        start = System.currentTimeMillis();

        //method
        List<CodeMethodAnalysis> codeMethodAnalyses = new ArrayList<>();
        for (MethodNode methodNode : javaAnalysisRes.getMethodNodeMap().values()) {
            CodeMethodAnalysis codeMethodAnalysis = new CodeMethodAnalysis();
            codeMethodAnalysis.setItemId(codeAnalysisItem.getId());
            codeMethodAnalysis.setSource(methodNode.getSource());
            codeMethodAnalysis.setModuleName(methodNode.getModuleName());
            codeMethodAnalysis.setMethodVid(methodNode.getVid());
            codeMethodAnalysis.setMethodName(methodNode.getMethodName());
            codeMethodAnalysis.setClassName(methodNode.getClassName());
            codeMethodAnalysis.setInClassName(methodNode.getInClassName());
            codeMethodAnalysis.setAccess(methodNode.getAccess());
            codeMethodAnalysis.setParams(JSON.toJSONString(methodNode.getParams()));
            codeMethodAnalysis.setReturnInfo(JSON.toJSONString(methodNode.getReturnInfo()));
            codeMethodAnalysis.setAnnotations(JSON.toJSONString(methodNode.getAnnotations()));
            codeMethodAnalysis.setExceptions(JSON.toJSONString(methodNode.getExceptions()));
            codeMethodAnalysis.setChangeType(methodNode.getChangeType());
            codeMethodAnalysis.setChangeLines(JSON.toJSONString(methodNode.getChangeLines()));
            codeMethodAnalysis.setCheckType(methodNode.getCheckType());
            codeMethodAnalysis.setCheckLines(JSON.toJSONString(methodNode.getCheckLines()));
            codeMethodAnalysis.setStartLine(methodNode.getStartLine());
            codeMethodAnalysis.setEndLine(methodNode.getEndLine());
            codeMethodAnalysis.setCommentLines(JSON.toJSONString(methodNode.getCommentLines()));
            codeMethodAnalysis.setComplexity(methodNode.getComplexity());
            codeMethodAnalysis.setValid(true);
            codeMethodAnalyses.add(codeMethodAnalysis);
        }
        List<List<CodeMethodAnalysis>> splitMethod = Lists.partition(codeMethodAnalyses, batchSize);
        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(splitMethod)) {
            for (List<CodeMethodAnalysis> methodAnalyses : splitMethod) {
                codeMethodAnalysisService.batchInsert(methodAnalyses);
            }
        }

        end = System.currentTimeMillis();
        LOGGER.info("存储method耗时:" + (end - start) + "ms");
        start = System.currentTimeMillis();

        //field
        List<CodeFieldAnalysis> codeFieldAnalyses = new ArrayList<>();
        for (FieldNode fieldNode : javaAnalysisRes.getFieldNodeMap().values()) {
            CodeFieldAnalysis codeFieldAnalysis = new CodeFieldAnalysis();
            codeFieldAnalysis.setItemId(codeAnalysisItem.getId());
            codeFieldAnalysis.setSource(fieldNode.getSource());
            codeFieldAnalysis.setModuleName(fieldNode.getModuleName());
            codeFieldAnalysis.setFieldVid(fieldNode.getVid());
            codeFieldAnalysis.setFieldName(fieldNode.getFieldName());
            codeFieldAnalysis.setClassName(fieldNode.getClassName());
            codeFieldAnalysis.setInClassName(fieldNode.getInClassName());
            codeFieldAnalysis.setAccess(fieldNode.getAccess());
            codeFieldAnalysis.setSignatures(JSON.toJSONString(fieldNode.getSignatures()));
            codeFieldAnalysis.setFieldType(fieldNode.getFieldType());
            if (null != fieldNode.getValue()) {
                codeFieldAnalysis.setFieldValue(fieldNode.getValue().toString());
            } else {
                codeFieldAnalysis.setFieldValue(null);
            }
            codeFieldAnalysis.setAnnotations(JSON.toJSONString(fieldNode.getAnnotations()));
            codeFieldAnalysis.setChangeType(fieldNode.getChangeType());
            codeFieldAnalysis.setChangeLines(JSON.toJSONString(fieldNode.getChangeLines()));
            codeFieldAnalysis.setCheckType(fieldNode.getCheckType());
            codeFieldAnalysis.setCheckLines(JSON.toJSONString(fieldNode.getCheckLines()));
            codeFieldAnalysis.setStartLine(fieldNode.getStartLine());
            codeFieldAnalysis.setEndLine(fieldNode.getEndLine());
            codeFieldAnalysis.setCommentLines(JSON.toJSONString(fieldNode.getCommentLines()));

            codeFieldAnalysis.setValid(true);
            codeFieldAnalyses.add(codeFieldAnalysis);
        }
        List<List<CodeFieldAnalysis>> splitField = Lists.partition(codeFieldAnalyses, batchSize);
        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(splitField)) {
            for (List<CodeFieldAnalysis> fieldAnalyses : splitField) {
                codeFieldAnalysisService.batchInsert(fieldAnalyses);
            }
        }
        end = System.currentTimeMillis();
        LOGGER.info("存储field耗时:" + (end - start) + "ms");
        start = System.currentTimeMillis();


        //file
        List<CodeFileAnalysis> codeFileAnalyses = new ArrayList<>();
        for (FileNode fileNode : javaAnalysisRes.getFileNodeMap().values()) {
            CodeFileAnalysis codeFileAnalysis = new CodeFileAnalysis();
            codeFileAnalysis.setItemId(codeAnalysisItem.getId());
            codeFileAnalysis.setFileVid(fileNode.getVid());
            codeFileAnalysis.setModuleName(fileNode.getModuleName());
            codeFileAnalysis.setFileName(fileNode.getFileName());
            codeFileAnalysis.setFilePath(fileNode.getPath());
            codeFileAnalysis.setFileType(fileNode.getFileType());
            codeFileAnalysis.setChangeType(fileNode.getChangeType());
            codeFileAnalysis.setChangeLines(JSON.toJSONString(fileNode.getChangeLines()));
            codeFileAnalysis.setCheckType(fileNode.getCheckType());
            codeFileAnalysis.setCheckLines(JSON.toJSONString(fileNode.getCheckLines()));
            codeFileAnalysis.setStartLine(fileNode.getStartLine());
            codeFileAnalysis.setEndLine(fileNode.getEndLine());
            codeFileAnalysis.setCommentLines(JSON.toJSONString(fileNode.getCommentLines()));
            codeFileAnalysis.setCommitCount(fileNode.getCommitCount());
            codeFileAnalysis.setValid(true);
            codeFileAnalyses.add(codeFileAnalysis);
        }
        List<List<CodeFileAnalysis>> splitFile = Lists.partition(codeFileAnalyses, batchSize);
        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(splitFile)) {
            for (List<CodeFileAnalysis> fieldAnalyses : splitFile) {
                codeFileAnalysisService.batchInsert(fieldAnalyses);
            }
        }
        end = System.currentTimeMillis();
        LOGGER.info("存储file耗时:" + (end - start) + "ms");
        start = System.currentTimeMillis();

        //classImport
        List<ClassImportClass> classImportClasses = new ArrayList<>();
        for (Map.Entry<String, Set<String>> entry : javaAnalysisRes.getClassImportClass().entrySet()) {
            for (String target : entry.getValue()) {
                ClassImportClass classImportClass = new ClassImportClass();
                classImportClass.setItemId(codeAnalysisItem.getId());
                classImportClass.setSource(entry.getKey());
                classImportClass.setTarget(target);
                classImportClass.setValid(true);
                classImportClasses.add(classImportClass);
            }
        }
        List<List<ClassImportClass>> splitClassImport = Lists.partition(classImportClasses, batchSize);
        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(splitClassImport)) {
            for (List<ClassImportClass> classImportAnalyses : splitClassImport) {
                classImportClassService.batchInsert(classImportAnalyses);
            }
        }
        end = System.currentTimeMillis();
        LOGGER.info("存储classImport耗时:" + (end - start) + "ms");
        start = System.currentTimeMillis();


        //methodInvoke
        List<MethodInvokeMethod> methodInvokeMethods = new ArrayList<>();
        for (Map.Entry<String, List<MethodInvokeMethodEdge>> entry : javaAnalysisRes.getMethodInvokeMethod().entrySet()) {
            for (MethodInvokeMethodEdge methodEdge : entry.getValue()) {
                MethodInvokeMethod methodInvokeMethod = new MethodInvokeMethod();
                methodInvokeMethod.setItemId(codeAnalysisItem.getId());
                methodInvokeMethod.setSource(entry.getKey());
                methodInvokeMethod.setSourceType(methodEdge.getSourceType());
                methodInvokeMethod.setTarget(methodEdge.getTarget());
                methodInvokeMethod.setTargetType(methodEdge.getTargetType());
                methodInvokeMethod.setInvokeParams(methodEdge.getInvokeParams().stream().map(i -> new InvokeLinePO().setInvokeLine(i.getInvokeLine())).collect(Collectors.toList()));
                methodInvokeMethod.setValid(true);
                methodInvokeMethods.add(methodInvokeMethod);
            }
        }
        List<List<MethodInvokeMethod>> splitMethodInvoke = Lists.partition(methodInvokeMethods, batchSize);
        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(splitMethodInvoke)) {
            for (List<MethodInvokeMethod> methodInvokeAnalyses : splitMethodInvoke) {
                methodInvokeMethodService.batchInsert(methodInvokeAnalyses);
            }
        }
        end = System.currentTimeMillis();
        LOGGER.info("存储methodInvoke耗时:" + (end - start) + "ms");


        //methodQuote
        List<MethodQuoteField> methodQuoteFields = new ArrayList<>();
        for (Map.Entry<String, List<MethodQuoteFieldEdge>> entry : javaAnalysisRes.getMethodQuoteField().entrySet()) {
            for (MethodQuoteFieldEdge methodQuote : entry.getValue()) {
                MethodQuoteField methodQuoteField = new MethodQuoteField();
                methodQuoteField.setItemId(codeAnalysisItem.getId());
                methodQuoteField.setSource(entry.getKey());
                methodQuoteField.setSourceType(methodQuote.getSourceType());
                methodQuoteField.setTarget(methodQuote.getTarget());
                methodQuoteField.setTargetType(methodQuote.getTargetType());
                methodQuoteField.setQuoteLines(JSON.toJSONString(methodQuote.getQuoteLines()));
                methodQuoteField.setValid(true);
                methodQuoteFields.add(methodQuoteField);
            }
        }
        List<List<MethodQuoteField>> splitMethodQuote = Lists.partition(methodQuoteFields, batchSize);
        if (CollectionUtils.isNotEmpty(splitMethodQuote)) {
            for (List<MethodQuoteField> methodQuoteAnalyses : splitMethodQuote) {
                methodQuoteFieldService.batchInsert(methodQuoteAnalyses);
            }
        }
        end = System.currentTimeMillis();
        LOGGER.info("存储methodQuote耗时:" + (end - start) + "ms");

        return javaAnalysisRes;
    }
}
