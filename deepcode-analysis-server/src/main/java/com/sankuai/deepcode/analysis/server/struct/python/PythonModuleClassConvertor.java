package com.sankuai.deepcode.analysis.server.struct.python;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.deepcode.ast.model.python3.PythonClassNode;
import com.sankuai.deepcode.ast.model.python3.PythonModuleNode;
import com.sankuai.deepcode.ast.util.Md5Util;
import com.sankuai.deepcode.commons.JacksonUtils;
import com.sankuai.deepcode.dao.domain.CodeClassAnalysis;
import org.apache.commons.collections4.CollectionUtils;
import org.mapstruct.*;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Mapper(
        componentModel = "spring",
        imports = {
                JSON.class,
                Md5Util.class
        }
)
public interface PythonModuleClassConvertor {
    @Mappings({
            @Mapping(target = "itemId", source = "itemId"),
            // 类 vid
            @Mapping(target = "classVid", expression = "java(pythonClassNode.getVid())"),
            @Mapping(target = "fileVid", expression = "java(pythonClassNode.getFileVid())"),

            // 文件路径
            @Mapping(target = "classPath", source = "pythonClassNode.fileName"),
            // 类所属的模块名
            @Mapping(target = "moduleName", source = "pythonClassNode.moduleName"),
            // 全类路径
            @Mapping(target = "className", source = "pythonClassNode.modulePath"),

            // Python 所有类暂且默认是 class
            @Mapping(target = "classType", constant = "class"),
            // 泛型暂时不处理
            @Mapping(target = "generics", constant = "[]"),
            // 所有Python类的访问权限都默认是 public
            @Mapping(target = "access", constant = "public"),

            @Mapping(target = "startLine", source = "pythonClassNode.start.line"),
            @Mapping(target = "endLine", source = "pythonClassNode.end.line"),

            @Mapping(target = "superClass", expression = "java(handleSuperClass(pythonClassNode.getSuperClasses()))"),


            @Mapping(target = "changeLines", expression = "java(JSON.toJSONString(pythonClassNode.getChangeLines()))"),
            @Mapping(target = "changeType", source = "pythonClassNode.changeType.code"),

            @Mapping(target = "valid", constant = "true"),
    })
    @BeanMapping(ignoreByDefault = true)
    CodeClassAnalysis convertClassNode(Long itemId, PythonClassNode pythonClassNode);

    /**
     * 处理Python中 class 的超类，转换成 Json 字符串
     * {
     * "PythonSuperClasses": [ ],
     * // 其他可能存在Java相关父类的属性
     * }
     */
    default String handleSuperClass(List<PythonClassNode.PythonSuperClass> superClasses) {
        Map<String, List<PythonClassNode.PythonSuperClass>> superClassMap = Maps.newHashMap();

        if (CollectionUtils.isNotEmpty(superClasses)) {
            superClassMap.put("PythonSuperClasses", superClasses);
        }

        return JacksonUtils.toJsonString(superClassMap);
    }

    @Mappings({
            @Mapping(target = "itemId", source = "itemId"),
            @Mapping(target = "classVid", expression = "java(pythonModuleNode.getVid())"),
            @Mapping(target = "fileVid", expression = "java(Md5Util.stringToMd5(pythonModuleNode.getFileName()))"),

            // 文件路径
            @Mapping(target = "classPath", source = "pythonModuleNode.fileName"),
            // 类所属的模块名
            @Mapping(target = "moduleName", source = "pythonModuleNode.moduleName"),
            // 全类路径
            @Mapping(target = "className", source = "pythonModuleNode.modulePath"),
            @Mapping(target = "classType", constant = "pythonModule"),
            @Mapping(target = "access", constant = "public"),

            // Python 所有类暂且默认是
            @Mapping(target = "changeLines", expression = "java(JSON.toJSONString(pythonModuleNode.getChangeLines()))"),
            @Mapping(target = "changeType", source = "pythonModuleNode.changeType.code"),

            @Mapping(target = "valid", constant = "true"),

    })
    @Named("convertModuleNode")
    @BeanMapping(ignoreByDefault = true)
    CodeClassAnalysis convertModuleNode(Long itemId, PythonModuleNode pythonModuleNode);

    default List<CodeClassAnalysis> convertModuleNodeList(Long itemId, List<PythonModuleNode> pythonModuleNodeList) {
        if (CollectionUtils.isEmpty(pythonModuleNodeList)) {
            return Lists.newArrayList();
        }
        return pythonModuleNodeList.stream()
                .map(pythonModuleNode -> convertModuleNode(itemId, pythonModuleNode))
                .collect(Collectors.toList());
    }

    ;
}
