package com.sankuai.deepcode.analysis.server.struct.python;

import com.alibaba.fastjson.JSON;
import com.sankuai.deepcode.ast.model.python3.PythonClassNode;
import com.sankuai.deepcode.ast.model.python3.PythonFunctionNode;
import com.sankuai.deepcode.ast.model.python3.PythonMethodNode;
import com.sankuai.deepcode.ast.model.python3.PythonModuleNode;
import com.sankuai.deepcode.dao.domain.CodeMethodAnalysis;
import org.mapstruct.BeanMapping;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;

/**
 * <AUTHOR>
 */
@Mapper(
        componentModel = "spring",
        imports = {
                JSON.class
        }
)
public interface PythonMethodConvertor {
    @Mappings({
            @Mapping(target = "itemId", source = "itemId"),

            @Mapping(target = "moduleName", source = "moduleNode.modulePath"),
            @Mapping(target = "methodName", source = "pythonFunctionNode.name"),
            // 模块下的方法，类名也设置成模块路径
            @Mapping(target = "className", source = "pythonFunctionNode.moduleName"),

            @Mapping(target = "startLine", source = "pythonFunctionNode.start.line"),
            @Mapping(target = "endLine", source = "pythonFunctionNode.end.line"),

            @Mapping(target = "access", constant = "public"),

            @Mapping(target = "methodVid", expression = "java(pythonFunctionNode.getVid())"),

            @Mapping(target = "changeLines", expression = "java(JSON.toJSONString(pythonFunctionNode.getChangeLines()))"),
            @Mapping(target = "changeType", source = "pythonFunctionNode.changeType.code"),

            @Mapping(target = "complexity", source = "pythonFunctionNode.complexity"),

            @Mapping(target = "valid", constant = "true"),
    })
    @BeanMapping(ignoreByDefault = true)
    CodeMethodAnalysis convertFunctionNode(Long itemId, PythonFunctionNode pythonFunctionNode, PythonModuleNode moduleNode);

    default CodeMethodAnalysis convertMethodNode(Long itemId, PythonMethodNode pythonMethodNode, PythonModuleNode moduleNode, PythonClassNode classNode) {
        CodeMethodAnalysis codeMethodAnalysis = convertFunctionNode(itemId, pythonMethodNode, moduleNode);
        codeMethodAnalysis.setClassName(pythonMethodNode.getModuleName());
        return codeMethodAnalysis;
    }

}
