package com.sankuai.deepcode.analysis.server.crane;


import com.cip.crane.client.spring.annotation.Crane;
import com.cip.crane.client.spring.annotation.CraneConfiguration;
import com.sankuai.deepcode.analysis.server.service.TaskRunService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
@CraneConfiguration
public class RunItemByStatus {
    private static final Logger LOGGER = LoggerFactory.getLogger(RunItemByStatus.class);

    @Autowired
    private TaskRunService taskRunService;


    @<PERSON>("runItemByStatus")
    public void runItemByStatus() throws Exception {
        taskRunService.runItemByStatus();
    }


    @Crane("runAsyncItemByStatus")
    public void runAsyncItemByStatus() throws Exception {
        taskRunService.runAsyncItemByStatus();
    }
}
