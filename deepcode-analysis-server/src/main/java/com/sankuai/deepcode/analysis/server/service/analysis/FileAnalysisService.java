package com.sankuai.deepcode.analysis.server.service.analysis;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.sankuai.deepcode.ast.model.base.CodeView;
import com.sankuai.deepcode.ast.model.base.GitDiffInfo;
import com.sankuai.deepcode.ast.util.Md5Util;
import com.sankuai.deepcode.dao.domain.CodeAnalysisItem;
import com.sankuai.deepcode.dao.domain.CodeFileAnalysis;
import com.sankuai.deepcode.dao.domain.CodeViewAnalysis;
import com.sankuai.deepcode.dao.service.CodeFileAnalysisService;
import com.sankuai.deepcode.dao.service.CodeViewAnalysisService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Service
public class FileAnalysisService {

    private int batchSize = 500;
    @Autowired
    private CodeFileAnalysisService codeFileAnalysisService;
    @Autowired
    private CodeViewAnalysisService codeViewAnalysisService;

    public void insertInfo(Map<String, GitDiffInfo> gitDiffInfoMap, CodeAnalysisItem codeAnalysisItem) {
        List<CodeFileAnalysis> codeFileAnalyses = new ArrayList<>();
        List<CodeViewAnalysis> codeViewAnalyses = new ArrayList<>();
        for (GitDiffInfo gitDiffInfo : gitDiffInfoMap.values()) {
            CodeFileAnalysis codeFileAnalysis = new CodeFileAnalysis();
            codeFileAnalysis.setItemId(codeAnalysisItem.getId());
            codeFileAnalysis.setModuleName(gitDiffInfo.getModuleName());
            codeFileAnalysis.setFileName(gitDiffInfo.getFileName());
            codeFileAnalysis.setFilePath(gitDiffInfo.getPath());
            codeFileAnalysis.setFileType(gitDiffInfo.getFileType());
            codeFileAnalysis.setChangeType(gitDiffInfo.getChangeType());
            codeFileAnalysis.setChangeLines(JSON.toJSONString(gitDiffInfo.getChangeLines()));
//            codeFileAnalysis.setCheckType(gitDiffInfo.getCheckType());
//            codeFileAnalysis.setCheckLines(JSON.toJSONString(gitDiffInfo.getCheckLines()));
            codeFileAnalysis.setStartLine(1);
            codeFileAnalysis.setEndLine(gitDiffInfo.getCodeViews().size() - 1);
            codeFileAnalysis.setCommentLines(JSON.toJSONString(new ArrayList<>()));
            codeFileAnalysis.setCommitCount(gitDiffInfo.getCommitCount());
            codeFileAnalysis.setValid(true);
            codeFileAnalysis.setFileVid(Md5Util.filePathToMd5(gitDiffInfo.getPath()));
            codeFileAnalyses.add(codeFileAnalysis);

            for (CodeView codeView : gitDiffInfo.getCodeViews()) {
                CodeViewAnalysis codeViewAnalysis = new CodeViewAnalysis();
                codeViewAnalysis.setItemId(codeAnalysisItem.getId());
                codeViewAnalysis.setSourceVid(codeFileAnalysis.getFileVid());
                codeViewAnalysis.setCodeId(codeView.getId());
                codeViewAnalysis.setCodeLine(codeView.getLine());
                codeViewAnalysis.setCodeType(codeView.getType());
                codeViewAnalysis.setCodeView(codeView.getView());
                codeViewAnalysis.setValid(true);
                codeViewAnalyses.add(codeViewAnalysis);
            }
        }

        List<List<CodeFileAnalysis>> splitFile = Lists.partition(codeFileAnalyses, batchSize);
        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(splitFile)) {
            for (List<CodeFileAnalysis> fieldAnalyses : splitFile) {
                codeFileAnalysisService.batchInsert(fieldAnalyses);
            }
        }

        List<List<CodeViewAnalysis>> splitCodeView = Lists.partition(codeViewAnalyses, batchSize * 2);
        for (List<CodeViewAnalysis> codeViewList : splitCodeView) {
            codeViewAnalysisService.batchInsert(codeViewList);
        }
    }
}