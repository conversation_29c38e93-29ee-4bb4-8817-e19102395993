package com.sankuai.deepcode.analysis.server.service;

import com.alibaba.fastjson.JSON;
import com.google.gson.JsonArray;
import com.google.gson.JsonObject;
import com.sankuai.deepcode.ai.embedding.model.CoStar;
import com.sankuai.deepcode.ai.embedding.model.EmbeddingsRes;
import com.sankuai.deepcode.ai.embedding.service.EmbeddingService;
import com.sankuai.deepcode.ai.enums.ChatEnum;
import com.sankuai.deepcode.ai.enums.EmbeddingEnum;
import com.sankuai.deepcode.ai.llm.model.chat.ChatMessage;
import com.sankuai.deepcode.ai.llm.model.chat.ChatMsgRes;
import com.sankuai.deepcode.ai.llm.service.OneApiService;
import com.sankuai.deepcode.dao.domain.CodeAnalysisItem;
import com.sankuai.deepcode.dao.domain.CodeFileAnalysis;
import com.sankuai.deepcode.dao.domain.CodeViewAnalysis;
import com.sankuai.deepcode.dao.enums.ItemStatusEnum;
import com.sankuai.deepcode.dao.enums.ItemStepEnum;
import com.sankuai.deepcode.dao.milvus.KnowledgeInit;
import com.sankuai.deepcode.dao.milvus.MilvusService;
import com.sankuai.deepcode.dao.model.AsyncTaskExtern;
import com.sankuai.deepcode.dao.service.CodeFileAnalysisService;
import com.sankuai.deepcode.dao.service.CodeViewAnalysisService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.AsyncResult;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.Future;

@Service
public class KnowledgeTaskService {

    @Autowired
    private CodeViewAnalysisService codeViewAnalysisService;
    @Autowired
    private CodeFileAnalysisService codeFileAnalysisService;
    @Autowired
    private EmbeddingService embeddingService;
    @Autowired
    private MilvusService milvusService;
    @Autowired
    private OneApiService oneApiService;
    @Autowired
    private KnowledgeInit knowledgeInit;
    @Autowired
    private ItemSatusService itemSatusService;

    private static final Logger LOGGER = LoggerFactory.getLogger(KnowledgeTaskService.class);

    public Future<Boolean> knowledgeByItem(CodeAnalysisItem codeAnalysisItem) {
        long start = System.currentTimeMillis();
        try {
            itemSatusService.updateItemAsyncStatus(codeAnalysisItem, ItemStatusEnum.PROCESSING);
            itemSatusService.insertDetail(codeAnalysisItem.getId(), ItemStepEnum.KNOWLEDGE, "开始源文本语义向量化", "");
            List<CodeFileAnalysis> codeFileAnalyses = codeFileAnalysisService.getByItemId(codeAnalysisItem.getId());
            if (milvusService.isExist("item_desc_" + codeAnalysisItem.getId())) {
                milvusService.drop("item_desc_" + codeAnalysisItem.getId());
            }
            knowledgeInit.createCollection(codeAnalysisItem.getId());

            AsyncTaskExtern knowledgeExtern = new AsyncTaskExtern();
            knowledgeExtern.setStatus(ItemStatusEnum.PROCESSING.getCode());
            knowledgeExtern.setModel(EmbeddingEnum.JINA_V3.getName());
            knowledgeExtern.setTotalNum(codeFileAnalyses.size());
            knowledgeExtern.setRunNum(0);
            knowledgeExtern.setPercent(0);

            for (CodeFileAnalysis codeFileAnalysis : codeFileAnalyses) {
                List<CodeViewAnalysis> codeViewAnalyses = codeViewAnalysisService.getAllContent(codeAnalysisItem.getId(), codeFileAnalysis.getFileVid());
                if (codeViewAnalyses.size() == 1) {
                    continue;
                }
                List<CodeViewAnalysis> allContent = new ArrayList<>();
                for (CodeViewAnalysis codeViewAnalysis : codeViewAnalyses) {
                    if (StringUtils.isNotEmpty(codeViewAnalysis.getCodeView())) {
                        allContent.add(codeViewAnalysis);
                    }
                }


                knowledgeExtern.setRunNum(knowledgeExtern.getRunNum() + 1);
                StringBuilder descView = new StringBuilder();
                for (CodeViewAnalysis codeViewAnalysis : allContent) {
                    descView.append(codeViewAnalysis.getCodeView()).append("\n");
                }

                List<ChatMessage> chatMessages = new ArrayList<>();
                ChatMessage system = new ChatMessage();
                system.setRole("system");
                CoStar coStar = new CoStar();
                coStar.setContext("你是一个代码中文描述生成助手");
                coStar.setObjective("目标对源码做中文总结，其中+开始代表新增行，-开始代表删除行");
                coStar.setStyle("风格上主要针对代码实现的功能、用途做描述，特别是对所有方法做总结");
                coStar.setTone("语气尽量客观严谨");
                coStar.setAudience("受众目标是研发、测试等软件工程专业人员");
                coStar.setReponse("回复使用中文，方法名保持英文但是要携带对应中文的翻译，接下来再带上总结内容");
                system.setContent(CoStar.initPrompt(coStar));
                ChatMessage user = new ChatMessage();
                user.setRole("user");
                user.setContent(descView.toString() + "\n#为以上代码生成中文描述");
                chatMessages.add(system);
                chatMessages.add(user);

                ChatMsgRes chatMsgRes = oneApiService.completions(chatMessages, ChatEnum.GPT_4_1_MINI.getName());
                if (chatMsgRes != null && CollectionUtils.isNotEmpty(chatMsgRes.getChoices())) {
                    String desc = chatMsgRes.getChoices().get(0).getMessage().getContent();
                    desc = "文件:" + codeFileAnalysis.getFilePath()
                            + "\n"
                            + desc;
                    EmbeddingsRes embeddingsRes = embeddingService.embeddings(desc, EmbeddingEnum.JINA_V3.getName());
                    List<JsonObject> data = new ArrayList<>();
                    JsonObject jsonObject = new JsonObject();
                    jsonObject.addProperty("file_vid", codeFileAnalysis.getFileVid());
                    JsonArray descArray = new JsonArray();
                    for (Float value : embeddingsRes.getData().get(0).getEmbedding()) {
                        descArray.add(value);
                    }
                    jsonObject.add("embedding", descArray);
                    jsonObject.addProperty("desc", desc);
                    data.add(jsonObject);
                    milvusService.insert("item_desc_" + codeAnalysisItem.getId(), "partition0", data);
                }

                if (knowledgeExtern.getRunNum() % 20 == 0) {
                    double percent = Math.round((double) knowledgeExtern.getRunNum() / knowledgeExtern.getTotalNum() * 10000) / 100.0;
                    knowledgeExtern.setPercent(percent);
                    itemSatusService.updateDetailAsyncExtern(codeAnalysisItem.getId(), ItemStepEnum.KNOWLEDGE, JSON.toJSONString(knowledgeExtern));
                    LOGGER.info("analysisByItem item:{} knowledge percent:{}", codeAnalysisItem.getId(), percent);
                }
            }
            knowledgeExtern.setStatus(ItemStatusEnum.SUCCESS.getCode());
            knowledgeExtern.setPercent(100.0);
            long cost = System.currentTimeMillis() - start;
            knowledgeExtern.setCostTime(cost);
            itemSatusService.updateDetailSucess(codeAnalysisItem.getId(), ItemStepEnum.KNOWLEDGE, "源文本语义向量化完成", JSON.toJSONString(knowledgeExtern));
            itemSatusService.updateItemAsyncSucess(codeAnalysisItem, cost);
        } catch (Exception e) {
            LOGGER.error("analysisByItemId error e:", e);
            itemSatusService.updateDetailFail(codeAnalysisItem.getId(), ItemStepEnum.KNOWLEDGE, "源文本语义向量化异常");
            itemSatusService.updateItemAsyncFail(codeAnalysisItem, System.currentTimeMillis() - start);
        } finally {
            return new AsyncResult<Boolean>(true);
        }
    }

}
