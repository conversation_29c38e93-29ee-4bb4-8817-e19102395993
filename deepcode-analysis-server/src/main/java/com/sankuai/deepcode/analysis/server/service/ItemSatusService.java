package com.sankuai.deepcode.analysis.server.service;

import com.sankuai.deepcode.dao.domain.CodeAnalysisItem;
import com.sankuai.deepcode.dao.domain.CodeAnalysisItemDetail;
import com.sankuai.deepcode.dao.enums.ItemStatusEnum;
import com.sankuai.deepcode.dao.enums.ItemStepEnum;
import com.sankuai.deepcode.dao.service.CodeAnalysisItemDetailService;
import com.sankuai.deepcode.dao.service.CodeAnalysisItemService;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;

@Service
public class ItemSatusService {

    @Autowired
    private CodeAnalysisItemService codeAnalysisItemService;
    @Autowired
    private CodeAnalysisItemDetailService codeAnalysisItemDetailService;

    public void initDetail(long itemId) {
        List<CodeAnalysisItemDetail> codeAnalysisItemDetails = codeAnalysisItemDetailService.getByItemId(itemId);
        if (CollectionUtils.isNotEmpty(codeAnalysisItemDetails)) {
            for (CodeAnalysisItemDetail codeAnalysisItemDetail : codeAnalysisItemDetails) {
                codeAnalysisItemDetailService.removeById(codeAnalysisItemDetail.getId());
            }
        }
    }


    public void insertDetail(long itemId, ItemStepEnum step, String contentStart, String contentEnd) {
        LocalDateTime dateTime = LocalDateTime.now();
        CodeAnalysisItemDetail codeAnalysisItemDetail = new CodeAnalysisItemDetail();
        codeAnalysisItemDetail.setItemId(itemId);
        codeAnalysisItemDetail.setType(step.getTypeName());
        codeAnalysisItemDetail.setContentStart(contentStart);
        codeAnalysisItemDetail.setContentEnd(contentEnd);
        codeAnalysisItemDetail.setStatus(ItemStatusEnum.PROCESSING.getCode());
        codeAnalysisItemDetail.setExtern("");
        if (step == ItemStepEnum.EMBEDDING || step == ItemStepEnum.KNOWLEDGE || step == ItemStepEnum.AI_REVIEW) {
            codeAnalysisItemDetail.setAsync(true);
        }
        codeAnalysisItemDetail.setCtime(dateTime);
        codeAnalysisItemDetail.setUtime(dateTime);
        codeAnalysisItemDetail.setValid(true);
        codeAnalysisItemDetailService.save(codeAnalysisItemDetail);
    }

    public void updateDetailSucess(long itemId, ItemStepEnum step, String contentEnd, String extern) {
        List<CodeAnalysisItemDetail> codeAnalysisItemDetails = codeAnalysisItemDetailService.getByItemIdAndType(itemId, step.getTypeName());
        for (CodeAnalysisItemDetail codeAnalysisItemDetail : codeAnalysisItemDetails) {
            codeAnalysisItemDetail.setItemId(itemId);
            codeAnalysisItemDetail.setType(step.getTypeName());
            codeAnalysisItemDetail.setContentEnd(contentEnd);
            codeAnalysisItemDetail.setStatus(ItemStatusEnum.SUCCESS.getCode());
            codeAnalysisItemDetail.setExtern(extern);
            codeAnalysisItemDetail.setUtime(LocalDateTime.now());
            codeAnalysisItemDetailService.updateById(codeAnalysisItemDetail);
        }
    }

    public void updateDetailFail(long itemId, ItemStepEnum step, String contentEnd) {
        updateDetail(itemId, step, contentEnd, ItemStatusEnum.FAILED);
    }

    public void updateDetail(long itemId, ItemStepEnum step, String contentEnd, ItemStatusEnum status) {
        List<CodeAnalysisItemDetail> codeAnalysisItemDetails = codeAnalysisItemDetailService.getByItemIdAndType(itemId, step.getTypeName());
        for (CodeAnalysisItemDetail codeAnalysisItemDetail : codeAnalysisItemDetails) {
            codeAnalysisItemDetail.setItemId(itemId);
            codeAnalysisItemDetail.setType(step.getTypeName());
            codeAnalysisItemDetail.setContentEnd(contentEnd);
            codeAnalysisItemDetail.setStatus(status.getCode());
            codeAnalysisItemDetail.setUtime(LocalDateTime.now());
            codeAnalysisItemDetailService.updateById(codeAnalysisItemDetail);
        }
    }


    public void updateItemSucess(CodeAnalysisItem codeAnalysisItem, long cost) {
        codeAnalysisItem.setStatus(ItemStatusEnum.SUCCESS.getCode());
        codeAnalysisItem.setAnalysisCost(cost);
        codeAnalysisItem.setUtime(LocalDateTime.now());
        codeAnalysisItemService.updateById(codeAnalysisItem);
    }

    public void updateItemAsyncSucess(CodeAnalysisItem codeAnalysisItem, long cost) {
        List<CodeAnalysisItemDetail> codeAnalysisItemDetails = codeAnalysisItemDetailService.getAllAsyncByItemId(codeAnalysisItem.getId());
        boolean isSuccess = true;
        for (CodeAnalysisItemDetail codeAnalysisItemDetail : codeAnalysisItemDetails) {
            if (codeAnalysisItemDetail.getStatus() != ItemStatusEnum.SUCCESS.getCode()) {
                isSuccess = false;
                break;
            }
        }
        codeAnalysisItem.setAnalysisCost(cost + codeAnalysisItem.getAnalysisCost());
        codeAnalysisItem.setUtime(LocalDateTime.now());
        if (isSuccess) {
            codeAnalysisItem.setAsyncStatus(ItemStatusEnum.SUCCESS.getCode());
        }
        codeAnalysisItemService.updateById(codeAnalysisItem);
    }

    public void updateItemFail(CodeAnalysisItem codeAnalysisItem, long cost) {
        codeAnalysisItem.setStatus(ItemStatusEnum.FAILED.getCode());
        codeAnalysisItem.setAnalysisCost(cost);
        codeAnalysisItem.setUtime(LocalDateTime.now());
        codeAnalysisItemService.updateById(codeAnalysisItem);
    }

    public void updateItemAsyncFail(CodeAnalysisItem codeAnalysisItem, long cost) {
        codeAnalysisItem.setAnalysisCost(codeAnalysisItem.getAnalysisCost() + cost);
        codeAnalysisItem.setAsyncStatus(ItemStatusEnum.FAILED.getCode());
        codeAnalysisItem.setUtime(LocalDateTime.now());
        codeAnalysisItemService.updateById(codeAnalysisItem);
    }


    public void updateDetailAsyncExtern(long itemId, ItemStepEnum step, String extern) {
        List<CodeAnalysisItemDetail> codeAnalysisItemDetails = codeAnalysisItemDetailService.getByItemIdAndType(itemId, step.getTypeName());
        for (CodeAnalysisItemDetail codeAnalysisItemDetail : codeAnalysisItemDetails) {
            codeAnalysisItemDetail.setExtern(extern);
            codeAnalysisItemDetail.setUtime(LocalDateTime.now());
            codeAnalysisItemDetailService.updateById(codeAnalysisItemDetail);
        }
    }


    public void updateItemTimeOut(long itemId, long cost) {
        CodeAnalysisItem codeAnalysisItem = codeAnalysisItemService.getById(itemId);
        codeAnalysisItem.setStatus(ItemStatusEnum.FAILED.getCode());
        codeAnalysisItem.setAnalysisCost(cost);
        codeAnalysisItem.setUtime(LocalDateTime.now());
        codeAnalysisItemService.updateById(codeAnalysisItem);
        updateDetailTimeOut(codeAnalysisItem.getId());
    }

    public void updateItemStatus(CodeAnalysisItem codeAnalysisItem, ItemStatusEnum itemStatusEnum) {
        codeAnalysisItem.setStatus(itemStatusEnum.getCode());
        codeAnalysisItemService.updateById(codeAnalysisItem);
    }

    public void updateItemAsyncStatus(CodeAnalysisItem codeAnalysisItem, ItemStatusEnum itemStatusEnum) {
        codeAnalysisItem.setAsyncStatus(itemStatusEnum.getCode());
        codeAnalysisItemService.updateById(codeAnalysisItem);
    }


    public void updateDetailTimeOut(long itemId) {
        CodeAnalysisItemDetail codeAnalysisItemDetail = codeAnalysisItemDetailService.getTimeOutDetailByItemId(itemId);
        codeAnalysisItemDetail.setStatus(ItemStatusEnum.FAILED.getCode());
        codeAnalysisItemDetail.setContentEnd("任务超时");
        codeAnalysisItemDetail.setUtime(LocalDateTime.now());
        codeAnalysisItemDetailService.updateById(codeAnalysisItemDetail);
    }

}
