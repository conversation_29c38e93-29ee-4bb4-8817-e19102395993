package com.sankuai.deepcode.analysis.server.struct.python;

import com.google.common.collect.Lists;
import com.sankuai.deepcode.ast.model.python3.PythonImportNode;
import com.sankuai.deepcode.ast.util.Md5Util;
import com.sankuai.deepcode.dao.domain.ClassImportClass;
import org.mapstruct.Mapper;

import java.util.List;
import java.util.stream.Collectors;

@Mapper(
        componentModel = "spring"
)
public abstract class PythonImportConvertor {
    public List<ClassImportClass> convertImportNode(Long itemId, PythonImportNode pythonImportNode) {
        // 引用其他模块的 模块
        String sourceModule = pythonImportNode.getCurrentModulePath();

        // 被引用的模模块 列表
        List<String> targetModuleList = Lists.newArrayList();

        switch (pythonImportNode.getImportType()) {
            case IMPORT:
                targetModuleList = pythonImportNode.getImportedItems();
                break;
            case FROM:
                targetModuleList = pythonImportNode.getImportedItems().stream().map(
                        item -> pythonImportNode.getFromModulePath() + "." + item).collect(Collectors.toList()
                );
                break;
        }

        return targetModuleList.stream().map(targetModule -> {
            ClassImportClass classImportClass = new ClassImportClass();
            classImportClass.setItemId(itemId);
            classImportClass.setSource(Md5Util.stringToMd5(sourceModule));
            classImportClass.setTarget(Md5Util.stringToMd5(targetModule));

            classImportClass.setValid(true);
            return classImportClass;
        }).collect(Collectors.toList());
    }

}
