package com.sankuai.deepcode.analysis.server.service;

import com.alibaba.fastjson.JSON;
import com.sankuai.deepcode.analysis.server.service.analysis.FileAnalysisService;
import com.sankuai.deepcode.analysis.server.service.analysis.ForeEndAnalysisService;
import com.sankuai.deepcode.analysis.server.service.analysis.JavaAnalysisService;
import com.sankuai.deepcode.analysis.server.service.analysis.PythonAnalysisService;
import com.sankuai.deepcode.analysis.server.struct.step.DownloadExtern;
import com.sankuai.deepcode.analysis.server.struct.step.FileExtern;
import com.sankuai.deepcode.ast.foreend.ForeEndAnalysis;
import com.sankuai.deepcode.ast.java.analysis.JavaAnalysis;
import com.sankuai.deepcode.ast.model.base.CompareRes;
import com.sankuai.deepcode.ast.model.base.GitDiffInfo;
import com.sankuai.deepcode.ast.model.base.LastMsgRes;
import com.sankuai.deepcode.ast.model.foreend.ForeEndAnalysisRes;
import com.sankuai.deepcode.ast.model.java.JavaAnalysisRes;
import com.sankuai.deepcode.ast.model.python3.PythonAnalysesResult;
import com.sankuai.deepcode.ast.python3.analysis.PythonAnalyzer;
import com.sankuai.deepcode.ast.util.CompareUtil;
import com.sankuai.deepcode.ast.util.FileUtil;
import com.sankuai.deepcode.ast.util.GitUtil;
import com.sankuai.deepcode.dao.domain.CodeAnalysisItem;
import com.sankuai.deepcode.dao.enums.ItemStatusEnum;
import com.sankuai.deepcode.dao.enums.ItemStepEnum;
import com.sankuai.deepcode.dao.service.CodeAnalysisItemService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.AsyncResult;
import org.springframework.stereotype.Service;

import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.Future;

import static com.sankuai.deepcode.ast.util.CompareUtil.WORKSPACE_PATH;

@Service
public class BaseTaskService {

    private static final Logger LOGGER = LoggerFactory.getLogger(BaseTaskService.class);
    @Autowired
    private ForeEndAnalysisService foreEndAnalysisService;
    @Autowired
    private PythonAnalysisService pythonAnalysisService;
    @Autowired
    private JavaAnalysisService javaAnalysisService;
    @Autowired
    private FileAnalysisService fileAnalysisService;
    @Autowired
    private ItemSatusService itemSatusService;

    private static final int TIME_OUT = 60;

    public Future<Boolean> analysisByItem(CodeAnalysisItem codeAnalysisItem) {
        long start = System.currentTimeMillis();
        try {
            itemSatusService.updateItemStatus(codeAnalysisItem, ItemStatusEnum.PROCESSING);
            itemSatusService.initDetail(codeAnalysisItem.getId());

            String repos = codeAnalysisItem.getGitUrl().split("/")[4].split("\\.git")[0];
            String uniquePath = CompareUtil.WORKSPACE_PATH + "/" + codeAnalysisItem.getId() + "/" + repos + "/analysis_src";

            //下载代码
            List<String> modules = downloadStep(codeAnalysisItem, uniquePath, repos);

            //文件分流
            CompareRes compareRes = fileStep(codeAnalysisItem, uniquePath, modules, repos);

            //java
            JavaAnalysisRes javaAnalysisRes = null;
            if (!compareRes.getGitJavaDiffInfoMap().isEmpty()) {
                javaAnalysisRes = javaStep(repos, codeAnalysisItem, compareRes);
            }

            //python
            PythonAnalysesResult pythonAnalysesResult = null;
            if (!compareRes.getGitPythonDiffInfoMap().isEmpty()) {
                pythonAnalysesResult = pythonStep(codeAnalysisItem, compareRes);
            }

            // 前端
            ForeEndAnalysisRes foreEndAnalysisRes = null;
            if (!compareRes.getGitForeEndDiffInfoMap().isEmpty()) {
                foreEndAnalysisRes = foreEndStep(repos, codeAnalysisItem, compareRes);
            }

            // 关系数据存储
            dataStep(codeAnalysisItem, pythonAnalysesResult, javaAnalysisRes, foreEndAnalysisRes, compareRes.getGitNoSourceInfoMap());

            itemSatusService.updateItemSucess(codeAnalysisItem, System.currentTimeMillis() - start);
        } catch (Exception e) {
            itemSatusService.updateItemFail(codeAnalysisItem, System.currentTimeMillis() - start);
            LOGGER.error("analysisByItemId error e:", e);
//                throw new RuntimeException(e);
        } finally {
            FileUtil.deleteFile(WORKSPACE_PATH, String.valueOf(codeAnalysisItem.getId()));
            return new AsyncResult<Boolean>(true);
        }

    }

    public List<String> downloadStep(CodeAnalysisItem codeAnalysisItem, String uniquePath, String repos) throws Exception {
        itemSatusService.insertDetail(codeAnalysisItem.getId(), ItemStepEnum.DOWNLOAD, "开始下载代码", "");
        FileUtil.createDir(uniquePath);
        //下载master
        long size = 0;
        try {
            size = GitUtil.gitCloneByAlias(uniquePath, codeAnalysisItem.getGitUrl(), repos, "_to", TIME_OUT);
            if (size > 1024 * 1024) {
                itemSatusService.updateDetailFail(codeAnalysisItem.getId(), ItemStepEnum.DOWNLOAD, "大于2G仓库不支持分析");
                throw new Exception("git size error");
            }
        } catch (Exception e) {
            LOGGER.error("git clone error e:", e);
            itemSatusService.updateDetailFail(codeAnalysisItem.getId(), ItemStepEnum.DOWNLOAD, "下载代码失败");
            throw new Exception("git clone error");
        }
        List<String> modules = CompareUtil.getModules(uniquePath + "/" + repos + "_to");

        try {
            String baseCommit = "";
            LastMsgRes lastMsgRes = null;
            if (codeAnalysisItem.isDiff()) {
                //copy
                GitUtil.copyR(uniquePath, repos + "_to", repos + "_from");
                lastMsgRes = GitUtil.gitCheckoutAndLastLog(uniquePath, repos + "_from", codeAnalysisItem.getFromBranch());
                GitUtil.gitResetHard(uniquePath, repos + "_from", codeAnalysisItem.getFromBranch());
                baseCommit = GitUtil.gitBaseByBranch(uniquePath, repos + "_from", codeAnalysisItem.getFromBranch(), codeAnalysisItem.getToBranch());
                codeAnalysisItem.setToCommitId(baseCommit);
                codeAnalysisItem.setBuildCommitId(lastMsgRes.getCommitId());
                codeAnalysisItem.setFromCommitId(lastMsgRes.getCommitId());
            } else {
                lastMsgRes = GitUtil.gitCheckoutAndLastLog(uniquePath, repos + "_to", codeAnalysisItem.getToBranch());
                codeAnalysisItem.setToCommitId(lastMsgRes.getCommitId());
                codeAnalysisItem.setBuildCommitId(lastMsgRes.getCommitId());
                codeAnalysisItem.setFromCommitId(lastMsgRes.getCommitId());
            }
            codeAnalysisItem.setLastCommitMsg(lastMsgRes.getMsg());
            codeAnalysisItem.setLastCommitTime(lastMsgRes.getDate());
            codeAnalysisItem.setLastCommitMsg(lastMsgRes.getMsg());
        } catch (Exception e) {
            LOGGER.error("git diff error e:", e);
            itemSatusService.updateDetailFail(codeAnalysisItem.getId(), ItemStepEnum.DOWNLOAD, "仓库文件解析异常");
            throw new Exception("git diff error");
        }
        DownloadExtern downloadExtern = new DownloadExtern();
        downloadExtern.setSize(size);
        itemSatusService.updateDetailSucess(codeAnalysisItem.getId(), ItemStepEnum.DOWNLOAD, "下载代码成功", JSON.toJSONString(downloadExtern));
        return modules;
    }

    public CompareRes fileStep(CodeAnalysisItem codeAnalysisItem, String uniquePath, List<String> modules, String repos) throws Exception {
        itemSatusService.insertDetail(codeAnalysisItem.getId(), ItemStepEnum.FILE, "开始文件分流", "");
        CompareRes compareRes = new CompareRes();
        try {
            Set<String> filterPath = new HashSet<>();
            compareRes.setFilterPath(filterPath);
            compareRes.setUniquePath(uniquePath);
            compareRes.setToCommit(codeAnalysisItem.getToCommitId());
            boolean diff = false;
            if (codeAnalysisItem.isDiff()) {
                diff = true;
            }
            if (diff) {
                compareRes.setDiff(true);
                compareRes.setGitPath(uniquePath + "/" + repos + "_from");
                compareRes.setGitToPath(uniquePath + "/" + repos + "_to");
            } else {
                compareRes.setGitPath(uniquePath + "/" + repos + "_to");
                compareRes.setFromCommit(codeAnalysisItem.getFromCommitId());
            }

            filterPath = new HashSet<>();
            compareRes.setFilterPath(filterPath);
            Map<String, GitDiffInfo> stringGitDiffInfoMap = CompareUtil.getDiffFileInfos(modules, repos, codeAnalysisItem.getToCommitId(), codeAnalysisItem.getFromCommitId(), uniquePath, codeAnalysisItem.isDiff(), false, filterPath);
            for (Map.Entry<String, GitDiffInfo> entry : stringGitDiffInfoMap.entrySet()) {
                if (entry.getValue().getFileType().equals("java")) {
                    compareRes.getGitJavaDiffInfoMap().put(entry.getKey(), entry.getValue());
                } else if (entry.getValue().getFileType().equals("py")) {
                    compareRes.getGitPythonDiffInfoMap().put(entry.getKey(), entry.getValue());
                } else if (entry.getValue().getFileType().equals("html")
                        || entry.getValue().getFileType().equals("wxml")
                        || entry.getValue().getFileType().equals("vue")
                        || entry.getValue().getFileType().equals("js")
                        || entry.getValue().getFileType().equals("jsx")
                        || entry.getValue().getFileType().equals("ts")
                        || entry.getValue().getFileType().equals("tsx")
                        || entry.getValue().getFileType().equals("css")
                        || entry.getValue().getFileType().equals("less")
                        || entry.getValue().getFileType().equals("scss")
                        || entry.getValue().getFileType().equals("wxss")) {
                    compareRes.getGitForeEndDiffInfoMap().put(entry.getKey(), entry.getValue());
                } else {
                    compareRes.getGitNoSourceInfoMap().put(entry.getKey(), entry.getValue());
                }
            }
        } catch (Exception e) {
            LOGGER.error("文件分流异常e ", e);
            itemSatusService.updateDetailFail(codeAnalysisItem.getId(), ItemStepEnum.FILE, "文件分流异常");
            throw new RuntimeException(e);
        }
        FileExtern fileExtern = new FileExtern();
        fileExtern.setJava(compareRes.getGitJavaDiffInfoMap().size());
        fileExtern.setPython(compareRes.getGitPythonDiffInfoMap().size());
        fileExtern.setFrontEnd(compareRes.getGitForeEndDiffInfoMap().size());
        fileExtern.setNoSource(compareRes.getGitNoSourceInfoMap().size());
        itemSatusService.updateDetailSucess(codeAnalysisItem.getId(), ItemStepEnum.FILE, "文件分流成功", JSON.toJSONString(fileExtern));
        return compareRes;
    }


    public JavaAnalysisRes javaStep(String repos, CodeAnalysisItem codeAnalysisItem, CompareRes compareRes) throws Exception {
        itemSatusService.insertDetail(codeAnalysisItem.getId(), ItemStepEnum.JAVA, "开始java分析", "");
        JavaAnalysisRes javaAnalysisRes = null;
        try {
            javaAnalysisRes = JavaAnalysis.javaAnalysis(repos, compareRes);
        } catch (Exception e) {
            LOGGER.error("java分析异常e ", e);
            itemSatusService.updateDetailFail(codeAnalysisItem.getId(), ItemStepEnum.JAVA, "java分析异常");
            throw new Exception("java分析异常");
        }
        itemSatusService.updateDetailSucess(codeAnalysisItem.getId(), ItemStepEnum.JAVA, "java分析成功", "");
        return javaAnalysisRes;
    }

    public PythonAnalysesResult pythonStep(CodeAnalysisItem codeAnalysisItem, CompareRes compareRes) throws Exception {
        itemSatusService.insertDetail(codeAnalysisItem.getId(), ItemStepEnum.PYTHON, "开始python分析", "");
        PythonAnalysesResult result = null;
        try {
            result = PythonAnalyzer.analyzePythonProject(compareRes);
        } catch (Exception e) {
            LOGGER.error("python分析异常e ", e);
            itemSatusService.updateDetailFail(codeAnalysisItem.getId(), ItemStepEnum.PYTHON, "python分析异常");
            throw new Exception("python分析异常");
        }
        itemSatusService.updateDetailSucess(codeAnalysisItem.getId(), ItemStepEnum.PYTHON, "python分析成功", "");
        return result;
    }

    public ForeEndAnalysisRes foreEndStep(String repos, CodeAnalysisItem codeAnalysisItem, CompareRes compareRes) throws Exception {
        itemSatusService.insertDetail(codeAnalysisItem.getId(), ItemStepEnum.FOREEND, "开始前端代码分析", "");
        ForeEndAnalysisRes result = null;
        try {
            result = ForeEndAnalysis.foreEndAnalysis(repos, compareRes);
        } catch (Exception e) {
            LOGGER.error("前端代码分析异常e ", e);
            itemSatusService.updateDetailFail(codeAnalysisItem.getId(), ItemStepEnum.FOREEND, "前端代码分析异常");
            throw new Exception("前端代码分析异常");
        }
        itemSatusService.updateDetailSucess(codeAnalysisItem.getId(), ItemStepEnum.FOREEND, "前端代码分析成功", "");
        return result;
    }

    public void dataStep(CodeAnalysisItem codeAnalysisItem,
                         PythonAnalysesResult pythonAnalysesResult,
                         JavaAnalysisRes javaAnalysisRes,
                         ForeEndAnalysisRes foreEndAnalysisRes,
                         Map<String, GitDiffInfo> gitNoSourceInfoMap) throws Exception {
        itemSatusService.insertDetail(codeAnalysisItem.getId(), ItemStepEnum.DATA, "开始存储关系数据", "");
        if (pythonAnalysesResult != null) {
            try {
                pythonAnalysisService.insertClassAnalysis(codeAnalysisItem.getId(), pythonAnalysesResult);
            } catch (Exception e) {
                LOGGER.error("存储python数据异常e ", e);
                itemSatusService.updateDetailFail(codeAnalysisItem.getId(), ItemStepEnum.DATA, "存储python数据异常");
                throw new Exception("存储python数据异常");
            }
        }
        if (javaAnalysisRes != null) {
            try {
                javaAnalysisService.insertInfo(javaAnalysisRes, codeAnalysisItem);
            } catch (Exception e) {
                LOGGER.error("存储java数据异常e ", e);
                itemSatusService.updateDetailFail(codeAnalysisItem.getId(), ItemStepEnum.DATA, "存储java数据异常");
                throw new Exception(e);
            }
        }
        if (foreEndAnalysisRes != null) {
            try {
                foreEndAnalysisService.insertInfo(foreEndAnalysisRes, codeAnalysisItem);
            } catch (Exception e) {
                LOGGER.error("存储前端代码数据异常e ", e);
                itemSatusService.updateDetailFail(codeAnalysisItem.getId(), ItemStepEnum.DATA, "存储前端代码数据异常");
                throw new Exception(e);
            }
        }
        if (!gitNoSourceInfoMap.isEmpty()) {
            try {
                fileAnalysisService.insertInfo(gitNoSourceInfoMap, codeAnalysisItem);
            } catch (Exception e) {
                LOGGER.error("存储资源文件数据异常e ", e);
                itemSatusService.updateDetailFail(codeAnalysisItem.getId(), ItemStepEnum.DATA, "存储资源文件数据异常");
                throw new Exception(e);
            }
        }
        itemSatusService.updateDetailSucess(codeAnalysisItem.getId(), ItemStepEnum.DATA, "存储关系数据成功", "");
    }

}
