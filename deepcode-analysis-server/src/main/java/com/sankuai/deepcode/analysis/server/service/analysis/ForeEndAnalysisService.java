package com.sankuai.deepcode.analysis.server.service.analysis;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.sankuai.deepcode.ast.enums.NodeSourceEnum;
import com.sankuai.deepcode.ast.enums.foreend.ForeEndEgdeTypeEnum;
import com.sankuai.deepcode.ast.enums.foreend.ForeEndNodeTypeEnum;
import com.sankuai.deepcode.ast.model.base.CodeView;
import com.sankuai.deepcode.ast.model.foreend.*;
import com.sankuai.deepcode.ast.model.html.ElementValue;
import com.sankuai.deepcode.ast.model.html.HtmlAttribute;
import com.sankuai.deepcode.ast.model.html.HtmlNode;
import com.sankuai.deepcode.ast.model.java.FileNode;
import com.sankuai.deepcode.ast.model.scss.ScssRule;
import com.sankuai.deepcode.ast.model.scss.ScssSelector;
import com.sankuai.deepcode.ast.model.scss.ScssVariable;
import com.sankuai.deepcode.ast.model.typescript.ScriptLiteral;
import com.sankuai.deepcode.ast.model.typescript.ScriptMethod;
import com.sankuai.deepcode.ast.model.typescript.ScriptMethodParam;
import com.sankuai.deepcode.ast.model.typescript.ScriptVariable;
import com.sankuai.deepcode.ast.util.Md5Util;
import com.sankuai.deepcode.dao.domain.*;
import com.sankuai.deepcode.dao.service.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;


@Service
@Slf4j
public class ForeEndAnalysisService {
    private static final Logger LOGGER = LoggerFactory.getLogger(ForeEndAnalysisService.class);

    @Autowired
    private CodeViewAnalysisService codeViewAnalysisService;
    @Autowired
    private ClassImportClassService classImportClassService;
    @Autowired
    private ForeEndEdgeService foreEndEdgeService;
    @Autowired
    private ForeEndNodeService foreEndNodeService;
    @Autowired
    private CodeFileAnalysisService codeFileAnalysisService;

    private int batchSize = 500;


    public void insertInfo(ForeEndAnalysisRes foreEndAnalysisRes, CodeAnalysisItem codeAnalysisItem) {
        long start = System.currentTimeMillis();
        List<CodeViewAnalysis> codeViewAnalyses = new ArrayList<>();
        for (FileNode fileNode : foreEndAnalysisRes.getFileNodeMap().values()) {
            for (CodeView codeView : fileNode.getCodeViews()) {
                CodeViewAnalysis codeViewAnalysis = new CodeViewAnalysis();
                codeViewAnalysis.setItemId(codeAnalysisItem.getId());
                codeViewAnalysis.setSourceVid(fileNode.getVid());
                codeViewAnalysis.setCodeId(codeView.getId());
                codeViewAnalysis.setCodeLine(codeView.getLine());
                codeViewAnalysis.setCodeType(codeView.getType());
                codeViewAnalysis.setCodeView(codeView.getView());
                codeViewAnalysis.setValid(true);
                codeViewAnalyses.add(codeViewAnalysis);
            }
        }

        List<List<CodeViewAnalysis>> splitCodeView = Lists.partition(codeViewAnalyses, batchSize * 2);
        for (List<CodeViewAnalysis> codeViewList : splitCodeView) {
            codeViewAnalysisService.batchInsert(codeViewList);
        }

        long end = System.currentTimeMillis();
        LOGGER.info("存储源码耗时:" + (end - start) + "ms");
        start = System.currentTimeMillis();


        //file
        List<CodeFileAnalysis> codeFileAnalyses = new ArrayList<>();
        for (FileNode fileNode : foreEndAnalysisRes.getFileNodeMap().values()) {
            CodeFileAnalysis codeFileAnalysis = new CodeFileAnalysis();
            codeFileAnalysis.setItemId(codeAnalysisItem.getId());
            codeFileAnalysis.setFileVid(fileNode.getVid());
            codeFileAnalysis.setModuleName(fileNode.getModuleName());
            codeFileAnalysis.setFileName(fileNode.getFileName());
            codeFileAnalysis.setFilePath(fileNode.getPath());
            codeFileAnalysis.setFileType(fileNode.getFileType());
            codeFileAnalysis.setChangeType(fileNode.getChangeType());
            codeFileAnalysis.setChangeLines(JSON.toJSONString(fileNode.getChangeLines()));
            codeFileAnalysis.setCheckType(fileNode.getCheckType());
            codeFileAnalysis.setCheckLines(JSON.toJSONString(fileNode.getCheckLines()));
            codeFileAnalysis.setStartLine(fileNode.getStartLine());
            codeFileAnalysis.setEndLine(fileNode.getEndLine());
            codeFileAnalysis.setCommentLines(JSON.toJSONString(fileNode.getCommentLines()));
            codeFileAnalysis.setCommitCount(fileNode.getCommitCount());
            codeFileAnalysis.setValid(true);
            codeFileAnalyses.add(codeFileAnalysis);
        }
        List<List<CodeFileAnalysis>> splitFile = Lists.partition(codeFileAnalyses, batchSize);
        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(splitFile)) {
            for (List<CodeFileAnalysis> fieldAnalyses : splitFile) {
                codeFileAnalysisService.batchInsert(fieldAnalyses);
            }
        }
        end = System.currentTimeMillis();
        LOGGER.info("存储file耗时:" + (end - start) + "ms");
        start = System.currentTimeMillis();


        List<ForeEndNode> foreEndNodes = new ArrayList<>();
        for (Map.Entry<String, Map<String, HtmlNode>> entry : foreEndAnalysisRes.getHtmlNodeMap().entrySet()) {
            String fileVid = Md5Util.filePathToMd5(entry.getKey());
            for (Map.Entry<String, HtmlNode> entryNode : entry.getValue().entrySet()) {
                ForeEndNode foreEndNode = new ForeEndNode();
                foreEndNode.setItemId(codeAnalysisItem.getId());
                foreEndNode.setNodeVid(entryNode.getKey());
                foreEndNode.setNodeType(ForeEndNodeTypeEnum.HTML.getCode());
                foreEndNode.setFileVid(fileVid);
                foreEndNode.setFilePath(entry.getKey());
                foreEndNode.setAnnotations(JSON.toJSONString(new ArrayList<>()));
                foreEndNode.setChangeType(entryNode.getValue().getChangeType());
                foreEndNode.setChangeLines(JSON.toJSONString(entryNode.getValue().getChangeLines()));
                foreEndNode.setCheckType(0);
                foreEndNode.setCheckLines(JSON.toJSONString(new ArrayList<>()));
                foreEndNode.setStartLine(entryNode.getValue().getStartLine());
                foreEndNode.setEndLine(entryNode.getValue().getEndLine());
                foreEndNode.setCommentLines(JSON.toJSONString(entryNode.getValue().getCommentLines()));
                foreEndNode.setExpand(JSON.toJSONString(covertHtmlNodeInfo(entryNode.getValue())));
                foreEndNode.setValid(true);
                foreEndNodes.add(foreEndNode);
            }
        }

        for (Map.Entry<String, Map<String, ScriptMethod>> entry : foreEndAnalysisRes.getScriptMethodMap().entrySet()) {
            String fileVid = Md5Util.filePathToMd5(entry.getKey());
            for (Map.Entry<String, ScriptMethod> entryNode : entry.getValue().entrySet()) {
                ForeEndNode foreEndNode = new ForeEndNode();
                foreEndNode.setItemId(codeAnalysisItem.getId());
                foreEndNode.setNodeVid(entryNode.getKey());
                foreEndNode.setNodeType(ForeEndNodeTypeEnum.JS_METHOD.getCode());
                foreEndNode.setFileVid(fileVid);
                foreEndNode.setFilePath(entry.getKey());
                foreEndNode.setAnnotations(JSON.toJSONString(new ArrayList<>()));
                foreEndNode.setChangeType(entryNode.getValue().getChangeType());
                foreEndNode.setChangeLines(JSON.toJSONString(entryNode.getValue().getChangeLines()));
                foreEndNode.setCheckType(0);
                foreEndNode.setCheckLines(JSON.toJSONString(new ArrayList<>()));
                foreEndNode.setStartLine(entryNode.getValue().getStartLine());
                foreEndNode.setEndLine(entryNode.getValue().getEndLine());
                foreEndNode.setCommentLines(JSON.toJSONString(entryNode.getValue().getCommentLines()));
                foreEndNode.setExpand(JSON.toJSONString(covertScriptMethodNodeInfo(entryNode.getValue())));
                foreEndNode.setValid(true);
                foreEndNodes.add(foreEndNode);
            }
        }

        for (Map.Entry<String, Map<String, ScriptVariable>> entry : foreEndAnalysisRes.getScriptVariableMap().entrySet()) {
            String fileVid = Md5Util.filePathToMd5(entry.getKey());
            for (Map.Entry<String, ScriptVariable> entryNode : entry.getValue().entrySet()) {
                ForeEndNode foreEndNode = new ForeEndNode();
                foreEndNode.setItemId(codeAnalysisItem.getId());
                foreEndNode.setNodeVid(entryNode.getKey());
                foreEndNode.setNodeType(ForeEndNodeTypeEnum.JS_FIELD.getCode());
                foreEndNode.setFileVid(fileVid);
                foreEndNode.setFilePath(entry.getKey());
                foreEndNode.setAnnotations(JSON.toJSONString(new ArrayList<>()));
                foreEndNode.setChangeType(entryNode.getValue().getChangeType());
                foreEndNode.setChangeLines(JSON.toJSONString(entryNode.getValue().getChangeLines()));
                foreEndNode.setCheckType(0);
                foreEndNode.setCheckLines(JSON.toJSONString(new ArrayList<>()));
                foreEndNode.setStartLine(entryNode.getValue().getStartLine());
                foreEndNode.setEndLine(entryNode.getValue().getEndLine());
                foreEndNode.setCommentLines(JSON.toJSONString(entryNode.getValue().getCommentLines()));
                foreEndNode.setExpand(JSON.toJSONString(covertScriptFieldNodeInfo(entryNode.getValue())));
                foreEndNode.setValid(true);
                foreEndNodes.add(foreEndNode);
            }
        }

        for (Map.Entry<String, Map<String, ScssRule>> entry : foreEndAnalysisRes.getScssRuleMap().entrySet()) {
            String fileVid = Md5Util.filePathToMd5(entry.getKey());
            for (Map.Entry<String, ScssRule> entryNode : entry.getValue().entrySet()) {
                ForeEndNode foreEndNode = new ForeEndNode();
                foreEndNode.setItemId(codeAnalysisItem.getId());
                foreEndNode.setNodeVid(entryNode.getKey());
                foreEndNode.setNodeType(ForeEndNodeTypeEnum.STYLE.getCode());
                foreEndNode.setFileVid(fileVid);
                foreEndNode.setFilePath(entry.getKey());
                foreEndNode.setAnnotations(JSON.toJSONString(new ArrayList<>()));
                foreEndNode.setChangeType(entryNode.getValue().getChangeType());
                foreEndNode.setChangeLines(JSON.toJSONString(entryNode.getValue().getChangeLines()));
                foreEndNode.setCheckType(0);
                foreEndNode.setCheckLines(JSON.toJSONString(new ArrayList<>()));
                foreEndNode.setStartLine(entryNode.getValue().getStartLine());
                foreEndNode.setEndLine(entryNode.getValue().getEndLine());
                foreEndNode.setCommentLines(JSON.toJSONString(entryNode.getValue().getCommentLines()));
                foreEndNode.setExpand(JSON.toJSONString(covertScssRuleNodeInfo(entryNode.getValue())));
                foreEndNode.setValid(true);
                foreEndNodes.add(foreEndNode);
            }
        }

        List<List<ForeEndNode>> splitForeEndNode = Lists.partition(foreEndNodes, batchSize);
        if (CollectionUtils.isNotEmpty(splitForeEndNode)) {
            for (List<ForeEndNode> foreEndNodeList : splitForeEndNode) {
                foreEndNodeService.batchInsert(foreEndNodeList);
            }
        }
        end = System.currentTimeMillis();
        LOGGER.info("存储前端node耗时:" + (end - start) + "ms");
        start = System.currentTimeMillis();


        //classImport
        List<ClassImportClass> classImportClasses = new ArrayList<>();
        for (Map.Entry<String, Set<String>> entry : foreEndAnalysisRes.getFileImportFile().entrySet()) {
            for (String target : entry.getValue()) {
                ClassImportClass classImportClass = new ClassImportClass();
                classImportClass.setItemId(codeAnalysisItem.getId());
                classImportClass.setSource(entry.getKey());
                classImportClass.setTarget(target);
                classImportClass.setValid(true);
                classImportClasses.add(classImportClass);
            }
        }
        List<List<ClassImportClass>> splitClassImport = Lists.partition(classImportClasses, batchSize);
        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(splitClassImport)) {
            for (List<ClassImportClass> classImportAnalyses : splitClassImport) {
                classImportClassService.batchInsert(classImportAnalyses);
            }
        }
        end = System.currentTimeMillis();
        LOGGER.info("存储fileImport耗时:" + (end - start) + "ms");
        start = System.currentTimeMillis();


        List<ForeEndEdge> foreEndEdges = new ArrayList<>();
        for (Map.Entry<String, List<ForeEndBaseEdge>> entry : foreEndAnalysisRes.getHtmlInvokeMethod().entrySet()) {
            for (ForeEndBaseEdge foreEndBaseEdge : entry.getValue()) {
                ForeEndEdge foreEndEdge = new ForeEndEdge();
                foreEndEdge.setItemId(codeAnalysisItem.getId());
                foreEndEdge.setType(ForeEndEgdeTypeEnum.HTML_INVOKE_METHOD.getCode());
                foreEndEdge.setSource(foreEndBaseEdge.getSource());
                foreEndEdge.setSourceType(foreEndBaseEdge.getSourceType());
                foreEndEdge.setTarget(foreEndBaseEdge.getTarget());
                foreEndEdge.setTargetType(foreEndBaseEdge.getTargetType());
                foreEndEdge.setInvokeParams(JSON.toJSONString(foreEndBaseEdge.getLines()));
                foreEndEdge.setValid(true);
                foreEndEdges.add(foreEndEdge);
            }
        }

        for (Map.Entry<String, List<ForeEndBaseEdge>> entry : foreEndAnalysisRes.getHtmlQuoteScss().entrySet()) {
            for (ForeEndBaseEdge foreEndBaseEdge : entry.getValue()) {
                ForeEndEdge foreEndEdge = new ForeEndEdge();
                foreEndEdge.setItemId(codeAnalysisItem.getId());
                foreEndEdge.setType(ForeEndEgdeTypeEnum.HTML_QUOTE_STYLE.getCode());
                foreEndEdge.setSource(foreEndBaseEdge.getSource());
                foreEndEdge.setSourceType(foreEndBaseEdge.getSourceType());
                foreEndEdge.setTarget(foreEndBaseEdge.getTarget());
                foreEndEdge.setTargetType(foreEndBaseEdge.getTargetType());
                foreEndEdge.setInvokeParams(JSON.toJSONString(foreEndBaseEdge.getLines()));
                foreEndEdge.setValid(true);
                foreEndEdges.add(foreEndEdge);
            }
        }

        for (Map.Entry<String, List<ForeEndBaseEdge>> entry : foreEndAnalysisRes.getMethodInvokeMethod().entrySet()) {
            for (ForeEndBaseEdge foreEndBaseEdge : entry.getValue()) {
                ForeEndEdge foreEndEdge = new ForeEndEdge();
                foreEndEdge.setItemId(codeAnalysisItem.getId());
                foreEndEdge.setType(ForeEndEgdeTypeEnum.HTML_INVOKE_METHOD.getCode());
                foreEndEdge.setSource(foreEndBaseEdge.getSource());
                foreEndEdge.setSourceType(foreEndBaseEdge.getSourceType());
                foreEndEdge.setTarget(foreEndBaseEdge.getTarget());
                foreEndEdge.setTargetType(foreEndBaseEdge.getTargetType());
                foreEndEdge.setInvokeParams(JSON.toJSONString(foreEndBaseEdge.getLines()));
                foreEndEdge.setValid(true);
                foreEndEdges.add(foreEndEdge);
            }
        }

        LOGGER.info("foreEndEdges总计" + foreEndEdges.size() + "条");
        List<List<ForeEndEdge>> splitForeEndEdge = Lists.partition(foreEndEdges, batchSize);
        if (CollectionUtils.isNotEmpty(splitForeEndEdge)) {
            for (List<ForeEndEdge> foreEndEdgeLists : splitForeEndEdge) {
                foreEndEdgeService.batchInsert(foreEndEdgeLists);
            }
        }
        end = System.currentTimeMillis();
        LOGGER.info("存储前端关系数据耗时:" + (end - start) + "ms");
    }

    public HtmlNodeInfo covertHtmlNodeInfo(HtmlNode htmlNode) {
        HtmlNodeInfo htmlNodeInfo = new HtmlNodeInfo();
        htmlNodeInfo.setNodeType(ForeEndNodeTypeEnum.HTML.getKey());
        htmlNodeInfo.setTagName(htmlNode.getTagName());
        htmlNodeInfo.setTagType(htmlNode.getTagType());
        htmlNodeInfo.setTagDepth(htmlNode.getTagDepth());
        htmlNodeInfo.setTagIndex(htmlNode.getTagIndex());
        htmlNodeInfo.setElements(htmlNode.getElementValues());
        htmlNodeInfo.setAttributes(htmlNode.getHtmlAttributes());
        return htmlNodeInfo;
    }

    public JsMethodNodeInfo covertScriptMethodNodeInfo(ScriptMethod scriptMethod) {
        JsMethodNodeInfo jsMethodNodeInfo = new JsMethodNodeInfo();
        jsMethodNodeInfo.setNodeType(ForeEndNodeTypeEnum.JS_METHOD.getKey());
        jsMethodNodeInfo.setAsync(scriptMethod.isAsync());
        jsMethodNodeInfo.setAwait(scriptMethod.isAwait());
        jsMethodNodeInfo.setYield(scriptMethod.isYield());
        jsMethodNodeInfo.setAbstract(scriptMethod.isAbstract());
        jsMethodNodeInfo.setCallSignature(scriptMethod.isCallSignature());
        jsMethodNodeInfo.setMethodName(scriptMethod.getMethodName());
        jsMethodNodeInfo.setModifier(scriptMethod.getModifier());
        jsMethodNodeInfo.setParams(scriptMethod.getParams());
        return jsMethodNodeInfo;
    }

    public JsFieldNodeInfo covertScriptFieldNodeInfo(ScriptVariable scriptVariable) {
        JsFieldNodeInfo jsFieldNodeInfo = new JsFieldNodeInfo();
        jsFieldNodeInfo.setNodeType(ForeEndNodeTypeEnum.JS_FIELD.getKey());
        jsFieldNodeInfo.setExport(scriptVariable.isExport());
        jsFieldNodeInfo.setReadOnly(scriptVariable.isReadOnly());
        jsFieldNodeInfo.setDecare(scriptVariable.isDecare());
        jsFieldNodeInfo.setType(scriptVariable.getType());
        jsFieldNodeInfo.setDecorators(scriptVariable.getDecorators());
        jsFieldNodeInfo.setLiteral(scriptVariable.getLiteral());
        jsFieldNodeInfo.setModifier(scriptVariable.getModifier());
        jsFieldNodeInfo.setName(scriptVariable.getName());
        jsFieldNodeInfo.setValue(scriptVariable.getValue());
        return jsFieldNodeInfo;
    }

    public StyleNodeInfo covertScssRuleNodeInfo(ScssRule scssRule) {
        StyleNodeInfo styleNodeInfo = new StyleNodeInfo();
        styleNodeInfo.setNodeType(ForeEndNodeTypeEnum.STYLE.getKey());
        styleNodeInfo.setVariables(scssRule.getVariables());
        styleNodeInfo.setSelectors(scssRule.getSelectors());
        return styleNodeInfo;
    }
}
