package com.sankuai.deepcode.analysis.server.domain;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 * Package: com.sankuai.deepcode.analysis.server.domain
 * Description:
 *
 * <AUTHOR>
 * @since 2025/6/3 19:52
 */
@Setter
@Getter
@ToString
@Accessors(chain = true)
@JsonIgnoreProperties(ignoreUnknown = true)
public class AiReviewResponse {
    /**
     * 代码行号
     */
    private Integer lineNumber;

    /**
     * 代码行范围
     */
    private String lineRange;

    /**
     * 规则ID
     */
    private Integer ruleId;

    /**
     * 问题严重程度
     */
    private String severity;

    /**
     * 问题说明
     */
    private String issueExplanation;

    /**
     * 修复建议
     */
    private String fixSuggestion;
}
