package com.sankuai.deepcode.analysis.server.service;

import com.sankuai.deepcode.analysis.server.common.AsyncPoolConfig;
import com.sankuai.deepcode.analysis.server.struct.step.FeatureInfo;
import com.sankuai.deepcode.dao.domain.CodeAnalysisItem;
import com.sankuai.deepcode.dao.enums.ItemStatusEnum;
import com.sankuai.deepcode.dao.enums.ItemStepEnum;
import com.sankuai.deepcode.dao.service.CodeAnalysisItemService;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.Future;

@Service
public class TaskRunService {
    private static final Logger LOGGER = LoggerFactory.getLogger(TaskRunService.class);

    @Autowired
    private CodeAnalysisItemService codeAnalysisItemService;
    @Autowired
    private BaseTaskService baseTaskService;
    @Autowired
    private ItemSatusService itemSatusService;
    @Autowired
    private EmbeddingTaskService embeddingTaskService;
    @Autowired
    private KnowledgeTaskService knowledgeTaskService;
    @Autowired
    private AiReviewTaskService aiReviewTaskService;
    @Autowired
    private AsyncPoolConfig asyncPoolConfig;

    private static final int TIME_OUT = 60;

    private static final List<Long> runItemIds = new ArrayList<>();

    private static final List<Long> runAsyncItemIds = new ArrayList<>();

    private final int maxRunCount = 4;

    public void runItemByStatus() throws Exception {
        List<CodeAnalysisItem> codeAnalysisItems = codeAnalysisItemService.getByStatus(ItemStatusEnum.INITIATING.getCode());
        List<FeatureInfo> futureResList = new ArrayList<>();
        for (CodeAnalysisItem codeAnalysisItem : codeAnalysisItems) {
            if (runItemIds.size() <= maxRunCount) {
                runItemIds.add(codeAnalysisItem.getId());
            } else {
                continue;
            }
            asyncPoolConfig.analysisRunExecutor().submit(() -> {
                Future<Boolean> futureRes = baseTaskService.analysisByItem(codeAnalysisItem);
                FeatureInfo featureInfo = new FeatureInfo();
                featureInfo.setItemId(codeAnalysisItem.getId());
                featureInfo.setFuture(futureRes);
                featureInfo.setItemType("base");
                futureResList.add(featureInfo);
            });
        }
        long startTime = System.currentTimeMillis();
        //轮询任务是否结束，校验是否有超时
        while (futureResList.size() > 0 && System.currentTimeMillis() - startTime < TIME_OUT * 10 * 1000L) {
            //减少轮询次数，每次歇1s
            Thread.sleep(1000);
            for (int i = futureResList.size() - 1; i >= 0; i--) {
                if (futureResList.get(i).getFuture().isDone()) {
                    runItemIds.remove(futureResList.get(i).getItemId());
                    futureResList.remove(i);
                }
            }
        }

        //处理没移除的超时线程
        if (CollectionUtils.isNotEmpty(futureResList)) {
            for (FeatureInfo futureInfo : futureResList) {
                LOGGER.info("处理超时item:{} type:{}", futureInfo.getItemId(), futureInfo.getItemType());
                futureInfo.getFuture().cancel(true);
                runItemIds.remove(futureInfo.getItemId());
                itemSatusService.updateItemTimeOut(futureInfo.getItemId(), System.currentTimeMillis() - startTime);
            }
        }

    }

    public void runAsyncItemByStatus() throws Exception {
        List<CodeAnalysisItem> codeAnalysisItems = codeAnalysisItemService.getByStatusAndAsyncStatus(ItemStatusEnum.SUCCESS.getCode(), ItemStatusEnum.INITIATING.getCode());
        List<FeatureInfo> futureResList = new ArrayList<>();
        for (CodeAnalysisItem codeAnalysisItem : codeAnalysisItems) {
            if (runAsyncItemIds.size() <= maxRunCount) {
                runAsyncItemIds.add(codeAnalysisItem.getId());
            } else {
                continue;
            }
            if (codeAnalysisItem.getAsyncTypes().contains(String.valueOf(ItemStepEnum.EMBEDDING.getCode()))) {
                asyncPoolConfig.analysisRunExecutor().submit(() -> {
                    Future<Boolean> embeddingByItem = embeddingTaskService.embeddingByItem(codeAnalysisItem);
                    FeatureInfo featureInfo = new FeatureInfo();
                    featureInfo.setItemId(codeAnalysisItem.getId());
                    featureInfo.setFuture(embeddingByItem);
                    featureInfo.setItemType(ItemStepEnum.EMBEDDING.getTypeName());
                    futureResList.add(featureInfo);
                });
            }

            if (codeAnalysisItem.getAsyncTypes().contains(String.valueOf(ItemStepEnum.KNOWLEDGE.getCode()))) {
                asyncPoolConfig.analysisRunExecutor().submit(() -> {
                    Future<Boolean> knowledgeByItem = knowledgeTaskService.knowledgeByItem(codeAnalysisItem);
                    FeatureInfo featureInfo = new FeatureInfo();
                    featureInfo.setItemId(codeAnalysisItem.getId());
                    featureInfo.setFuture(knowledgeByItem);
                    featureInfo.setItemType(ItemStepEnum.KNOWLEDGE.getTypeName());
                    futureResList.add(featureInfo);
                });
            }

            if (codeAnalysisItem.getAsyncTypes().contains(String.valueOf(ItemStepEnum.AI_REVIEW.getCode()))) {
                asyncPoolConfig.analysisRunExecutor().submit(() -> {
                    Future<Boolean> aiReviewByItem = aiReviewTaskService.aiReviewByItem(codeAnalysisItem);
                    FeatureInfo featureInfo = new FeatureInfo();
                    featureInfo.setItemId(codeAnalysisItem.getId());
                    featureInfo.setFuture(aiReviewByItem);
                    featureInfo.setItemType(ItemStepEnum.AI_REVIEW.getTypeName());
                    futureResList.add(featureInfo);
                });
            }
        }
        long startTime = System.currentTimeMillis();
        //轮询任务是否结束，校验是否有超时
        while (futureResList.size() > 0 && System.currentTimeMillis() - startTime < TIME_OUT * 45 * 1000L) {
            //减少轮询次数，每次歇1s
            Thread.sleep(1000);
            futureResList.removeIf(
                    futureInfo -> futureInfo.getFuture().isDone()
            );
        }

        //处理没移除的超时线程
        if (CollectionUtils.isNotEmpty(futureResList)) {
            for (FeatureInfo futureInfo : futureResList) {
                LOGGER.info("处理超时item async:{} type:{}", futureInfo.getItemId(), futureInfo.getItemType());
                futureInfo.getFuture().cancel(true);
                runAsyncItemIds.remove(futureInfo.getItemId());
                itemSatusService.updateDetailTimeOut(futureInfo.getItemId());
            }
        }
    }
}
