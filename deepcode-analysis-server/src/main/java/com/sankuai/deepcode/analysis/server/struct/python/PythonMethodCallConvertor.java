package com.sankuai.deepcode.analysis.server.struct.python;

import com.sankuai.deepcode.ast.model.python3.PythonFunctionCallNode;
import com.sankuai.deepcode.ast.util.Md5Util;
import com.sankuai.deepcode.dao.domain.MethodInvokeMethod;
import com.sankuai.deepcode.dao.po.InvokeLinePO;
import org.mapstruct.Mapper;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Mapper(
        componentModel = "spring",
        uses = {
                Md5Util.class
        }
)
public abstract class PythonMethodCallConvertor {

    public MethodInvokeMethod convertCallNode(Long itemId, String sourceVid, String targetVid, List<PythonFunctionCallNode> invokeInfo) {
        MethodInvokeMethod methodInvokeMethod = new MethodInvokeMethod();

        methodInvokeMethod.setItemId(itemId);
        methodInvokeMethod.setSource(sourceVid);
        methodInvokeMethod.setSourceType("local");
        methodInvokeMethod.setTarget(targetVid);

        // TODO: 此处的 调用类型，需要在原始解析时从整体的作用域数据中二次处理，获取被调用的方法是 内部，还是未知
        methodInvokeMethod.setTargetType("local");

        methodInvokeMethod.setInvokeParams(invokeInfo.stream().map(
                invoke -> new InvokeLinePO().setInvokeLine(invoke.getStart().getLine())
        ).collect(Collectors.toList()));


        methodInvokeMethod.setValid(true);
        return methodInvokeMethod;
    }
}
