# web服务端口号
server.port: 8080
management:
  endpoints:
    web:
      base-path: /monitor
      path-mapping:
        health: /alive

spring:
  profiles:
    active: '@active-profile@'
    include: dao
  mvc:
    pathmatch:
      matching-strategy: ant_path_matcher


#日志配置文件位置
logging:
  config: classpath:log4j2/log4j2-server.xml

# SpringDoc配置
springdoc:
  api-docs:
    path: /api-docs
  swagger-ui:
    path: /swagger-ui.html
  default-produces-media-type: application/json



---

spring:
  profiles: local

#日志配置文件位置
logging:
  config: classpath:log4j2/log4j2-local.xml


---

spring:
  profiles: server
#日志配置文件位置
logging:
  config: classpath:log4j2/log4j2-server.xml


