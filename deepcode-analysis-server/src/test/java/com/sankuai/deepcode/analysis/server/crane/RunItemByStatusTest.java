package com.sankuai.deepcode.analysis.server.crane;

import com.sankuai.deepcode.analysis.server.DeepCodeAnalysis;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = DeepCodeAnalysis.class)
class RunItemByStatusTest {

    @Autowired
    private RunItemByStatus runItemByStatus;

    @Test
    void runItemByStatus() throws Exception {
        runItemByStatus.runItemByStatus();
    }

    @Test
    void runAsyncItemByStatus() throws Exception {
        runItemByStatus.runAsyncItemByStatus();
    }
}