package com.sankuai.deepcode.analysis.server.service;

import com.sankuai.deepcode.analysis.server.DeepCodeAnalysis;
import com.sankuai.deepcode.dao.milvus.MilvusService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = DeepCodeAnalysis.class)
public class MilvusServiceTest {
    @Autowired
    private MilvusService milvusService;


    @Test
    public void test() {
        milvusService.isExist("test");
    }
}
