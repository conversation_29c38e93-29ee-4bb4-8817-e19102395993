package com.sankuai.deepcode.analysis.server.service;

import com.sankuai.deepcode.analysis.server.BaseTest;
import com.sankuai.deepcode.dao.domain.CodeAnalysisItem;
import com.sankuai.deepcode.dao.domain.CodeFileAnalysis;
import com.sankuai.deepcode.dao.service.CodeAnalysisItemService;
import com.sankuai.deepcode.dao.service.CodeFileAnalysisService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

class AiReviewTaskServiceTest extends BaseTest {
    @Autowired
    AiReviewTaskService aiReviewTaskService;
    @Autowired
    CodeAnalysisItemService codeAnalysisItemService;
    @Autowired
    CodeFileAnalysisService codeFileAnalysisService;

    @Test
    public void aiReviewByItem() {
        Long itemId = 71L;
        CodeAnalysisItem analysisItem = codeAnalysisItemService.getById(itemId);

        aiReviewTaskService.aiReviewByItem(analysisItem);
    }

    @Test
    public void testDiffContent() {
        Long fileId = 24325L;
        CodeFileAnalysis codeFileAnalysis = codeFileAnalysisService.getById(fileId);

        String result = aiReviewTaskService.getDiffFileContent(codeFileAnalysis);
        System.out.println("=========================>>>>>>>");
        System.out.println(result);
    }
}