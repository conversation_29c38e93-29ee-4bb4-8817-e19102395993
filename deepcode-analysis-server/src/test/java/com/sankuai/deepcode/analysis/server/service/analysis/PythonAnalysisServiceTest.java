package com.sankuai.deepcode.analysis.server.service.analysis;

import com.sankuai.deepcode.analysis.server.BaseTest;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import static org.junit.jupiter.api.Assertions.*;

class PythonAnalysisServiceTest extends BaseTest {
    @Autowired
    PythonAnalysisService pythonAnalysisService;

    @Test
    void analysis() {
        pythonAnalysisService.analysis(
                "ssh://*******************/wbqa/banma_fastdata_agent_copilot.git",
                "banma_fastdata_agent_copilot",
                "master",
                "main",
                "master",
                ""
        );
    }
}