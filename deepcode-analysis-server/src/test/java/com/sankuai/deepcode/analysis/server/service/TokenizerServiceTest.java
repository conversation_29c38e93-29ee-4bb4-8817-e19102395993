package com.sankuai.deepcode.analysis.server.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.reflect.TypeToken;
import com.sankuai.deepcode.ai.embedding.model.CheckTokenRes;
import com.sankuai.deepcode.ai.embedding.service.EmbeddingService;
import com.sankuai.deepcode.ai.llm.model.chat.ChatMessage;
import com.sankuai.deepcode.ai.llm.model.chat.ChatMsgRes;
import com.sankuai.deepcode.ai.tokenizer.model.TokenLength;
import com.sankuai.deepcode.ai.tokenizer.service.TokenizerService;
import com.sankuai.deepcode.analysis.server.DeepCodeAnalysis;
import io.restassured.response.Response;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.sankuai.deepcode.ai.common.OneApi.ONE_API_URL;
import static io.restassured.RestAssured.given;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = DeepCodeAnalysis.class)
public class TokenizerServiceTest {
    @Autowired
    private TokenizerService tokenizerService;

    @Autowired
    private EmbeddingService embeddingService;


    @Test
    public void test() {
        String text = "package com.sankuai.deepcode.manage.server.controller;\n" +
                "\n" +
                "\n" +
                "import com.sankuai.deepcode.ai.llm.model.chat.ChatByStreamParam;\n" +
                "import com.sankuai.deepcode.ai.llm.model.chat.ChatMessage;\n" +
                "import com.sankuai.deepcode.ai.llm.model.chat.ChatMsgRes;\n" +
                "import com.sankuai.deepcode.ai.llm.model.chat.StreamRes;\n" +
                "import com.sankuai.deepcode.manage.server.enums.ErrorCodeEnum;\n" +
                "import com.sankuai.deepcode.commons.CommonResult;\n" +
                "import com.sankuai.deepcode.manage.server.service.GptApiService;\n" +
                "import org.slf4j.Logger;\n" +
                "import org.slf4j.LoggerFactory;\n" +
                "import org.springframework.beans.factory.annotation.Autowired;\n" +
                "import org.springframework.http.MediaType;\n" +
                "import org.springframework.web.bind.annotation.PostMapping;\n" +
                "import org.springframework.web.bind.annotation.RequestBody;\n" +
                "import org.springframework.web.bind.annotation.RequestMapping;\n" +
                "import org.springframework.web.bind.annotation.RestController;\n" +
                "import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;\n" +
                "\n" +
                "import java.util.ArrayList;\n" +
                "import java.util.List;\n" +
                "\n" +
                "@RestController\n" +
                "@RequestMapping(\"chat\")\n" +
                "public class ChatController {\n" +
                "    private static final Logger LOGGER = LoggerFactory.getLogger(ChatController.class);\n" +
                "\n" +
                "    @Autowired\n" +
                "    private GptApiService gptApiService;\n" +
                "\n" +
                "    @PostMapping(value = \"/chatByStream\", produces = MediaType.TEXT_EVENT_STREAM_VALUE)\n" +
                "    public SseEmitter chatByStream(@RequestBody ChatByStreamParam chatByStreamParam) throws Exception {\n" +
                "        try {\n" +
                "            if (null == chatByStreamParam.getItemId() || chatByStreamParam.getItemId() == 0) {\n" +
                "                SseEmitter emitter = new SseEmitter();\n" +
                "                StreamRes streamRes = new StreamRes();\n" +
                "                streamRes.setContent(\"itemId不能为空\");\n" +
                "                streamRes.setFullContent(\"itemId不能为空\");\n" +
                "                emitter.send(SseEmitter.event().data(streamRes));\n" +
                "                return emitter;\n" +
                "            }\n" +
                "            return gptApiService.chatStream(chatByStreamParam.getItemId(), chatByStreamParam.getChatMessages());\n" +
                "        } catch (Exception e) {\n" +
                "            LOGGER.error(\"chatByStream 异常e:\", e);\n" +
                "            SseEmitter emitter = new SseEmitter();\n" +
                "            StreamRes streamRes = new StreamRes();\n" +
                "            streamRes.setContent(\"chatByStream 异常e::\" + e.getMessage());\n" +
                "            streamRes.setFullContent(\"chatByStream 异常e::\" + e.getMessage());\n" +
                "            emitter.send(SseEmitter.event().data(streamRes));\n" +
                "            return emitter;\n" +
                "        }\n" +
                "    }\n" +
                "\n" +
                "\n" +
                "    @PostMapping(value = \"/test\")\n" +
                "    public void test() throws Exception {\n" +
                "        try {\n" +
                "            // 获取系统属性 \"java.class.path\"\n" +
                "            String classPath = System.getProperty(\"java.class.path\");\n" +
                "            // 使用操作系统特定的分隔符分割类路径\n" +
                "            String[] classPathEntries = classPath.split(System.getProperty(\"path.separator\"));\n" +
                "\n" +
                "            ChatByStreamParam chatByStreamParam = new ChatByStreamParam();\n" +
                "            List<ChatMessage> chatMessages = new ArrayList<>();\n" +
                "            ChatMessage chatMessage = new ChatMessage();\n" +
                "            chatMessage.setContent(\"查询所有变更方法并展示前十个变更方法的vid\");\n" +
                "            chatMessage.setRole(\"user\");\n" +
                "            chatMessages.add(chatMessage);\n" +
                "            chatByStreamParam.setChatMessages(chatMessages);\n" +
                "            chatByStream(chatByStreamParam);\n" +
                "        } catch (Exception e) {\n" +
                "            LOGGER.error(\"chatByStream 异常e:\", e);\n" +
                "        }\n" +
                "    }\n" +
                "\n" +
                "    @PostMapping(\"/chatByCompletions\")\n" +
                "    public CommonResult chatByCompletions(@RequestBody ChatByStreamParam chatByStreamParam) throws Exception {\n" +
                "        try {\n" +
                "            ChatMsgRes res = gptApiService.completions(chatByStreamParam.getChatMessages());\n" +
                "            ChatMessage chatMessage = new ChatMessage();\n" +
                "            chatMessage.setRole(\"assistant\");\n" +
                "            chatMessage.setContent(res.getChoices().get(0).getMessage().getContent());\n" +
                "            return CommonResult.success(chatMessage);\n" +
                "        } catch (Exception e) {\n" +
                "            LOGGER.error(\"chatByStream 异常e:\", e);\n" +
                "            return CommonResult.fail(ErrorCodeEnum.FAILURE, \"chatByStream异常\");\n" +
                "        }\n" +
                "    }\n" +
                "\n" +
                "\n" +
                "    @PostMapping(value = \"/apiChatByStream\", produces = MediaType.TEXT_EVENT_STREAM_VALUE)\n" +
                "    public SseEmitter apiChatByStream(@RequestBody ChatByStreamParam chatByStreamParam) throws Exception {\n" +
                "        try {\n" +
                "            return gptApiService.apiChatStream(chatByStreamParam.getChatMessages());\n" +
                "        } catch (Exception e) {\n" +
                "            LOGGER.error(\"chatByStream 异常e:\", e);\n" +
                "            SseEmitter emitter = new SseEmitter();\n" +
                "            StreamRes streamRes = new StreamRes();\n" +
                "            streamRes.setConteasdkmaidjaijdiadoiasndoanda" +
                "" +
                "" +
                "askdnasodas" +
                "" +
                "" +
                "a,sdmiasnmdma" +
                "" +
                "dnaundasnt(\"chatByStream 异常e::\" + e.getMessage());\n" +
                "            streamRes.setFullContent(\"chatByStream 异常e::\" + e.getMessage());\n" +
                "            emitter.send(SseEmitter.event().data(streamRes));\n" +
                "            return emitter;\n" +
                "        }\n" +
                "    }\n" +
                "\n" +
                "}\n";
        int count = tokenizerService.countTokens(text);

        long start = System.currentTimeMillis();
        TokenLength tokenLength = tokenizerService.countTokensAndMax(text, 500);
        System.out.println("token耗时:" + (System.currentTimeMillis() - start));


        start = System.currentTimeMillis();
        CheckTokenRes checkTokenRes = embeddingService.checkTokenLength(text);
        System.out.println("embeding api token耗时:" + (System.currentTimeMillis() - start));


        start = System.currentTimeMillis();
        Map<String, String> headersMap = new HashMap<>();
        headersMap.put("Content-Type", "application/json");
        Map<String, Object> paramsMap = new HashMap<>();
        paramsMap.put("model", "gpt-3.5-turbo-0613");
        List<ChatMessage> chatMessages = new ArrayList<>();
        ChatMessage chatMessage = new ChatMessage();
        chatMessage.setRole("user");
        chatMessage.setContent(text);
        chatMessages.add(chatMessage);
        paramsMap.put("messages", chatMessages);
        Response response = given().headers(headersMap).body(JSONObject.toJSONString(paramsMap))
                .when().post("https://aigc.sankuai.com/v1/openai/tiktoken");

        System.out.println("接口 token耗时:" + (System.currentTimeMillis() - start));

        System.out.println(response.getBody().asString());
        System.out.println();
    }
}
