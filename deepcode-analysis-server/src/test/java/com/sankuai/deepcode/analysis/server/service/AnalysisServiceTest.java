package com.sankuai.deepcode.analysis.server.service;

import com.sankuai.deepcode.analysis.server.DeepCodeAnalysis;
import com.sankuai.deepcode.ast.util.CompareUtil;
import com.sankuai.deepcode.dao.domain.CodeAnalysisItem;
import com.sankuai.deepcode.dao.service.CodeAnalysisItemService;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = DeepCodeAnalysis.class)
class AnalysisServiceTest {

    @Autowired
    private BaseTaskService analysisService;

    @Autowired
    private CodeAnalysisItemService codeAnalysisItemService;

    @Test
    void downloadStep() throws Exception {
        CodeAnalysisItem codeAnalysisItem = codeAnalysisItemService.getById(6L);
        String repos = codeAnalysisItem.getGitUrl().split("/")[4].split("\\.git")[0];
        String uniquePath = CompareUtil.WORKSPACE_PATH + "/" + codeAnalysisItem.getId() + "/" + repos + "/analysis_src";
        analysisService.downloadStep(codeAnalysisItem, uniquePath, repos);
        System.out.println();
    }

    @Test
    void analysisByItemId() throws Exception {
        CodeAnalysisItem codeAnalysisItem = codeAnalysisItemService.getById(73L);
        analysisService.analysisByItem(codeAnalysisItem);
    }
}