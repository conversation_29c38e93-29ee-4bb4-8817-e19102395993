package com.sankuai.deepcode.analysis.server.service;

import com.sankuai.deepcode.ai.agnets.service.PlanFlowService;
import com.sankuai.deepcode.ai.enums.ChatEnum;
import com.sankuai.deepcode.ai.llm.model.chat.ChatMessage;
import com.sankuai.deepcode.ai.llm.model.chat.ChatMsgRes;
import com.sankuai.deepcode.ai.llm.service.OneApiService;
import com.sankuai.deepcode.analysis.server.DeepCodeAnalysis;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.ArrayList;
import java.util.List;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = DeepCodeAnalysis.class)
public class PlanFlowServiceTest {
    @Autowired
    private PlanFlowService planFlowService;

    @Autowired
    private OneApiService oneApiService;

    @Test
    public void testChat() {
        List<ChatMessage> chatMessages = new ArrayList<>();
        ChatMessage system = new ChatMessage();
        system.setRole("system");
        system.setContent(PlanFlowService.SystemPrompt);
        ChatMessage chatMessage = new ChatMessage();
        chatMessage.setRole("user");
        chatMessage.setContent("这个工程是做什么的");
        chatMessages.add(system);
        chatMessages.add(chatMessage);
        ChatMsgRes chatMsgRes = oneApiService.completions(chatMessages, ChatEnum.GPT_4_1_MINI.getName());
        System.out.println(chatMsgRes);

    }

}
