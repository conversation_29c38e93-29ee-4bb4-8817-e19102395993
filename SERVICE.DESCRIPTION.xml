<?xml version="1.0" encoding="UTF-8"?>
<serviceCatalog
        xmlns="http://service.sankuai.com/1.0.0"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://service.sankuai.com/1.0.0
            http://pixel.sankuai.com/repository/releases/com/meituan/apidoc/servicecatalog/1.0.0/servicecatalog-1.0.0.xsd">

    <serviceDescs>
        <!--
              该示例描述的是使用 Restful 服务框架的情况下如何进行文档编写。该类型的编写方式和普通 java 的一样，都是通过注解的方式在
              代码中进行相关文档的描述。接入文档可以参考：https://km.sankuai.com/page/60715770
              可以将 <serviceDesc> 中 name, description, scenarios 等信息以 @ServiceDoc 的注解在代码中标注，也可以直接
              按下面这种方式进行编写。
        -->
        <serviceDesc>
            <appkey>com.sankuai.deepcode.manage.server</appkey>
            <name>服务文档示例</name>
            <description>服务编写文档示例</description>
            <scenarios>服务文档示例</scenarios>
            <interfaceDescs>
                <interfaceDesc>
                    <type>restful</type>
                    <!-- 替换为自己定义的Controller类名称 -->
                    <class>com.sankuai.deepcode.manage.server.controller</class>
                </interfaceDesc>
            </interfaceDescs>
        </serviceDesc>
    </serviceDescs>

</serviceCatalog>