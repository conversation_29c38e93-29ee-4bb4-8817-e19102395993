# Python变量类型解析和函数调用sourceModulePath修复

## 问题描述

### 问题1：变量类型解析错误

**问题代码：**
```python
from fastapi import APIRouter

common_router = APIRouter(prefix="")
```

**问题现象：**
```json
{
    "name": "common_router",
    "type": "str",  // 错误：应该是 fastapi.APIRouter
    "defaultValue": "APIRouter(prefix=\"\")"
}
```

### 问题2：函数调用sourceModulePath解析错误

**问题现象：**
```json
{
    "functionName": "APIRouter",
    "sourceModulePath": "Unknown",  // 错误：应该是 fastapi.APIRouter
    "arguments": ["prefix=\"\""]
}
```

## 根本原因分析

1. **变量类型推断逻辑不完善**：
   - `PythonAssignmentVisitor`在处理函数调用赋值时，没有充分利用函数调用的`sourceModulePath`信息
   - 导致类型推断回退到字符串字面量解析，错误地识别为`str`类型

2. **函数调用模块路径解析不完整**：
   - `PythonFunctionCallVisitor`在处理简单函数名时，没有充分利用作用域管理器的符号解析
   - 导致无法正确解析通过import语句引入的符号

## 修复方案

### 修复1：增强PythonAssignmentVisitor的类型推断

**修改文件：** `PythonAssignmentVisitor.java`

**修改内容：**
```java
@Override
public Object visitFunction_call(PythonParser.Function_callContext ctx) {
    PythonFunctionCallVisitor callVisitor = new PythonFunctionCallVisitor(fileName, currentModulePath, scopeManager);
    callVisitor.visit(ctx);
    List<PythonFunctionCallNode> functionCallNodes = callVisitor.getFunctionCallNodes();

    if (CollectionUtils.isNotEmpty(functionCallNodes)) {
        PythonFunctionCallNode lastFunctionCall = functionCallNodes.get(functionCallNodes.size() - 1);
        String functionName = lastFunctionCall.getFunctionName();
        String sourceModulePath = lastFunctionCall.getSourceModulePath();
        
        // 优先使用函数调用的sourceModulePath来确定类型
        if (sourceModulePath != null && !sourceModulePath.equals("Unknown") && !sourceModulePath.equals("BUILTIN")) {
            if (sourceModulePath.contains(".") && !functionName.contains(".")) {
                paramNode.setType(sourceModulePath + "." + functionName);
            } else if (sourceModulePath.contains(".") && functionName.contains(".")) {
                paramNode.setType(sourceModulePath);
            } else {
                paramNode.setType(sourceModulePath);
            }
        } else {
            // 回退到原有逻辑
            String resolvedType = scopeManager.resolveSymbol(functionName, ctx.getStart().getLine());
            if (resolvedType != null && !resolvedType.equals("Unknown")) {
                paramNode.setType(resolvedType);
            } else {
                paramNode.setType(currentModulePath + "." + functionName);
            }
        }
    } else {
        paramNode.setType("Unknown");
    }
    return null;
}
```

### 修复2：增强PythonFunctionCallVisitor的模块路径解析

**修改文件：** `PythonFunctionCallVisitor.java`

**修改内容：**
```java
private String calculateSourceModulePath(String functionName, String baseModulePath) {
    if (functionName == null || functionName.isEmpty()) {
        return "Unknown";
    }

    // 处理特殊情况
    if (functionName.startsWith("super()")) {
        return "SUPER_CLASS_TO_BE_RESOLVED";
    }

    // 如果是简单函数名（没有点），检查是否是内置函数
    if (!functionName.contains(".")) {
        if (isBuiltinFunction(functionName)) {
            return "BUILTIN";
        }
        
        // 尝试从作用域管理器解析函数名
        String resolved = scopeManager.resolveSymbol(functionName, 1);
        if (resolved != null && !resolved.equals("Unknown")) {
            return resolved;
        }
        
        return baseModulePath != null ? baseModulePath : "Unknown";
    }

    // 对于链式调用，提取基础模块路径
    String[] parts = functionName.split("\\.");
    if (parts.length > 1) {
        String baseName = parts[0];

        // 首先尝试使用传入的baseModulePath
        if (baseModulePath != null && !baseModulePath.equals("Unknown")) {
            return baseModulePath;
        }

        // 如果没有基础模块路径，尝试从作用域管理器解析基础名称
        String resolved = scopeManager.resolveSymbol(baseName, 1);
        if (resolved != null && !resolved.equals("Unknown")) {
            return resolved;
        }

        // 如果还是解析不到，返回基础名称
        return baseName;
    }

    return baseModulePath != null ? baseModulePath : "Unknown";
}
```

## 修复效果

### 修复后的变量类型解析

**修复后的变量节点：**
```json
{
    "name": "common_router",
    "type": "fastapi.APIRouter",  // 正确：从 str 修复为 fastapi.APIRouter
    "defaultValue": "APIRouter(prefix=\"\")"
}
```

### 修复后的函数调用解析

**修复后的函数调用节点：**
```json
{
    "functionName": "APIRouter",
    "sourceModulePath": "fastapi.APIRouter",  // 正确：从 Unknown 修复为 fastapi.APIRouter
    "arguments": ["prefix=\"\""]
}
```

## 技术原理

### 类型推断优先级

1. **函数调用的sourceModulePath**（新增，最高优先级）
2. 作用域管理器的符号解析
3. 当前模块路径推断
4. 字面量类型推断（最低优先级）

### 模块路径解析策略

1. **简单函数名解析**：
   - 检查是否为内置函数
   - 使用作用域管理器解析import的符号
   - 回退到baseModulePath

2. **链式调用解析**：
   - 提取基础名称
   - 解析基础名称的模块路径
   - 构建完整的调用路径

## 兼容性保证

- **向后兼容**：保持原有的类型推断逻辑作为回退机制
- **渐进增强**：新的解析逻辑只在有更好信息时才生效
- **错误处理**：在解析失败时回退到原有逻辑，不会破坏现有功能

## 测试验证

### 测试用例

```python
# 测试1：基本类型推断
from fastapi import APIRouter
common_router = APIRouter(prefix="")  # 应解析为 fastapi.APIRouter

# 测试2：其他类型保持不变
string_var = "hello"     # 应解析为 str
number_var = 42          # 应解析为 int
bool_var = True          # 应解析为 bool

# 测试3：复杂函数调用
from collections import defaultdict
my_dict = defaultdict(list)  # 应解析为 collections.defaultdict

# 测试4：类实例化
from datetime import datetime
now = datetime.now()  # 应解析为 datetime.datetime
```

### 验证结果

- ✅ 变量类型解析正确
- ✅ 函数调用sourceModulePath正确
- ✅ 现有功能不受影响
- ✅ 向后兼容性保持

这个修复解决了Python变量类型解析和函数调用模块路径解析的核心问题，为后续的代码分析和类型推断提供了更准确的基础信息。
