# Python注释分配功能

## 概述

本功能实现了Python代码中单行注释的智能分配，将注释按照其在代码中的位置分配到相应的AST节点中。

## 实现原理

### 注释分配逻辑

根据Python的代码结构特点，注释按照以下规则进行分配：

1. **函数内部的注释** → 分配给函数节点
2. **类内部但不在方法内的注释** → 分配给类节点  
3. **方法内部的注释** → 分配给方法节点
4. **其他注释** → 分配给模块节点

### 与Java注释处理的区别

- **函数前面的注释**：在Java中可能属于函数，但在Python中属于模块
- **类前面的注释**：在Java中可能属于类，但在Python中属于模块
- **简化的分配逻辑**：Python使用更直接的位置判断，不需要复杂的前置注释检测

## 核心实现

### 主要方法

```java
// PythonAnalyzer.java
private static void assignCommentsToNodes(PythonModuleNode moduleNode, CommonTokenStream tokens)
```

### 注释提取

```java
private static List<PythonComment> extractComments(CommonTokenStream tokens)
```

### 位置判断

```java
private static boolean isCommentInRange(int commentLine, LocationInfo start, LocationInfo end)
```

## 使用示例

```python
# 这是模块级注释1
# 这是模块级注释2

import os  # 导入注释

# 模块级变量注释
MODULE_VAR = "test"

# 这是模块级函数的注释（属于模块）
def module_function():
    # 函数内部注释1（属于函数）
    x = 1  # 行尾注释（属于函数）
    # 函数内部注释2（属于函数）
    return x

# 类定义前的注释（属于模块）
class TestClass:
    # 类内部注释1（属于类）
    class_var = "test"  # 类变量注释（属于类）
    
    # 构造函数注释（属于类）
    def __init__(self):
        # 构造函数内部注释（属于方法）
        self.instance_var = 1
    
    # 方法注释（属于类）
    def method1(self):
        # 方法内部注释1（属于方法）
        result = 2
        # 方法内部注释2（属于方法）
        return result
```

## 注释分配结果

- **模块注释**：模块级注释、函数前注释、类前注释、导入注释等
- **函数注释**：函数内部的所有注释
- **类注释**：类内部但不在方法内的注释
- **方法注释**：方法内部的所有注释

## 技术细节

### 注释识别

- 从ANTLR的隐藏通道（HIDDEN_CHANNEL）提取注释token
- 过滤以`#`开头的单行注释
- 记录注释的行号和内容

### 位置匹配

- 使用AST节点的start和end位置信息
- 通过行号范围判断注释归属
- 优先级：方法内 > 类内 > 模块级

### 数据结构

- 每个节点都有`List<PythonComment> comments`字段
- PythonComment包含位置信息和注释内容
- 支持多行注释的收集和分配

## 测试验证

实现了完整的测试用例验证注释分配的正确性：

- 模块级注释分配
- 函数内注释分配  
- 类内注释分配
- 方法内注释分配
- 边界情况处理

## 扩展性

该实现为后续功能扩展提供了良好的基础：

- 支持块注释（docstring）的处理
- 支持注释与代码的关联分析
- 支持注释的语义分析和提取
