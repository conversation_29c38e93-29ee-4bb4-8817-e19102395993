# Python变量类型解析问题分析

## 当前状态分析

从测试结果可以看出：

### 函数调用解析（已修复）
- ✅ `APIRouter`的`sourceModulePath`正确显示为`fastapi.APIRouter`
- ✅ `datetime.now`的`sourceModulePath`正确显示为`datetime`
- ❌ `defaultdict`的`sourceModulePath`仍然是`Unknown`（需要进一步修复）

### 变量类型解析（仍有问题）
- ❌ `common_router`的`type`仍然是`str`，应该是`fastapi.APIRouter`
- ❌ `my_dict`没有`type`字段，应该是`collections.defaultdict`
- ❌ `now`没有`type`字段，应该是`datetime.datetime`

## 问题根因

1. **PythonAssignmentVisitor的类型推断逻辑**：
   - 虽然我们修改了`visitFunction_call`方法，但这个方法可能没有被正确调用
   - `inferTypeFromRightHandSide`方法中的AST遍历可能没有正确处理函数调用

2. **AST解析路径**：
   - `common_router = APIRouter(prefix="")`可能被解析为字符串表达式而不是函数调用
   - 需要在正确的AST节点上进行类型推断

## 解决方案

### 方案1：增强inferTypeFromRightHandSide方法
在`inferTypeFromRightHandSide`方法中添加对函数调用的处理：

```java
@Override
public Void visitPrimary(PythonParser.PrimaryContext ctx) {
    // 检查是否包含函数调用
    if (ctx.primary_suffix() != null && !ctx.primary_suffix().isEmpty()) {
        for (PythonParser.Primary_suffixContext suffix : ctx.primary_suffix()) {
            if (suffix.LPAR() != null) {
                // 这是一个函数调用，使用PythonFunctionCallVisitor处理
                PythonFunctionCallVisitor callVisitor = new PythonFunctionCallVisitor(fileName, currentModulePath, scopeManager);
                callVisitor.visitPrimary(ctx);
                List<PythonFunctionCallNode> functionCallNodes = callVisitor.getFunctionCallNodes();
                
                if (!functionCallNodes.isEmpty()) {
                    PythonFunctionCallNode lastFunctionCall = functionCallNodes.get(functionCallNodes.size() - 1);
                    String sourceModulePath = lastFunctionCall.getSourceModulePath();
                    String functionName = lastFunctionCall.getFunctionName();
                    
                    // 使用函数调用信息设置变量类型
                    if (sourceModulePath != null && !sourceModulePath.equals("Unknown")) {
                        paramNode.setType(sourceModulePath + "." + functionName);
                    }
                }
                return null;
            }
        }
    }
    return super.visitPrimary(ctx);
}
```

### 方案2：修复defaultdict的sourceModulePath解析
在`PythonFunctionCallVisitor`中增强作用域解析：

```java
// 尝试从作用域管理器解析函数名
String resolved = scopeManager.resolveSymbol(functionName, 1);
if (resolved != null && !resolved.equals("Unknown")) {
    return resolved;
}
```

## 预期效果

修复后的变量类型解析结果：

```json
{
    "name": "common_router",
    "type": "fastapi.APIRouter",  // 从 str 修复为 fastapi.APIRouter
    "defaultValue": "APIRouter(prefix=\"\")"
},
{
    "name": "my_dict", 
    "type": "collections.defaultdict",  // 新增类型字段
    "defaultValue": "defaultdict(list)"
},
{
    "name": "now",
    "type": "datetime.datetime",  // 新增类型字段  
    "defaultValue": "datetime.now()"
}
```

## 技术细节

### AST遍历路径
1. `visitAssignment` → 处理赋值语句
2. `visit(ctx.star_expressions())` → 访问右侧表达式
3. `inferTypeFromRightHandSide` → 推断类型
4. `visitPrimary` → 处理primary表达式（包含函数调用）

### 类型推断优先级
1. 函数调用的sourceModulePath（最高优先级）
2. 作用域管理器的符号解析
3. 字面量类型推断（最低优先级）

这个分析为后续的修复提供了明确的方向和技术路径。
