{"analyseDuration": 613, "moduleMap": {"builtin": {"changeType": "DEFAULT", "classNodeMap": {}, "classNodes": [{"changeType": "DEFAULT", "classBody": "classFasten(BaseFormatter):\n<INDENT>def__init__(self,cg_generator,package,product,forge,version,timestamp):\n<INDENT>self.cg_generator=cg_generator\nself.internal_mods=self.cg_generator.output_internal_mods()or{}\nself.external_mods=self.cg_generator.output_external_mods()or{}\nself.classes=self.cg_generator.output_classes()or{}\nself.edges=self.cg_generator.output_edges()or[]\nself.functions=self.cg_generator.output_functions()or[]\nself.unique=0\nself.namespace_map={}\nself.package=package\nself.product=product\nself.forge=forge\nself.version=version\nself.timestamp=timestamp\n<DEDENT>defget_unique_and_increment(self):\n<INDENT>unique=self.unique\nself.unique+=1\nreturnunique\n<DEDENT>defto_uri(self,modname,name=\"\"):\n<INDENT>cleared=name\nifname:\n<INDENT>ifname==modname:\n<INDENT>cleared=\"\"\n<DEDENT>else:\n<INDENT>ifnotname.startswith(modname+\".\"):\n<INDENT>raiseException(\"name should start with modname\",name,modname)\n<DEDENT>cleared=name[(len(modname)+1):]\n<DEDENT><DEDENT>suffix=\"\"\nifnameinself.functions:\n<INDENT>suffix=\"()\"\n<DEDENT>return\"/{}/{}{}\".format(modname.replace(\"-\",\"_\"),cleared,suffix)\n<DEDENT>defto_external_uri(self,modname,name=\"\"):\n<INDENT>ifmodname==utils.constants.BUILTIN_NAME:\n<INDENT>name=name[len(modname)+1:]\nmodname=\".builtin\"\n<DEDENT>return\"//{}//{}\".format(modname.replace(\"-\",\"_\"),name)\n<DEDENT>deffind_dependencies(self,package_path):\n<INDENT>res=[]\nifnotpackage_path:\n<INDENT>returnres\n<DEDENT>requirements_path=os.path.join(package_path,\"requirements.txt\")\nifnotos.path.exists(requirements_path):\n<INDENT>returnres\n<DEDENT>withopen(requirements_path,\"r\")asf:\n<INDENT>lines=[_l.strip()for_linf.readlines()]\n<DEDENT>forlineinlines:\n<INDENT>ifnotline:\n<INDENT>continue\n<DEDENT>try:\n<INDENT>req=Requirement.parse(line)\n<DEDENT>exceptValueError:\n<INDENT>continue\n<DEDENT>specs=req.specs\nconstraints=[]\ndefadd_range(begin,end):\n<INDENT>ifbeginandend:\n<INDENT>ifbegin[1]andend[1]:\n<INDENT>constraints.append(\"[{}..{}]\".format(begin[0],end[0]))\n<DEDENT>elifbegin[1]:\n<INDENT>constraints.append(\"[{}..{})\".format(begin[0],end[0]))\n<DEDENT>elifend[1]:\n<INDENT>constraints.append(\"({}..{}]\".format(begin[0],end[0]))\n<DEDENT>else:\n<INDENT>constraints.append(\"({}..{})\".format(begin[0],end[0]))\n<DEDENT><DEDENT>elifbegin:\n<INDENT>ifbegin[1]:\n<INDENT>constraints.append(\"[{}..]\".format(begin[0]))\n<DEDENT>else:\n<INDENT>constraints.append(\"({}..]\".format(begin[0]))\n<DEDENT><DEDENT>elifend:\n<INDENT>ifend[1]:\n<INDENT>constraints.append(\"[..{}]\".format(end[0]))\n<DEDENT>else:\n<INDENT>constraints.append(\"[..{})\".format(end[0]))\n<DEDENT><DEDENT><DEDENT>begin=None\nend=None\nforkey,valinsorted(specs,key=lambdax:x[1]):\n<INDENT>ifkey==\"==\":\n<INDENT>ifbeginandend:\n<INDENT>add_range(begin,end)\nbegin=None\nend=None\n<DEDENT>ifnotbegin:\n<INDENT>constraints.append(\"[{}]\".format(val))\n<DEDENT><DEDENT>ifkey==\">\":\n<INDENT>ifend:\n<INDENT>add_range(begin,end)\nend=None\nbegin=None\n<DEDENT>ifnotbegin:\n<INDENT>begin=(val,False)\n<DEDENT><DEDENT>ifkey==\">=\":\n<INDENT>ifend:\n<INDENT>add_range(begin,end)\nbegin=None\nend=None\n<DEDENT>ifnotbegin:\n<INDENT>begin=(val,True)\n<DEDENT><DEDENT>ifkey==\"<\":\n<INDENT>end=(val,False)\n<DEDENT>ifkey==\"<=\":\n<INDENT>end=(val,True)\n<DEDENT><DEDENT>add_range(begin,end)\nres.append({\"forge\":\"PyPI\",\"product\":req.name,\"constraints\":constraints})\n<DEDENT>returnres\n<DEDENT>defget_internal_modules(self):\n<INDENT>mods={}\nformodname,moduleinself.internal_mods.items():\n<INDENT>name=self.to_uri(modname)\nfilename=module[\"filename\"]\nnamespaces=module[\"methods\"]\nmods[name]={\"sourceFile\":filename,\"namespaces\":{}}\nfornamespace,infoinnamespaces.items():\n<INDENT>namespace_uri=self.to_uri(modname,info[\"name\"])\nunique=self.get_unique_and_increment()\nmods[name][\"namespaces\"][unique]=dict(namespace=namespace_uri,metadata=dict(first=info[\"first\"],last=info[\"last\"]),)\nself.namespace_map[namespace_uri]=unique\n<DEDENT><DEDENT>mods=self.add_superclasses(mods)\nreturnmods\n<DEDENT>defadd_superclasses(self,mods):\n<INDENT>forcls_name,clsinself.classes.items():\n<INDENT>cls_uri=self.namespace_map.get(self.to_uri(cls[\"module\"],cls_name))\nmods[self.to_uri(cls[\"module\"])][\"namespaces\"][cls_uri][\"metadata\"][\"superClasses\"]=[]\nforparentincls[\"mro\"]:\n<INDENT>ifparent==cls_name:\n<INDENT>continue\n<DEDENT>ifself.classes.get(parent):\n<INDENT>parent_uri=self.to_uri(self.classes[parent][\"module\"],parent)\n<DEDENT>else:\n<INDENT>parent_mod=parent.split(\".\")[0]\nparent_uri=self.to_external_uri(parent_mod,parent)\n<DEDENT>mods[self.to_uri(cls[\"module\"])][\"namespaces\"][cls_uri][\"metadata\"][\"superClasses\"].append(parent_uri)\n<DEDENT><DEDENT>returnmods\n<DEDENT>defcreate_namespaces_map(self):\n<INDENT>namespaces_maps=[{},{}]\nforres,hmapinzip(namespaces_maps,[self.internal_mods,self.external_mods]):\n<INDENT>formodinhmap:\n<INDENT>fornamespaceinhmap[mod][\"methods\"]:\n<INDENT>res[namespace]=mod\n<DEDENT><DEDENT><DEDENT>returnnamespaces_maps\n<DEDENT>defget_external_modules(self):\n<INDENT>mods={}\nformodname,moduleinself.external_mods.items():\n<INDENT>name=self.to_external_uri(modname).split(\"/\")[2]\nnamespaces=module[\"methods\"]\nmods[name]={\"sourceFile\":\"\",\"namespaces\":{}}\nfornamespace,infoinnamespaces.items():\n<INDENT>ifinfo[\"name\"]!=modname:\n<INDENT>namespace_uri=self.to_external_uri(modname,info[\"name\"])\nunique=self.get_unique_and_increment()\nmods[name][\"namespaces\"][str(unique)]=dict(namespace=namespace_uri,metadata={})\nself.namespace_map[namespace_uri]=unique\n<DEDENT><DEDENT><DEDENT>returnmods\n<DEDENT>defget_graph(self):\n<INDENT>graph={\"internalCalls\":[],\"externalCalls\":[],\"resolvedCalls\":[]}\ninternal,external=self.create_namespaces_map()\nforsrc,dstinself.edges:\n<INDENT>uris=[]\nfornodein[src,dst]:\n<INDENT>ifnodeininternal:\n<INDENT>mod=internal[node]\nuri=self.to_uri(mod,node)\nuris.append(self.namespace_map.get(uri,uri))\n<DEDENT>elifnodeinexternal:\n<INDENT>mod=external[node]\nuris.append(self.namespace_map.get(self.to_external_uri(mod,node)))\n<DEDENT><DEDENT>iflen(uris)==2:\n<INDENT>ifdstinexternal:\n<INDENT>graph[\"externalCalls\"].append([str(uris[0]),str(uris[1]),{}])\n<DEDENT>else:\n<INDENT>graph[\"internalCalls\"].append([str(uris[0]),str(uris[1]),{}])\n<DEDENT><DEDENT><DEDENT>returngraph\n<DEDENT>defgenerate(self):\n<INDENT>return{\"product\":self.product,\"forge\":self.forge,\"generator\":\"PyCG\",\"depset\":self.find_dependencies(self.package),\"version\":self.version,\"timestamp\":self.timestamp,\"modules\":{\"internal\":self.get_internal_modules(),\"external\":self.get_external_modules(),},\"graph\":self.get_graph(),\"nodes\":self.get_unique_and_increment(),}\n<DEDENT><DEDENT>", "comments": [{"comment": "# Copyright (c) 2020 <PERSON><PERSON>.", "start": {"column": 0, "line": 2}}, {"comment": "#", "start": {"column": 0, "line": 3}}, {"comment": "# Licensed to the Apache Software Foundation (ASF) under one", "start": {"column": 0, "line": 4}}, {"comment": "# or more contributor license agreements.  See the NOTICE file", "start": {"column": 0, "line": 5}}, {"comment": "# distributed with this work for additional information", "start": {"column": 0, "line": 6}}, {"comment": "# regarding copyright ownership.  The ASF licenses this file", "start": {"column": 0, "line": 7}}, {"comment": "# to you under the Apache License, Version 2.0 (the", "start": {"column": 0, "line": 8}}, {"comment": "# \"License\"); you may not use this file except in compliance", "start": {"column": 0, "line": 9}}, {"comment": "# with the License.  You may obtain a copy of the License at", "start": {"column": 0, "line": 10}}, {"comment": "#", "start": {"column": 0, "line": 11}}, {"comment": "#   http://www.apache.org/licenses/LICENSE-2.0", "start": {"column": 0, "line": 12}}, {"comment": "#", "start": {"column": 0, "line": 13}}, {"comment": "# Unless required by applicable law or agreed to in writing,", "start": {"column": 0, "line": 14}}, {"comment": "# software distributed under the License is distributed on an", "start": {"column": 0, "line": 15}}, {"comment": "# \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY", "start": {"column": 0, "line": 16}}, {"comment": "# KIND, either express or implied.  See the License for the", "start": {"column": 0, "line": 17}}, {"comment": "# specific language governing permissions and limitations", "start": {"column": 0, "line": 18}}, {"comment": "# under the License.", "start": {"column": 0, "line": 19}}, {"comment": "#", "start": {"column": 0, "line": 20}}], "decorators": [], "end": {"column": -1, "line": 273}, "fileName": "builtin.py", "fileVid": "fc2e76518c30208f3c9517c278817375", "innerClasses": [], "innerImports": [], "methods": [{"async": false, "callNodes": [], "changeType": "DEFAULT", "className": "<PERSON><PERSON>", "comments": [], "complexity": 1, "decorators": [], "end": {"column": 4, "line": 45}, "fileName": "builtin.py", "fullPath": "builtin.Fasten.__init__", "funcBody": "def__init__(self,cg_generator,package,product,forge,version,timestamp):\n<INDENT>self.cg_generator=cg_generator\nself.internal_mods=self.cg_generator.output_internal_mods()or{}\nself.external_mods=self.cg_generator.output_external_mods()or{}\nself.classes=self.cg_generator.output_classes()or{}\nself.edges=self.cg_generator.output_edges()or[]\nself.functions=self.cg_generator.output_functions()or[]\nself.unique=0\nself.namespace_map={}\nself.package=package\nself.product=product\nself.forge=forge\nself.version=version\nself.timestamp=timestamp\n<DEDENT>", "hasArgs": false, "hasKwargs": false, "innerFunctions": [], "innerImports": [], "isGenerator": false, "moduleName": "builtin.Fasten", "modulePath": "builtin.Fasten.__init__", "name": "__init__", "otherBlockComments": [], "parameterCount": 7, "parameters": [{"location": {"column": 17, "line": 31}, "name": "self", "paramIndex": 0, "type": "<PERSON><PERSON>", "typeModule": "builtin.Fasten", "vid": "f8393ea147f6b750a263171c3ec05ad4"}, {"location": {"column": 23, "line": 31}, "name": "cg_generator", "paramIndex": 1, "typeModule": "Unknown", "vid": "d93cc59c91174ff0f5a7d23f7430ad0a"}, {"location": {"column": 37, "line": 31}, "name": "package", "paramIndex": 2, "typeModule": "Unknown", "vid": "e7f1810e6f4344c25eebb2a0eed75475"}, {"location": {"column": 46, "line": 31}, "name": "product", "paramIndex": 3, "typeModule": "Unknown", "vid": "88d10343419d06b0a2e40cf1de67b5c6"}, {"location": {"column": 55, "line": 31}, "name": "forge", "paramIndex": 4, "typeModule": "Unknown", "vid": "3ecd4d8348cde74225743aedcbe88a10"}, {"location": {"column": 62, "line": 31}, "name": "version", "paramIndex": 5, "typeModule": "Unknown", "vid": "9cfebe249d9b978e312528e9798c9bf3"}, {"location": {"column": 71, "line": 31}, "name": "timestamp", "paramIndex": 6, "typeModule": "Unknown", "vid": "1847ee8f8e1f3a58b82d76b3dfb6b2bb"}], "returnNode": [], "slashIndex": -1, "starIndex": -1, "start": {"column": 4, "line": 31}, "vid": "6243d534e13f26080c44e69fe75ee263"}, {"async": false, "callNodes": [], "changeType": "DEFAULT", "className": "<PERSON><PERSON>", "comments": [], "complexity": 1, "decorators": [], "end": {"column": 4, "line": 50}, "fileName": "builtin.py", "fullPath": "builtin.Fasten.get_unique_and_increment", "funcBody": "defget_unique_and_increment(self):\n<INDENT>unique=self.unique\nself.unique+=1\nreturnunique\n<DEDENT>", "hasArgs": false, "hasKwargs": false, "innerFunctions": [], "innerImports": [], "isGenerator": false, "moduleName": "builtin.Fasten", "modulePath": "builtin.Fasten.get_unique_and_increment", "name": "get_unique_and_increment", "otherBlockComments": [], "parameterCount": 1, "parameters": [{"location": {"column": 33, "line": 46}, "name": "self", "paramIndex": 0, "type": "<PERSON><PERSON>", "typeModule": "builtin.Fasten", "vid": "f8393ea147f6b750a263171c3ec05ad4"}], "returnNode": [{"rawReturn": "unique", "returnLine": {"column": 8, "line": 49}, "type": "Unknown"}], "slashIndex": -1, "starIndex": -1, "start": {"column": 4, "line": 46}, "vid": "b34ec766dfa677122427090339c0a990"}, {"async": false, "callNodes": [{"arguments": ["modname+\".\""], "end": {"column": 52, "line": 57}, "filePath": "builtin.py", "functionName": "startswith", "moduleName": "builtin.Fasten", "rawCode": "name.startswith(modname+\".\")", "sourceModulePath": "Unknown", "start": {"column": 23, "line": 57}, "superCall": false, "vid": "d5c92ccb662f5ecb4834d7f389856277"}, {"arguments": ["\"name should start with modname\"", "name", "modname"], "end": {"column": 83, "line": 58}, "filePath": "builtin.py", "functionName": "Exception", "moduleName": "builtin.Fasten", "rawCode": "Exception(\"name should start with modname\", name, modname)", "sourceModulePath": "BUILTIN_EXCEPTION.Exception", "start": {"column": 26, "line": 58}, "superCall": false, "vid": "0f095ffa6fa73be5be2698fcb8141c3b"}], "changeType": "DEFAULT", "className": "<PERSON><PERSON>", "comments": [], "complexity": 6, "decorators": [], "end": {"column": 4, "line": 67}, "fileName": "builtin.py", "fullPath": "builtin.Fasten.to_uri", "funcBody": "defto_uri(self,modname,name=\"\"):\n<INDENT>cleared=name\nifname:\n<INDENT>ifname==modname:\n<INDENT>cleared=\"\"\n<DEDENT>else:\n<INDENT>ifnotname.startswith(modname+\".\"):\n<INDENT>raiseException(\"name should start with modname\",name,modname)\n<DEDENT>cleared=name[(len(modname)+1):]\n<DEDENT><DEDENT>suffix=\"\"\nifnameinself.functions:\n<INDENT>suffix=\"()\"\n<DEDENT>return\"/{}/{}{}\".format(modname.replace(\"-\",\"_\"),cleared,suffix)\n<DEDENT>", "hasArgs": false, "hasKwargs": false, "innerFunctions": [], "innerImports": [], "isGenerator": false, "moduleName": "builtin.Fasten", "modulePath": "builtin.Fasten.to_uri", "name": "to_uri", "otherBlockComments": [], "parameterCount": 3, "parameters": [{"location": {"column": 15, "line": 51}, "name": "self", "paramIndex": 0, "type": "<PERSON><PERSON>", "typeModule": "builtin.Fasten", "vid": "f8393ea147f6b750a263171c3ec05ad4"}, {"location": {"column": 21, "line": 51}, "name": "modname", "paramIndex": 1, "typeModule": "Unknown", "vid": "eab2567afb262d6c232855e2d67e000e"}, {"defaultValue": "\"\"", "location": {"column": 30, "line": 51}, "name": "name", "paramIndex": 0, "typeModule": "Unknown", "vid": "33ce0305e29f6cd1e943157ad99f3a55"}], "returnNode": [{"rawReturn": "\"/{}/{}{}\".format(modname.replace(\"-\",\"_\"),cleared,suffix)", "returnLine": {"column": 8, "line": 66}, "type": "Unknown"}], "slashIndex": -1, "starIndex": -1, "start": {"column": 4, "line": 51}, "vid": "2aefa729dbf12e4d778b0f2d9b6ec84c"}, {"async": false, "callNodes": [], "changeType": "DEFAULT", "className": "<PERSON><PERSON>", "comments": [], "complexity": 2, "decorators": [], "end": {"column": 4, "line": 74}, "fileName": "builtin.py", "fullPath": "builtin.Fasten.to_external_uri", "funcBody": "defto_external_uri(self,modname,name=\"\"):\n<INDENT>ifmodname==utils.constants.BUILTIN_NAME:\n<INDENT>name=name[len(modname)+1:]\nmodname=\".builtin\"\n<DEDENT>return\"//{}//{}\".format(modname.replace(\"-\",\"_\"),name)\n<DEDENT>", "hasArgs": false, "hasKwargs": false, "innerFunctions": [], "innerImports": [], "isGenerator": false, "moduleName": "builtin.Fasten", "modulePath": "builtin.Fasten.to_external_uri", "name": "to_external_uri", "otherBlockComments": [], "parameterCount": 3, "parameters": [{"location": {"column": 24, "line": 68}, "name": "self", "paramIndex": 0, "type": "<PERSON><PERSON>", "typeModule": "builtin.Fasten", "vid": "f8393ea147f6b750a263171c3ec05ad4"}, {"location": {"column": 30, "line": 68}, "name": "modname", "paramIndex": 1, "typeModule": "Unknown", "vid": "eab2567afb262d6c232855e2d67e000e"}, {"defaultValue": "\"\"", "location": {"column": 39, "line": 68}, "name": "name", "paramIndex": 0, "typeModule": "Unknown", "vid": "33ce0305e29f6cd1e943157ad99f3a55"}], "returnNode": [{"rawReturn": "\"//{}//{}\".format(modname.replace(\"-\",\"_\"),name)", "returnLine": {"column": 8, "line": 73}, "type": "Unknown"}], "slashIndex": -1, "starIndex": -1, "start": {"column": 4, "line": 68}, "vid": "abc6b36e557eac6cc7d23bb573e2c922"}, {"async": false, "callNodes": [{"arguments": ["requirements_path"], "end": {"column": 47, "line": 81}, "filePath": "builtin.py", "functionName": "exists", "moduleName": "builtin.Fasten", "rawCode": "os.path.exists(requirements_path)", "sourceModulePath": "os.path", "start": {"column": 15, "line": 81}, "superCall": false, "vid": "0d6235231c4d0a3285e4c0553563e982"}, {"arguments": ["requirements_path", "\"r\""], "end": {"column": 40, "line": 84}, "filePath": "builtin.py", "functionName": "open", "moduleName": "builtin.Fasten", "rawCode": "open(requirements_path, \"r\")", "sourceModulePath": "BUILTIN.open", "start": {"column": 13, "line": 84}, "superCall": false, "vid": "79ec3e7b1942ae9ff7d46e88c336e205"}, {"arguments": ["specs", "key=lambdax:x[1]"], "end": {"column": 60, "line": 125}, "filePath": "builtin.py", "functionName": "sorted", "moduleName": "builtin.Fasten", "rawCode": "sorted(specs, key=lambdax:x[1])", "sourceModulePath": "BUILTIN.sorted", "start": {"column": 28, "line": 125}, "superCall": false, "vid": "ba8cc59fa006c842cf81e8e583173258"}, {"arguments": ["begin", "end"], "end": {"column": 44, "line": 129}, "filePath": "builtin.py", "functionName": "add_range", "moduleName": "builtin.Fasten", "rawCode": "add_range(begin, end)", "sourceModulePath": "Unknown", "start": {"column": 24, "line": 129}, "superCall": false, "vid": "8ff251c7d561804f4b2294b2bfbb8f07"}, {"arguments": ["\"[{}]\".format(val)"], "end": {"column": 61, "line": 133}, "filePath": "builtin.py", "functionName": "append", "moduleName": "builtin.Fasten", "rawCode": "constraints.append(\"[{}]\".format(val))", "sourceModulePath": "BUILTIN.list", "start": {"column": 24, "line": 133}, "superCall": false, "vid": "f99dbaed7f9fd3e9268bc7c31aeac616"}, {"arguments": ["val"], "end": {"column": 60, "line": 133}, "filePath": "builtin.py", "functionName": "format", "moduleName": "builtin.Fasten", "rawCode": "\"[{}]\".format(val)", "sourceModulePath": "Unknown", "start": {"column": 43, "line": 133}, "superCall": false, "vid": "ddac08855bb465a6206e6313348b0f39"}, {"arguments": ["val"], "end": {"column": 60, "line": 133}, "filePath": "builtin.py", "functionName": "format", "moduleName": "builtin.Fasten", "rawCode": "\"[{}]\".format(val)", "sourceModulePath": "Unknown", "start": {"column": 43, "line": 133}, "superCall": false, "vid": "ddac08855bb465a6206e6313348b0f39"}, {"arguments": ["begin", "end"], "end": {"column": 44, "line": 137}, "filePath": "builtin.py", "functionName": "add_range", "moduleName": "builtin.Fasten", "rawCode": "add_range(begin, end)", "sourceModulePath": "Unknown", "start": {"column": 24, "line": 137}, "superCall": false, "vid": "8ff251c7d561804f4b2294b2bfbb8f07"}, {"arguments": ["begin", "end"], "end": {"column": 44, "line": 144}, "filePath": "builtin.py", "functionName": "add_range", "moduleName": "builtin.Fasten", "rawCode": "add_range(begin, end)", "sourceModulePath": "Unknown", "start": {"column": 24, "line": 144}, "superCall": false, "vid": "8ff251c7d561804f4b2294b2bfbb8f07"}, {"arguments": ["begin", "end"], "end": {"column": 32, "line": 154}, "filePath": "builtin.py", "functionName": "add_range", "moduleName": "builtin.Fasten", "rawCode": "add_range(begin, end)", "sourceModulePath": "Unknown", "start": {"column": 12, "line": 154}, "superCall": false, "vid": "8ff251c7d561804f4b2294b2bfbb8f07"}, {"arguments": ["{\"forge\":\"PyPI\",\"product\":req.name,\"constraints\":constraints}"], "end": {"column": 13, "line": 158}, "filePath": "builtin.py", "functionName": "append", "moduleName": "builtin.Fasten", "rawCode": "res.append({\"forge\":\"PyPI\",\"product\":req.name,\"constraints\":constraints})", "sourceModulePath": "BUILTIN.list", "start": {"column": 12, "line": 156}, "superCall": false, "vid": "f99dbaed7f9fd3e9268bc7c31aeac616"}], "changeType": "DEFAULT", "className": "<PERSON><PERSON>", "comments": [{"comment": "# The specific line in the requirements.txt", "start": {"column": 16, "line": 94}}, {"comment": "# does not follow the Requirements File Format", "start": {"column": 16, "line": 95}}, {"comment": "# if begin, then it is already in a range", "start": {"column": 16, "line": 126}}], "complexity": 21, "decorators": [], "end": {"column": 4, "line": 161}, "fileName": "builtin.py", "fullPath": "builtin.Fasten.find_dependencies", "funcBody": "deffind_dependencies(self,package_path):\n<INDENT>res=[]\nifnotpackage_path:\n<INDENT>returnres\n<DEDENT>requirements_path=os.path.join(package_path,\"requirements.txt\")\nifnotos.path.exists(requirements_path):\n<INDENT>returnres\n<DEDENT>withopen(requirements_path,\"r\")asf:\n<INDENT>lines=[_l.strip()for_linf.readlines()]\n<DEDENT>forlineinlines:\n<INDENT>ifnotline:\n<INDENT>continue\n<DEDENT>try:\n<INDENT>req=Requirement.parse(line)\n<DEDENT>exceptValueError:\n<INDENT>continue\n<DEDENT>specs=req.specs\nconstraints=[]\ndefadd_range(begin,end):\n<INDENT>ifbeginandend:\n<INDENT>ifbegin[1]andend[1]:\n<INDENT>constraints.append(\"[{}..{}]\".format(begin[0],end[0]))\n<DEDENT>elifbegin[1]:\n<INDENT>constraints.append(\"[{}..{})\".format(begin[0],end[0]))\n<DEDENT>elifend[1]:\n<INDENT>constraints.append(\"({}..{}]\".format(begin[0],end[0]))\n<DEDENT>else:\n<INDENT>constraints.append(\"({}..{})\".format(begin[0],end[0]))\n<DEDENT><DEDENT>elifbegin:\n<INDENT>ifbegin[1]:\n<INDENT>constraints.append(\"[{}..]\".format(begin[0]))\n<DEDENT>else:\n<INDENT>constraints.append(\"({}..]\".format(begin[0]))\n<DEDENT><DEDENT>elifend:\n<INDENT>ifend[1]:\n<INDENT>constraints.append(\"[..{}]\".format(end[0]))\n<DEDENT>else:\n<INDENT>constraints.append(\"[..{})\".format(end[0]))\n<DEDENT><DEDENT><DEDENT>begin=None\nend=None\nforkey,valinsorted(specs,key=lambdax:x[1]):\n<INDENT>ifkey==\"==\":\n<INDENT>ifbeginandend:\n<INDENT>add_range(begin,end)\nbegin=None\nend=None\n<DEDENT>ifnotbegin:\n<INDENT>constraints.append(\"[{}]\".format(val))\n<DEDENT><DEDENT>ifkey==\">\":\n<INDENT>ifend:\n<INDENT>add_range(begin,end)\nend=None\nbegin=None\n<DEDENT>ifnotbegin:\n<INDENT>begin=(val,False)\n<DEDENT><DEDENT>ifkey==\">=\":\n<INDENT>ifend:\n<INDENT>add_range(begin,end)\nbegin=None\nend=None\n<DEDENT>ifnotbegin:\n<INDENT>begin=(val,True)\n<DEDENT><DEDENT>ifkey==\"<\":\n<INDENT>end=(val,False)\n<DEDENT>ifkey==\"<=\":\n<INDENT>end=(val,True)\n<DEDENT><DEDENT>add_range(begin,end)\nres.append({\"forge\":\"PyPI\",\"product\":req.name,\"constraints\":constraints})\n<DEDENT>returnres\n<DEDENT>", "hasArgs": false, "hasKwargs": false, "innerFunctions": [], "innerImports": [], "isGenerator": false, "moduleName": "builtin.Fasten", "modulePath": "builtin.Fasten.find_dependencies", "name": "find_dependencies", "otherBlockComments": [], "parameterCount": 2, "parameters": [{"location": {"column": 26, "line": 75}, "name": "self", "paramIndex": 0, "type": "<PERSON><PERSON>", "typeModule": "builtin.Fasten", "vid": "f8393ea147f6b750a263171c3ec05ad4"}, {"location": {"column": 32, "line": 75}, "name": "package_path", "paramIndex": 1, "typeModule": "Unknown", "vid": "ae0e6871b5c851fe9985db11f4dc708f"}], "returnNode": [{"rawReturn": "res", "returnLine": {"column": 12, "line": 78}, "type": "Unknown"}, {"rawReturn": "res", "returnLine": {"column": 12, "line": 82}, "type": "Unknown"}, {"rawReturn": "res", "returnLine": {"column": 8, "line": 160}, "type": "Unknown"}], "slashIndex": -1, "starIndex": -1, "start": {"column": 4, "line": 75}, "vid": "3b4176e65723f9ccde4b44de789faf0c"}, {"async": false, "callNodes": [{"end": {"column": 56, "line": 165}, "filePath": "builtin.py", "functionName": "items", "moduleName": "builtin.Fasten", "rawCode": "self.internal_mods.items()", "sourceModulePath": "builtin.Fasten.internal_mods", "start": {"column": 31, "line": 165}, "superCall": false, "vid": "fbbb565a5730452e4cf9890792fcede0"}, {"end": {"column": 52, "line": 172}, "filePath": "builtin.py", "functionName": "items", "moduleName": "builtin.Fasten", "rawCode": "namespaces.items()", "sourceModulePath": "BUILTIN.str", "start": {"column": 35, "line": 172}, "superCall": false, "vid": "b0cc97386944b4414eafbe00a154613b"}], "changeType": "DEFAULT", "className": "<PERSON><PERSON>", "comments": [], "complexity": 3, "decorators": [], "end": {"column": 4, "line": 184}, "fileName": "builtin.py", "fullPath": "builtin.Fasten.get_internal_modules", "funcBody": "defget_internal_modules(self):\n<INDENT>mods={}\nformodname,moduleinself.internal_mods.items():\n<INDENT>name=self.to_uri(modname)\nfilename=module[\"filename\"]\nnamespaces=module[\"methods\"]\nmods[name]={\"sourceFile\":filename,\"namespaces\":{}}\nfornamespace,infoinnamespaces.items():\n<INDENT>namespace_uri=self.to_uri(modname,info[\"name\"])\nunique=self.get_unique_and_increment()\nmods[name][\"namespaces\"][unique]=dict(namespace=namespace_uri,metadata=dict(first=info[\"first\"],last=info[\"last\"]),)\nself.namespace_map[namespace_uri]=unique\n<DEDENT><DEDENT>mods=self.add_superclasses(mods)\nreturnmods\n<DEDENT>", "hasArgs": false, "hasKwargs": false, "innerFunctions": [], "innerImports": [], "isGenerator": false, "moduleName": "builtin.Fasten", "modulePath": "builtin.Fasten.get_internal_modules", "name": "get_internal_modules", "otherBlockComments": [], "parameterCount": 1, "parameters": [{"location": {"column": 29, "line": 162}, "name": "self", "paramIndex": 0, "type": "<PERSON><PERSON>", "typeModule": "builtin.Fasten", "vid": "f8393ea147f6b750a263171c3ec05ad4"}], "returnNode": [{"rawReturn": "mods", "returnLine": {"column": 8, "line": 183}, "type": "Unknown"}], "slashIndex": -1, "starIndex": -1, "start": {"column": 4, "line": 162}, "vid": "9897436c3697010e18b5ca6318ae69f9"}, {"async": false, "callNodes": [{"end": {"column": 48, "line": 186}, "filePath": "builtin.py", "functionName": "items", "moduleName": "builtin.Fasten", "rawCode": "self.classes.items()", "sourceModulePath": "builtin.Fasten.classes", "start": {"column": 29, "line": 186}, "superCall": false, "vid": "cdc2b6360245d90f59dcefce611b1be0"}, {"arguments": ["parent"], "end": {"column": 42, "line": 195}, "filePath": "builtin.py", "functionName": "get", "moduleName": "builtin.Fasten", "rawCode": "self.classes.get(parent)", "sourceModulePath": "builtin.Fasten.classes", "start": {"column": 19, "line": 195}, "superCall": false, "vid": "f5821835aa0fa7737d4c233edfaa4e7f"}, {"arguments": ["parent_uri"], "end": {"column": 35, "line": 203}, "filePath": "builtin.py", "functionName": "append", "moduleName": "builtin.Fasten", "rawCode": "mods[self.to_uri(cls[\"module\"])][\"namespaces\"][cls_uri][\"metadata\"][\"superClasses\"].append(parent_uri)", "sourceModulePath": "Unknown", "start": {"column": 16, "line": 201}, "superCall": false, "vid": "e410e2e2303bc29d515339eb91e2089e"}, {"arguments": ["cls[\"module\"]"], "end": {"column": 46, "line": 201}, "filePath": "builtin.py", "functionName": "to_uri", "moduleName": "builtin.Fasten", "rawCode": "self.to_uri(cls[\"module\"])", "sourceModulePath": "builtin.Fasten", "start": {"column": 21, "line": 201}, "superCall": false, "vid": "2aefa729dbf12e4d778b0f2d9b6ec84c"}, {"arguments": ["cls[\"module\"]"], "end": {"column": 46, "line": 201}, "filePath": "builtin.py", "functionName": "to_uri", "moduleName": "builtin.Fasten", "rawCode": "self.to_uri(cls[\"module\"])", "sourceModulePath": "builtin.Fasten", "start": {"column": 21, "line": 201}, "superCall": false, "vid": "2aefa729dbf12e4d778b0f2d9b6ec84c"}], "changeType": "DEFAULT", "className": "<PERSON><PERSON>", "comments": [], "complexity": 7, "decorators": [], "end": {"column": 4, "line": 206}, "fileName": "builtin.py", "fullPath": "builtin.Fasten.add_superclasses", "funcBody": "defadd_superclasses(self,mods):\n<INDENT>forcls_name,clsinself.classes.items():\n<INDENT>cls_uri=self.namespace_map.get(self.to_uri(cls[\"module\"],cls_name))\nmods[self.to_uri(cls[\"module\"])][\"namespaces\"][cls_uri][\"metadata\"][\"superClasses\"]=[]\nforparentincls[\"mro\"]:\n<INDENT>ifparent==cls_name:\n<INDENT>continue\n<DEDENT>ifself.classes.get(parent):\n<INDENT>parent_uri=self.to_uri(self.classes[parent][\"module\"],parent)\n<DEDENT>else:\n<INDENT>parent_mod=parent.split(\".\")[0]\nparent_uri=self.to_external_uri(parent_mod,parent)\n<DEDENT>mods[self.to_uri(cls[\"module\"])][\"namespaces\"][cls_uri][\"metadata\"][\"superClasses\"].append(parent_uri)\n<DEDENT><DEDENT>returnmods\n<DEDENT>", "hasArgs": false, "hasKwargs": false, "innerFunctions": [], "innerImports": [], "isGenerator": false, "moduleName": "builtin.Fasten", "modulePath": "builtin.Fasten.add_superclasses", "name": "add_superclasses", "otherBlockComments": [], "parameterCount": 2, "parameters": [{"location": {"column": 25, "line": 185}, "name": "self", "paramIndex": 0, "type": "<PERSON><PERSON>", "typeModule": "builtin.Fasten", "vid": "f8393ea147f6b750a263171c3ec05ad4"}, {"location": {"column": 31, "line": 185}, "name": "mods", "paramIndex": 1, "typeModule": "Unknown", "vid": "afd614295cb3f8bf513b508b4963ab7b"}], "returnNode": [{"rawReturn": "mods", "returnLine": {"column": 8, "line": 205}, "type": "Unknown"}], "slashIndex": -1, "starIndex": -1, "start": {"column": 4, "line": 185}, "vid": "a3bbf9e50e47280806a922e93abc2b9f"}, {"async": false, "callNodes": [{"arguments": ["namespaces_maps", "[self.internal_mods,self.external_mods]"], "end": {"column": 86, "line": 209}, "filePath": "builtin.py", "functionName": "zip", "moduleName": "builtin.Fasten", "rawCode": "zip(namespaces_maps, [self.internal_mods,self.external_mods])", "sourceModulePath": "BUILTIN.zip", "start": {"column": 25, "line": 209}, "superCall": false, "vid": "0d6eb49251092e3e900b8790a6134c75"}], "changeType": "DEFAULT", "className": "<PERSON><PERSON>", "comments": [], "complexity": 4, "decorators": [], "end": {"column": 4, "line": 215}, "fileName": "builtin.py", "fullPath": "builtin.Fasten.create_namespaces_map", "funcBody": "defcreate_namespaces_map(self):\n<INDENT>namespaces_maps=[{},{}]\nforres,hmapinzip(namespaces_maps,[self.internal_mods,self.external_mods]):\n<INDENT>formodinhmap:\n<INDENT>fornamespaceinhmap[mod][\"methods\"]:\n<INDENT>res[namespace]=mod\n<DEDENT><DEDENT><DEDENT>returnnamespaces_maps\n<DEDENT>", "hasArgs": false, "hasKwargs": false, "innerFunctions": [], "innerImports": [], "isGenerator": false, "moduleName": "builtin.Fasten", "modulePath": "builtin.Fasten.create_namespaces_map", "name": "create_namespaces_map", "otherBlockComments": [], "parameterCount": 1, "parameters": [{"location": {"column": 30, "line": 207}, "name": "self", "paramIndex": 0, "type": "<PERSON><PERSON>", "typeModule": "builtin.Fasten", "vid": "f8393ea147f6b750a263171c3ec05ad4"}], "returnNode": [{"rawReturn": "namespaces_maps", "returnLine": {"column": 8, "line": 214}, "type": "Unknown"}], "slashIndex": -1, "starIndex": -1, "start": {"column": 4, "line": 207}, "vid": "6abab2702f755466dac13ca23e2de190"}, {"async": false, "callNodes": [{"end": {"column": 56, "line": 218}, "filePath": "builtin.py", "functionName": "items", "moduleName": "builtin.Fasten", "rawCode": "self.external_mods.items()", "sourceModulePath": "builtin.Fasten.external_mods", "start": {"column": 31, "line": 218}, "superCall": false, "vid": "aaee02f3af84627cbb717c6020080a94"}, {"end": {"column": 52, "line": 224}, "filePath": "builtin.py", "functionName": "items", "moduleName": "builtin.Fasten", "rawCode": "namespaces.items()", "sourceModulePath": "BUILTIN.str", "start": {"column": 35, "line": 224}, "superCall": false, "vid": "b0cc97386944b4414eafbe00a154613b"}], "changeType": "DEFAULT", "className": "<PERSON><PERSON>", "comments": [{"comment": "# We avoid saving the external module as external method", "start": {"column": 16, "line": 225}}], "complexity": 4, "decorators": [], "end": {"column": 4, "line": 235}, "fileName": "builtin.py", "fullPath": "builtin.Fasten.get_external_modules", "funcBody": "defget_external_modules(self):\n<INDENT>mods={}\nformodname,moduleinself.external_mods.items():\n<INDENT>name=self.to_external_uri(modname).split(\"/\")[2]\nnamespaces=module[\"methods\"]\nmods[name]={\"sourceFile\":\"\",\"namespaces\":{}}\nfornamespace,infoinnamespaces.items():\n<INDENT>ifinfo[\"name\"]!=modname:\n<INDENT>namespace_uri=self.to_external_uri(modname,info[\"name\"])\nunique=self.get_unique_and_increment()\nmods[name][\"namespaces\"][str(unique)]=dict(namespace=namespace_uri,metadata={})\nself.namespace_map[namespace_uri]=unique\n<DEDENT><DEDENT><DEDENT>returnmods\n<DEDENT>", "hasArgs": false, "hasKwargs": false, "innerFunctions": [], "innerImports": [], "isGenerator": false, "moduleName": "builtin.Fasten", "modulePath": "builtin.Fasten.get_external_modules", "name": "get_external_modules", "otherBlockComments": [], "parameterCount": 1, "parameters": [{"location": {"column": 29, "line": 216}, "name": "self", "paramIndex": 0, "type": "<PERSON><PERSON>", "typeModule": "builtin.Fasten", "vid": "f8393ea147f6b750a263171c3ec05ad4"}], "returnNode": [{"rawReturn": "mods", "returnLine": {"column": 8, "line": 234}, "type": "Unknown"}], "slashIndex": -1, "starIndex": -1, "start": {"column": 4, "line": 216}, "vid": "63b0ca5867b056b6167f4255e2d80ed8"}, {"async": false, "callNodes": [{"arguments": ["self.namespace_map.get(uri,uri)"], "end": {"column": 64, "line": 247}, "filePath": "builtin.py", "functionName": "append", "moduleName": "builtin.Fasten", "rawCode": "uris.append(self.namespace_map.get(uri,uri))", "sourceModulePath": "BUILTIN.list", "start": {"column": 20, "line": 247}, "superCall": false, "vid": "f99dbaed7f9fd3e9268bc7c31aeac616"}, {"arguments": ["uri", "uri"], "end": {"column": 63, "line": 247}, "filePath": "builtin.py", "functionName": "get", "moduleName": "builtin.Fasten", "rawCode": "self.namespace_map.get(uri, uri)", "sourceModulePath": "builtin.Fasten.namespace_map", "start": {"column": 32, "line": 247}, "superCall": false, "vid": "7a534aeb3049d44c403a6d6b64272767"}, {"arguments": ["uri", "uri"], "end": {"column": 63, "line": 247}, "filePath": "builtin.py", "functionName": "get", "moduleName": "builtin.Fasten", "rawCode": "self.namespace_map.get(uri, uri)", "sourceModulePath": "builtin.Fasten.namespace_map", "start": {"column": 32, "line": 247}, "superCall": false, "vid": "7a534aeb3049d44c403a6d6b64272767"}, {"arguments": ["self.namespace_map.get(self.to_external_uri(mod,node))"], "end": {"column": 87, "line": 250}, "filePath": "builtin.py", "functionName": "append", "moduleName": "builtin.Fasten", "rawCode": "uris.append(self.namespace_map.get(self.to_external_uri(mod,node)))", "sourceModulePath": "BUILTIN.list", "start": {"column": 20, "line": 250}, "superCall": false, "vid": "f99dbaed7f9fd3e9268bc7c31aeac616"}, {"arguments": ["self.to_external_uri(mod,node)"], "end": {"column": 86, "line": 250}, "filePath": "builtin.py", "functionName": "get", "moduleName": "builtin.Fasten", "rawCode": "self.namespace_map.get(self.to_external_uri(mod,node))", "sourceModulePath": "builtin.Fasten.namespace_map", "start": {"column": 32, "line": 250}, "superCall": false, "vid": "7a534aeb3049d44c403a6d6b64272767"}, {"arguments": ["mod", "node"], "end": {"column": 85, "line": 250}, "filePath": "builtin.py", "functionName": "to_external_uri", "moduleName": "builtin.Fasten", "rawCode": "self.to_external_uri(mod, node)", "sourceModulePath": "builtin.Fasten", "start": {"column": 55, "line": 250}, "superCall": false, "vid": "abc6b36e557eac6cc7d23bb573e2c922"}, {"arguments": ["self.to_external_uri(mod,node)"], "end": {"column": 86, "line": 250}, "filePath": "builtin.py", "functionName": "get", "moduleName": "builtin.Fasten", "rawCode": "self.namespace_map.get(self.to_external_uri(mod,node))", "sourceModulePath": "builtin.Fasten.namespace_map", "start": {"column": 32, "line": 250}, "superCall": false, "vid": "7a534aeb3049d44c403a6d6b64272767"}, {"arguments": ["mod", "node"], "end": {"column": 85, "line": 250}, "filePath": "builtin.py", "functionName": "to_external_uri", "moduleName": "builtin.Fasten", "rawCode": "self.to_external_uri(mod, node)", "sourceModulePath": "builtin.Fasten", "start": {"column": 55, "line": 250}, "superCall": false, "vid": "abc6b36e557eac6cc7d23bb573e2c922"}, {"arguments": ["mod", "node"], "end": {"column": 85, "line": 250}, "filePath": "builtin.py", "functionName": "to_external_uri", "moduleName": "builtin.Fasten", "rawCode": "self.to_external_uri(mod, node)", "sourceModulePath": "builtin.Fasten", "start": {"column": 55, "line": 250}, "superCall": false, "vid": "abc6b36e557eac6cc7d23bb573e2c922"}, {"arguments": ["uris"], "end": {"column": 23, "line": 252}, "filePath": "builtin.py", "functionName": "len", "moduleName": "builtin.Fasten", "rawCode": "len(uris)", "sourceModulePath": "BUILTIN.len", "start": {"column": 15, "line": 252}, "superCall": false, "vid": "********************************"}, {"arguments": ["[str(uris[0]),str(uris[1]),{}]"], "end": {"column": 82, "line": 254}, "filePath": "builtin.py", "functionName": "append", "moduleName": "builtin.Fasten", "rawCode": "graph[\"externalCalls\"].append([str(uris[0]),str(uris[1]),{}])", "sourceModulePath": "Unknown", "start": {"column": 20, "line": 254}, "superCall": false, "vid": "e410e2e2303bc29d515339eb91e2089e"}, {"arguments": ["uris[0]"], "end": {"column": 62, "line": 254}, "filePath": "builtin.py", "functionName": "str", "moduleName": "builtin.Fasten", "rawCode": "str(uris[0])", "sourceModulePath": "BUILTIN.str", "start": {"column": 51, "line": 254}, "superCall": false, "vid": "cad8cb7270823123fa45e726841933fb"}, {"arguments": ["uris[1]"], "end": {"column": 76, "line": 254}, "filePath": "builtin.py", "functionName": "str", "moduleName": "builtin.Fasten", "rawCode": "str(uris[1])", "sourceModulePath": "BUILTIN.str", "start": {"column": 65, "line": 254}, "superCall": false, "vid": "cad8cb7270823123fa45e726841933fb"}, {"arguments": ["uris[0]"], "end": {"column": 62, "line": 254}, "filePath": "builtin.py", "functionName": "str", "moduleName": "builtin.Fasten", "rawCode": "str(uris[0])", "sourceModulePath": "BUILTIN.str", "start": {"column": 51, "line": 254}, "superCall": false, "vid": "cad8cb7270823123fa45e726841933fb"}, {"arguments": ["uris[1]"], "end": {"column": 76, "line": 254}, "filePath": "builtin.py", "functionName": "str", "moduleName": "builtin.Fasten", "rawCode": "str(uris[1])", "sourceModulePath": "BUILTIN.str", "start": {"column": 65, "line": 254}, "superCall": false, "vid": "cad8cb7270823123fa45e726841933fb"}, {"arguments": ["[str(uris[0]),str(uris[1]),{}]"], "end": {"column": 82, "line": 256}, "filePath": "builtin.py", "functionName": "append", "moduleName": "builtin.Fasten", "rawCode": "graph[\"internalCalls\"].append([str(uris[0]),str(uris[1]),{}])", "sourceModulePath": "Unknown", "start": {"column": 20, "line": 256}, "superCall": false, "vid": "e410e2e2303bc29d515339eb91e2089e"}, {"arguments": ["uris[0]"], "end": {"column": 62, "line": 256}, "filePath": "builtin.py", "functionName": "str", "moduleName": "builtin.Fasten", "rawCode": "str(uris[0])", "sourceModulePath": "BUILTIN.str", "start": {"column": 51, "line": 256}, "superCall": false, "vid": "cad8cb7270823123fa45e726841933fb"}, {"arguments": ["uris[1]"], "end": {"column": 76, "line": 256}, "filePath": "builtin.py", "functionName": "str", "moduleName": "builtin.Fasten", "rawCode": "str(uris[1])", "sourceModulePath": "BUILTIN.str", "start": {"column": 65, "line": 256}, "superCall": false, "vid": "cad8cb7270823123fa45e726841933fb"}, {"arguments": ["uris[0]"], "end": {"column": 62, "line": 256}, "filePath": "builtin.py", "functionName": "str", "moduleName": "builtin.Fasten", "rawCode": "str(uris[0])", "sourceModulePath": "BUILTIN.str", "start": {"column": 51, "line": 256}, "superCall": false, "vid": "cad8cb7270823123fa45e726841933fb"}, {"arguments": ["uris[1]"], "end": {"column": 76, "line": 256}, "filePath": "builtin.py", "functionName": "str", "moduleName": "builtin.Fasten", "rawCode": "str(uris[1])", "sourceModulePath": "BUILTIN.str", "start": {"column": 65, "line": 256}, "superCall": false, "vid": "cad8cb7270823123fa45e726841933fb"}], "changeType": "DEFAULT", "className": "<PERSON><PERSON>", "comments": [], "complexity": 8, "decorators": [], "end": {"column": 4, "line": 258}, "fileName": "builtin.py", "fullPath": "builtin.Fasten.get_graph", "funcBody": "defget_graph(self):\n<INDENT>graph={\"internalCalls\":[],\"externalCalls\":[],\"resolvedCalls\":[]}\ninternal,external=self.create_namespaces_map()\nforsrc,dstinself.edges:\n<INDENT>uris=[]\nfornodein[src,dst]:\n<INDENT>ifnodeininternal:\n<INDENT>mod=internal[node]\nuri=self.to_uri(mod,node)\nuris.append(self.namespace_map.get(uri,uri))\n<DEDENT>elifnodeinexternal:\n<INDENT>mod=external[node]\nuris.append(self.namespace_map.get(self.to_external_uri(mod,node)))\n<DEDENT><DEDENT>iflen(uris)==2:\n<INDENT>ifdstinexternal:\n<INDENT>graph[\"externalCalls\"].append([str(uris[0]),str(uris[1]),{}])\n<DEDENT>else:\n<INDENT>graph[\"internalCalls\"].append([str(uris[0]),str(uris[1]),{}])\n<DEDENT><DEDENT><DEDENT>returngraph\n<DEDENT>", "hasArgs": false, "hasKwargs": false, "innerFunctions": [], "innerImports": [], "isGenerator": false, "moduleName": "builtin.Fasten", "modulePath": "builtin.Fasten.get_graph", "name": "get_graph", "otherBlockComments": [], "parameterCount": 1, "parameters": [{"location": {"column": 18, "line": 236}, "name": "self", "paramIndex": 0, "type": "<PERSON><PERSON>", "typeModule": "builtin.Fasten", "vid": "f8393ea147f6b750a263171c3ec05ad4"}], "returnNode": [{"rawReturn": "graph", "returnLine": {"column": 8, "line": 257}, "type": "Unknown"}], "slashIndex": -1, "starIndex": -1, "start": {"column": 4, "line": 236}, "vid": "5d2f8fb84b9bd3c17f165b7efc9ed282"}, {"async": false, "callNodes": [], "changeType": "DEFAULT", "className": "<PERSON><PERSON>", "comments": [], "complexity": 1, "decorators": [], "end": {"column": 0, "line": 273}, "fileName": "builtin.py", "fullPath": "builtin.Fasten.generate", "funcBody": "defgenerate(self):\n<INDENT>return{\"product\":self.product,\"forge\":self.forge,\"generator\":\"PyCG\",\"depset\":self.find_dependencies(self.package),\"version\":self.version,\"timestamp\":self.timestamp,\"modules\":{\"internal\":self.get_internal_modules(),\"external\":self.get_external_modules(),},\"graph\":self.get_graph(),\"nodes\":self.get_unique_and_increment(),}\n<DEDENT>", "hasArgs": false, "hasKwargs": false, "innerFunctions": [], "innerImports": [], "isGenerator": false, "moduleName": "builtin.Fasten", "modulePath": "builtin.Fasten.generate", "name": "generate", "otherBlockComments": [], "parameterCount": 1, "parameters": [{"location": {"column": 17, "line": 259}, "name": "self", "paramIndex": 0, "type": "<PERSON><PERSON>", "typeModule": "builtin.Fasten", "vid": "f8393ea147f6b750a263171c3ec05ad4"}], "returnNode": [{"rawReturn": "{\"product\":self.product,\"forge\":self.forge,\"generator\":\"PyCG\",\"depset\":self.find_dependencies(self.package),\"version\":self.version,\"timestamp\":self.timestamp,\"modules\":{\"internal\":self.get_internal_modules(),\"external\":self.get_external_modules(),},\"graph\":self.get_graph(),\"nodes\":self.get_unique_and_increment(),}", "returnLine": {"column": 8, "line": 260}, "type": "Unknown"}], "slashIndex": -1, "starIndex": -1, "start": {"column": 4, "line": 259}, "vid": "e18d92443becdb876830cd89ef6b4e14"}], "moduleName": "builtin", "modulePath": "builtin.Fasten", "name": "<PERSON><PERSON>", "otherBlockComments": [], "parameters": [], "start": {"column": -1, "line": 30}, "superClasses": [{"index": 0, "name": "BaseFormatter", "sourceModulePath": ".base.BaseFormatter"}], "vid": "3bcd184cf742f83d2d99b4e22ba10c9a"}], "comments": [{"comment": "#", "start": {"column": 0, "line": 1}}], "fileName": "builtin.py", "functionCallNodeMap": {}, "functionCallNodes": [], "functionNodeMap": {}, "functionNodes": [], "importNodeMap": {}, "importNodes": [{"aliases": {"os": "os"}, "currentModuleName": "builtin", "currentModulePath": "builtin", "fileName": "builtin.py", "fromModulePath": "os", "fullImportModulePath": "os", "importType": "IMPORT", "importedItems": ["os"], "location": {"column": 0, "line": 21}}, {"aliases": {"Requirement": "Requirement"}, "currentModuleName": "builtin", "currentModulePath": "builtin", "fileName": "builtin.py", "fromModulePath": "pkg_resources", "fullImportModulePath": "pkg_resources", "importType": "FROM", "importedItems": ["Requirement"], "location": {"column": 0, "line": 23}}, {"aliases": {"utils": "utils"}, "currentModuleName": "builtin", "currentModulePath": "builtin", "fileName": "builtin.py", "fromModulePath": "pycg", "fullImportModulePath": "pycg", "importType": "FROM", "importedItems": ["utils"], "location": {"column": 0, "line": 25}}, {"aliases": {"BaseFormatter": "BaseFormatter"}, "currentModuleName": "builtin", "currentModulePath": "builtin", "fileName": "builtin.py", "fromModulePath": ".base", "fullImportModulePath": ".base", "importType": "FROM", "importedItems": ["BaseFormatter"], "location": {"column": 0, "line": 27}}], "moduleName": "builtin", "moduleParamNodes": [], "modulePath": "builtin", "parentModule": "", "subModuleNodes": [], "vid": "388d33adeb7f579156ee4a0c86777760"}, "func_call": {"changeType": "DEFAULT", "classNodeMap": {}, "classNodes": [], "comments": [], "fileName": "func_call.py", "functionCallNodeMap": {}, "functionCallNodes": [{"arguments": ["'..'"], "end": {"column": 20, "line": 5}, "filePath": "func_call.py", "functionName": "sys.path.append", "moduleName": "func_call", "rawCode": "sys.path.append('..')", "sourceModulePath": "sys", "start": {"column": 0, "line": 5}, "superCall": false, "vid": "2c3434515aee9d32679d5763ab3cff51"}, {"end": {"column": 11, "line": 15}, "filePath": "func_call.py", "functionName": "<PERSON><PERSON>", "moduleName": "func_call", "rawCode": "Fasten()", "sourceModulePath": "Unknown", "start": {"column": 4, "line": 15}, "superCall": false, "vid": "cdd3e646222a7de8761101e28cd7b0aa"}, {"end": {"column": 27, "line": 17}, "filePath": "func_call.py", "functionName": "f.get_unique_and_increment", "moduleName": "func_call", "rawCode": "f.get_unique_and_increment()", "sourceModulePath": "f", "start": {"column": 0, "line": 17}, "superCall": false, "vid": "3d17c6c61bf99ed4c0f18e51e39941bc"}, {"end": {"column": 27, "line": 19}, "filePath": "func_call.py", "functionName": "f.to_external_uri", "moduleName": "func_call", "rawCode": "f.to_external_uri()", "sourceModulePath": "f", "start": {"column": 0, "line": 19}, "superCall": false, "vid": "71d1b6b847c86597524b54707ef503fb"}, {"end": {"$ref": "$.moduleMap.func\\_call.functionCallNodes[3].end"}, "filePath": "func_call.py", "functionName": "f.to_external_uri.length", "moduleName": "func_call", "rawCode": "f.to_external_uri().length()", "sourceModulePath": "f", "start": {"$ref": "$.moduleMap.func\\_call.functionCallNodes[3].start"}, "superCall": false, "vid": "e443337aa02a006dc5fb0b1da59aa624"}], "functionNodeMap": {}, "functionNodes": [{"async": false, "callNodes": [{"arguments": ["z"], "end": {"column": 11, "line": 12}, "filePath": "func_call.py", "functionName": "print", "moduleName": "func_call", "rawCode": "print(z)", "sourceModulePath": "BUILTIN", "start": {"column": 4, "line": 12}, "superCall": false, "vid": "bb68339553efab1d78e57ce1b74bd7cf"}, {"arguments": ["F\"x = {print(x)}\""], "end": {"column": 27, "line": 13}, "filePath": "func_call.py", "functionName": "print", "moduleName": "func_call", "rawCode": "print(F\"x = {print(x)}\")", "sourceModulePath": "BUILTIN", "start": {"column": 4, "line": 13}, "superCall": false, "vid": "bb68339553efab1d78e57ce1b74bd7cf"}, {"arguments": ["x"], "end": {"column": 24, "line": 13}, "filePath": "func_call.py", "functionName": "print", "moduleName": "func_call", "rawCode": "print(x)", "sourceModulePath": "BUILTIN", "start": {"column": 17, "line": 13}, "superCall": false, "vid": "bb68339553efab1d78e57ce1b74bd7cf"}, {"arguments": ["x"], "end": {"column": 24, "line": 13}, "filePath": "func_call.py", "functionName": "print", "moduleName": "func_call", "rawCode": "print(x)", "sourceModulePath": "BUILTIN", "start": {"column": 17, "line": 13}, "superCall": false, "vid": "bb68339553efab1d78e57ce1b74bd7cf"}], "changeType": "DEFAULT", "comments": [], "complexity": 1, "decorators": [], "end": {"column": 0, "line": 14}, "fileName": "func_call.py", "fullPath": "func_call.demo", "funcBody": "defdemo():\n<INDENT>x=1\ny=2\nz=x+y\nprint(z)\nprint(F\"x = {print(x)}\")\n<DEDENT>", "innerFunctions": [], "innerImports": [], "isGenerator": false, "moduleName": "func_call", "modulePath": "func_call.demo", "name": "demo", "otherBlockComments": [], "parameters": [], "returnNode": [], "slashIndex": -1, "starIndex": -1, "start": {"column": 0, "line": 7}, "vid": "837d375f8f4adaa45d1d48b7aa08b5f1"}], "importNodeMap": {}, "importNodes": [{"aliases": {"sys": "sys"}, "currentModuleName": "func_call", "currentModulePath": "func_call", "fileName": "func_call.py", "fromModulePath": "sys", "fullImportModulePath": "sys", "importType": "IMPORT", "importedItems": ["sys"], "location": {"column": 0, "line": 1}}, {"aliases": {"sub_module": "sub_module"}, "currentModuleName": "func_call", "currentModulePath": "func_call", "fileName": "func_call.py", "fromModulePath": "sub_module", "fullImportModulePath": "sub_module", "importType": "IMPORT", "importedItems": ["sub_module"], "location": {"column": 0, "line": 2}}, {"aliases": {"Fasten": "<PERSON><PERSON>"}, "currentModuleName": "func_call", "currentModulePath": "func_call", "fileName": "func_call.py", "fromModulePath": ".builtin", "fullImportModulePath": ".builtin", "importType": "FROM", "importedItems": ["<PERSON><PERSON>"], "location": {"column": 0, "line": 3}}], "moduleName": "func_call", "moduleParamNodes": [{"defaultValue": "Fasten()", "modulePath": "func_call", "name": "f", "vid": "467ad03a69330ba843661d0b4b9a2544"}], "modulePath": "func_call", "parentModule": "", "subModuleNodes": [], "vid": "e2188c60591392acbc3662a3438cd25b"}, "relative_import_test": {"changeType": "DEFAULT", "classNodeMap": {}, "classNodes": [], "comments": [{"comment": "# 测试相对导入", "start": {"column": 0, "line": 1}}, {"comment": "# 假设当前模块路径为: package.subpackage.relative_import_test", "start": {"column": 0, "line": 2}}, {"comment": "# 绝对导入", "start": {"column": 0, "line": 4}}, {"comment": "# 相对导入", "start": {"column": 0, "line": 8}}, {"comment": "# 同级模块: package.subpackage.sibling_module", "start": {"column": 38, "line": 9}}, {"comment": "# 同级模块的函数: package.subpackage.sibling_module.some_func", "start": {"column": 38, "line": 10}}, {"comment": "# 父级模块: package.parent_module", "start": {"column": 38, "line": 11}}, {"comment": "# 父级模块的类: package.parent_module.some_class", "start": {"column": 39, "line": 12}}, {"comment": "# 祖父级模块: grandparent.utils", "start": {"column": 38, "line": 13}}], "fileName": "relative_import_test.py", "functionCallNodeMap": {}, "functionCallNodes": [], "functionNodeMap": {}, "functionNodes": [], "importNodeMap": {}, "importNodes": [{"aliases": {"os": "os"}, "currentModuleName": "relative_import_test", "currentModulePath": "relative_import_test", "fileName": "relative_import_test.py", "fromModulePath": "os", "fullImportModulePath": "os", "importType": "IMPORT", "importedItems": ["os"], "location": {"column": 0, "line": 5}}, {"aliases": {"defaultdict": "defaultdict"}, "currentModuleName": "relative_import_test", "currentModulePath": "relative_import_test", "fileName": "relative_import_test.py", "fromModulePath": "collections", "fullImportModulePath": "collections", "importType": "FROM", "importedItems": ["defaultdict"], "location": {"column": 0, "line": 6}}, {"aliases": {"sibling_module": "sibling_module"}, "currentModuleName": "relative_import_test", "currentModulePath": "relative_import_test", "fileName": "relative_import_test.py", "fromModulePath": ".", "fullImportModulePath": ".", "importType": "FROM", "importedItems": ["sibling_module"], "location": {"column": 0, "line": 9}}, {"aliases": {"some_func": "some_func"}, "currentModuleName": "relative_import_test", "currentModulePath": "relative_import_test", "fileName": "relative_import_test.py", "fromModulePath": ".sibling_module", "fullImportModulePath": ".sibling_module", "importType": "FROM", "importedItems": ["some_func"], "location": {"column": 0, "line": 10}}, {"aliases": {"parent_module": "parent_module"}, "currentModuleName": "relative_import_test", "currentModulePath": "relative_import_test", "fileName": "relative_import_test.py", "fromModulePath": "..", "fullImportModulePath": "..", "importType": "FROM", "importedItems": ["parent_module"], "location": {"column": 0, "line": 11}}, {"aliases": {"some_class": "some_class"}, "currentModuleName": "relative_import_test", "currentModulePath": "relative_import_test", "fileName": "relative_import_test.py", "fromModulePath": "..parent_module", "fullImportModulePath": "..parent_module", "importType": "FROM", "importedItems": ["some_class"], "location": {"column": 0, "line": 12}}, {"aliases": {"utils": "utils"}, "currentModuleName": "relative_import_test", "currentModulePath": "relative_import_test", "fileName": "relative_import_test.py", "fromModulePath": "grandparent", "fullImportModulePath": "grandparent", "importType": "FROM", "importedItems": ["utils"], "location": {"column": 0, "line": 13}}], "moduleName": "relative_import_test", "moduleParamNodes": [], "modulePath": "relative_import_test", "parentModule": "", "subModuleNodes": [], "vid": "0130e0ce193278bdd1b6900daca27b5c"}, "sub_module.inner": {"changeType": "DEFAULT", "classNodeMap": {}, "classNodes": [], "comments": [], "fileName": "sub_module/inner.py", "functionCallNodeMap": {}, "functionCallNodes": [], "functionNodeMap": {}, "functionNodes": [], "importNodeMap": {}, "importNodes": [{"aliases": {"utils": "utils"}, "currentModuleName": "inner", "currentModulePath": "sub_module.inner", "fileName": "sub_module/inner.py", "fromModulePath": "sub_module.sub_utils", "fullImportModulePath": "sub_module.sub_utils", "importType": "FROM", "importedItems": ["utils"], "location": {"column": 0, "line": 1}}], "moduleName": "inner", "moduleParamNodes": [], "modulePath": "sub_module.inner", "parentModule": "sub_module", "subModuleNodes": [], "vid": "dc06af842761d7b0d45fbd71b4d7e955"}, "sub_module.sub_utils": {"changeType": "DEFAULT", "classNodeMap": {}, "classNodes": [], "comments": [], "fileName": "sub_module/sub_utils.py", "functionCallNodeMap": {}, "functionCallNodes": [], "functionNodeMap": {}, "functionNodes": [], "importNodeMap": {}, "importNodes": [], "moduleName": "sub_utils", "moduleParamNodes": [], "modulePath": "sub_module.sub_utils", "parentModule": "sub_module", "subModuleNodes": [], "vid": "c6d4be4ad25da00ff735eb8d2ead10aa"}, "test_expressions": {"changeType": "DEFAULT", "classNodeMap": {}, "classNodes": [], "comments": [{"comment": "# 测试不同类型的表达式", "start": {"column": 0, "line": 1}}, {"comment": "# 这些应该被 function_call 规则匹配", "start": {"column": 0, "line": 4}}, {"comment": "# 简单函数调用", "start": {"column": 26, "line": 5}}, {"comment": "# 方法调用", "start": {"column": 25, "line": 6}}, {"comment": "# 链式调用", "start": {"column": 25, "line": 7}}, {"comment": "# 索引后调用", "start": {"column": 25, "line": 8}}, {"comment": "# 这些应该被 primary 规则匹配，但不是 function_call", "start": {"column": 0, "line": 10}}, {"comment": "# 属性访问（没有调用）", "start": {"column": 25, "line": 11}}, {"comment": "# 索引访问（没有调用）", "start": {"column": 25, "line": 12}}, {"comment": "# 链式属性访问（没有调用）", "start": {"column": 25, "line": 13}}], "fileName": "test_expressions.py", "functionCallNodeMap": {}, "functionCallNodes": [{"end": {"column": 16, "line": 2}, "filePath": "test_expressions.py", "functionName": "SomeClass", "moduleName": "test_expressions", "rawCode": "SomeClass()", "sourceModulePath": "Unknown", "start": {"column": 6, "line": 2}, "superCall": false, "vid": "04e0149afc406fb77191a7624040ca9f"}, {"end": {"column": 5, "line": 5}, "filePath": "test_expressions.py", "functionName": "func", "moduleName": "test_expressions", "rawCode": "func()", "sourceModulePath": "Unknown", "start": {"column": 0, "line": 5}, "superCall": false, "vid": "aeae768492e773740505293315bf5337"}, {"end": {"column": 11, "line": 6}, "filePath": "test_expressions.py", "functionName": "obj.method", "moduleName": "test_expressions", "rawCode": "obj.method()", "sourceModulePath": "obj", "start": {"column": 0, "line": 6}, "superCall": false, "vid": "c9482f30305c09a2ee6d852bab3a5043"}, {"end": {"column": 22, "line": 7}, "filePath": "test_expressions.py", "functionName": "obj.method1", "moduleName": "test_expressions", "rawCode": "obj.method1()", "sourceModulePath": "obj", "start": {"column": 0, "line": 7}, "superCall": false, "vid": "c8d4197ea723ca50cbb202b95b61f6b7"}, {"end": {"$ref": "$.moduleMap.test\\_expressions.functionCallNodes[3].end"}, "filePath": "test_expressions.py", "functionName": "obj.method1.method2", "moduleName": "test_expressions", "rawCode": "obj.method1().method2()", "sourceModulePath": "obj", "start": {"$ref": "$.moduleMap.test\\_expressions.functionCallNodes[3].start"}, "superCall": false, "vid": "9993794cdb1e22ecf23da808a7aabac5"}, {"end": {"column": 7, "line": 8}, "filePath": "test_expressions.py", "functionName": "obj", "moduleName": "test_expressions", "rawCode": "obj[0]()", "sourceModulePath": "Unknown", "start": {"column": 0, "line": 8}, "superCall": false, "vid": "3b8736ed4bdd70974f421dff9b556a46"}], "functionNodeMap": {}, "functionNodes": [], "importNodeMap": {}, "importNodes": [], "moduleName": "test_expressions", "moduleParamNodes": [{"defaultValue": "SomeClass()", "modulePath": "test_expressions", "name": "obj", "vid": "71cedb4582f252e1fdcb411244df7ae4"}], "modulePath": "test_expressions", "parentModule": "", "subModuleNodes": [], "vid": "482ff3556651d20e61aea361d1aec25e"}, "utils": {"changeType": "DEFAULT", "classNodeMap": {}, "classNodes": [{"changeType": "DEFAULT", "classBody": "classStringMatcher(BaseModel):\n<INDENT>raw_pattern:str\ncase_sensitive:bool\nexact_match:bool=False\nvariable_names:List[str]=[]\npattern:Optional[re.Pattern]\n_GLOB_PATTERN=re.compile(r\"\\?|\\*|\\{((?:\\{[^/]+?\\}|[^/{}]|\\\\[{}])+?)\\}\")\n_DEFAULT_VARIABLE_PATTERN=\"((?s).*)\"\ndef__init__(self,pattern:str,case_sensitive:bool,**kwargs):\n<INDENT>super().__init__(raw_pattern=pattern,case_sensitive=case_sensitive,**kwargs)\npattern_builder=[]\nend=0\nformatchinre.finditer(self._GLOB_PATTERN,pattern):\n<INDENT>pattern_builder.append(re.escape(pattern[end:match.start()]))\nifmatch.group()==\"?\":\n<INDENT>pattern_builder.append(\".\")\n<DEDENT>elifmatch.group()==\"*\":\n<INDENT>pattern_builder.append(\".*\")\n<DEDENT>elifmatch.group().startswith(\"{\")andmatch.group().endswith(\"}\"):\n<INDENT>colon_idx=match.group().find(\":\")\nifcolon_idx==-1:\n<INDENT>pattern_builder.append(self._DEFAULT_VARIABLE_PATTERN)\nself.variable_names.append(match.group(1))\n<DEDENT>else:\n<INDENT>variable_pattern=match.group()[colon_idx+1:-1]\npattern_builder.append(F\"({variable_pattern})\")\nvariable_name=match.group()[1:colon_idx]\nself.variable_names.append(variable_name)\n<DEDENT><DEDENT>end=match.end()\n<DEDENT>ifend==0:\n<INDENT>self.exact_match=True\nself.pattern=None\n<DEDENT>else:\n<INDENT>self.exact_match=False\npattern_builder.append(re.escape(pattern[end:]))\nself.pattern=re.compile(\"\".join(pattern_builder),re.DOTALL|(re.NOFLAGifself.case_sensitiveelsere.IGNORECASE))\n<DEDENT><DEDENT>defmatch_strings(self,raw_str:str,uri_template_variables:Optional[Dict[str,str]])->bool:\n<INDENT>ifself.exact_match:\n<INDENT>returnself.raw_pattern==raw_strifself.case_sensitiveelseself.raw_pattern.upper()==raw_str.upper()\n<DEDENT>elifself.pattern:\n<INDENT>m=self.pattern.match(raw_str)\nifm:\n<INDENT>ifuri_template_variables:\n<INDENT>iflen(self.variable_names)!=len(m.groups()):\n<INDENT>raiseValueError(F\"The number of capturing groups in the pattern segment {self.pattern.pattern}\"\" does not match the number of URI template variables it defines, \"\"which can occur if capturing groups are used in a URI template regex. \"\"Use non-capturing groups instead.\")\n<DEDENT>fori,nameinenumerate(self.variable_names,1):\n<INDENT>ifname.startswith(\"*\"):\n<INDENT>raiseValueError(F\"Capturing patterns ({name}) are not \"\"supported by the AntPathMatcher. Use the PathPatternParser instead.\")\n<DEDENT>uri_template_variables[name]=m.group(i)\n<DEDENT><DEDENT>returnTrue\n<DEDENT><DEDENT>returnFalse\n<DEDENT><DEDENT>", "comments": [], "decorators": [], "end": {"column": -1, "line": 78}, "fileName": "utils.py", "fileVid": "04d2c9518498da64bfa9db44c6211645", "innerClasses": [], "innerImports": [], "methods": [{"async": false, "callNodes": [{"end": {"column": 85, "line": 19}, "filePath": "utils.py", "functionName": "super", "moduleName": "utils.StringMatcher", "rawCode": "super()", "sourceModulePath": "BUILTIN.super", "start": {"column": 8, "line": 19}, "superCall": false, "vid": "7c72467e6993382bcd570f7b84d9f6d9"}, {"arguments": ["raw_pattern=pattern", "case_sensitive=case_sensitive", "**kwargs"], "end": {"$ref": "$.moduleMap.utils.classNodes[0].methods[0].callNodes[0].end"}, "filePath": "utils.py", "functionName": "__init__", "moduleName": "utils.StringMatcher", "rawCode": "super().__init__(raw_pattern=pattern, case_sensitive=case_sensitive, **kwargs)", "sourceModulePath": "SUPER_CLASS_TO_BE_RESOLVED", "start": {"$ref": "$.moduleMap.utils.classNodes[0].methods[0].callNodes[0].start"}, "superCall": true, "superMethodName": "__init__", "vid": "06d5416ec18864d1286319a455967929"}, {"arguments": ["self._GLOB_PATTERN", "pattern"], "end": {"column": 60, "line": 22}, "filePath": "utils.py", "functionName": "finditer", "moduleName": "utils.StringMatcher", "rawCode": "re.finditer(self._GLOB_PATTERN, pattern)", "sourceModulePath": "re", "start": {"column": 21, "line": 22}, "superCall": false, "vid": "a7b8a7bb3d222d02180611200490c8d5"}, {"arguments": ["re.escape(pattern[end:match.start()])"], "end": {"column": 72, "line": 23}, "filePath": "utils.py", "functionName": "append", "moduleName": "utils.StringMatcher", "rawCode": "pattern_builder.append(re.escape(pattern[end:match.start()]))", "sourceModulePath": "BUILTIN.list", "start": {"column": 12, "line": 23}, "superCall": false, "vid": "f99dbaed7f9fd3e9268bc7c31aeac616"}, {"arguments": ["pattern[end:match.start()]"], "end": {"column": 71, "line": 23}, "filePath": "utils.py", "functionName": "escape", "moduleName": "utils.StringMatcher", "rawCode": "re.escape(pattern[end:match.start()])", "sourceModulePath": "re", "start": {"column": 35, "line": 23}, "superCall": false, "vid": "108b5e22249cf42f0aceee9867a9598b"}, {"end": {"column": 69, "line": 23}, "filePath": "utils.py", "functionName": "start", "moduleName": "utils.StringMatcher", "rawCode": "match.start()", "sourceModulePath": "Unknown", "start": {"column": 57, "line": 23}, "superCall": false, "vid": "7076d6ebc9d56b22efb529502f3bf457"}, {"arguments": ["pattern[end:match.start()]"], "end": {"column": 71, "line": 23}, "filePath": "utils.py", "functionName": "escape", "moduleName": "utils.StringMatcher", "rawCode": "re.escape(pattern[end:match.start()])", "sourceModulePath": "re", "start": {"column": 35, "line": 23}, "superCall": false, "vid": "108b5e22249cf42f0aceee9867a9598b"}, {"end": {"column": 69, "line": 23}, "filePath": "utils.py", "functionName": "start", "moduleName": "utils.StringMatcher", "rawCode": "match.start()", "sourceModulePath": "Unknown", "start": {"column": 57, "line": 23}, "superCall": false, "vid": "7076d6ebc9d56b22efb529502f3bf457"}, {"end": {"column": 69, "line": 23}, "filePath": "utils.py", "functionName": "start", "moduleName": "utils.StringMatcher", "rawCode": "match.start()", "sourceModulePath": "Unknown", "start": {"column": 57, "line": 23}, "superCall": false, "vid": "7076d6ebc9d56b22efb529502f3bf457"}, {"end": {"column": 27, "line": 24}, "filePath": "utils.py", "functionName": "group", "moduleName": "utils.StringMatcher", "rawCode": "match.group()", "sourceModulePath": "Unknown", "start": {"column": 15, "line": 24}, "superCall": false, "vid": "71fb20c28725e036dbe92f9d3bbdfd1d"}, {"arguments": ["\".\""], "end": {"column": 42, "line": 25}, "filePath": "utils.py", "functionName": "append", "moduleName": "utils.StringMatcher", "rawCode": "pattern_builder.append(\".\")", "sourceModulePath": "BUILTIN.list", "start": {"column": 16, "line": 25}, "superCall": false, "vid": "f99dbaed7f9fd3e9268bc7c31aeac616"}, {"end": {"column": 29, "line": 26}, "filePath": "utils.py", "functionName": "group", "moduleName": "utils.StringMatcher", "rawCode": "match.group()", "sourceModulePath": "Unknown", "start": {"column": 17, "line": 26}, "superCall": false, "vid": "71fb20c28725e036dbe92f9d3bbdfd1d"}, {"arguments": ["\".*\""], "end": {"column": 43, "line": 27}, "filePath": "utils.py", "functionName": "append", "moduleName": "utils.StringMatcher", "rawCode": "pattern_builder.append(\".*\")", "sourceModulePath": "BUILTIN.list", "start": {"column": 16, "line": 27}, "superCall": false, "vid": "f99dbaed7f9fd3e9268bc7c31aeac616"}, {"end": {"column": 45, "line": 28}, "filePath": "utils.py", "functionName": "group", "moduleName": "utils.StringMatcher", "rawCode": "match.group()", "sourceModulePath": "Unknown", "start": {"column": 17, "line": 28}, "superCall": false, "vid": "71fb20c28725e036dbe92f9d3bbdfd1d"}, {"arguments": ["\"{\""], "end": {"$ref": "$.moduleMap.utils.classNodes[0].methods[0].callNodes[13].end"}, "filePath": "utils.py", "functionName": "startswith", "moduleName": "utils.StringMatcher", "rawCode": "match.group().startswith(\"{\")", "sourceModulePath": "Unknown", "start": {"$ref": "$.moduleMap.utils.classNodes[0].methods[0].callNodes[13].start"}, "superCall": false, "vid": "d5c92ccb662f5ecb4834d7f389856277"}, {"end": {"column": 77, "line": 28}, "filePath": "utils.py", "functionName": "group", "moduleName": "utils.StringMatcher", "rawCode": "match.group()", "sourceModulePath": "Unknown", "start": {"column": 51, "line": 28}, "superCall": false, "vid": "71fb20c28725e036dbe92f9d3bbdfd1d"}, {"arguments": ["\"}\""], "end": {"$ref": "$.moduleMap.utils.classNodes[0].methods[0].callNodes[15].end"}, "filePath": "utils.py", "functionName": "endswith", "moduleName": "utils.StringMatcher", "rawCode": "match.group().endswith(\"}\")", "sourceModulePath": "Unknown", "start": {"$ref": "$.moduleMap.utils.classNodes[0].methods[0].callNodes[15].start"}, "superCall": false, "vid": "b8a24ea3f019eb9f37b1b41756c0ac74"}, {"arguments": ["self._DEFAULT_VARIABLE_PATTERN"], "end": {"column": 73, "line": 31}, "filePath": "utils.py", "functionName": "append", "moduleName": "utils.StringMatcher", "rawCode": "pattern_builder.append(self._DEFAULT_VARIABLE_PATTERN)", "sourceModulePath": "BUILTIN.list", "start": {"column": 20, "line": 31}, "superCall": false, "vid": "f99dbaed7f9fd3e9268bc7c31aeac616"}, {"arguments": ["match.group(1)"], "end": {"column": 61, "line": 32}, "filePath": "utils.py", "functionName": "append", "moduleName": "utils.StringMatcher", "rawCode": "self.variable_names.append(match.group(1))", "sourceModulePath": "utils.StringMatcher.variable_names", "start": {"column": 20, "line": 32}, "superCall": false, "vid": "6bc98a4282b55aa660e51f3d03bc1c1f"}, {"arguments": ["1"], "end": {"column": 60, "line": 32}, "filePath": "utils.py", "functionName": "group", "moduleName": "utils.StringMatcher", "rawCode": "match.group(1)", "sourceModulePath": "Unknown", "start": {"column": 47, "line": 32}, "superCall": false, "vid": "71fb20c28725e036dbe92f9d3bbdfd1d"}, {"arguments": ["1"], "end": {"column": 60, "line": 32}, "filePath": "utils.py", "functionName": "group", "moduleName": "utils.StringMatcher", "rawCode": "match.group(1)", "sourceModulePath": "Unknown", "start": {"column": 47, "line": 32}, "superCall": false, "vid": "71fb20c28725e036dbe92f9d3bbdfd1d"}, {"arguments": ["F\"({variable_pattern})\""], "end": {"column": 66, "line": 35}, "filePath": "utils.py", "functionName": "append", "moduleName": "utils.StringMatcher", "rawCode": "pattern_builder.append(F\"({variable_pattern})\")", "sourceModulePath": "BUILTIN.list", "start": {"column": 20, "line": 35}, "superCall": false, "vid": "f99dbaed7f9fd3e9268bc7c31aeac616"}, {"arguments": ["variable_name"], "end": {"column": 60, "line": 37}, "filePath": "utils.py", "functionName": "append", "moduleName": "utils.StringMatcher", "rawCode": "self.variable_names.append(variable_name)", "sourceModulePath": "utils.StringMatcher.variable_names", "start": {"column": 20, "line": 37}, "superCall": false, "vid": "6bc98a4282b55aa660e51f3d03bc1c1f"}, {"arguments": ["re.escape(pattern[end:])"], "end": {"column": 59, "line": 44}, "filePath": "utils.py", "functionName": "append", "moduleName": "utils.StringMatcher", "rawCode": "pattern_builder.append(re.escape(pattern[end:]))", "sourceModulePath": "BUILTIN.list", "start": {"column": 12, "line": 44}, "superCall": false, "vid": "f99dbaed7f9fd3e9268bc7c31aeac616"}, {"arguments": ["pattern[end:]"], "end": {"column": 58, "line": 44}, "filePath": "utils.py", "functionName": "escape", "moduleName": "utils.StringMatcher", "rawCode": "re.escape(pattern[end:])", "sourceModulePath": "re", "start": {"column": 35, "line": 44}, "superCall": false, "vid": "108b5e22249cf42f0aceee9867a9598b"}, {"arguments": ["pattern[end:]"], "end": {"column": 58, "line": 44}, "filePath": "utils.py", "functionName": "escape", "moduleName": "utils.StringMatcher", "rawCode": "re.escape(pattern[end:])", "sourceModulePath": "re", "start": {"column": 35, "line": 44}, "superCall": false, "vid": "108b5e22249cf42f0aceee9867a9598b"}], "changeType": "DEFAULT", "className": "StringMatcher", "comments": [], "complexity": 10, "decorators": [], "end": {"column": 4, "line": 49}, "fileName": "utils.py", "fullPath": "utils.StringMatcher.__init__", "funcBody": "def__init__(self,pattern:str,case_sensitive:bool,**kwargs):\n<INDENT>super().__init__(raw_pattern=pattern,case_sensitive=case_sensitive,**kwargs)\npattern_builder=[]\nend=0\nformatchinre.finditer(self._GLOB_PATTERN,pattern):\n<INDENT>pattern_builder.append(re.escape(pattern[end:match.start()]))\nifmatch.group()==\"?\":\n<INDENT>pattern_builder.append(\".\")\n<DEDENT>elifmatch.group()==\"*\":\n<INDENT>pattern_builder.append(\".*\")\n<DEDENT>elifmatch.group().startswith(\"{\")andmatch.group().endswith(\"}\"):\n<INDENT>colon_idx=match.group().find(\":\")\nifcolon_idx==-1:\n<INDENT>pattern_builder.append(self._DEFAULT_VARIABLE_PATTERN)\nself.variable_names.append(match.group(1))\n<DEDENT>else:\n<INDENT>variable_pattern=match.group()[colon_idx+1:-1]\npattern_builder.append(F\"({variable_pattern})\")\nvariable_name=match.group()[1:colon_idx]\nself.variable_names.append(variable_name)\n<DEDENT><DEDENT>end=match.end()\n<DEDENT>ifend==0:\n<INDENT>self.exact_match=True\nself.pattern=None\n<DEDENT>else:\n<INDENT>self.exact_match=False\npattern_builder.append(re.escape(pattern[end:]))\nself.pattern=re.compile(\"\".join(pattern_builder),re.DOTALL|(re.NOFLAGifself.case_sensitiveelsere.IGNORECASE))\n<DEDENT><DEDENT>", "hasArgs": false, "hasKwargs": true, "innerFunctions": [], "innerImports": [], "isGenerator": false, "moduleName": "utils.StringMatcher", "modulePath": "utils.StringMatcher.__init__", "name": "__init__", "otherBlockComments": [], "parameterCount": 4, "parameters": [{"location": {"column": 17, "line": 18}, "name": "self", "paramIndex": 0, "type": "StringMatcher", "typeModule": "utils.StringMatcher", "vid": "f8393ea147f6b750a263171c3ec05ad4"}, {"location": {"column": 23, "line": 18}, "name": "pattern", "paramIndex": 1, "type": "str", "typeModule": "BUILTIN.str", "vid": "2a1321c343a36542b73339cb78350ba5"}, {"location": {"column": 37, "line": 18}, "name": "case_sensitive", "paramIndex": 2, "type": "bool", "typeModule": "BUILTIN.bool", "vid": "294bbfce9a8efa37aa105485b6047221"}, {"location": {"column": 61, "line": 18}, "name": "**kwargs", "paramIndex": 0, "typeModule": "Unknown", "vid": "0e55870514de66b9ca18d7ba22c43c06"}], "returnNode": [], "slashIndex": -1, "starIndex": -1, "start": {"column": 4, "line": 18}, "vid": "b3e0a269083ca0ae663d4e9964cae45c"}, {"async": false, "callNodes": [{"arguments": ["self.variable_names"], "end": {"column": 46, "line": 61}, "filePath": "utils.py", "functionName": "len", "moduleName": "utils.StringMatcher", "rawCode": "len(self.variable_names)", "sourceModulePath": "BUILTIN.len", "start": {"column": 23, "line": 61}, "superCall": false, "vid": "********************************"}, {"arguments": ["m.groups()"], "end": {"column": 65, "line": 61}, "filePath": "utils.py", "functionName": "len", "moduleName": "utils.StringMatcher", "rawCode": "len(m.groups())", "sourceModulePath": "BUILTIN.len", "start": {"column": 51, "line": 61}, "superCall": false, "vid": "********************************"}, {"end": {"column": 64, "line": 61}, "filePath": "utils.py", "functionName": "groups", "moduleName": "utils.StringMatcher", "rawCode": "m.groups()", "sourceModulePath": "utils.StringMatcher.m", "start": {"column": 55, "line": 61}, "superCall": false, "vid": "c4b425acb69d6e163d255704f70646ee"}, {"end": {"column": 64, "line": 61}, "filePath": "utils.py", "functionName": "groups", "moduleName": "utils.StringMatcher", "rawCode": "m.groups()", "sourceModulePath": "utils.StringMatcher.m", "start": {"column": 55, "line": 61}, "superCall": false, "vid": "c4b425acb69d6e163d255704f70646ee"}, {"arguments": ["F\"The number of capturing groups in the pattern segment {self.pattern.pattern}\"\" does not match the number of URI template variables it defines, \"\"which can occur if capturing groups are used in a URI template regex. \"\"Use non-capturing groups instead.\""], "end": {"column": 24, "line": 67}, "filePath": "utils.py", "functionName": "ValueError", "moduleName": "utils.StringMatcher", "rawCode": "ValueError(F\"The number of capturing groups in the pattern segment {self.pattern.pattern}\"\" does not match the number of URI template variables it defines, \"\"which can occur if capturing groups are used in a URI template regex. \"\"Use non-capturing groups instead.\")", "sourceModulePath": "BUILTIN_EXCEPTION.ValueError", "start": {"column": 30, "line": 62}, "superCall": false, "vid": "226d03d79f53194e9bb3da4dadf714ee"}, {"arguments": ["self.variable_names", "1"], "end": {"column": 67, "line": 68}, "filePath": "utils.py", "functionName": "enumerate", "moduleName": "utils.StringMatcher", "rawCode": "enumerate(self.variable_names, 1)", "sourceModulePath": "BUILTIN.enumerate", "start": {"column": 35, "line": 68}, "superCall": false, "vid": "33fbdb0e4b989f67d1e8628434c6f4a5"}, {"arguments": ["\"*\""], "end": {"column": 46, "line": 69}, "filePath": "utils.py", "functionName": "startswith", "moduleName": "utils.StringMatcher", "rawCode": "name.startswith(\"*\")", "sourceModulePath": "Unknown", "start": {"column": 27, "line": 69}, "superCall": false, "vid": "d5c92ccb662f5ecb4834d7f389856277"}, {"arguments": ["F\"Capturing patterns ({name}) are not \"\"supported by the AntPathMatcher. Use the PathPatternParser instead.\""], "end": {"column": 28, "line": 73}, "filePath": "utils.py", "functionName": "ValueError", "moduleName": "utils.StringMatcher", "rawCode": "ValueError(F\"Capturing patterns ({name}) are not \"\"supported by the AntPathMatcher. Use the PathPatternParser instead.\")", "sourceModulePath": "BUILTIN_EXCEPTION.ValueError", "start": {"column": 34, "line": 70}, "superCall": false, "vid": "226d03d79f53194e9bb3da4dadf714ee"}], "changeType": "DEFAULT", "className": "StringMatcher", "comments": [], "complexity": 8, "decorators": [], "end": {"column": 0, "line": 78}, "fileName": "utils.py", "fullPath": "utils.StringMatcher.match_strings", "funcBody": "defmatch_strings(self,raw_str:str,uri_template_variables:Optional[Dict[str,str]])->bool:\n<INDENT>ifself.exact_match:\n<INDENT>returnself.raw_pattern==raw_strifself.case_sensitiveelseself.raw_pattern.upper()==raw_str.upper()\n<DEDENT>elifself.pattern:\n<INDENT>m=self.pattern.match(raw_str)\nifm:\n<INDENT>ifuri_template_variables:\n<INDENT>iflen(self.variable_names)!=len(m.groups()):\n<INDENT>raiseValueError(F\"The number of capturing groups in the pattern segment {self.pattern.pattern}\"\" does not match the number of URI template variables it defines, \"\"which can occur if capturing groups are used in a URI template regex. \"\"Use non-capturing groups instead.\")\n<DEDENT>fori,nameinenumerate(self.variable_names,1):\n<INDENT>ifname.startswith(\"*\"):\n<INDENT>raiseValueError(F\"Capturing patterns ({name}) are not \"\"supported by the AntPathMatcher. Use the PathPatternParser instead.\")\n<DEDENT>uri_template_variables[name]=m.group(i)\n<DEDENT><DEDENT>returnTrue\n<DEDENT><DEDENT>returnFalse\n<DEDENT>", "functionReturnType": "bool", "hasArgs": false, "hasKwargs": false, "innerFunctions": [], "innerImports": [], "isGenerator": false, "moduleName": "utils.StringMatcher", "modulePath": "utils.StringMatcher.match_strings", "name": "match_strings", "otherBlockComments": [], "parameterCount": 3, "parameters": [{"location": {"column": 12, "line": 51}, "name": "self", "paramIndex": 0, "type": "StringMatcher", "typeModule": "utils.StringMatcher", "vid": "f8393ea147f6b750a263171c3ec05ad4"}, {"location": {"column": 12, "line": 52}, "name": "raw_str", "paramIndex": 1, "type": "str", "typeModule": "BUILTIN.str", "vid": "44edb86fa318c68df87190f8dbdbd5b2"}, {"location": {"column": 12, "line": 53}, "name": "uri_template_variables", "paramIndex": 2, "type": "Optional[Dict[str,str]]", "typeModule": "Unknown", "vid": "5b30132cc50b6a7e97612df65354c271"}], "returnNode": [{"rawReturn": "self.raw_pattern==raw_strifself.case_sensitiveelseself.raw_pattern.upper()==raw_str.upper()", "returnLine": {"column": 12, "line": 56}, "type": "Unknown"}, {"rawReturn": "True", "returnLine": {"column": 16, "line": 75}, "type": "Unknown"}, {"rawReturn": "False", "returnLine": {"column": 8, "line": 76}, "type": "Unknown"}], "slashIndex": -1, "starIndex": -1, "start": {"column": 4, "line": 50}, "vid": "34669ea73d71c5ea83395068a115cd1c"}], "moduleName": "utils", "modulePath": "utils.StringMatcher", "name": "StringMatcher", "otherBlockComments": [], "parameters": [{"name": "raw_pattern", "type": "str", "vid": "12e8e0baaa82920a93aa88ee7296abff"}, {"name": "case_sensitive", "type": "bool", "vid": "294bbfce9a8efa37aa105485b6047221"}, {"defaultValue": "False", "name": "exact_match", "type": "bool", "vid": "af1a12fe45109438a11e01133d4bea6c"}, {"defaultValue": "[]", "name": "variable_names", "type": "List", "vid": "3591fcfddaa372f7084b5ed3044f814c"}, {"name": "pattern", "type": "Optional", "vid": "2a1321c343a36542b73339cb78350ba5"}, {"defaultValue": "re.compile(r\"\\?|\\*|\\{((?:\\{[^/]+?\\}|[^/{}]|\\\\[{}])+?)\\}\")", "name": "_GLOB_PATTERN", "vid": "02b6d7b7527fcbf9e9d135416a04342d"}, {"defaultValue": "((?s).*)", "name": "_DEFAULT_VARIABLE_PATTERN", "vid": "8fc378d0644c1e70a24b89711fb57bb5"}], "start": {"column": -1, "line": 8}, "superClasses": [{"index": 0, "name": "BaseModel", "sourceModulePath": "pydantic.BaseModel"}], "vid": "1fc038b031bae20a2556f89dc8fb5c84"}, {"changeType": "DEFAULT", "classBody": "classAntPathMatcher(BaseModel):\n<INDENT>path_separator:str=\"/\"\ncase_sensitive:bool=False\ntrim_tokens:bool=False\ncache_patterns:bool=True\ntokenized_pattern_cache:Dict[str,List[str]]=defaultdict(list)\nstring_matcher_cache:Dict[str,StringMatcher]=dict()\n_WILDCARD_CHARS={'*','?','{'}\ndefdo_match(self,pattern,path=None,full_match=False,uri_template_variables=None):\n<INDENT>ifnotpathorpath.startswith(self.path_separator)!=pattern.startswith(self.path_separator):\n<INDENT>returnFalse\n<DEDENT>patt_dirs=self.tokenize_pattern(pattern)\niffull_matchandself.case_sensitiveandnotself.is_potential_match(path,patt_dirs):\n<INDENT>returnFalse\n<DEDENT>path_dirs=self.tokenize_path(path)\npatt_idx_start=0\npatt_idx_end=len(patt_dirs)-1\npath_idx_start=0\npath_idx_end=len(path_dirs)-1\nwhilepatt_idx_start<=patt_idx_endandpath_idx_start<=path_idx_end:\n<INDENT>patt_dir=patt_dirs[patt_idx_start]\nifpatt_dir==\"**\":\n<INDENT>break\n<DEDENT>ifnotself.match_strings(patt_dir,path_dirs[path_idx_start],uri_template_variables):\n<INDENT>returnFalse\n<DEDENT>patt_idx_start+=1\npath_idx_start+=1\n<DEDENT>ifpath_idx_start>path_idx_end:\n<INDENT>ifpatt_idx_start>patt_idx_end:\n<INDENT>returnpattern.endswith(self.path_separator)==path.endswith(self.path_separator)\n<DEDENT>elifnotfull_match:\n<INDENT>returnTrue\n<DEDENT>elifpatt_idx_start==patt_idx_endandpatt_dirs[patt_idx_start]==\"*\"andpath.endswith(self.path_separator):\n<INDENT>returnTrue\n<DEDENT>else:\n<INDENT>forpat_idx_tmpinrange(patt_idx_start,patt_idx_end+1):\n<INDENT>ifpatt_dirs[pat_idx_tmp]!=\"**\":\n<INDENT>returnFalse\n<DEDENT><DEDENT>returnTrue\n<DEDENT><DEDENT>elifpatt_idx_start>patt_idx_end:\n<INDENT>returnFalse\n<DEDENT>elifnotfull_matchandpatt_dirs[patt_idx_start]==\"**\":\n<INDENT>returnTrue\n<DEDENT>whilepatt_idx_start<=patt_idx_endandpath_idx_start<=path_idx_end:\n<INDENT>patt_dir=patt_dirs[patt_idx_end]\nifpatt_dir==\"**\":\n<INDENT>break\n<DEDENT>ifnotself.match_strings(patt_dir,path_dirs[path_idx_end],uri_template_variables):\n<INDENT>returnFalse\n<DEDENT>patt_idx_end-=1\npath_idx_end-=1\n<DEDENT>ifpath_idx_start>path_idx_end:\n<INDENT>forpat_idx_tmpinrange(patt_idx_start,patt_idx_end+1):\n<INDENT>ifpatt_dirs[pat_idx_tmp]!=\"**\":\n<INDENT>returnFalse\n<DEDENT><DEDENT>returnTrue\n<DEDENT>whilepatt_idx_start!=patt_idx_endandpath_idx_start<=path_idx_end:\n<INDENT>pat_idx_tmp=-1\nforpat_lengthinrange(patt_idx_start+1,patt_idx_end+1):\n<INDENT>ifpatt_dirs[pat_length]==\"**\":\n<INDENT>pat_idx_tmp=pat_length\nbreak\n<DEDENT><DEDENT>ifpat_idx_tmp==patt_idx_start+1:\n<INDENT>patt_idx_start+=1\n<DEDENT>else:\n<INDENT>pat_length=pat_idx_tmp-patt_idx_start-1\nstr_length=path_idx_end-path_idx_start+1\nfound_idx=-1\nforiinrange(0,str_length-pat_length+1):\n<INDENT>forjinrange(pat_length):\n<INDENT>sub_pat=patt_dirs[patt_idx_start+j+1]\nsub_str=path_dirs[path_idx_start+i+j]\nifnotself.match_strings(sub_pat,sub_str,uri_template_variables):\n<INDENT>break\n<DEDENT><DEDENT>else:\n<INDENT>found_idx=path_idx_start+i\nbreak\n<DEDENT><DEDENT>iffound_idx==-1:\n<INDENT>returnFalse\n<DEDENT>patt_idx_start=pat_idx_tmp\npath_idx_start=found_idx+pat_length\n<DEDENT><DEDENT>forpat_idx_tmpinrange(patt_idx_start,patt_idx_end+1):\n<INDENT>ifpatt_dirs[pat_idx_tmp]!=\"**\":\n<INDENT>returnFalse\n<DEDENT><DEDENT>returnTrue\n<DEDENT>deftokenize_pattern(self,pattern:str)->List[str]:\n<INDENT>ifself.cache_patternsandpatterninself.tokenized_pattern_cacheand(r:=self.tokenized_pattern_cache.get(pattern)):\n<INDENT>returnr\n<DEDENT>result=self.tokenize_path(pattern)\nself.tokenized_pattern_cache[pattern]=result\nreturnresult\n<DEDENT>defmatch(self,pattern:str,path:str)->bool:\n<INDENT>returnself.do_match(pattern,path,True)\n<DEDENT>defmatch_start(self,pattern:str,path:str)->bool:\n<INDENT>returnself.do_match(pattern,path,False)\n<DEDENT>defis_potential_match(self,path,patt_dirs):\n<INDENT>ifnotself.trim_tokens:\n<INDENT>pos=0\nforpatt_dirinpatt_dirs:\n<INDENT>skipped=self._skip_separator(path,pos,self.path_separator)\npos+=skipped\nskipped=self._skip_segment(path,pos,patt_dir)\nifskipped<len(patt_dir):\n<INDENT>returnskipped>0or(len(patt_dir)>0andpatt_dir[0]inAntPathMatcher._WILDCARD_CHARS)\n<DEDENT>pos+=skipped\n<DEDENT><DEDENT>returnTrue\n<DEDENT>deftokenize_path(self,path):\n<INDENT>return[self.trim_tokensand_.strip()or_for_inpath.split(self.path_separator)if_]\n<DEDENT>defmatch_strings(self,pattern,raw_str,uri_template_variables):\n<INDENT>returnself.get_string_matcher(pattern).match_strings(raw_str,uri_template_variables)\n<DEDENT>@staticmethod\ndef_skip_separator(path,pos,path_separator):\n<INDENT>skipped=0\nwhilepath.startswith(path_separator,pos+skipped):\n<INDENT>skipped+=len(path_separator)\n<DEDENT>returnskipped\n<DEDENT>@staticmethod\ndef_skip_segment(path,pos,patt_dir):\n<INDENT>skipped=0\nforiinrange(len(patt_dir)):\n<INDENT>c=patt_dir[i]\nifcinAntPathMatcher._WILDCARD_CHARS:\n<INDENT>returnskipped\n<DEDENT>curr_pos=pos+skipped\nifcurr_pos>=len(path):\n<INDENT>return0\n<DEDENT>ifc==path[curr_pos]:\n<INDENT>skipped+=1\n<DEDENT><DEDENT>returnskipped\n<DEDENT>defget_string_matcher(self,pattern)->StringMatcher:\n<INDENT>ifself.cache_patternsandpatterninself.string_matcher_cacheand(r:=self.string_matcher_cache.get(pattern)):\n<INDENT>returnr\n<DEDENT>str_matcher=StringMatcher(pattern=pattern,case_sensitive=self.case_sensitive)\nself.string_matcher_cache[pattern]=str_matcher\nreturnstr_matcher\n<DEDENT><DEDENT>", "comments": [], "decorators": [], "end": {"column": -1, "line": 245}, "fileName": "utils.py", "fileVid": "04d2c9518498da64bfa9db44c6211645", "innerClasses": [], "innerImports": [], "methods": [{"async": false, "callNodes": [{"arguments": ["self.path_separator"], "end": {"column": 58, "line": 91}, "filePath": "utils.py", "functionName": "startswith", "moduleName": "utils.AntPathMatcher", "rawCode": "path.startswith(self.path_separator)", "sourceModulePath": "Unknown", "start": {"column": 23, "line": 91}, "superCall": false, "vid": "d5c92ccb662f5ecb4834d7f389856277"}, {"arguments": ["self.path_separator"], "end": {"column": 101, "line": 91}, "filePath": "utils.py", "functionName": "startswith", "moduleName": "utils.AntPathMatcher", "rawCode": "pattern.startswith(self.path_separator)", "sourceModulePath": "Unknown", "start": {"column": 63, "line": 91}, "superCall": false, "vid": "d5c92ccb662f5ecb4834d7f389856277"}, {"arguments": ["path", "patt_dirs"], "end": {"column": 93, "line": 95}, "filePath": "utils.py", "functionName": "is_potential_match", "moduleName": "utils.AntPathMatcher", "rawCode": "self.is_potential_match(path, patt_dirs)", "sourceModulePath": "utils.AntPathMatcher", "start": {"column": 54, "line": 95}, "superCall": false, "vid": "cdf22eacd1e4d44d9a63df0d33d73b53"}, {"arguments": ["patt_dir", "path_dirs[path_idx_start]", "uri_template_variables"], "end": {"column": 97, "line": 108}, "filePath": "utils.py", "functionName": "match_strings", "moduleName": "utils.AntPathMatcher", "rawCode": "self.match_strings(patt_dir, path_dirs[path_idx_start], uri_template_variables)", "sourceModulePath": "utils.AntPathMatcher", "start": {"column": 19, "line": 108}, "superCall": false, "vid": "0329984edbbeac213d93f1d60c110be8"}, {"arguments": ["self.path_separator"], "end": {"column": 39, "line": 119}, "filePath": "utils.py", "functionName": "endswith", "moduleName": "utils.AntPathMatcher", "rawCode": "path.endswith(self.path_separator)", "sourceModulePath": "Unknown", "start": {"column": 89, "line": 118}, "superCall": false, "vid": "b8a24ea3f019eb9f37b1b41756c0ac74"}, {"arguments": ["patt_idx_start", "patt_idx_end+1"], "end": {"column": 73, "line": 122}, "filePath": "utils.py", "functionName": "range", "moduleName": "utils.AntPathMatcher", "rawCode": "range(patt_idx_start, patt_idx_end+1)", "sourceModulePath": "BUILTIN.range", "start": {"column": 35, "line": 122}, "superCall": false, "vid": "0cccf48982b98b9806bc0a4d00064dfd"}, {"arguments": ["patt_dir", "path_dirs[path_idx_end]", "uri_template_variables"], "end": {"column": 95, "line": 135}, "filePath": "utils.py", "functionName": "match_strings", "moduleName": "utils.AntPathMatcher", "rawCode": "self.match_strings(patt_dir, path_dirs[path_idx_end], uri_template_variables)", "sourceModulePath": "utils.AntPathMatcher", "start": {"column": 19, "line": 135}, "superCall": false, "vid": "0329984edbbeac213d93f1d60c110be8"}, {"arguments": ["patt_idx_start", "patt_idx_end+1"], "end": {"column": 69, "line": 141}, "filePath": "utils.py", "functionName": "range", "moduleName": "utils.AntPathMatcher", "rawCode": "range(patt_idx_start, patt_idx_end+1)", "sourceModulePath": "BUILTIN.range", "start": {"column": 31, "line": 141}, "superCall": false, "vid": "0cccf48982b98b9806bc0a4d00064dfd"}, {"arguments": ["patt_idx_start+1", "patt_idx_end+1"], "end": {"column": 72, "line": 148}, "filePath": "utils.py", "functionName": "range", "moduleName": "utils.AntPathMatcher", "rawCode": "range(patt_idx_start+1, patt_idx_end+1)", "sourceModulePath": "BUILTIN.range", "start": {"column": 30, "line": 148}, "superCall": false, "vid": "0cccf48982b98b9806bc0a4d00064dfd"}, {"arguments": ["0", "str_length-pat_length+1"], "end": {"column": 61, "line": 158}, "filePath": "utils.py", "functionName": "range", "moduleName": "utils.AntPathMatcher", "rawCode": "range(0, str_length-pat_length+1)", "sourceModulePath": "BUILTIN.range", "start": {"column": 25, "line": 158}, "superCall": false, "vid": "0cccf48982b98b9806bc0a4d00064dfd"}, {"arguments": ["pat_length"], "end": {"column": 45, "line": 159}, "filePath": "utils.py", "functionName": "range", "moduleName": "utils.AntPathMatcher", "rawCode": "range(pat_length)", "sourceModulePath": "BUILTIN.range", "start": {"column": 29, "line": 159}, "superCall": false, "vid": "0cccf48982b98b9806bc0a4d00064dfd"}, {"arguments": ["sub_pat", "sub_str", "uri_template_variables"], "end": {"column": 90, "line": 162}, "filePath": "utils.py", "functionName": "match_strings", "moduleName": "utils.AntPathMatcher", "rawCode": "self.match_strings(sub_pat, sub_str, uri_template_variables)", "sourceModulePath": "utils.AntPathMatcher", "start": {"column": 31, "line": 162}, "superCall": false, "vid": "0329984edbbeac213d93f1d60c110be8"}, {"arguments": ["patt_idx_start", "patt_idx_end+1"], "end": {"column": 65, "line": 172}, "filePath": "utils.py", "functionName": "range", "moduleName": "utils.AntPathMatcher", "rawCode": "range(patt_idx_start, patt_idx_end+1)", "sourceModulePath": "BUILTIN.range", "start": {"column": 27, "line": 172}, "superCall": false, "vid": "0cccf48982b98b9806bc0a4d00064dfd"}], "changeType": "DEFAULT", "className": "AntPathMatcher", "comments": [], "complexity": 45, "decorators": [], "end": {"column": 4, "line": 176}, "fileName": "utils.py", "fullPath": "utils.AntPathMatcher.do_match", "funcBody": "defdo_match(self,pattern,path=None,full_match=False,uri_template_variables=None):\n<INDENT>ifnotpathorpath.startswith(self.path_separator)!=pattern.startswith(self.path_separator):\n<INDENT>returnFalse\n<DEDENT>patt_dirs=self.tokenize_pattern(pattern)\niffull_matchandself.case_sensitiveandnotself.is_potential_match(path,patt_dirs):\n<INDENT>returnFalse\n<DEDENT>path_dirs=self.tokenize_path(path)\npatt_idx_start=0\npatt_idx_end=len(patt_dirs)-1\npath_idx_start=0\npath_idx_end=len(path_dirs)-1\nwhilepatt_idx_start<=patt_idx_endandpath_idx_start<=path_idx_end:\n<INDENT>patt_dir=patt_dirs[patt_idx_start]\nifpatt_dir==\"**\":\n<INDENT>break\n<DEDENT>ifnotself.match_strings(patt_dir,path_dirs[path_idx_start],uri_template_variables):\n<INDENT>returnFalse\n<DEDENT>patt_idx_start+=1\npath_idx_start+=1\n<DEDENT>ifpath_idx_start>path_idx_end:\n<INDENT>ifpatt_idx_start>patt_idx_end:\n<INDENT>returnpattern.endswith(self.path_separator)==path.endswith(self.path_separator)\n<DEDENT>elifnotfull_match:\n<INDENT>returnTrue\n<DEDENT>elifpatt_idx_start==patt_idx_endandpatt_dirs[patt_idx_start]==\"*\"andpath.endswith(self.path_separator):\n<INDENT>returnTrue\n<DEDENT>else:\n<INDENT>forpat_idx_tmpinrange(patt_idx_start,patt_idx_end+1):\n<INDENT>ifpatt_dirs[pat_idx_tmp]!=\"**\":\n<INDENT>returnFalse\n<DEDENT><DEDENT>returnTrue\n<DEDENT><DEDENT>elifpatt_idx_start>patt_idx_end:\n<INDENT>returnFalse\n<DEDENT>elifnotfull_matchandpatt_dirs[patt_idx_start]==\"**\":\n<INDENT>returnTrue\n<DEDENT>whilepatt_idx_start<=patt_idx_endandpath_idx_start<=path_idx_end:\n<INDENT>patt_dir=patt_dirs[patt_idx_end]\nifpatt_dir==\"**\":\n<INDENT>break\n<DEDENT>ifnotself.match_strings(patt_dir,path_dirs[path_idx_end],uri_template_variables):\n<INDENT>returnFalse\n<DEDENT>patt_idx_end-=1\npath_idx_end-=1\n<DEDENT>ifpath_idx_start>path_idx_end:\n<INDENT>forpat_idx_tmpinrange(patt_idx_start,patt_idx_end+1):\n<INDENT>ifpatt_dirs[pat_idx_tmp]!=\"**\":\n<INDENT>returnFalse\n<DEDENT><DEDENT>returnTrue\n<DEDENT>whilepatt_idx_start!=patt_idx_endandpath_idx_start<=path_idx_end:\n<INDENT>pat_idx_tmp=-1\nforpat_lengthinrange(patt_idx_start+1,patt_idx_end+1):\n<INDENT>ifpatt_dirs[pat_length]==\"**\":\n<INDENT>pat_idx_tmp=pat_length\nbreak\n<DEDENT><DEDENT>ifpat_idx_tmp==patt_idx_start+1:\n<INDENT>patt_idx_start+=1\n<DEDENT>else:\n<INDENT>pat_length=pat_idx_tmp-patt_idx_start-1\nstr_length=path_idx_end-path_idx_start+1\nfound_idx=-1\nforiinrange(0,str_length-pat_length+1):\n<INDENT>forjinrange(pat_length):\n<INDENT>sub_pat=patt_dirs[patt_idx_start+j+1]\nsub_str=path_dirs[path_idx_start+i+j]\nifnotself.match_strings(sub_pat,sub_str,uri_template_variables):\n<INDENT>break\n<DEDENT><DEDENT>else:\n<INDENT>found_idx=path_idx_start+i\nbreak\n<DEDENT><DEDENT>iffound_idx==-1:\n<INDENT>returnFalse\n<DEDENT>patt_idx_start=pat_idx_tmp\npath_idx_start=found_idx+pat_length\n<DEDENT><DEDENT>forpat_idx_tmpinrange(patt_idx_start,patt_idx_end+1):\n<INDENT>ifpatt_dirs[pat_idx_tmp]!=\"**\":\n<INDENT>returnFalse\n<DEDENT><DEDENT>returnTrue\n<DEDENT>", "hasArgs": false, "hasKwargs": false, "innerFunctions": [], "innerImports": [], "isGenerator": false, "moduleName": "utils.AntPathMatcher", "modulePath": "utils.AntPathMatcher.do_match", "name": "do_match", "otherBlockComments": [], "parameterCount": 5, "parameters": [{"location": {"column": 17, "line": 90}, "name": "self", "paramIndex": 0, "type": "AntPathMatcher", "typeModule": "utils.AntPathMatcher", "vid": "f8393ea147f6b750a263171c3ec05ad4"}, {"location": {"column": 23, "line": 90}, "name": "pattern", "paramIndex": 1, "typeModule": "Unknown", "vid": "2a1321c343a36542b73339cb78350ba5"}, {"defaultValue": "None", "location": {"column": 32, "line": 90}, "name": "path", "paramIndex": 0, "typeModule": "Unknown", "vid": "cd5ac8a48ff465965d2d3501881366a4"}, {"defaultValue": "False", "location": {"column": 43, "line": 90}, "name": "full_match", "paramIndex": 1, "typeModule": "Unknown", "vid": "a0cc06c0f5fff13fbea7c348cb67fb73"}, {"defaultValue": "None", "location": {"column": 61, "line": 90}, "name": "uri_template_variables", "paramIndex": 2, "typeModule": "Unknown", "vid": "5b30132cc50b6a7e97612df65354c271"}], "returnNode": [{"rawReturn": "False", "returnLine": {"column": 12, "line": 92}, "type": "Unknown"}, {"rawReturn": "False", "returnLine": {"column": 12, "line": 96}, "type": "Unknown"}, {"rawReturn": "False", "returnLine": {"column": 16, "line": 109}, "type": "Unknown"}, {"rawReturn": "pattern.endswith(self.path_separator)==path.endswith(self.path_separator)", "returnLine": {"column": 16, "line": 115}, "type": "Unknown"}, {"rawReturn": "True", "returnLine": {"column": 16, "line": 117}, "type": "Unknown"}, {"rawReturn": "True", "returnLine": {"column": 16, "line": 120}, "type": "Unknown"}, {"rawReturn": "False", "returnLine": {"column": 24, "line": 124}, "type": "Unknown"}, {"rawReturn": "True", "returnLine": {"column": 16, "line": 125}, "type": "Unknown"}, {"rawReturn": "False", "returnLine": {"column": 12, "line": 127}, "type": "Unknown"}, {"rawReturn": "True", "returnLine": {"column": 12, "line": 129}, "type": "Unknown"}, {"rawReturn": "False", "returnLine": {"column": 16, "line": 136}, "type": "Unknown"}, {"rawReturn": "False", "returnLine": {"column": 20, "line": 143}, "type": "Unknown"}, {"rawReturn": "True", "returnLine": {"column": 12, "line": 144}, "type": "Unknown"}, {"rawReturn": "False", "returnLine": {"column": 20, "line": 168}, "type": "Unknown"}, {"rawReturn": "False", "returnLine": {"column": 16, "line": 174}, "type": "Unknown"}, {"rawReturn": "True", "returnLine": {"column": 8, "line": 175}, "type": "Unknown"}], "slashIndex": -1, "starIndex": -1, "start": {"column": 4, "line": 90}, "vid": "161a8a55a83f526e488fba35698c1faa"}, {"async": false, "callNodes": [{"arguments": ["pattern"], "end": {"column": 106, "line": 179}, "filePath": "utils.py", "functionName": "get", "moduleName": "utils.AntPathMatcher", "rawCode": "self.tokenized_pattern_cache.get(pattern)", "sourceModulePath": "utils.AntPathMatcher.tokenized_pattern_cache", "start": {"column": 66, "line": 179}, "superCall": false, "vid": "fe4ae3034b668ddce7a26981f83eb19f"}], "changeType": "DEFAULT", "className": "AntPathMatcher", "comments": [{"comment": "\\", "start": {"column": 35, "line": 178}}], "complexity": 3, "decorators": [], "end": {"column": 4, "line": 185}, "fileName": "utils.py", "fullPath": "utils.AntPathMatcher.tokenize_pattern", "funcBody": "deftokenize_pattern(self,pattern:str)->List[str]:\n<INDENT>ifself.cache_patternsandpatterninself.tokenized_pattern_cacheand(r:=self.tokenized_pattern_cache.get(pattern)):\n<INDENT>returnr\n<DEDENT>result=self.tokenize_path(pattern)\nself.tokenized_pattern_cache[pattern]=result\nreturnresult\n<DEDENT>", "functionReturnType": "List[str]", "hasArgs": false, "hasKwargs": false, "innerFunctions": [], "innerImports": [], "isGenerator": false, "moduleName": "utils.AntPathMatcher", "modulePath": "utils.AntPathMatcher.tokenize_pattern", "name": "tokenize_pattern", "otherBlockComments": [], "parameterCount": 2, "parameters": [{"location": {"column": 25, "line": 177}, "name": "self", "paramIndex": 0, "type": "AntPathMatcher", "typeModule": "utils.AntPathMatcher", "vid": "f8393ea147f6b750a263171c3ec05ad4"}, {"location": {"column": 31, "line": 177}, "name": "pattern", "paramIndex": 1, "type": "str", "typeModule": "BUILTIN.str", "vid": "2a1321c343a36542b73339cb78350ba5"}], "returnNode": [{"rawReturn": "r", "returnLine": {"column": 12, "line": 180}, "type": "Unknown"}, {"rawReturn": "result", "returnLine": {"column": 8, "line": 184}, "type": "Unknown"}], "slashIndex": -1, "starIndex": -1, "start": {"column": 4, "line": 177}, "vid": "81646be05ef397b279711fd9be1dc6b1"}, {"async": false, "callNodes": [], "changeType": "DEFAULT", "className": "AntPathMatcher", "comments": [], "complexity": 1, "decorators": [], "end": {"column": 4, "line": 188}, "fileName": "utils.py", "fullPath": "utils.AntPathMatcher.match", "funcBody": "defmatch(self,pattern:str,path:str)->bool:\n<INDENT>returnself.do_match(pattern,path,True)\n<DEDENT>", "functionReturnType": "bool", "hasArgs": false, "hasKwargs": false, "innerFunctions": [], "innerImports": [], "isGenerator": false, "moduleName": "utils.AntPathMatcher", "modulePath": "utils.AntPathMatcher.match", "name": "match", "otherBlockComments": [], "parameterCount": 3, "parameters": [{"location": {"column": 14, "line": 186}, "name": "self", "paramIndex": 0, "type": "AntPathMatcher", "typeModule": "utils.AntPathMatcher", "vid": "f8393ea147f6b750a263171c3ec05ad4"}, {"location": {"column": 20, "line": 186}, "name": "pattern", "paramIndex": 1, "type": "str", "typeModule": "BUILTIN.str", "vid": "2a1321c343a36542b73339cb78350ba5"}, {"location": {"column": 34, "line": 186}, "name": "path", "paramIndex": 2, "type": "str", "typeModule": "BUILTIN.str", "vid": "cd5ac8a48ff465965d2d3501881366a4"}], "returnNode": [{"rawReturn": "self.do_match(pattern,path,True)", "returnLine": {"column": 8, "line": 187}, "type": "Unknown"}], "slashIndex": -1, "starIndex": -1, "start": {"column": 4, "line": 186}, "vid": "7dd28f9a8d161441fc4e76078cfa02b9"}, {"async": false, "callNodes": [], "changeType": "DEFAULT", "className": "AntPathMatcher", "comments": [], "complexity": 1, "decorators": [], "end": {"column": 4, "line": 191}, "fileName": "utils.py", "fullPath": "utils.AntPathMatcher.match_start", "funcBody": "defmatch_start(self,pattern:str,path:str)->bool:\n<INDENT>returnself.do_match(pattern,path,False)\n<DEDENT>", "functionReturnType": "bool", "hasArgs": false, "hasKwargs": false, "innerFunctions": [], "innerImports": [], "isGenerator": false, "moduleName": "utils.AntPathMatcher", "modulePath": "utils.AntPathMatcher.match_start", "name": "match_start", "otherBlockComments": [], "parameterCount": 3, "parameters": [{"location": {"column": 20, "line": 189}, "name": "self", "paramIndex": 0, "type": "AntPathMatcher", "typeModule": "utils.AntPathMatcher", "vid": "f8393ea147f6b750a263171c3ec05ad4"}, {"location": {"column": 26, "line": 189}, "name": "pattern", "paramIndex": 1, "type": "str", "typeModule": "BUILTIN.str", "vid": "2a1321c343a36542b73339cb78350ba5"}, {"location": {"column": 40, "line": 189}, "name": "path", "paramIndex": 2, "type": "str", "typeModule": "BUILTIN.str", "vid": "cd5ac8a48ff465965d2d3501881366a4"}], "returnNode": [{"rawReturn": "self.do_match(pattern,path,False)", "returnLine": {"column": 8, "line": 190}, "type": "Unknown"}], "slashIndex": -1, "starIndex": -1, "start": {"column": 4, "line": 189}, "vid": "57e8c0c9ee43e72a569637ad3c4f4c17"}, {"async": false, "callNodes": [{"arguments": ["patt_dir"], "end": {"column": 41, "line": 199}, "filePath": "utils.py", "functionName": "len", "moduleName": "utils.AntPathMatcher", "rawCode": "len(patt_dir)", "sourceModulePath": "BUILTIN.len", "start": {"column": 29, "line": 199}, "superCall": false, "vid": "********************************"}], "changeType": "DEFAULT", "className": "AntPathMatcher", "comments": [], "complexity": 4, "decorators": [], "end": {"column": 4, "line": 203}, "fileName": "utils.py", "fullPath": "utils.AntPathMatcher.is_potential_match", "funcBody": "defis_potential_match(self,path,patt_dirs):\n<INDENT>ifnotself.trim_tokens:\n<INDENT>pos=0\nforpatt_dirinpatt_dirs:\n<INDENT>skipped=self._skip_separator(path,pos,self.path_separator)\npos+=skipped\nskipped=self._skip_segment(path,pos,patt_dir)\nifskipped<len(patt_dir):\n<INDENT>returnskipped>0or(len(patt_dir)>0andpatt_dir[0]inAntPathMatcher._WILDCARD_CHARS)\n<DEDENT>pos+=skipped\n<DEDENT><DEDENT>returnTrue\n<DEDENT>", "hasArgs": false, "hasKwargs": false, "innerFunctions": [], "innerImports": [], "isGenerator": false, "moduleName": "utils.AntPathMatcher", "modulePath": "utils.AntPathMatcher.is_potential_match", "name": "is_potential_match", "otherBlockComments": [], "parameterCount": 3, "parameters": [{"location": {"column": 27, "line": 192}, "name": "self", "paramIndex": 0, "type": "AntPathMatcher", "typeModule": "utils.AntPathMatcher", "vid": "f8393ea147f6b750a263171c3ec05ad4"}, {"location": {"column": 33, "line": 192}, "name": "path", "paramIndex": 1, "typeModule": "Unknown", "vid": "cd5ac8a48ff465965d2d3501881366a4"}, {"location": {"column": 39, "line": 192}, "name": "patt_dirs", "paramIndex": 2, "typeModule": "Unknown", "vid": "8f52dee4e870c873ff625adb3b5f487b"}], "returnNode": [{"rawReturn": "skipped>0or(len(patt_dir)>0andpatt_dir[0]inAntPathMatcher._WILDCARD_CHARS)", "returnLine": {"column": 20, "line": 200}, "type": "Unknown"}, {"rawReturn": "True", "returnLine": {"column": 8, "line": 202}, "type": "Unknown"}], "slashIndex": -1, "starIndex": -1, "start": {"column": 4, "line": 192}, "vid": "cdf22eacd1e4d44d9a63df0d33d73b53"}, {"async": false, "callNodes": [], "changeType": "DEFAULT", "className": "AntPathMatcher", "comments": [], "complexity": 1, "decorators": [], "end": {"column": 4, "line": 209}, "fileName": "utils.py", "fullPath": "utils.AntPathMatcher.tokenize_path", "funcBody": "deftokenize_path(self,path):\n<INDENT>return[self.trim_tokensand_.strip()or_for_inpath.split(self.path_separator)if_]\n<DEDENT>", "hasArgs": false, "hasKwargs": false, "innerFunctions": [], "innerImports": [], "isGenerator": false, "moduleName": "utils.AntPathMatcher", "modulePath": "utils.AntPathMatcher.tokenize_path", "name": "tokenize_path", "otherBlockComments": [], "parameterCount": 2, "parameters": [{"location": {"column": 22, "line": 204}, "name": "self", "paramIndex": 0, "type": "AntPathMatcher", "typeModule": "utils.AntPathMatcher", "vid": "f8393ea147f6b750a263171c3ec05ad4"}, {"location": {"column": 28, "line": 204}, "name": "path", "paramIndex": 1, "typeModule": "Unknown", "vid": "cd5ac8a48ff465965d2d3501881366a4"}], "returnNode": [{"rawReturn": "[self.trim_tokensand_.strip()or_for_inpath.split(self.path_separator)if_]", "returnLine": {"column": 8, "line": 205}, "type": "Unknown"}], "slashIndex": -1, "starIndex": -1, "start": {"column": 4, "line": 204}, "vid": "db16ed2aa0b377f9d5478ce9f159e165"}, {"async": false, "callNodes": [], "changeType": "DEFAULT", "className": "AntPathMatcher", "comments": [], "complexity": 1, "decorators": [], "end": {"column": 4, "line": 212}, "fileName": "utils.py", "fullPath": "utils.AntPathMatcher.match_strings", "funcBody": "defmatch_strings(self,pattern,raw_str,uri_template_variables):\n<INDENT>returnself.get_string_matcher(pattern).match_strings(raw_str,uri_template_variables)\n<DEDENT>", "hasArgs": false, "hasKwargs": false, "innerFunctions": [], "innerImports": [], "isGenerator": false, "moduleName": "utils.AntPathMatcher", "modulePath": "utils.AntPathMatcher.match_strings", "name": "match_strings", "otherBlockComments": [], "parameterCount": 4, "parameters": [{"location": {"column": 22, "line": 210}, "name": "self", "paramIndex": 0, "type": "AntPathMatcher", "typeModule": "utils.AntPathMatcher", "vid": "f8393ea147f6b750a263171c3ec05ad4"}, {"location": {"column": 28, "line": 210}, "name": "pattern", "paramIndex": 1, "typeModule": "Unknown", "vid": "2a1321c343a36542b73339cb78350ba5"}, {"location": {"column": 37, "line": 210}, "name": "raw_str", "paramIndex": 2, "typeModule": "Unknown", "vid": "44edb86fa318c68df87190f8dbdbd5b2"}, {"location": {"column": 46, "line": 210}, "name": "uri_template_variables", "paramIndex": 3, "typeModule": "Unknown", "vid": "5b30132cc50b6a7e97612df65354c271"}], "returnNode": [{"rawReturn": "self.get_string_matcher(pattern).match_strings(raw_str,uri_template_variables)", "returnLine": {"column": 8, "line": 211}, "type": "Unknown"}], "slashIndex": -1, "starIndex": -1, "start": {"column": 4, "line": 210}, "vid": "0329984edbbeac213d93f1d60c110be8"}, {"async": false, "callNodes": [{"arguments": ["path_separator", "pos+skipped"], "end": {"column": 59, "line": 216}, "filePath": "utils.py", "functionName": "startswith", "moduleName": "utils.AntPathMatcher", "rawCode": "path.startswith(path_separator, pos+skipped)", "sourceModulePath": "Unknown", "start": {"column": 14, "line": 216}, "superCall": false, "vid": "d5c92ccb662f5ecb4834d7f389856277"}], "changeType": "DEFAULT", "className": "AntPathMatcher", "comments": [], "complexity": 2, "decorators": [{"functionName": "staticmethod", "isDecorator": true, "level": 1, "start": {"column": 5, "line": 213}, "superCall": false, "vid": "fc22cf49c0df21ac4f470bd5f3137aee"}], "end": {"column": 4, "line": 219}, "fileName": "utils.py", "fullPath": "utils.AntPathMatcher._skip_separator", "funcBody": "@staticmethod\ndef_skip_separator(path,pos,path_separator):\n<INDENT>skipped=0\nwhilepath.startswith(path_separator,pos+skipped):\n<INDENT>skipped+=len(path_separator)\n<DEDENT>returnskipped\n<DEDENT>", "hasArgs": false, "hasKwargs": false, "innerFunctions": [], "innerImports": [], "isGenerator": false, "moduleName": "utils.AntPathMatcher", "modulePath": "utils.AntPathMatcher._skip_separator", "name": "_skip_separator", "otherBlockComments": [], "parameterCount": 3, "parameters": [{"location": {"column": 24, "line": 214}, "name": "path", "paramIndex": 0, "typeModule": "Unknown", "vid": "cd5ac8a48ff465965d2d3501881366a4"}, {"location": {"column": 30, "line": 214}, "name": "pos", "paramIndex": 1, "typeModule": "Unknown", "vid": "53a84c6356decc62ad601806f1241d67"}, {"location": {"column": 35, "line": 214}, "name": "path_separator", "paramIndex": 2, "typeModule": "Unknown", "vid": "941eabc0e7109186f79d8f9660c7140d"}], "returnNode": [{"rawReturn": "skipped", "returnLine": {"column": 8, "line": 218}, "type": "Unknown"}], "slashIndex": -1, "starIndex": -1, "start": {"column": 4, "line": 214}, "vid": "57d06e7bf8eee687a2d8b2aae1525e57"}, {"async": false, "callNodes": [{"arguments": ["len(patt_dir)"], "end": {"column": 36, "line": 223}, "filePath": "utils.py", "functionName": "range", "moduleName": "utils.AntPathMatcher", "rawCode": "range(len(patt_dir))", "sourceModulePath": "BUILTIN.range", "start": {"column": 17, "line": 223}, "superCall": false, "vid": "0cccf48982b98b9806bc0a4d00064dfd"}, {"arguments": ["patt_dir"], "end": {"column": 35, "line": 223}, "filePath": "utils.py", "functionName": "len", "moduleName": "utils.AntPathMatcher", "rawCode": "len(patt_dir)", "sourceModulePath": "BUILTIN.len", "start": {"column": 23, "line": 223}, "superCall": false, "vid": "********************************"}, {"arguments": ["patt_dir"], "end": {"column": 35, "line": 223}, "filePath": "utils.py", "functionName": "len", "moduleName": "utils.AntPathMatcher", "rawCode": "len(patt_dir)", "sourceModulePath": "BUILTIN.len", "start": {"column": 23, "line": 223}, "superCall": false, "vid": "********************************"}, {"arguments": ["path"], "end": {"column": 35, "line": 228}, "filePath": "utils.py", "functionName": "len", "moduleName": "utils.AntPathMatcher", "rawCode": "len(path)", "sourceModulePath": "BUILTIN.len", "start": {"column": 27, "line": 228}, "superCall": false, "vid": "********************************"}], "changeType": "DEFAULT", "className": "AntPathMatcher", "comments": [], "complexity": 5, "decorators": [{"functionName": "staticmethod", "isDecorator": true, "level": 1, "start": {"column": 5, "line": 220}, "superCall": false, "vid": "fc22cf49c0df21ac4f470bd5f3137aee"}], "end": {"column": 4, "line": 233}, "fileName": "utils.py", "fullPath": "utils.AntPathMatcher._skip_segment", "funcBody": "@staticmethod\ndef_skip_segment(path,pos,patt_dir):\n<INDENT>skipped=0\nforiinrange(len(patt_dir)):\n<INDENT>c=patt_dir[i]\nifcinAntPathMatcher._WILDCARD_CHARS:\n<INDENT>returnskipped\n<DEDENT>curr_pos=pos+skipped\nifcurr_pos>=len(path):\n<INDENT>return0\n<DEDENT>ifc==path[curr_pos]:\n<INDENT>skipped+=1\n<DEDENT><DEDENT>returnskipped\n<DEDENT>", "hasArgs": false, "hasKwargs": false, "innerFunctions": [], "innerImports": [], "isGenerator": false, "moduleName": "utils.AntPathMatcher", "modulePath": "utils.AntPathMatcher._skip_segment", "name": "_skip_segment", "otherBlockComments": [], "parameterCount": 3, "parameters": [{"location": {"column": 22, "line": 221}, "name": "path", "paramIndex": 0, "typeModule": "Unknown", "vid": "cd5ac8a48ff465965d2d3501881366a4"}, {"location": {"column": 28, "line": 221}, "name": "pos", "paramIndex": 1, "typeModule": "Unknown", "vid": "53a84c6356decc62ad601806f1241d67"}, {"location": {"column": 33, "line": 221}, "name": "patt_dir", "paramIndex": 2, "typeModule": "Unknown", "vid": "fed2220e8a6eff8a967e343ebb3c4741"}], "returnNode": [{"rawReturn": "skipped", "returnLine": {"column": 16, "line": 226}, "type": "Unknown"}, {"rawReturn": "0", "returnLine": {"column": 16, "line": 229}, "type": "Unknown"}, {"rawReturn": "skipped", "returnLine": {"column": 8, "line": 232}, "type": "Unknown"}], "slashIndex": -1, "starIndex": -1, "start": {"column": 4, "line": 221}, "vid": "a18745587161648e0821f26eee6e899a"}, {"async": false, "callNodes": [{"arguments": ["pattern"], "end": {"column": 104, "line": 236}, "filePath": "utils.py", "functionName": "get", "moduleName": "utils.AntPathMatcher", "rawCode": "self.string_matcher_cache.get(pattern)", "sourceModulePath": "utils.AntPathMatcher.string_matcher_cache", "start": {"column": 67, "line": 236}, "superCall": false, "vid": "614bfe0a2e4632391b6e23497ea1ca3b"}], "changeType": "DEFAULT", "className": "AntPathMatcher", "comments": [{"comment": "\\", "start": {"column": 31, "line": 235}}], "complexity": 3, "decorators": [], "end": {"column": 0, "line": 245}, "fileName": "utils.py", "fullPath": "utils.AntPathMatcher.get_string_matcher", "funcBody": "defget_string_matcher(self,pattern)->StringMatcher:\n<INDENT>ifself.cache_patternsandpatterninself.string_matcher_cacheand(r:=self.string_matcher_cache.get(pattern)):\n<INDENT>returnr\n<DEDENT>str_matcher=StringMatcher(pattern=pattern,case_sensitive=self.case_sensitive)\nself.string_matcher_cache[pattern]=str_matcher\nreturnstr_matcher\n<DEDENT>", "functionReturnType": "StringMatcher", "hasArgs": false, "hasKwargs": false, "innerFunctions": [], "innerImports": [], "isGenerator": false, "moduleName": "utils.AntPathMatcher", "modulePath": "utils.AntPathMatcher.get_string_matcher", "name": "get_string_matcher", "otherBlockComments": [], "parameterCount": 2, "parameters": [{"location": {"column": 27, "line": 234}, "name": "self", "paramIndex": 0, "type": "AntPathMatcher", "typeModule": "utils.AntPathMatcher", "vid": "f8393ea147f6b750a263171c3ec05ad4"}, {"location": {"column": 33, "line": 234}, "name": "pattern", "paramIndex": 1, "typeModule": "Unknown", "vid": "2a1321c343a36542b73339cb78350ba5"}], "returnNode": [{"rawReturn": "r", "returnLine": {"column": 12, "line": 237}, "type": "Unknown"}, {"rawReturn": "str_matcher", "returnLine": {"column": 8, "line": 243}, "type": "Unknown"}], "slashIndex": -1, "starIndex": -1, "start": {"column": 4, "line": 234}, "vid": "5c3d84cd251075624c2850cbb272040b"}], "moduleName": "utils", "modulePath": "utils.AntPathMatcher", "name": "AntPathMatcher", "otherBlockComments": [], "parameters": [{"defaultValue": "/", "name": "path_separator", "type": "str", "vid": "941eabc0e7109186f79d8f9660c7140d"}, {"defaultValue": "False", "name": "case_sensitive", "type": "bool", "vid": "294bbfce9a8efa37aa105485b6047221"}, {"defaultValue": "False", "name": "trim_tokens", "type": "bool", "vid": "b5263d3fc3d3f1d28db3a0a879f05a18"}, {"defaultValue": "True", "name": "cache_patterns", "type": "bool", "vid": "5142ee984c23b43befb98daa6d28457c"}, {"defaultValue": "defaultdict(list)", "name": "tokenized_pattern_cache", "type": "Dict", "vid": "99909085f8a4458ad5bee2adaf9b93b3"}, {"defaultValue": "dict()", "name": "string_matcher_cache", "type": "Dict", "vid": "933841a15ffce4e4b23cc7a8414a7b01"}, {"defaultValue": "{'*','?','{'}", "name": "_WILDCARD_CHARS", "vid": "3034048a6d77dd0b51b61d1773716a14"}], "start": {"column": -1, "line": 79}, "superClasses": [{"index": 0, "name": "BaseModel", "sourceModulePath": "pydantic.BaseModel"}], "vid": "030f41e0c9e09e5e5e07a36ba0fee8be"}], "comments": [], "fileName": "utils.py", "functionCallNodeMap": {}, "functionCallNodes": [{"end": {"column": 29, "line": 246}, "filePath": "utils.py", "functionName": "AntPathMatcher", "moduleName": "utils", "rawCode": "AntPathMatcher()", "sourceModulePath": "Unknown", "start": {"column": 14, "line": 246}, "superCall": false, "vid": "a955b219fd9210200a8f0af3e38fe0d2"}], "functionNodeMap": {}, "functionNodes": [{"async": false, "callNodes": [], "changeType": "DEFAULT", "comments": [], "complexity": 3, "decorators": [], "end": {"column": 61, "line": 251}, "fileName": "utils.py", "fullPath": "utils.batch_pattern_match", "funcBody": "defbatch_pattern_match(patterns:Iterable[str],url):\n<INDENT>ifnot(patternsorurl):\n<INDENT>returnFalse\n<DEDENT>returnany((URL_MATCHER.match(p,url)forpinpatterns))<NEWLINE><DEDENT>", "hasArgs": false, "hasKwargs": false, "innerFunctions": [], "innerImports": [], "isGenerator": false, "moduleName": "utils", "modulePath": "utils.batch_pattern_match", "name": "batch_pattern_match", "otherBlockComments": [], "parameterCount": 2, "parameters": [{"location": {"column": 24, "line": 249}, "name": "patterns", "paramIndex": 0, "type": "Iterable[str]", "typeModule": "Unknown", "vid": "d6d1b0d5b0c84b52a689a42d1257f544"}, {"location": {"column": 49, "line": 249}, "name": "url", "paramIndex": 1, "typeModule": "Unknown", "vid": "7b8ac6a2c8d397b3009f4a0bfd0b5cef"}], "returnNode": [{"rawReturn": "False", "returnLine": {"column": 8, "line": 251}, "type": "Unknown"}, {"rawReturn": "any((URL_MATCHER.match(p,url)forpinpatterns))", "returnLine": {"column": 4, "line": 252}, "type": "Unknown"}], "slashIndex": -1, "starIndex": -1, "start": {"column": 0, "line": 249}, "vid": "59f19f7dcabf4afe00b8e506460693d1"}], "importNodeMap": {}, "importNodes": [{"aliases": {"re": "re"}, "currentModuleName": "utils", "currentModulePath": "utils", "fileName": "utils.py", "fromModulePath": "re", "fullImportModulePath": "re", "importType": "IMPORT", "importedItems": ["re"], "location": {"column": 0, "line": 1}}, {"aliases": {"defaultdict": "defaultdict"}, "currentModuleName": "utils", "currentModulePath": "utils", "fileName": "utils.py", "fromModulePath": "collections", "fullImportModulePath": "collections", "importType": "FROM", "importedItems": ["defaultdict"], "location": {"column": 0, "line": 2}}, {"aliases": {"Dict": "Dict", "Iterable": "Iterable", "List": "List", "Optional": "Optional"}, "currentModuleName": "utils", "currentModulePath": "utils", "fileName": "utils.py", "fromModulePath": "typing", "fullImportModulePath": "typing", "importType": "FROM", "importedItems": ["List", "Dict", "Optional", "Iterable"], "location": {"column": 0, "line": 3}}, {"aliases": {"BaseModel": "BaseModel"}, "currentModuleName": "utils", "currentModulePath": "utils", "fileName": "utils.py", "fromModulePath": "pydantic", "fullImportModulePath": "pydantic", "importType": "FROM", "importedItems": ["BaseModel"], "location": {"column": 0, "line": 5}}], "moduleName": "utils", "moduleParamNodes": [{"defaultValue": "AntPathMatcher()", "modulePath": "utils", "name": "URL_MATCHER", "vid": "954481e5caa3290409702e2d00559dbf"}], "modulePath": "utils", "parentModule": "", "subModuleNodes": [], "vid": "2b3583e6e17721c54496bd04e57a0c15"}}, "scopeManagerMap": {"builtin": {"currentScope": "builtin", "scopes": {"builtin": {"BaseFormatter": {"27": {"fullPath": ".base.BaseFormatter", "line": 27}}, "Fasten": {"30": {"fullPath": "builtin.Fasten", "line": 30}}, "Requirement": {"23": {"fullPath": "pkg_resources.Requirement", "line": 23}}, "os": {"21": {"fullPath": "os", "line": 21}}, "utils": {"25": {"fullPath": "pycg.utils", "line": 25}}}, "builtin.Fasten": {"__init__": {"31": {"fullPath": "builtin.Fasten.__init__", "line": 31}}, "add_superclasses": {"185": {"fullPath": "builtin.Fasten.add_superclasses", "line": 185}}, "create_namespaces_map": {"207": {"fullPath": "builtin.Fasten.create_namespaces_map", "line": 207}}, "find_dependencies": {"75": {"fullPath": "builtin.Fasten.find_dependencies", "line": 75}}, "generate": {"259": {"fullPath": "builtin.Fasten.generate", "line": 259}}, "get_external_modules": {"216": {"fullPath": "builtin.Fasten.get_external_modules", "line": 216}}, "get_graph": {"236": {"fullPath": "builtin.Fasten.get_graph", "line": 236}}, "get_internal_modules": {"162": {"fullPath": "builtin.Fasten.get_internal_modules", "line": 162}}, "get_unique_and_increment": {"46": {"fullPath": "builtin.Fasten.get_unique_and_increment", "line": 46}}, "self": {"30": {"fullPath": "builtin.Fasten", "line": 30}}, "to_external_uri": {"68": {"fullPath": "builtin.Fasten.to_external_uri", "line": 68}}, "to_uri": {"51": {"fullPath": "builtin.Fasten.to_uri", "line": 51}}}, "builtin.Fasten.__init__": {"__init__": {"31": {"fullPath": "builtin.Fasten.__init__", "line": 31}}}, "builtin.Fasten.add_superclasses": {"add_superclasses": {"185": {"fullPath": "builtin.Fasten.add_superclasses", "line": 185}}, "cls_uri": {"187": {"fullPath": "str", "line": 187}}, "parent_mod": {"198": {"fullPath": "int", "line": 198}}, "parent_uri": {"196": {"fullPath": "str", "line": 196}, "199": {"fullPath": "builtin.Fasten.parent_uri", "line": 199}}}, "builtin.Fasten.create_namespaces_map": {"create_namespaces_map": {"207": {"fullPath": "builtin.Fasten.create_namespaces_map", "line": 207}}, "namespaces_maps": {"208": {"fullPath": "list", "line": 208}}}, "builtin.Fasten.find_dependencies": {"begin": {"123": {"fullPath": "None", "line": 123}, "130": {"fullPath": "None", "line": 130}, "139": {"fullPath": "None", "line": 139}, "141": {"fullPath": "tuple", "line": 141}, "145": {"fullPath": "None", "line": 145}, "148": {"fullPath": "tuple", "line": 148}}, "constraints": {"100": {"fullPath": "list", "line": 100}}, "end": {"124": {"fullPath": "None", "line": 124}, "131": {"fullPath": "None", "line": 131}, "138": {"fullPath": "None", "line": 138}, "146": {"fullPath": "None", "line": 146}, "151": {"fullPath": "tuple", "line": 151}, "153": {"fullPath": "tuple", "line": 153}}, "find_dependencies": {"75": {"fullPath": "builtin.Fasten.find_dependencies", "line": 75}}, "lines": {"85": {"fullPath": "builtin.Fasten.lines", "line": 85}}, "req": {"92": {"fullPath": "builtin.Fasten.req", "line": 92}}, "requirements_path": {"79": {"fullPath": "str", "line": 79}}, "res": {"76": {"fullPath": "list", "line": 76}}, "specs": {"98": {"fullPath": "builtin.Fasten.specs", "line": 98}}}, "builtin.Fasten.generate": {"generate": {"259": {"fullPath": "builtin.Fasten.generate", "line": 259}}}, "builtin.Fasten.get_external_modules": {"get_external_modules": {"216": {"fullPath": "builtin.Fasten.get_external_modules", "line": 216}}, "mods": {"217": {"fullPath": "dict", "line": 217}}, "name": {"219": {"fullPath": "int", "line": 219}}, "namespace_uri": {"227": {"fullPath": "str", "line": 227}}, "namespaces": {"220": {"fullPath": "str", "line": 220}}, "unique": {"229": {"fullPath": "builtin.Fasten.unique", "line": 229}}}, "builtin.Fasten.get_graph": {"get_graph": {"236": {"fullPath": "builtin.Fasten.get_graph", "line": 236}}, "graph": {"237": {"fullPath": "dict", "line": 237}}, "mod": {"245": {"fullPath": "builtin.Fasten.mod", "line": 245}, "249": {"fullPath": "builtin.Fasten.mod", "line": 249}}, "uri": {"246": {"fullPath": "builtin.Fasten.uri", "line": 246}}, "uris": {"242": {"fullPath": "list", "line": 242}}}, "builtin.Fasten.get_internal_modules": {"filename": {"167": {"fullPath": "str", "line": 167}}, "get_internal_modules": {"162": {"fullPath": "builtin.Fasten.get_internal_modules", "line": 162}}, "mods": {"163": {"fullPath": "dict", "line": 163}, "181": {"fullPath": "builtin.Fasten.mods", "line": 181}}, "name": {"166": {"fullPath": "builtin.Fasten.name", "line": 166}}, "namespace_uri": {"173": {"fullPath": "str", "line": 173}}, "namespaces": {"168": {"fullPath": "str", "line": 168}}, "unique": {"175": {"fullPath": "builtin.Fasten.unique", "line": 175}}}, "builtin.Fasten.get_unique_and_increment": {"get_unique_and_increment": {"46": {"fullPath": "builtin.Fasten.get_unique_and_increment", "line": 46}}, "unique": {"47": {"fullPath": "builtin.Fasten.unique", "line": 47}}}, "builtin.Fasten.to_external_uri": {"modname": {"71": {"fullPath": "str", "line": 71}}, "name": {"70": {"fullPath": "int", "line": 70}}, "to_external_uri": {"68": {"fullPath": "builtin.Fasten.to_external_uri", "line": 68}}}, "builtin.Fasten.to_uri": {"cleared": {"52": {"fullPath": "builtin.Fasten.cleared", "line": 52}, "55": {"fullPath": "str", "line": 55}, "60": {"fullPath": "builtin.Fasten.cleared", "line": 60}}, "suffix": {"62": {"fullPath": "str", "line": 62}, "64": {"fullPath": "str", "line": 64}}, "to_uri": {"51": {"fullPath": "builtin.Fasten.to_uri", "line": 51}}}}}, "func_call": {"currentScope": "func_call", "scopes": {"func_call": {"Fasten": {"3": {"fullPath": ".builtin.<PERSON>en", "line": 3}}, "demo": {"7": {"fullPath": "func_call.demo", "line": 7}}, "f": {"15": {"fullPath": "func_call.f", "line": 15}}, "sub_module": {"2": {"fullPath": "sub_module", "line": 2}}, "sys": {"1": {"fullPath": "sys", "line": 1}}}, "func_call.demo": {"demo": {"7": {"fullPath": "func_call.demo", "line": 7}}, "x": {"8": {"fullPath": "int", "line": 8}}, "y": {"9": {"fullPath": "int", "line": 9}}, "z": {"10": {"fullPath": "func_call.z", "line": 10}}}}}, "relative_import_test": {"currentScope": "relative_import_test", "scopes": {"relative_import_test": {"defaultdict": {"6": {"fullPath": "collections.defaultdict", "line": 6}}, "os": {"5": {"fullPath": "os", "line": 5}}, "parent_module": {"11": {"fullPath": "...parent_module", "line": 11}}, "sibling_module": {"9": {"fullPath": "..sibling_module", "line": 9}}, "some_class": {"12": {"fullPath": "..parent_module.some_class", "line": 12}}, "some_func": {"10": {"fullPath": ".sibling_module.some_func", "line": 10}}, "utils": {"13": {"fullPath": "grandparent.utils", "line": 13}}}}}, "sub_module.inner": {"currentScope": "inner", "scopes": {"inner": {"utils": {"1": {"fullPath": "sub_module.sub_utils.utils", "line": 1}}}}}, "sub_module.sub_utils": {"currentScope": "sub_utils", "scopes": {"sub_utils": {}}}, "test_expressions": {"currentScope": "test_expressions", "scopes": {"test_expressions": {"obj": {"2": {"fullPath": "test_expressions.obj", "line": 2}}}}}, "utils": {"currentScope": "utils", "scopes": {"utils": {"AntPathMatcher": {"79": {"fullPath": "utils.AntPathMatcher", "line": 79}}, "BaseModel": {"5": {"fullPath": "pydantic.BaseModel", "line": 5}}, "Dict": {"3": {"fullPath": "typing.Dict", "line": 3}}, "Iterable": {"3": {"fullPath": "typing.Iterable", "line": 3}}, "List": {"3": {"fullPath": "typing.List", "line": 3}}, "Optional": {"3": {"fullPath": "typing.Optional", "line": 3}}, "StringMatcher": {"8": {"fullPath": "utils.StringMatcher", "line": 8}}, "URL_MATCHER": {"246": {"fullPath": "utils.URL_MATCHER", "line": 246}}, "batch_pattern_match": {"249": {"fullPath": "utils.batch_pattern_match", "line": 249}}, "defaultdict": {"2": {"fullPath": "collections.defaultdict", "line": 2}}, "re": {"1": {"fullPath": "re", "line": 1}}}, "utils.AntPathMatcher": {"_WILDCARD_CHARS": {"88": {"fullPath": "utils.AntPathMatcher._WILDCARD_CHARS", "line": 88}}, "_skip_segment": {"220": {"fullPath": "utils.AntPathMatcher._skip_segment", "line": 220}}, "_skip_separator": {"213": {"fullPath": "utils.AntPathMatcher._skip_separator", "line": 213}}, "cache_patterns": {"84": {"fullPath": "utils.AntPathMatcher.cache_patterns", "line": 84}}, "case_sensitive": {"81": {"fullPath": "utils.AntPathMatcher.case_sensitive", "line": 81}}, "do_match": {"90": {"fullPath": "utils.AntPathMatcher.do_match", "line": 90}}, "get_string_matcher": {"234": {"fullPath": "utils.AntPathMatcher.get_string_matcher", "line": 234}}, "is_potential_match": {"192": {"fullPath": "utils.AntPathMatcher.is_potential_match", "line": 192}}, "match": {"186": {"fullPath": "utils.AntPathMatcher.match", "line": 186}}, "match_start": {"189": {"fullPath": "utils.AntPathMatcher.match_start", "line": 189}}, "match_strings": {"210": {"fullPath": "utils.AntPathMatcher.match_strings", "line": 210}}, "path_separator": {"80": {"fullPath": "utils.AntPathMatcher.path_separator", "line": 80}}, "self": {"79": {"fullPath": "utils.AntPathMatcher", "line": 79}}, "string_matcher_cache": {"86": {"fullPath": "utils.AntPathMatcher.string_matcher_cache", "line": 86}}, "tokenize_path": {"204": {"fullPath": "utils.AntPathMatcher.tokenize_path", "line": 204}}, "tokenize_pattern": {"177": {"fullPath": "utils.AntPathMatcher.tokenize_pattern", "line": 177}}, "tokenized_pattern_cache": {"85": {"fullPath": "utils.AntPathMatcher.tokenized_pattern_cache", "line": 85}}, "trim_tokens": {"82": {"fullPath": "utils.AntPathMatcher.trim_tokens", "line": 82}}}, "utils.AntPathMatcher._skip_segment": {"_skip_segment": {"220": {"fullPath": "utils.AntPathMatcher._skip_segment", "line": 220}}, "c": {"224": {"fullPath": "utils.AntPathMatcher.c", "line": 224}}, "curr_pos": {"227": {"fullPath": "utils.AntPathMatcher.curr_pos", "line": 227}}, "skipped": {"222": {"fullPath": "int", "line": 222}, "231": {"fullPath": "int", "line": 231}}}, "utils.AntPathMatcher._skip_separator": {"_skip_separator": {"213": {"fullPath": "utils.AntPathMatcher._skip_separator", "line": 213}}, "skipped": {"215": {"fullPath": "int", "line": 215}, "217": {"fullPath": "utils.AntPathMatcher.path_separator", "line": 217}}}, "utils.AntPathMatcher.do_match": {"do_match": {"90": {"fullPath": "utils.AntPathMatcher.do_match", "line": 90}}, "found_idx": {"157": {"fullPath": "int", "line": 157}, "165": {"fullPath": "utils.AntPathMatcher.found_idx", "line": 165}}, "pat_idx_tmp": {"147": {"fullPath": "int", "line": 147}, "150": {"fullPath": "utils.AntPathMatcher.pat_idx_tmp", "line": 150}}, "pat_length": {"155": {"fullPath": "int", "line": 155}}, "path_dirs": {"98": {"fullPath": "utils.AntPathMatcher.path_dirs", "line": 98}}, "path_idx_end": {"102": {"fullPath": "int", "line": 102}, "138": {"fullPath": "int", "line": 138}}, "path_idx_start": {"101": {"fullPath": "int", "line": 101}, "111": {"fullPath": "int", "line": 111}, "170": {"fullPath": "utils.AntPathMatcher.path_idx_start", "line": 170}}, "patt_dir": {"105": {"fullPath": "utils.AntPathMatcher.patt_dir", "line": 105}, "132": {"fullPath": "utils.AntPathMatcher.patt_dir", "line": 132}}, "patt_dirs": {"94": {"fullPath": "utils.AntPathMatcher.patt_dirs", "line": 94}}, "patt_idx_end": {"100": {"fullPath": "int", "line": 100}, "137": {"fullPath": "int", "line": 137}}, "patt_idx_start": {"99": {"fullPath": "int", "line": 99}, "110": {"fullPath": "int", "line": 110}, "153": {"fullPath": "int", "line": 153}, "169": {"fullPath": "utils.AntPathMatcher.patt_idx_start", "line": 169}}, "str_length": {"156": {"fullPath": "int", "line": 156}}, "sub_pat": {"160": {"fullPath": "int", "line": 160}}, "sub_str": {"161": {"fullPath": "utils.AntPathMatcher.sub_str", "line": 161}}}, "utils.AntPathMatcher.get_string_matcher": {"get_string_matcher": {"234": {"fullPath": "utils.AntPathMatcher.get_string_matcher", "line": 234}}, "str_matcher": {"238": {"fullPath": "utils.AntPathMatcher.str_matcher", "line": 238}}}, "utils.AntPathMatcher.is_potential_match": {"is_potential_match": {"192": {"fullPath": "utils.AntPathMatcher.is_potential_match", "line": 192}}, "pos": {"194": {"fullPath": "int", "line": 194}, "197": {"fullPath": "utils.AntPathMatcher.skipped", "line": 197}, "201": {"fullPath": "utils.AntPathMatcher.skipped", "line": 201}}, "skipped": {"196": {"fullPath": "utils.AntPathMatcher.skipped", "line": 196}, "198": {"fullPath": "utils.AntPathMatcher.skipped", "line": 198}}}, "utils.AntPathMatcher.match": {"match": {"186": {"fullPath": "utils.AntPathMatcher.match", "line": 186}}, "path": {"186": {"fullPath": "BUILTIN.str", "line": 186}}, "pattern": {"186": {"fullPath": "BUILTIN.str", "line": 186}}}, "utils.AntPathMatcher.match_start": {"match_start": {"189": {"fullPath": "utils.AntPathMatcher.match_start", "line": 189}}, "path": {"189": {"fullPath": "BUILTIN.str", "line": 189}}, "pattern": {"189": {"fullPath": "BUILTIN.str", "line": 189}}}, "utils.AntPathMatcher.match_strings": {"match_strings": {"210": {"fullPath": "utils.AntPathMatcher.match_strings", "line": 210}}}, "utils.AntPathMatcher.tokenize_path": {"tokenize_path": {"204": {"fullPath": "utils.AntPathMatcher.tokenize_path", "line": 204}}}, "utils.AntPathMatcher.tokenize_pattern": {"pattern": {"177": {"fullPath": "BUILTIN.str", "line": 177}}, "result": {"182": {"fullPath": "utils.AntPathMatcher.result", "line": 182}}, "tokenize_pattern": {"177": {"fullPath": "utils.AntPathMatcher.tokenize_pattern", "line": 177}}}, "utils.StringMatcher": {"_DEFAULT_VARIABLE_PATTERN": {"16": {"fullPath": "utils.StringMatcher._DEFAULT_VARIABLE_PATTERN", "line": 16}}, "_GLOB_PATTERN": {"15": {"fullPath": "utils.StringMatcher._GLOB_PATTERN", "line": 15}}, "__init__": {"18": {"fullPath": "utils.StringMatcher.__init__", "line": 18}}, "case_sensitive": {"10": {"fullPath": "utils.StringMatcher.case_sensitive", "line": 10}}, "exact_match": {"11": {"fullPath": "utils.StringMatcher.exact_match", "line": 11}}, "match_strings": {"50": {"fullPath": "utils.StringMatcher.match_strings", "line": 50}}, "pattern": {"13": {"fullPath": "utils.StringMatcher.pattern", "line": 13}}, "raw_pattern": {"9": {"fullPath": "utils.StringMatcher.raw_pattern", "line": 9}}, "self": {"8": {"fullPath": "utils.StringMatcher", "line": 8}}, "variable_names": {"12": {"fullPath": "utils.StringMatcher.variable_names", "line": 12}}}, "utils.StringMatcher.__init__": {"__init__": {"18": {"fullPath": "utils.StringMatcher.__init__", "line": 18}}, "case_sensitive": {"18": {"fullPath": "BUILTIN.bool", "line": 18}}, "colon_idx": {"29": {"fullPath": "str", "line": 29}}, "end": {"21": {"fullPath": "int", "line": 21}, "38": {"fullPath": "utils.StringMatcher.end", "line": 38}}, "pattern": {"18": {"fullPath": "BUILTIN.str", "line": 18}}, "pattern_builder": {"20": {"fullPath": "list", "line": 20}}, "variable_name": {"36": {"fullPath": "int", "line": 36}}, "variable_pattern": {"34": {"fullPath": "int", "line": 34}}}, "utils.StringMatcher.match_strings": {"m": {"58": {"fullPath": "utils.StringMatcher.m", "line": 58}}, "match_strings": {"50": {"fullPath": "utils.StringMatcher.match_strings", "line": 50}}, "raw_str": {"52": {"fullPath": "BUILTIN.str", "line": 52}}, "uri_template_variables": {"53": {"fullPath": "Unknown", "line": 53}}}, "utils.batch_pattern_match": {"batch_pattern_match": {"249": {"fullPath": "utils.batch_pattern_match", "line": 249}}, "patterns": {"249": {"fullPath": "Unknown", "line": 249}}}}}}}