package com.sankuai.deepcode.ast.util;

import com.sankuai.deepcode.ast.model.base.LastMsgRes;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.io.File;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;

import static com.sankuai.deepcode.ast.util.LinuxCmdUtil.*;

@Slf4j
public class GitUtil {

    public static long gitCloneByAlias(String filePath, String gitUrl, String gitName, String alias, int timeout) throws Exception {
        long result = 0;
        if (existsFile(filePath + "/" + gitName + alias)) {
            deleteFile(filePath, gitName + alias);
            gitCloneSingleByAlias(filePath, gitUrl, gitName, alias, timeout);
        } else {
            gitCloneSingleByAlias(filePath, gitUrl, gitName, alias, timeout);
        }
        result = gitSize(filePath, gitName, alias, timeout);
        return result;
    }

    public static String gitCloneSingleByAlias(String filePath, String gitUrl, String gitName, String alias, int timeout) throws Exception {
        List<String> command1 = new ArrayList<>();
        command1.add("git");
        command1.add("clone");
        command1.add(gitUrl);
        command1.add(gitName + alias);
        log.info("[Git] clone {} to {}", gitUrl, gitName + alias);
        return execCmdTimeout(command1, filePath, timeout);
    }

    public static long gitSize(String filePath, String gitName, String alias, int timeout) throws Exception {
        long result = 0;
        List<String> command1 = new ArrayList<>();
        command1.add("du");
        command1.add("-sk");
        String path = filePath + "/" + gitName + alias;
        String sizeRes = execCmdTimeout(command1, path, timeout);
        if (StringUtils.isNotEmpty(sizeRes)) {
            long size = 0;
            try {
                size = Long.valueOf(sizeRes.split("\t")[0]);
            } catch (Exception e) {
            } finally {
                if (size > 0) {
                    result = size;
                }
            }
        }
        return result;
    }

    public static void deleteFile(String filePath, String fileName) {
        List<String> command1 = new ArrayList<>();
        command1.add("rm");
        command1.add("-rf");
        command1.add(fileName);
        execCmd(command1, filePath);
    }

    public static boolean existsFile(String strDir) {
        File file = new File(strDir);
        return file.exists();
    }

    public static String gitResetHard(String filePath, String gitName, String branch) {
        String result = "";
        String path = filePath + "/" + gitName;
        if (existsFile(path)) {
            List<String> command1 = new ArrayList<>();
            command1.add("git");
            command1.add("checkout");
            command1.add(branch);
            result = execCmd(command1, path);

            List<String> command2 = new ArrayList<>();
            command2.add("git");
            command2.add("rev-parse");
            command2.add("HEAD");
            result = execCmd(command2, path);
        }
        return result.trim();
    }

    public static LastMsgRes gitCheckoutAndLastLog(String filePath, String gitName, String branch) {
        LastMsgRes result = new LastMsgRes();
        String path = filePath + "/" + gitName;
        if (existsFile(path)) {
            List<String> command1 = new ArrayList<>();
            command1.add("git");
            command1.add("checkout");
            command1.add(branch);
            execCmd(command1, path);
            String log = execCmdShell("git log -1 --pretty=format:\"%H%n%ad%n%s\" --date=iso", path);
            for (String str : log.split("\n")) {
                result.setCommitId(str);
            }
            result.setCommitId(log.split("\n")[0]);
            LocalDateTime localDateTime = null;
            try {
                SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss Z");
                Date date = formatter.parse(log.split("\n")[1]);
                localDateTime = date.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
            } catch (Exception e) {
//                throw new RuntimeException(e);
            }
            result.setDate(localDateTime);
            try {
                if (log.split("\n")[2].length() > 256) {
                    result.setMsg(log.split("\n")[2].substring(0, 256));
                } else {
                    result.setMsg(log.split("\n")[2]);
                }
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        }
        return result;
    }

    public static String copyR(String filePath, String fileName, String newFileName) {
        if (existsFile(filePath + "/" + newFileName)) {
            deleteFile(filePath, newFileName);
        }
        List<String> command1 = new ArrayList<>();
        command1.add("cp");
        command1.add("-R");
        command1.add(fileName);
        command1.add(newFileName);
        return execCmd(command1, filePath);
    }


    public static String gitBaseByCommit(String filePath, String gitName, String fromCommit, String toCommit) {
        String baseCommit = "";
        String path = filePath + "/" + gitName;
        if (existsFile(filePath)) {
            List<String> command1 = new ArrayList<>();
            command1.add("git");
            command1.add("merge-base");
            command1.add(fromCommit);
            command1.add(toCommit);
            baseCommit = execCmd(command1, path);
        }
        return baseCommit.trim();
    }

    public static String gitBaseByBranch(String filePath, String gitName, String fromBranch, String toBranch) {
        String baseCommit = "";
        String path = filePath + "/" + gitName;
        if (existsFile(filePath)) {
            List<String> command1 = new ArrayList<>();
            command1.add("git");
            command1.add("merge-base");
            command1.add(fromBranch);
            command1.add(toBranch);
            baseCommit = execCmd(command1, path);
        }
        return baseCommit.trim();
    }

    public static List<String> getDiffFileInfos(String path, String repos, String baseCommitId, String lastCommitId) {
        List<String> result = new ArrayList<>();
        path = path + "/" + repos;
        String res = "";
        if (existsFile(path)) {
            List<String> command1 = new ArrayList<>();
            command1.add("git");
            command1.add("diff");
            command1.add("--numstat");
            if (baseCommitId != null) {
                command1.add(baseCommitId);
            }
            command1.add(lastCommitId);
            res = execCmd(command1, path);
            result = Arrays.asList(res.split("\n"));
            if (CollectionUtils.isNotEmpty(result)) {
                return result;
            } else {
                return new ArrayList<>();
            }
        } else {
            return new ArrayList<>();
        }
    }


    public static Map<String, Integer> getCommitCount(String path, String repos) {
        try {
            Map<String, Integer> result = new HashMap<>();
            path = path + "/" + repos;
            String res = "";
            if (existsFile(path)) {
                res = execCmdShell("git log --since=\"6 months ago\" --pretty=format: --name-only | sort | uniq -c | sort -nr", path);
                if (StringUtils.isNotEmpty(res)) {
                    int index = 0;
                    for (String str : res.split("\n")) {
                        if (index == 0) {
                            index++;
                            continue;
                        } else {
                            str = str.trim();
                            String count = str.split(" ")[0];
                            String filePath = str.split(" ")[1];
                            result.put(filePath, Integer.valueOf(count));
                        }
                        index++;
                    }
                }
                return result;
            } else {
                return new HashMap<>();
            }
        } catch (Exception e) {
            System.out.println("计算commit count异常" + e);
            return new HashMap<>();
        }
    }
}
