package com.sankuai.deepcode.ast.scss.visitor;

import com.sankuai.deepcode.ast.model.scss.ScssRule;
import com.sankuai.deepcode.ast.model.scss.ScssSelector;
import com.sankuai.deepcode.ast.model.scss.ScssVariable;
import com.sankuai.deepcode.ast.scss.gen.ScssParser;
import com.sankuai.deepcode.ast.scss.gen.ScssParserBaseVisitor;
import com.sankuai.deepcode.ast.scss.visitor.MyBlockVisitor;
import lombok.Getter;

import java.util.ArrayList;
import java.util.List;

public class MyMediaDeclarationVisitor extends ScssParserBaseVisitor<Object> {

    @Getter
    private ScssRule scssRule;

    @Override
    public Object visitMediaDeclaration(ScssParser.MediaDeclarationContext ctx) {
        scssRule = new ScssRule();
        scssRule.setStartLine(ctx.getStart().getLine());
        scssRule.setEndLine(ctx.getStop().getLine());
        scssRule.setBody(ctx.getText());
        visitMediaQueryList(ctx.mediaQueryList());
        visitBlock(ctx.block());
        return null;
    }


    @Override
    public Object visitMediaQueryList(ScssParser.MediaQueryListContext ctx) {
        //todo 媒体查询 简单处理成string
        ScssParser.MediaQueryContext mediaQueryContext = ctx.mediaQuery(0);
        if (null != mediaQueryContext.identifier()) {
            List<ScssSelector> selectors = new ArrayList<>();
            ScssSelector scssSelector = new ScssSelector();
            scssSelector.setName(mediaQueryContext.identifier().getText());
            selectors.add(scssSelector);
            scssRule.setSelectors(selectors);
        }
        return null;
    }


    @Override
    public Object visitBlock(ScssParser.BlockContext ctx) {
        com.sankuai.deepcode.ast.scss.visitor.MyBlockVisitor visitor = new MyBlockVisitor(scssRule);
        visitor.visitBlock(ctx);
//todo 媒体block简单处理成string
        return null;
    }


}
