package com.sankuai.deepcode.ast.scss.visitor;

import com.sankuai.deepcode.ast.model.scss.ScssVariable;
import com.sankuai.deepcode.ast.scss.gen.ScssParser;
import com.sankuai.deepcode.ast.scss.gen.ScssParserBaseVisitor;
import lombok.Getter;

public class MyExtendDeclarationVisitor extends ScssParserBaseVisitor<Object> {

    @Getter
    private ScssVariable scssVariable;


    @Override
    public Object visitExtendDeclaration(ScssParser.ExtendDeclarationContext ctx) {
        //todo 继承
        return null;
    }


}
