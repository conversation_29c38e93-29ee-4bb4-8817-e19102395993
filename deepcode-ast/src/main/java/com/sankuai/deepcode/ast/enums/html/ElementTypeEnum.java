package com.sankuai.deepcode.ast.enums.html;

public enum ElementTypeEnum {
    CDATA("cdata", "字符数据"),
    SCRIPTLET("scriptLet", "脚本片段"),
    XMLDESC("xmlDesc", "xml声明"),
    DTDDESC("dtdDesc", "dtd声明"),
    ELEMENT("element", "元素"),
    SCRIPT("script", "脚本"),
    STYLE("style", "样式");

    private String code;

    private String desc;

    ElementTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return this.code;
    }


    public String getDesc() {
        return desc;
    }

    @Override
    public String toString() {
        return "ErrorCodeEnum{" +
                "code=" + code +
                ", desc='" + desc + '\'' +
                '}';
    }
}
