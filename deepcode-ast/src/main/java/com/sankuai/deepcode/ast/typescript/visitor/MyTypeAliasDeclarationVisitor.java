package com.sankuai.deepcode.ast.typescript.visitor;

import com.sankuai.deepcode.ast.model.typescript.ScriptNode;
import com.sankuai.deepcode.ast.model.typescript.ScriptGenerics;
import com.sankuai.deepcode.ast.model.typescript.ScriptLiteral;
import com.sankuai.deepcode.ast.model.typescript.ScriptVariable;
import com.sankuai.deepcode.ast.typescript.gen.TypeScriptParser;
import com.sankuai.deepcode.ast.typescript.gen.TypeScriptParserBaseVisitor;
import lombok.Getter;

import java.util.ArrayList;
import java.util.List;

public class MyTypeAliasDeclarationVisitor extends TypeScriptParserBaseVisitor<Object> {

    @Getter
    private ScriptVariable scriptVariable;

    private ScriptNode scriptNode;

    MyTypeAliasDeclarationVisitor(ScriptNode myScriptNode) {
        scriptVariable = new ScriptVariable();
        scriptNode = myScriptNode;
        scriptNode.getVariables().add(scriptVariable);
    }

    @Override
    public Object visitTypeAliasDeclaration(TypeScriptParser.TypeAliasDeclarationContext ctx) {
        scriptVariable.setStartLine(ctx.getStart().getLine());
        scriptVariable.setEndLine(ctx.getStop().getLine());
        scriptVariable.setBody(ctx.getText());
        if (ctx.Export() != null) {
            scriptVariable.setExport(true);
        }
        List<ScriptLiteral> literal = new ArrayList<>();
        ScriptLiteral scriptLiteral = new ScriptLiteral();
        scriptLiteral.setType("type");
        literal.add(scriptLiteral);
        scriptVariable.setLiteral(literal);

        scriptVariable.setName(ctx.identifier().getText());

        //todo   typeParameters 简单处理
        if (ctx.typeParameters() != null && ctx.typeParameters().typeParameterList() != null) {
            ScriptGenerics scriptGenerics = new ScriptGenerics();
            scriptGenerics.setName(ctx.typeParameters().typeParameterList().getText());
            scriptVariable.setGenerics(scriptGenerics);
        }

        visitType_(ctx.type_());
        return null;
    }


    @Override
    public Object visitType_(TypeScriptParser.Type_Context ctx) {
        //todo  type_ 暂不处理

        return null;
    }


}
