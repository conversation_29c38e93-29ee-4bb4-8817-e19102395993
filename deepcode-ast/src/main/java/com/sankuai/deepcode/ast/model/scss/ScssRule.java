package com.sankuai.deepcode.ast.model.scss;

import com.sankuai.deepcode.ast.enums.ChangeTypeEnum;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

@Data
public class ScssRule {
    private String type;
    private int startLine;
    private int endLine;
    private int changeType = ChangeTypeEnum.DEFAULT.getCode();
    private List<Integer> changeLines = new ArrayList<>();
    private String body="";
    private List<Integer> commentLines = new ArrayList<>();
    private List<ScssVariable> variables = new ArrayList<>();
    private List<ScssSelector> selectors = new ArrayList<>();
    private List<ScssRule> children = new ArrayList<>();

    private String pathName = "";

    public void initPathName() {
        initPathName(new StringBuffer());
    }

    private void initPathName(StringBuffer pathName) {
        for (ScssSelector selector : selectors) {
            if (StringUtils.isNotEmpty(selector.getName())) {
                pathName.append(selector.getName());
            }
            if (CollectionUtils.isNotEmpty(children)) {
                for (ScssRule child : children) {
                    child.initPathName(pathName);
                }
            }
        }
        this.pathName = pathName.toString();
    }

}
