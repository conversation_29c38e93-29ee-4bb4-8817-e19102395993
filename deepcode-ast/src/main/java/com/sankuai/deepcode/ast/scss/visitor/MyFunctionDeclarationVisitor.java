package com.sankuai.deepcode.ast.scss.visitor;

import com.sankuai.deepcode.ast.model.scss.ScssRule;
import com.sankuai.deepcode.ast.scss.gen.ScssParser;
import com.sankuai.deepcode.ast.scss.gen.ScssParserBaseVisitor;
import lombok.Getter;

public class MyFunctionDeclarationVisitor extends ScssParserBaseVisitor<Object> {

    @Getter
    private ScssRule scssRule;


    @Override
    public Object visitFunctionDeclaration(ScssParser.FunctionDeclarationContext ctx) {
        scssRule = new ScssRule();
        scssRule.setStartLine(ctx.Function().getSymbol().getLine());
        scssRule.setEndLine(ctx.Function().getSymbol().getLine());
        scssRule.setBody(ctx.getText());
        //todo 函数声明  定义方法
//        contentDeclaration
//        : Content (Lparen parameters Rparen)? Semi?
//        ;
        return null;
    }


}
