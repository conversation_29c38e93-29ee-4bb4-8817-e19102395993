package com.sankuai.deepcode.ast.model.python3;

import com.sankuai.deepcode.ast.model.base.BaseNode;
import com.sankuai.deepcode.ast.model.base.LocationInfo;
import com.sankuai.deepcode.ast.util.Md5Util;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ToString(callSuper = true)
public class PythonFunctionCallNode extends BaseNode {
    /**
     * 发起调用的模块名称，如果在类内部，会包含类名称，如：module.ClassName
     */
    private String moduleName;
    /**
     * 模块路径, 会包
     */
    private String modulePath;

    /**
     * 函数名称
     */
    private String functionName;

    /**
     * 完整路径
     */
    private String filePath;

    /**
     * 函数调用原始代码
     */
    private String rawCode;

    /**
     * 原始调用参数
     */
    private String originCallArgs;

    /**
     * 函数参数列表
     * 权且只保存 字符串表，不保存对象
     */
    private List<String> arguments;
    /**
     * 调用来源模块路径
     */
    private String sourceModulePath;
    /**
     * 标识是否为 super() 调用
     */
    private boolean isSuperCall;

    /**
     * 如果是 super() 调用，存储被调用的方法名
     */
    private String superMethodName;
    /**
     * 函数调用的位置信息
     */
    private LocationInfo start;
    private LocationInfo end;

    /**
     * 设置为 super() 调用
     *
     * @param methodName 被调用的方法名
     */
    public PythonFunctionCallNode setSuperCall(String methodName) {
        this.isSuperCall = true;
        this.superMethodName = methodName;
        this.functionName = "super()." + methodName;

        return this;
    }


    @Override
    public String getVid() {
        String sourceModulePathWithName = getSourceModulePath() + "." + getFunctionName();
        if (StringUtils.isNotEmpty(sourceModulePathWithName)) {
            return Md5Util.stringToMd5(sourceModulePathWithName);
        }
        return StringUtils.EMPTY;
    }
}