package com.sankuai.deepcode.ast.model.foreend;

import com.sankuai.deepcode.ast.model.scss.ScssSelector;
import com.sankuai.deepcode.ast.model.scss.ScssVariable;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
public class StyleNodeInfo extends ForeEndNodeInfo {
    private List<ScssVariable> variables = new ArrayList<>();
    private List<ScssSelector> selectors = new ArrayList<>();
}
