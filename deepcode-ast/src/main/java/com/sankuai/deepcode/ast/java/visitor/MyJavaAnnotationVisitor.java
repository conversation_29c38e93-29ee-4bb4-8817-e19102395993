package com.sankuai.deepcode.ast.java.visitor;

import com.sankuai.deepcode.ast.java.gen.JavaParser;
import com.sankuai.deepcode.ast.java.gen.JavaParserBaseVisitor;
import com.sankuai.deepcode.ast.model.java.JavaAnnotation;
import com.sankuai.deepcode.ast.model.java.JavaAnnotationValue;
import lombok.Getter;
import org.apache.commons.collections4.CollectionUtils;

import java.util.List;

public class MyJavaAnnotationVisitor extends JavaParserBaseVisitor<Object> {
    //TODO 注解元素全部处理成键值对  不做复杂解析
    // 类字面量  @MyAnnotation(String.class)
    // 枚举常量  @StatusAnnotation(Status.ACTIVE)
    // 注解嵌套  @Outer(@Outer.Inner(name = "example"))
    // 元素数组  @DBUnit(replacers = {Replacer.class})

    @Getter
    private JavaAnnotation annotation = new JavaAnnotation();

    @Override
    public Object visitAnnotation(JavaParser.AnnotationContext ctx) {
        annotation.setStartLine(ctx.getStart().getLine());
        annotation.setEndLine(ctx.getStop().getLine());
        if (null != ctx.qualifiedName()) {
            annotation.setName(ctx.qualifiedName().getText());
        } else {
            List<JavaParser.IdentifierContext> identifierContexts = ctx.altAnnotationQualifiedName().identifier();
            annotation.setName(identifierContexts.get(identifierContexts.size() - 1).getText());
        }

        if (ctx.elementValuePairs() != null && CollectionUtils.isNotEmpty(ctx.elementValuePairs().elementValuePair())) {
            for (JavaParser.ElementValuePairContext elementValuePairContext : ctx.elementValuePairs().elementValuePair()) {
                JavaAnnotationValue javaAnnotationValue = new JavaAnnotationValue();
                javaAnnotationValue.setName(elementValuePairContext.identifier().getText());
                javaAnnotationValue.setValue(elementValuePairContext.elementValue().getText());
                annotation.getValues().add(javaAnnotationValue);
            }
        }

        if (null != ctx.elementValue()) {
            JavaAnnotationValue javaAnnotationValue = new JavaAnnotationValue();
            javaAnnotationValue.setName("");
            javaAnnotationValue.setValue(ctx.getText());
            annotation.getValues().add(javaAnnotationValue);
        }

        return null;
    }
}
