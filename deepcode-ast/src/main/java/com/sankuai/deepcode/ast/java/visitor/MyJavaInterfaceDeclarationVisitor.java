package com.sankuai.deepcode.ast.java.visitor;

import com.sankuai.deepcode.ast.enums.java.ClassTypeEnum;
import com.sankuai.deepcode.ast.java.gen.JavaParser;
import com.sankuai.deepcode.ast.java.gen.JavaParserBaseVisitor;
import com.sankuai.deepcode.ast.java.visitor.*;
import com.sankuai.deepcode.ast.java.visitor.MyJavaAnnotationVisitor;
import com.sankuai.deepcode.ast.java.visitor.MyJavaClassDeclarationVisitor;
import com.sankuai.deepcode.ast.java.visitor.MyJavaConstDeclarationVisitor;
import com.sankuai.deepcode.ast.java.visitor.MyJavaEnumDeclarationVisitor;
import com.sankuai.deepcode.ast.java.visitor.MyJavaTypeTypeVistor;
import com.sankuai.deepcode.ast.model.java.*;
import com.sankuai.deepcode.ast.util.Md5Util;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;

public class MyJavaInterfaceDeclarationVisitor extends JavaParserBaseVisitor<Object> {

    private ClassNode parentClassNode;
    @Getter
    private ClassNode classNode = new ClassNode();
    private String packageName;

    public MyJavaInterfaceDeclarationVisitor(ClassNode myParentClassNode, ClassNode myClassNode, String myPackageName) {
        parentClassNode = myParentClassNode;
        classNode = myClassNode;
        packageName = myPackageName;
    }

    @Override
    public Object visitInterfaceDeclaration(JavaParser.InterfaceDeclarationContext ctx) {
        classNode.setClassType(ClassTypeEnum.INTERFACE.getCode());
        classNode.setStartLine(ctx.getStart().getLine());
        classNode.setEndLine(ctx.getStop().getLine());
        if (null == parentClassNode) {
            if (StringUtils.isEmpty(packageName)) {
                classNode.setClassName(ctx.identifier().getText());
            } else {
                classNode.setClassName(packageName + "." + ctx.identifier().getText());
            }
        } else {
            classNode.setClassName(parentClassNode.getClassName());
            if (StringUtils.isEmpty(parentClassNode.getInClassName())) {
                classNode.setInClassName(ctx.identifier().getText());
            } else {
                classNode.setInClassName(parentClassNode.getInClassName() + "$" + ctx.identifier().getText());
            }
        }
        if (ctx.typeParameters() != null) {
            for (JavaParser.TypeParameterContext typeParameterContext : ctx.typeParameters().typeParameter()) {
                String typeName = typeParameterContext.identifier().getText();
                JavaGenerics javaGenerics = new JavaGenerics();
                javaGenerics.setName(typeName);
                classNode.getGenerics().add(javaGenerics);
            }
        }
        if (ctx.typeList() != null) {
            for (JavaParser.TypeListContext typeListContext : ctx.typeList()) {
                com.sankuai.deepcode.ast.java.visitor.MyJavaTypeTypeVistor visitor = new MyJavaTypeTypeVistor();
                visitor.visit(typeListContext);
                JavaImplements javaImplements = new JavaImplements();
                javaImplements.setName(visitor.getType());
                javaImplements.setGenerics(visitor.getGenerics());
                classNode.getInterfaces().add(javaImplements);
            }
        }
        //todo PERMITS typeList  Java17暂不处理
        if (ctx.interfaceBody() != null) {
            visitInterfaceBody(ctx.interfaceBody());
        }
        return null;
    }

    @Override
    public Object visitInterfaceBody(JavaParser.InterfaceBodyContext ctx) {
        if (ctx.interfaceBodyDeclaration() != null) {
            for (JavaParser.InterfaceBodyDeclarationContext interfaceMemberDeclarationContext : ctx.interfaceBodyDeclaration()) {
                String access = "";
                List<JavaAnnotation> annotations = new ArrayList<>();
                for (JavaParser.ModifierContext modifierContext : interfaceMemberDeclarationContext.modifier()) {
                    if (null != modifierContext.classOrInterfaceModifier()) {
                        if (null != modifierContext.classOrInterfaceModifier().annotation()) {
                            com.sankuai.deepcode.ast.java.visitor.MyJavaAnnotationVisitor visitor = new MyJavaAnnotationVisitor();
                            visitor.visit(modifierContext.classOrInterfaceModifier().annotation());
                            annotations.add(visitor.getAnnotation());
                        } else {
                            access += " " + modifierContext.classOrInterfaceModifier().getText();
                        }
                    }
                }
                access = access.trim();
                JavaParser.InterfaceMemberDeclarationContext interfaced = interfaceMemberDeclarationContext.interfaceMemberDeclaration();
                if (interfaced != null) {
                    if (interfaced.constDeclaration() != null) {
                        com.sankuai.deepcode.ast.java.visitor.MyJavaConstDeclarationVisitor visitor = new MyJavaConstDeclarationVisitor(classNode);
                        visitor.visit(interfaced.constDeclaration());
                        for (FieldNode fieldNode : visitor.getFieldNodes()) {
                            fieldNode.setAccess(access);
                            fieldNode.setAnnotations(annotations);
                            classNode.getFieldNodes().add(fieldNode);
                        }
                    } else if (interfaced.interfaceMethodDeclaration() != null) {
                        MyJavaInterfaceMethodDeclarationVisitor visitor = new MyJavaInterfaceMethodDeclarationVisitor(classNode);
                        visitor.visit(interfaced.interfaceMethodDeclaration());
                        visitor.getMethodNode().setAccess(access);
                        visitor.getMethodNode().setAnnotations(annotations);
                        classNode.getMethodNodes().add(visitor.getMethodNode());
                    } else if (interfaced.genericInterfaceMethodDeclaration() != null) {
                        MyJavaGenericInterfaceMethodDeclarationVisitor visitor = new MyJavaGenericInterfaceMethodDeclarationVisitor(classNode);
                        visitor.visit(interfaced.genericInterfaceMethodDeclaration());
                        visitor.getMethodNode().setAccess(access);
                        visitor.getMethodNode().setAnnotations(annotations);
                        classNode.getMethodNodes().add(visitor.getMethodNode());
                    } else if (interfaced.interfaceDeclaration() != null) {
                        ClassNode inClassNode = initInClassNode(classNode, interfaced);
                        MyJavaInterfaceDeclarationVisitor visitor = new MyJavaInterfaceDeclarationVisitor(classNode, inClassNode, packageName);
                        visitor.visit(interfaced.interfaceDeclaration());
                        visitor.getClassNode().setAccess(access);
                        visitor.getClassNode().setAnnotations(annotations);
                        classNode.getInClassNodes().add(visitor.getClassNode());
                    } else if (interfaced.annotationTypeDeclaration() != null) {
                        ClassNode inClassNode = initInClassNode(classNode, interfaced);
                        MyAnnotationTypeDeclarationVisitor visitor = new MyAnnotationTypeDeclarationVisitor(classNode, inClassNode, packageName);
                        visitor.visitAnnotationTypeDeclaration(interfaced.annotationTypeDeclaration());
                        visitor.getClassNode().setAccess(access);
                        visitor.getClassNode().setAnnotations(annotations);
                        classNode.getInClassNodes().add(visitor.getClassNode());
                    } else if (interfaced.classDeclaration() != null) {
                        ClassNode inClassNode = initInClassNode(classNode, interfaced);
                        com.sankuai.deepcode.ast.java.visitor.MyJavaClassDeclarationVisitor visitor = new MyJavaClassDeclarationVisitor(classNode, inClassNode, packageName);
                        visitor.visit(interfaced.classDeclaration());
                        visitor.getClassNode().setAccess(access);
                        visitor.getClassNode().setAnnotations(annotations);
                        classNode.getInClassNodes().add(visitor.getClassNode());
                    } else if (interfaced.enumDeclaration() != null) {
                        ClassNode inClassNode = initInClassNode(classNode, interfaced);
                        com.sankuai.deepcode.ast.java.visitor.MyJavaEnumDeclarationVisitor visitor = new MyJavaEnumDeclarationVisitor(classNode, inClassNode, packageName);
                        visitor.visit(interfaced.enumDeclaration());
                        visitor.getClassNode().setAccess(access);
                        visitor.getClassNode().setAnnotations(annotations);
                        classNode.getInClassNodes().add(visitor.getClassNode());
                    }
                    //todo   recordDeclaration Java17暂不处理
                }
            }
        }
        return null;
    }

    public ClassNode initInClassNode(ClassNode classNode, JavaParser.InterfaceMemberDeclarationContext interfaced) {
        ClassNode inClassNode = new ClassNode();
        inClassNode.setFileVid(Md5Util.filePathToMd5(classNode.getClassPath()));
        inClassNode.setClassPath(classNode.getClassPath());
        inClassNode.setClassName(classNode.getClassName());
        inClassNode.setStartLine(interfaced.getStart().getLine());
        inClassNode.setEndLine(interfaced.getStop().getLine());
        return inClassNode;
    }
}
