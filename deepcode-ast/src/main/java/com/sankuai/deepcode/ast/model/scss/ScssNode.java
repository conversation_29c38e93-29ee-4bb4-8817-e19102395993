package com.sankuai.deepcode.ast.model.scss;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
public class ScssNode {
    private List<ScssImport> imports = new ArrayList<>();
    private List<ScssVariable> variables = new ArrayList<>();
    private List<ScssRule> scssRules = new ArrayList<>();
    private List<Integer> commentLines = new ArrayList<>();
    private String filePath;


    public ScssNode deepCopy() {
        ScssNode scssNode = new ScssNode();
        scssNode.setImports(this.imports);
        scssNode.setVariables(this.variables);
        scssNode.setScssRules(this.scssRules);
        scssNode.setCommentLines(this.commentLines);
        scssNode.setFilePath(this.filePath);
        return scssNode;
    }
}
