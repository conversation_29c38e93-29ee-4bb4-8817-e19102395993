package com.sankuai.deepcode.ast.model.foreend;

import com.sankuai.deepcode.ast.model.html.ElementValue;
import com.sankuai.deepcode.ast.model.html.HtmlAttribute;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
public class HtmlNodeInfo extends ForeEndNodeInfo {
    private String tagName;
    private String tagType;
    private int tagDepth;
    private int tagIndex;
    private List<ElementValue> elements = new ArrayList<>();
    private List<HtmlAttribute> attributes = new ArrayList();
}
