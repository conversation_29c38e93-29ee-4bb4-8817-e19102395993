package com.sankuai.deepcode.ast.model.base;

import com.sankuai.deepcode.ast.enums.ChangeTypeEnum;
import com.sankuai.deepcode.ast.enums.CheckTypeEnum;
import com.sankuai.deepcode.ast.model.base.CodeView;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class GitDiffInfo {
    private String moduleName = "";
    private String path = "";
    private String fileName = "";
    private String fileType = "";
    private int changeType = ChangeTypeEnum.DEFAULT.getCode();
    private List<Integer> changeLines = new ArrayList<>();

    private int checkType = CheckTypeEnum.DEFAULT.getCode();
    private List<Integer> checkLines = new ArrayList<>();
    private List<CodeView> codeViews = new ArrayList<>();
    private int commitCount = 0;
}
