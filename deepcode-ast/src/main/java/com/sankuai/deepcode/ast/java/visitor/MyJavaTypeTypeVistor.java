package com.sankuai.deepcode.ast.java.visitor;

import com.sankuai.deepcode.ast.java.gen.JavaParser;
import com.sankuai.deepcode.ast.java.gen.JavaParserBaseVisitor;
import com.sankuai.deepcode.ast.java.visitor.MyJavaAnnotationVisitor;
import com.sankuai.deepcode.ast.model.java.JavaAnnotation;
import com.sankuai.deepcode.ast.model.java.JavaGenerics;
import lombok.Getter;
import org.antlr.v4.runtime.tree.TerminalNode;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;

public class MyJavaTypeTypeVistor extends JavaParserBaseVisitor<Object> {

    //TODO 泛型目前仅处理签名名称  未解析类型

    @Getter
    private List<JavaAnnotation> annotations = new ArrayList<>();

    @Getter
    private String type;

    @Getter
    private List<JavaGenerics> generics = new ArrayList<>();

    @Override
    public Object visitTypeType(JavaParser.TypeTypeContext ctx) {
        if (CollectionUtils.isNotEmpty(ctx.annotation())) {
            for (JavaParser.AnnotationContext annotationContext : ctx.annotation()) {
                visitAnnotation(annotationContext);
            }
        }
        if (ctx.primitiveType() != null) {
            visitPrimitiveType(ctx.primitiveType());
        }
        if (CollectionUtils.isNotEmpty(ctx.LBRACK())) {
            for (TerminalNode terminalNode : ctx.LBRACK()) {
                JavaGenerics javaGenerics = new JavaGenerics();
                javaGenerics.setName("[]");
                generics.add(javaGenerics);
            }
        }
        if (ctx.classOrInterfaceType() != null) {
            visitClassOrInterfaceType(ctx.classOrInterfaceType());
        }
        return null;
    }


    @Override
    public Object visitAnnotation(JavaParser.AnnotationContext ctx) {
        MyJavaAnnotationVisitor visitor = new MyJavaAnnotationVisitor();
        visitor.visit(ctx);
        annotations.add(visitor.getAnnotation());
        return null;
    }

    @Override
    public Object visitClassOrInterfaceType(JavaParser.ClassOrInterfaceTypeContext ctx) {
        if (StringUtils.isEmpty(type)) {
            //todo   这样写内部类 全路径解析有问题  暂不修正
            if (ctx.getText() != null && ctx.getText().contains(".")) {
                if (ctx.getText().split("\\.").length == 2 && !ctx.getText().contains("(")) {
                    type = ctx.getStart().getText();
                } else {
                    type = ctx.typeIdentifier().getText();
                }
            } else {
                type = ctx.typeIdentifier().getText();
            }
            if (null != ctx.typeArguments()) {
                for (JavaParser.TypeArgumentsContext typeArgumentsContext : ctx.typeArguments()) {
                    if (null != typeArgumentsContext.typeArgument()) {
                        for (JavaParser.TypeArgumentContext typeArgumentContext : typeArgumentsContext.typeArgument()) {
                            JavaGenerics javaGenerics = new JavaGenerics();
                            javaGenerics.setName(typeArgumentContext.getText());
                            generics.add(javaGenerics);
                        }
                    }
                }
            }
        }
        return null;
    }

    @Override
    public Object visitPrimitiveType(JavaParser.PrimitiveTypeContext ctx) {
        type = ctx.getText();
        return null;
    }


}
