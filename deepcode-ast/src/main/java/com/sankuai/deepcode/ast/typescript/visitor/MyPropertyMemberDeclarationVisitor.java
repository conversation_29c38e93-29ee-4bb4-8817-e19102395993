package com.sankuai.deepcode.ast.typescript.visitor;

import com.sankuai.deepcode.ast.model.typescript.*;
import com.sankuai.deepcode.ast.typescript.gen.TypeScriptParser;
import com.sankuai.deepcode.ast.typescript.gen.TypeScriptParserBaseVisitor;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;

public class MyPropertyMemberDeclarationVisitor extends TypeScriptParserBaseVisitor<Object> {

    private ScriptClass scriptClass;
    private List<String> decorators;
    private ScriptMethod method;
    private ScriptVariable variable;

    MyPropertyMemberDeclarationVisitor(ScriptClass myScriptClass, List<String> myDecorators) {
        scriptClass = myScriptClass;
        decorators = myDecorators;
    }


    @Override
    public Object visitPropertyDeclarationExpression(TypeScriptParser.PropertyDeclarationExpressionContext ctx) {
        variable = new ScriptVariable();
        variable.setDecorators(decorators);
        variable.setStartLine(ctx.start.getLine());
        variable.setEndLine(ctx.stop.getLine());
        variable.setBody(ctx.getText());
        visitPropertyMemberBase(ctx.propertyMemberBase());
        //todo propertyName 简单处理
        variable.setName(ctx.propertyName().getText());
        if (ctx.QuestionMark() != null) {
            variable.setQuestionMark(true);
        }
        if (ctx.typeAnnotation() != null) {
            variable.setType(ctx.typeAnnotation().getText());
        }
        if (ctx.initializer() != null) {
            variable.setValue(ctx.initializer().getText());
        }
        scriptClass.getVariables().add(variable);
        return null;
    }


    @Override
    public Object visitMethodDeclarationExpression(TypeScriptParser.MethodDeclarationExpressionContext ctx) {
        method = new ScriptMethod();
        method.setDecorators(decorators);
        method.setStartLine(ctx.start.getLine());
        method.setEndLine(ctx.stop.getLine());
        method.setBody(ctx.getText());
        visitPropertyMemberBase(ctx.propertyMemberBase());
        method.setMethodName(ctx.propertyName().getText());
        visitCallSignature(ctx.callSignature());
        com.sankuai.deepcode.ast.typescript.visitor.MyFunctionBodyVisitor.visitFunctionBody(ctx.functionBody(), method);
        scriptClass.getMethods().add(method);
        return null;
    }


    @Override
    public Object visitCallSignature(TypeScriptParser.CallSignatureContext ctx) {
        //todo   callSignature 简单处理
        if (ctx.typeParameters() != null && ctx.typeParameters().typeParameterList() != null) {
            for (TypeScriptParser.TypeParameterContext typeParameterContext : ctx.typeParameters().typeParameterList().typeParameter()) {
                ScriptGenerics scriptGenerics = new ScriptGenerics();
                scriptGenerics.setName(typeParameterContext.getText());
                method.getGenerics().add(scriptGenerics);
            }
        }

        if (ctx.parameterList() != null) {
            if (ctx.parameterList().restParameter() != null) {
                ScriptMethodParam scriptMethodParam1 = new ScriptMethodParam();
                scriptMethodParam1.setName("...");
                method.getParams().add(scriptMethodParam1);
                ScriptMethodParam scriptMethodParam2 = new ScriptMethodParam();
                scriptMethodParam2.setName("...");
                if (ctx.parameterList().restParameter().typeAnnotation() != null) {
                    scriptMethodParam2.setType(ctx.parameterList().restParameter().typeAnnotation().type_().getText());
                }
                method.getParams().add(scriptMethodParam2);
            } else if (ctx.parameterList().parameter() != null) {
                for (TypeScriptParser.ParameterContext parameterContext : ctx.parameterList().parameter()) {
                    ScriptMethodParam scriptMethodParam = new ScriptMethodParam();
                    if (parameterContext.requiredParameter() != null) {
                        scriptMethodParam.setRequired(true);
                        if (parameterContext.requiredParameter().decoratorList() != null) {
                            List<String> decorators = new ArrayList<>();
                            for (TypeScriptParser.DecoratorContext decoratorContext : parameterContext.requiredParameter().decoratorList().decorator()) {
                                decorators.add(decoratorContext.getText());
                            }
                            scriptMethodParam.setDecorators(decorators);
                        }
                        if (parameterContext.requiredParameter().accessibilityModifier() != null) {
                            scriptMethodParam.setModifier(parameterContext.requiredParameter().accessibilityModifier().getText());
                        }
                        scriptMethodParam.setName(parameterContext.requiredParameter().identifierOrPattern().getText());
                        if (parameterContext.requiredParameter().typeAnnotation() != null) {
                            scriptMethodParam.setType(parameterContext.requiredParameter().typeAnnotation().type_().getText());
                        }
                    } else {
                        if (parameterContext.optionalParameter().decoratorList() != null) {
                            List<String> decorators = new ArrayList<>();
                            for (TypeScriptParser.DecoratorContext decoratorContext : parameterContext.optionalParameter().decoratorList().decorator()) {
                                decorators.add(decoratorContext.getText());
                            }
                            scriptMethodParam.setDecorators(decorators);
                        }
                        if (parameterContext.optionalParameter().accessibilityModifier() != null) {
                            scriptMethodParam.setModifier(parameterContext.optionalParameter().accessibilityModifier().getText());
                        }
                        scriptMethodParam.setName(parameterContext.optionalParameter().identifierOrPattern().getText());
                        if (parameterContext.optionalParameter().initializer() != null) {
                            scriptMethodParam.setDefaultValue(parameterContext.optionalParameter().initializer().getText());
                        }
                        if (parameterContext.optionalParameter().typeAnnotation() != null) {
                            scriptMethodParam.setType(parameterContext.optionalParameter().typeAnnotation().type_().getText());
                        }
                    }
                    method.getParams().add(scriptMethodParam);
                }
            }
        }

        if (ctx.typeAnnotation() != null) {
            ScriptMethodReturn scriptMethodReturn = new ScriptMethodReturn();
            scriptMethodReturn.setType(ctx.typeAnnotation().type_().getText());
            method.setMethodReturn(scriptMethodReturn);
        } else {
            ScriptMethodReturn scriptMethodReturn = new ScriptMethodReturn();
            scriptMethodReturn.setType("void");
            method.setMethodReturn(scriptMethodReturn);
        }
        return null;
    }


    @Override
    public Object visitGetterSetterDeclarationExpression(TypeScriptParser.GetterSetterDeclarationExpressionContext ctx) {
        method = new ScriptMethod();
        method.setDecorators(decorators);
        method.setStartLine(ctx.start.getLine());
        method.setEndLine(ctx.stop.getLine());
        method.setBody(ctx.getText());
        visitPropertyMemberBase(ctx.propertyMemberBase());
        if (ctx.getAccessor() != null) {
            method.setMethodName("get " + ctx.getAccessor().getter().classElementName().getText());
            ScriptMethodReturn methodReturn = new ScriptMethodReturn();
            if (ctx.getAccessor().typeAnnotation() != null) {
                methodReturn.setType(ctx.getAccessor().typeAnnotation().type_().getText());
            }
            method.setMethodReturn(methodReturn);
            com.sankuai.deepcode.ast.typescript.visitor.MyFunctionBodyVisitor.visitFunctionBody(ctx.getAccessor().functionBody(), method);
        } else if (ctx.setAccessor() != null) {
            method.setMethodName("set " + ctx.setAccessor().setter().classElementName().getText());
            //todo 定义属性set参数暂不处理 formalParameterList
            MyFunctionBodyVisitor.visitFunctionBody(ctx.setAccessor().functionBody(), method);
        }
        scriptClass.getMethods().add(method);
        return null;
    }


    @Override
    public Object visitAbstractMemberDeclaration(TypeScriptParser.AbstractMemberDeclarationContext ctx) {
        com.sankuai.deepcode.ast.typescript.visitor.MyAbstractDeclarationVisitor myAbstractDeclarationVisitor = new MyAbstractDeclarationVisitor(null);
        myAbstractDeclarationVisitor.visit(ctx);
        myAbstractDeclarationVisitor.getScriptMethod().setDecorators(decorators);
        scriptClass.getMethods().add(myAbstractDeclarationVisitor.getScriptMethod());
        return null;
    }


    @Override
    public Object visitPropertyMemberBase(TypeScriptParser.PropertyMemberBaseContext ctx) {
        String modifier = "";
        if (ctx.accessibilityModifier() != null) {
            modifier = ctx.accessibilityModifier().getText();
        }
        if (ctx.Async() != null) {
            if (StringUtils.isEmpty(modifier)) {
                modifier = ctx.Async().getText();
            } else {
                modifier += " " + ctx.Async().getText();
            }
        }
        if (ctx.Static() != null) {
            if (StringUtils.isEmpty(modifier)) {
                modifier = ctx.Static().getText();
            } else {
                modifier += " " + ctx.Static().getText();
            }
        }
        if (ctx.ReadOnly() != null) {
            if (StringUtils.isEmpty(modifier)) {
                modifier = ctx.ReadOnly().getText();
            } else {
                modifier += " " + ctx.ReadOnly().getText();
            }
        }
        if (variable != null) {
            variable.setModifier(modifier);
        } else if (method != null) {
            method.setModifier(modifier);
        }
        return null;
    }


}
