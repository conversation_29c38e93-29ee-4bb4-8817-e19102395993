package com.sankuai.deepcode.ast.model.java;

import com.sankuai.deepcode.ast.model.java.*;
import com.sankuai.deepcode.ast.model.java.ClassNode;
import com.sankuai.deepcode.ast.model.java.FieldNode;
import com.sankuai.deepcode.ast.model.java.FileNode;
import com.sankuai.deepcode.ast.model.java.MethodNode;
import lombok.Data;

import java.util.*;

@Data
public class JavaAnalysisRes {
    private String fromCommit;
    private String toCommit;
    private String buildCommit;
    private Map<String, ClassNode> classNodeMap = new HashMap<>();
    private Map<String, MethodNode> methodNodeMap = new HashMap<>();
    private Map<String, FieldNode> fieldNodeMap = new HashMap<>();
    private Map<String, FileNode> fileNodeMap = new HashMap<>();
//    private List<LocalFieldNode> localFieldNodes = new ArrayList<>();

    private Map<String, Set<String>> classImportClass = new HashMap<>();
    private Map<String, Set<String>> classDefineMethod = new HashMap<>();
    private Map<String, Set<String>> classDefineField = new HashMap<>();
    private Map<String, List<MethodInvokeMethodEdge>> methodInvokeMethod = new HashMap<>();
    private Map<String, List<MethodQuoteFieldEdge>> methodQuoteField = new HashMap<>();
//    private Map<String, String> methodQuoteField = new HashMap<>();
//    private Map<String, String> methodDefineField = new HashMap<>();

    private Map<String, List<MethodNode>> overloadingMethodMap = new HashMap<>();
    private Set<String> filterPath = new HashSet<>();

    public void addClassImportClass(String source, String target) {
        if (classImportClass.containsKey(source)) {
            classImportClass.get(source).add(target);
        } else {
            Set<String> set = new HashSet<>();
            set.add(target);
            classImportClass.put(source, set);
        }
    }

    public void addClassDefineMethod(String source, String target) {
        if (classDefineMethod.containsKey(source)) {
            classDefineMethod.get(source).add(target);
        } else {
            Set<String> set = new HashSet<>();
            set.add(target);
            classDefineMethod.put(source, set);
        }
    }

    public void addClassDefineField(String source, String target) {
        if (classDefineField.containsKey(source)) {
            classDefineField.get(source).add(target);
        } else {
            Set<String> set = new HashSet<>();
            set.add(target);
            classDefineField.put(source, set);
        }
    }
}
