package com.sankuai.deepcode.ast.html.visitor;

import com.sankuai.deepcode.ast.enums.html.ElementTypeEnum;
import com.sankuai.deepcode.ast.foreend.InitForeEndCommon;
import com.sankuai.deepcode.ast.html.gen.HTMLParser;
import com.sankuai.deepcode.ast.html.gen.HTMLParserBaseVisitor;
import com.sankuai.deepcode.ast.model.html.HtmlAttribute;
import com.sankuai.deepcode.ast.model.html.HtmlNode;
import com.sankuai.deepcode.ast.scss.gen.ScssLexer;
import com.sankuai.deepcode.ast.scss.gen.ScssParser;
import com.sankuai.deepcode.ast.scss.visitor.MyScssVisitor;
import com.sankuai.deepcode.ast.typescript.gen.TypeScriptLexer;
import com.sankuai.deepcode.ast.typescript.gen.TypeScriptParser;
import com.sankuai.deepcode.ast.typescript.visitor.MyTypeScriptVisitor;
import lombok.Getter;
import org.antlr.v4.runtime.CharStreams;
import org.antlr.v4.runtime.CommonTokenStream;
import org.antlr.v4.runtime.tree.ParseTree;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

public class MyHtmlElementVisitor extends HTMLParserBaseVisitor<Object> {

    @Getter
    private HtmlNode htmlNode;

    public MyHtmlElementVisitor(HtmlNode myHtmlNode) {
        htmlNode = myHtmlNode;
    }

    @Override
    public Object visitHtmlElement(HTMLParser.HtmlElementContext ctx) {
        htmlNode.setStartLine(ctx.start.getLine());
        htmlNode.setEndLine(ctx.stop.getLine());
        if (ctx.SCRIPTLET() != null) {
            htmlNode.setTagType(ElementTypeEnum.SCRIPTLET.getCode());
        } else if (ctx.script() != null) {
            String code = "";
            if (ctx.script().SCRIPT_BODY() != null) {
                int startLine = ctx.script().SCRIPT_BODY().getSymbol().getLine();
                code = ctx.script().SCRIPT_BODY().getText();
                int lastIndex = code.lastIndexOf("</script>");
                if (lastIndex != -1) {
                    code = code.substring(0, lastIndex)
                            + code.substring(lastIndex + "</script>".length());
                }
                for (int i = 1; i < startLine - 1; i++) {
                    code = "\n" + code;
                }
            }
            if (ctx.script().SCRIPT_SHORT_BODY() != null) {
                int startLine = ctx.script().SCRIPT_SHORT_BODY().getSymbol().getLine();
                code = ctx.script().SCRIPT_SHORT_BODY().getText();
                int lastIndex = code.lastIndexOf("</>");
                if (lastIndex != -1) {
                    code = code.substring(0, lastIndex)
                            + code.substring(lastIndex + "</>".length());
                }
                for (int i = 1; i < startLine - 1; i++) {
                    code = "\n" + code;
                }
            }

            TypeScriptLexer lexer = new TypeScriptLexer(CharStreams.fromString(code));
            CommonTokenStream tokens = new CommonTokenStream(lexer);
            TypeScriptParser parser = new TypeScriptParser(tokens);
            ParseTree tree = parser.program();

            MyTypeScriptVisitor myTypeScriptVisitor = new MyTypeScriptVisitor();
            myTypeScriptVisitor.visit(tree);
            if (myTypeScriptVisitor.getScriptNode() != null) {
                htmlNode.getScriptStates().add(myTypeScriptVisitor.getScriptNode());
                InitForeEndCommon.initCommonByToken(tokens, htmlNode.getCommentLines());
            }
        } else if (ctx.style() != null) {
            String code = ctx.style().STYLE_BODY().getText();
            int lastIndex = code.lastIndexOf("</style>");
            if (lastIndex != -1) {
                code = code.substring(0, lastIndex)
                        + code.substring(lastIndex + "</style>".length());
            }
            int startLine = ctx.getStart().getLine();
            for (int i = 1; i < startLine - 1; i++) {
                code = "\n" + code;
            }

            ScssLexer lexer = new ScssLexer(CharStreams.fromString(code));
            CommonTokenStream tokens = new CommonTokenStream(lexer);
            ScssParser parser = new ScssParser(tokens);
            ParseTree tree = parser.stylesheet();

            MyScssVisitor myScssVisitor = new MyScssVisitor();
            myScssVisitor.visit(tree);

            if (myScssVisitor.getScssNode() != null) {
                htmlNode.getScssNodes().add(myScssVisitor.getScssNode());
                InitForeEndCommon.initCommonByToken(tokens, htmlNode.getCommentLines());
            }
        } else if (ctx.TAG_OPEN() != null) {
            htmlNode.setTagType(ElementTypeEnum.ELEMENT.getCode());
            if(CollectionUtils.isNotEmpty(ctx.TAG_NAME())){
                htmlNode.setTagName(ctx.TAG_NAME().get(0).getText());
            }
            if (CollectionUtils.isNotEmpty(ctx.htmlAttribute())) {
                for (HTMLParser.HtmlAttributeContext attributeContext : ctx.htmlAttribute()) {
                    visitHtmlAttribute(attributeContext);
                }
            }
            if (ctx.htmlContent() != null && StringUtils.isNotEmpty(ctx.htmlContent().getText())) {
                com.sankuai.deepcode.ast.html.visitor.MyHtmlContentVisitor myHtmlContentVisitor = new MyHtmlContentVisitor(htmlNode);
                myHtmlContentVisitor.visit(ctx.htmlContent());
            }
        }
        return null;
    }


    @Override
    public Object visitHtmlAttribute(HTMLParser.HtmlAttributeContext ctx) {
        HtmlAttribute htmlAttribute = new HtmlAttribute();
        htmlAttribute.setStartLine(ctx.start.getLine());
        htmlAttribute.setEndLine(ctx.stop.getLine());
        htmlAttribute.setTagName(ctx.TAG_NAME().getText());
        if (ctx.ATTVALUE_VALUE() != null) {
            htmlAttribute.setTagValue(ctx.ATTVALUE_VALUE().getText());
        }
        htmlNode.getHtmlAttributes().add(htmlAttribute);
        return null;
    }


}
