package com.sankuai.deepcode.ast.xml.visitor;

import com.sankuai.deepcode.ast.model.xml.XmlElement;
import com.sankuai.deepcode.ast.xml.gen.XMLParser;
import com.sankuai.deepcode.ast.xml.gen.XMLParserBaseVisitor;
import lombok.Getter;
import org.antlr.v4.runtime.ParserRuleContext;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class MyXmlVisitor extends XMLParserBaseVisitor<Object> {

    @Getter
    private XmlElement element;

    private XmlElement lastXmlElement;

    public Map<ParserRuleContext, List<XmlElement>> parentElement = new HashMap<>();

    @Override
    public Object visitElement(XMLParser.ElementContext ctx) {
        XmlElement xmlElement = new XmlElement();
        xmlElement.setName(ctx.Name(0).getText());
        if(null != ctx.content()){
            xmlElement.setContent(ctx.content().getText());
        }
        if (element == null) {
            element = xmlElement;
        } else {
            List<XmlElement> xmlElementParents = parentElement.get(ctx.getParent());
            if (xmlElementParents != null) {
                xmlElementParents.add(xmlElement);
            } else {
                xmlElementParents = new ArrayList<>();
                xmlElementParents.add(xmlElement);
                parentElement.put(ctx.getParent(), xmlElementParents);
                if (null != lastXmlElement){
                    lastXmlElement.setChildElement(xmlElementParents);
                }
            }
        }
        lastXmlElement = xmlElement;
        return visitChildren(ctx);
    }


    @Override
    public Object visitAttribute(XMLParser.AttributeContext ctx) {
        return visitChildren(ctx);
    }
}
