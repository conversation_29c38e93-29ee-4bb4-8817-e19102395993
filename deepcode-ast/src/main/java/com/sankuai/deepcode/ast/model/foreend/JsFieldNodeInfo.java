package com.sankuai.deepcode.ast.model.foreend;

import com.sankuai.deepcode.ast.model.typescript.ScriptLiteral;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
public class JsFieldNodeInfo extends ForeEndNodeInfo {
    private boolean isExport;
    private boolean isReadOnly;
    private boolean isDecare;
    private String type;  //array object
    private List<String> decorators = new ArrayList<>();
    private List<ScriptLiteral> literal = new ArrayList<>();
    private String modifier;
    private String name;
    private String value;
}
