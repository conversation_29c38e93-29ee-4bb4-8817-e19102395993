package com.sankuai.deepcode.ast.java.visitor;

import com.sankuai.deepcode.ast.java.gen.JavaParser;
import com.sankuai.deepcode.ast.java.gen.JavaParserBaseVisitor;
import com.sankuai.deepcode.ast.model.java.ClassNode;
import com.sankuai.deepcode.ast.model.java.FieldNode;
import com.sankuai.deepcode.ast.model.java.JavaGenerics;
import com.sankuai.deepcode.ast.util.DeepCopy;
import lombok.Getter;

import java.util.ArrayList;
import java.util.List;

public class MyJavaFieldVisitor extends JavaParserBaseVisitor<Object> {

    @Getter
    private List<FieldNode> fieldNodes = new ArrayList();

    private FieldNode baseFieldNode = new FieldNode();

    private ClassNode classNode;

    public MyJavaFieldVisitor(ClassNode myClassNode) {
        classNode = myClassNode;
    }

    private String typeStr = "";

    @Override
    public Object visitFieldDeclaration(JavaParser.FieldDeclarationContext ctx) {
        if (null != ctx.typeType()) {
            typeStr = ctx.typeType().getText();
            MyJavaTypeTypeVistor myJavaTypeTypeVistor = new MyJavaTypeTypeVistor();
            myJavaTypeTypeVistor.visit(ctx.typeType());
            baseFieldNode.setFieldType(myJavaTypeTypeVistor.getType());
            List<String> generics = new ArrayList<>();
            for (JavaGenerics javaGenerics : myJavaTypeTypeVistor.getGenerics()) {
                generics.add(javaGenerics.getName());
            }
            baseFieldNode.setSignatures(generics);
        }
        baseFieldNode.setClassName(classNode.getClassName());
        baseFieldNode.setInClassName(classNode.getInClassName());
        baseFieldNode.setStartLine(ctx.getStart().getLine());
        baseFieldNode.setEndLine(ctx.getStop().getLine());
        visitVariableDeclarators(ctx.variableDeclarators());
        return null;
    }

    @Override
    public Object visitVariableDeclarators(JavaParser.VariableDeclaratorsContext ctx) {
        int index = 0;
        for (JavaParser.VariableDeclaratorContext variableDeclaratorContext : ctx.variableDeclarator()) {
            if (index == 0) {
                baseFieldNode.setFieldName(variableDeclaratorContext.variableDeclaratorId().getText());
                if (null != variableDeclaratorContext.variableInitializer()) {
                    baseFieldNode.setValue(variableDeclaratorContext.variableInitializer().getText());
                }
                baseFieldNode.setBody(typeStr + variableDeclaratorContext.getText());
                fieldNodes.add(baseFieldNode);
            } else {
                FieldNode fieldNode = DeepCopy.deepCopy(baseFieldNode, FieldNode.class);
                if (null != variableDeclaratorContext.variableInitializer()) {
                    fieldNode.setValue(variableDeclaratorContext.variableInitializer().getText());
                }
                fieldNode.setBody(typeStr + variableDeclaratorContext.getText());
                fieldNodes.add(fieldNode);
            }
            index++;
        }
        return null;
    }
}
