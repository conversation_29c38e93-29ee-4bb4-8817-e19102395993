package com.sankuai.deepcode.ast.typescript.visitor;

import com.sankuai.deepcode.ast.model.typescript.*;
import com.sankuai.deepcode.ast.typescript.gen.TypeScriptParser;
import com.sankuai.deepcode.ast.typescript.gen.TypeScriptParserBaseVisitor;
import lombok.Getter;
import org.antlr.v4.runtime.tree.ParseTree;
import org.apache.commons.collections4.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

public class MySingleExpressionVisitor extends TypeScriptParserBaseVisitor<Object> {

    @Getter
    private ScriptNode scriptNode;

    MySingleExpressionVisitor(ScriptNode myScriptNode) {
        if (myScriptNode == null) {
            scriptNode = new ScriptNode();
        } else {
            scriptNode = myScriptNode;
        }
    }

    @Override
    public Object visitFunctionExpression(TypeScriptParser.FunctionExpressionContext ctx) {
        if (ctx.anonymousFunction().functionDeclaration() != null) {
            MyFunctionDeclarationVisitor myFunctionDeclarationVisitor = new MyFunctionDeclarationVisitor(null);
            myFunctionDeclarationVisitor.visit(ctx.anonymousFunction().functionDeclaration());
            if (myFunctionDeclarationVisitor.getScriptMethod() != null) {
                scriptNode.getMethods().add(myFunctionDeclarationVisitor.getScriptMethod());
            }
        } else if (ctx.anonymousFunction().arrowFunctionDeclaration() != null) {
            MyArrowFunctionDeclarationVisitor myArrowFunctionDeclarationVisitor = new MyArrowFunctionDeclarationVisitor(null);
            myArrowFunctionDeclarationVisitor.visit(ctx.anonymousFunction().arrowFunctionDeclaration());
            if (myArrowFunctionDeclarationVisitor.getScriptMethod() != null) {
                scriptNode.getMethods().add(myArrowFunctionDeclarationVisitor.getScriptMethod());
            }
        } else {
            ScriptMethod scriptMethod = new ScriptMethod();
            scriptMethod.setStartLine(ctx.anonymousFunction().start.getLine());
            scriptMethod.setEndLine(ctx.anonymousFunction().stop.getLine());
            scriptMethod.setBody(ctx.getText());
            scriptNode.getMethods().add(scriptMethod);
            if (ctx.anonymousFunction().Async() != null) {
                scriptMethod.setAsync(true);
            }
            if (ctx.anonymousFunction().formalParameterList() != null) {
                //todo 参数暂不处理 formalParameterList
            }
            if (ctx.anonymousFunction().typeAnnotation() != null) {
                ScriptMethodReturn scriptMethodReturn = new ScriptMethodReturn();
                scriptMethodReturn.setType(ctx.anonymousFunction().typeAnnotation().type_().getText());
                scriptMethod.setMethodReturn(scriptMethodReturn);
            }
            MyFunctionBodyVisitor.visitFunctionBody(ctx.anonymousFunction().functionBody(), scriptMethod);
        }
        return null;
    }

    @Override
    public Object visitClassExpression(TypeScriptParser.ClassExpressionContext ctx) {
        com.sankuai.deepcode.ast.typescript.visitor.MyClassExpressionVisitor myClassExpressionVisitor = new MyClassExpressionVisitor();
        myClassExpressionVisitor.visit(ctx);
        if (myClassExpressionVisitor.getScriptClass() != null) {
            scriptNode.getClasses().add(myClassExpressionVisitor.getScriptClass());
        }
        return null;
    }


    @Override
    public Object visitMemberIndexExpression(TypeScriptParser.MemberIndexExpressionContext ctx) {
        ScriptMethod scriptMethod = new ScriptMethod();
        scriptMethod.setStartLine(ctx.start.getLine());
        scriptMethod.setEndLine(ctx.stop.getLine());
        scriptMethod.setBody(ctx.getText());
        //TODO 简单处理...[]调用
        scriptMethod.setMethodName(ctx.expressionSequence().getText());
        scriptMethod.setDefineType(ctx.singleExpression().getText());
        for (TypeScriptParser.SingleExpressionContext singleExpressionContext : ctx.expressionSequence().singleExpression()) {
            MySingleExpressionVisitor mySingleExpressionVisitor = new MySingleExpressionVisitor(null);
            mySingleExpressionVisitor.visit(singleExpressionContext);
            if (mySingleExpressionVisitor.getScriptNode() != null) {
                if (CollectionUtils.isNotEmpty(mySingleExpressionVisitor.getScriptNode().getInvokeMethods())) {
                    scriptNode.getInvokeMethods().addAll(mySingleExpressionVisitor.getScriptNode().getInvokeMethods());
                }
            }
        }
        scriptNode.getInvokeMethods().add(scriptMethod);
        return null;
    }


    @Override
    public Object visitOptionalChainExpression(TypeScriptParser.OptionalChainExpressionContext ctx) {
        ScriptMethod scriptMethod = new ScriptMethod();
        scriptMethod.setStartLine(ctx.start.getLine());
        scriptMethod.setEndLine(ctx.stop.getLine());
        scriptMethod.setBody(ctx.getText());
        //TODO 简单处理...调用
        scriptMethod.setMethodName(ctx.singleExpression(1).getText());
        scriptMethod.setDefineType(ctx.singleExpression(0).getText());
        MySingleExpressionVisitor mySingleExpressionVisitor = new MySingleExpressionVisitor(null);
        mySingleExpressionVisitor.visit(ctx.singleExpression(1));
        if (mySingleExpressionVisitor.getScriptNode() != null) {
            if (CollectionUtils.isNotEmpty(mySingleExpressionVisitor.getScriptNode().getInvokeMethods())) {
                scriptNode.getInvokeMethods().addAll(mySingleExpressionVisitor.getScriptNode().getInvokeMethods());
            }
        }
        scriptNode.getInvokeMethods().add(scriptMethod);
        return null;
    }


    @Override
    public Object visitMemberDotExpression(TypeScriptParser.MemberDotExpressionContext ctx) {
        ScriptMethod scriptMethod = new ScriptMethod();
        scriptMethod.setStartLine(ctx.start.getLine());
        scriptMethod.setEndLine(ctx.stop.getLine());
        scriptMethod.setBody(ctx.getText());
        scriptMethod.setMethodName(ctx.identifierName().getText());
        //TODO 简单处理...调用
        scriptMethod.setDefineType(ctx.singleExpression().getText());
        MySingleExpressionVisitor mySingleExpressionVisitor = new MySingleExpressionVisitor(null);
        mySingleExpressionVisitor.visit(ctx.singleExpression());
        if (mySingleExpressionVisitor.getScriptNode() != null) {
            if (CollectionUtils.isNotEmpty(mySingleExpressionVisitor.getScriptNode().getInvokeMethods())) {
                scriptNode.getInvokeMethods().addAll(mySingleExpressionVisitor.getScriptNode().getInvokeMethods());
            }
        }
        if (ctx.typeGeneric() != null) {
            List<ScriptGenerics> generics = new ArrayList<>();
            for (TypeScriptParser.TypeArgumentContext typeArgumentContext : ctx.typeGeneric().typeArgumentList().typeArgument()) {
                ScriptGenerics scriptGenerics = new ScriptGenerics();
                scriptGenerics.setName(typeArgumentContext.getText());
                generics.add(scriptGenerics);
            }
            if (ctx.typeGeneric().typeGeneric() != null) {
                ScriptGenerics scriptGenerics = new ScriptGenerics();
                scriptGenerics.setName(ctx.typeGeneric().typeGeneric().getText());
                generics.add(scriptGenerics);
            }
            scriptMethod.setGenerics(generics);
        }
        scriptNode.getInvokeMethods().add(scriptMethod);
        return null;
    }

    @Override
    public Object visitNewExpression(TypeScriptParser.NewExpressionContext ctx) {
        ScriptMethod scriptMethod = new ScriptMethod();
        scriptMethod.setStartLine(ctx.start.getLine());
        scriptMethod.setEndLine(ctx.stop.getLine());
        scriptMethod.setBody(ctx.getText());
        //todo 构造函数简单处理
        scriptMethod.setMethodName(ctx.singleExpression().getText());
        scriptMethod.getMethodTypes().add("constructor");
        //todo 构造函数参数简单处理
        if (ctx.typeArguments() != null) {
            List<ScriptGenerics> generics = new ArrayList<>();
            for (TypeScriptParser.TypeArgumentContext typeArgumentContext : ctx.typeArguments().typeArgumentList().typeArgument()) {
                ScriptGenerics scriptGenerics = new ScriptGenerics();
                scriptGenerics.setName(typeArgumentContext.getText());
                generics.add(scriptGenerics);
            }
            scriptMethod.setGenerics(generics);
        }
        if (ctx.arguments() != null && ctx.arguments().argumentList() != null) {
            List<ScriptMethodParam> params = new ArrayList<>();
            for (TypeScriptParser.ArgumentContext argumentContext : ctx.arguments().argumentList().argument()) {
                ScriptMethodParam scriptMethodParam = new ScriptMethodParam();
                scriptMethodParam.setValue(argumentContext.getText());
                params.add(scriptMethodParam);

                MySingleExpressionVisitor mySingleExpressionVisitor = new MySingleExpressionVisitor(null);
                mySingleExpressionVisitor.visit(argumentContext.singleExpression());
                if (mySingleExpressionVisitor.getScriptNode() != null) {
                    if (CollectionUtils.isNotEmpty(mySingleExpressionVisitor.getScriptNode().getInvokeMethods())) {
                        scriptNode.getInvokeMethods().addAll(mySingleExpressionVisitor.getScriptNode().getInvokeMethods());
                    }
                }
            }
            scriptMethod.setParams(params);
        }
        scriptNode.getInvokeMethods().add(scriptMethod);
        return null;
    }

    @Override
    public Object visitArgumentsExpression(TypeScriptParser.ArgumentsExpressionContext ctx) {
        ScriptMethod scriptMethod = new ScriptMethod();
        scriptMethod.setStartLine(ctx.start.getLine());
        scriptMethod.setEndLine(ctx.stop.getLine());
        scriptMethod.setBody(ctx.getText());
        StringBuilder stringBuffer = new StringBuilder();
        for (ParseTree parseTree : ctx.singleExpression().children) {
            stringBuffer.append(parseTree.getText());
        }
        if (stringBuffer.toString().startsWith("(")) {
            stringBuffer.deleteCharAt(0);
            stringBuffer.deleteCharAt(stringBuffer.length() - 1);
        }
        scriptMethod.setMethodName(stringBuffer.toString().split("\\.")[stringBuffer.toString().split("\\.").length - 1]);
        try {
            scriptMethod.setDefineType(stringBuffer.toString().split("." + scriptMethod.getMethodName())[0]);
        } catch (Exception e) {
            //todo 待处理  先简单切分
            scriptMethod.setDefineType(stringBuffer.toString());
        }
        List<ScriptMethodParam> params = new ArrayList<>();
        if (ctx.arguments().argumentList() != null) {
            for (TypeScriptParser.ArgumentContext argumentContext : ctx.arguments().argumentList().argument()) {
                ScriptMethodParam scriptMethodParam = new ScriptMethodParam();
                scriptMethodParam.setValue(argumentContext.getText());
                params.add(scriptMethodParam);

                MySingleExpressionVisitor mySingleExpressionVisitor = new MySingleExpressionVisitor(null);
                mySingleExpressionVisitor.visit(argumentContext.singleExpression());
                if (mySingleExpressionVisitor.getScriptNode() != null) {
                    if (CollectionUtils.isNotEmpty(mySingleExpressionVisitor.getScriptNode().getInvokeMethods())) {
                        scriptNode.getInvokeMethods().addAll(mySingleExpressionVisitor.getScriptNode().getInvokeMethods());
                    }
                }
            }
        }
        scriptMethod.setParams(params);
        scriptNode.getInvokeMethods().add(scriptMethod);
        return null;
    }

    @Override
    public Object visitPostIncrementExpression(TypeScriptParser.PostIncrementExpressionContext ctx) {
        //无需处理
        return null;
    }

    @Override
    public Object visitPostDecreaseExpression(TypeScriptParser.PostDecreaseExpressionContext ctx) {
        //无需处理
        return null;
    }

    @Override
    public Object visitDeleteExpression(TypeScriptParser.DeleteExpressionContext ctx) {
        MySingleExpressionVisitor mySingleExpressionVisitor = new MySingleExpressionVisitor(null);
        mySingleExpressionVisitor.visit(ctx.singleExpression());
        if (mySingleExpressionVisitor.getScriptNode() != null) {
            if (CollectionUtils.isNotEmpty(mySingleExpressionVisitor.getScriptNode().getInvokeMethods())) {
                scriptNode.getInvokeMethods().addAll(mySingleExpressionVisitor.getScriptNode().getInvokeMethods());
            }
        }
        return null;
    }

    @Override
    public Object visitVoidExpression(TypeScriptParser.VoidExpressionContext ctx) {
        MySingleExpressionVisitor mySingleExpressionVisitor = new MySingleExpressionVisitor(null);
        mySingleExpressionVisitor.visit(ctx.singleExpression());
        if (mySingleExpressionVisitor.getScriptNode() != null) {
            if (CollectionUtils.isNotEmpty(mySingleExpressionVisitor.getScriptNode().getInvokeMethods())) {
                scriptNode.getInvokeMethods().addAll(mySingleExpressionVisitor.getScriptNode().getInvokeMethods());
            }
        }
        return null;
    }

    @Override
    public Object visitTypeofExpression(TypeScriptParser.TypeofExpressionContext ctx) {
        MySingleExpressionVisitor mySingleExpressionVisitor = new MySingleExpressionVisitor(null);
        mySingleExpressionVisitor.visit(ctx.singleExpression());
        if (mySingleExpressionVisitor.getScriptNode() != null) {
            if (CollectionUtils.isNotEmpty(mySingleExpressionVisitor.getScriptNode().getInvokeMethods())) {
                scriptNode.getInvokeMethods().addAll(mySingleExpressionVisitor.getScriptNode().getInvokeMethods());
            }
        }
        return null;
    }

    @Override
    public Object visitPreIncrementExpression(TypeScriptParser.PreIncrementExpressionContext ctx) {
        //无需处理
        return null;
    }

    @Override
    public Object visitPreDecreaseExpression(TypeScriptParser.PreDecreaseExpressionContext ctx) {
        //无需处理
        return null;
    }

    @Override
    public Object visitUnaryPlusExpression(TypeScriptParser.UnaryPlusExpressionContext ctx) {
        //无需处理
        return null;
    }

    @Override
    public Object visitUnaryMinusExpression(TypeScriptParser.UnaryMinusExpressionContext ctx) {
        //无需处理
        return null;
    }

    @Override
    public Object visitBitNotExpression(TypeScriptParser.BitNotExpressionContext ctx) {
        //无需处理
        return null;
    }

    @Override
    public Object visitNotExpression(TypeScriptParser.NotExpressionContext ctx) {
        //无需处理
        return null;
    }

    @Override
    public Object visitAwaitExpression(TypeScriptParser.AwaitExpressionContext ctx) {
        MySingleExpressionVisitor mySingleExpressionVisitor = new MySingleExpressionVisitor(null);
        mySingleExpressionVisitor.visit(ctx.singleExpression());
        if (mySingleExpressionVisitor.getScriptNode() != null) {
            if (CollectionUtils.isNotEmpty(mySingleExpressionVisitor.getScriptNode().getMethods())) {
                for (ScriptMethod scriptMethod : mySingleExpressionVisitor.getScriptNode().getMethods()) {
                    scriptMethod.setAwait(true);
                    scriptNode.getMethods().add(scriptMethod);
                }
            }
        }
        return null;
    }

    @Override
    public Object visitPowerExpression(TypeScriptParser.PowerExpressionContext ctx) {
        for (TypeScriptParser.SingleExpressionContext singleExpressionContext : ctx.singleExpression()) {
            MySingleExpressionVisitor mySingleExpressionVisitor = new MySingleExpressionVisitor(null);
            mySingleExpressionVisitor.visit(singleExpressionContext);
            if (mySingleExpressionVisitor.getScriptNode() != null) {
                if (CollectionUtils.isNotEmpty(mySingleExpressionVisitor.getScriptNode().getInvokeMethods())) {
                    scriptNode.getInvokeMethods().addAll(mySingleExpressionVisitor.getScriptNode().getInvokeMethods());
                }
            }
        }
        return null;
    }

    @Override
    public Object visitMultiplicativeExpression(TypeScriptParser.MultiplicativeExpressionContext ctx) {
        for (TypeScriptParser.SingleExpressionContext singleExpressionContext : ctx.singleExpression()) {
            MySingleExpressionVisitor mySingleExpressionVisitor = new MySingleExpressionVisitor(null);
            mySingleExpressionVisitor.visit(singleExpressionContext);
            if (mySingleExpressionVisitor.getScriptNode() != null) {
                if (CollectionUtils.isNotEmpty(mySingleExpressionVisitor.getScriptNode().getInvokeMethods())) {
                    scriptNode.getInvokeMethods().addAll(mySingleExpressionVisitor.getScriptNode().getInvokeMethods());
                }
            }
        }
        return null;
    }

    @Override
    public Object visitAdditiveExpression(TypeScriptParser.AdditiveExpressionContext ctx) {
        for (TypeScriptParser.SingleExpressionContext singleExpressionContext : ctx.singleExpression()) {
            MySingleExpressionVisitor mySingleExpressionVisitor = new MySingleExpressionVisitor(null);
            mySingleExpressionVisitor.visit(singleExpressionContext);
            if (mySingleExpressionVisitor.getScriptNode() != null) {
                if (CollectionUtils.isNotEmpty(mySingleExpressionVisitor.getScriptNode().getInvokeMethods())) {
                    scriptNode.getInvokeMethods().addAll(mySingleExpressionVisitor.getScriptNode().getInvokeMethods());
                }
            }
        }
        return null;
    }

    @Override
    public Object visitCoalesceExpression(TypeScriptParser.CoalesceExpressionContext ctx) {
        for (TypeScriptParser.SingleExpressionContext singleExpressionContext : ctx.singleExpression()) {
            MySingleExpressionVisitor mySingleExpressionVisitor = new MySingleExpressionVisitor(null);
            mySingleExpressionVisitor.visit(singleExpressionContext);
            if (mySingleExpressionVisitor.getScriptNode() != null) {
                if (CollectionUtils.isNotEmpty(mySingleExpressionVisitor.getScriptNode().getInvokeMethods())) {
                    scriptNode.getInvokeMethods().addAll(mySingleExpressionVisitor.getScriptNode().getInvokeMethods());
                }
            }
        }
        return null;
    }

    @Override
    public Object visitBitShiftExpression(TypeScriptParser.BitShiftExpressionContext ctx) {
        for (TypeScriptParser.SingleExpressionContext singleExpressionContext : ctx.singleExpression()) {
            MySingleExpressionVisitor mySingleExpressionVisitor = new MySingleExpressionVisitor(null);
            mySingleExpressionVisitor.visit(singleExpressionContext);
            if (mySingleExpressionVisitor.getScriptNode() != null) {
                if (CollectionUtils.isNotEmpty(mySingleExpressionVisitor.getScriptNode().getInvokeMethods())) {
                    scriptNode.getInvokeMethods().addAll(mySingleExpressionVisitor.getScriptNode().getInvokeMethods());
                }
            }
        }
        return null;
    }

    @Override
    public Object visitRelationalExpression(TypeScriptParser.RelationalExpressionContext ctx) {
        for (TypeScriptParser.SingleExpressionContext singleExpressionContext : ctx.singleExpression()) {
            MySingleExpressionVisitor mySingleExpressionVisitor = new MySingleExpressionVisitor(null);
            mySingleExpressionVisitor.visit(singleExpressionContext);
            if (mySingleExpressionVisitor.getScriptNode() != null) {
                if (CollectionUtils.isNotEmpty(mySingleExpressionVisitor.getScriptNode().getInvokeMethods())) {
                    scriptNode.getInvokeMethods().addAll(mySingleExpressionVisitor.getScriptNode().getInvokeMethods());
                }
            }
        }
        return null;
    }

    @Override
    public Object visitInstanceofExpression(TypeScriptParser.InstanceofExpressionContext ctx) {
        for (TypeScriptParser.SingleExpressionContext singleExpressionContext : ctx.singleExpression()) {
            MySingleExpressionVisitor mySingleExpressionVisitor = new MySingleExpressionVisitor(null);
            mySingleExpressionVisitor.visit(singleExpressionContext);
            if (mySingleExpressionVisitor.getScriptNode() != null) {
                if (CollectionUtils.isNotEmpty(mySingleExpressionVisitor.getScriptNode().getInvokeMethods())) {
                    scriptNode.getInvokeMethods().addAll(mySingleExpressionVisitor.getScriptNode().getInvokeMethods());
                }
            }
        }
        return null;
    }

    @Override
    public Object visitInExpression(TypeScriptParser.InExpressionContext ctx) {
        for (TypeScriptParser.SingleExpressionContext singleExpressionContext : ctx.singleExpression()) {
            MySingleExpressionVisitor mySingleExpressionVisitor = new MySingleExpressionVisitor(null);
            mySingleExpressionVisitor.visit(singleExpressionContext);
            if (mySingleExpressionVisitor.getScriptNode() != null) {
                if (CollectionUtils.isNotEmpty(mySingleExpressionVisitor.getScriptNode().getInvokeMethods())) {
                    scriptNode.getInvokeMethods().addAll(mySingleExpressionVisitor.getScriptNode().getInvokeMethods());
                }
            }
        }
        return null;
    }

    @Override
    public Object visitEqualityExpression(TypeScriptParser.EqualityExpressionContext ctx) {
        for (TypeScriptParser.SingleExpressionContext singleExpressionContext : ctx.singleExpression()) {
            MySingleExpressionVisitor mySingleExpressionVisitor = new MySingleExpressionVisitor(null);
            mySingleExpressionVisitor.visit(singleExpressionContext);
            if (mySingleExpressionVisitor.getScriptNode() != null) {
                if (CollectionUtils.isNotEmpty(mySingleExpressionVisitor.getScriptNode().getInvokeMethods())) {
                    scriptNode.getInvokeMethods().addAll(mySingleExpressionVisitor.getScriptNode().getInvokeMethods());
                }
            }
        }
        return null;
    }

    @Override
    public Object visitBitAndExpression(TypeScriptParser.BitAndExpressionContext ctx) {
        for (TypeScriptParser.SingleExpressionContext singleExpressionContext : ctx.singleExpression()) {
            MySingleExpressionVisitor mySingleExpressionVisitor = new MySingleExpressionVisitor(null);
            mySingleExpressionVisitor.visit(singleExpressionContext);
            if (mySingleExpressionVisitor.getScriptNode() != null) {
                if (CollectionUtils.isNotEmpty(mySingleExpressionVisitor.getScriptNode().getInvokeMethods())) {
                    scriptNode.getInvokeMethods().addAll(mySingleExpressionVisitor.getScriptNode().getInvokeMethods());
                }
            }
        }
        return null;
    }

    @Override
    public Object visitBitXOrExpression(TypeScriptParser.BitXOrExpressionContext ctx) {
        for (TypeScriptParser.SingleExpressionContext singleExpressionContext : ctx.singleExpression()) {
            MySingleExpressionVisitor mySingleExpressionVisitor = new MySingleExpressionVisitor(null);
            mySingleExpressionVisitor.visit(singleExpressionContext);
            if (mySingleExpressionVisitor.getScriptNode() != null) {
                if (CollectionUtils.isNotEmpty(mySingleExpressionVisitor.getScriptNode().getInvokeMethods())) {
                    scriptNode.getInvokeMethods().addAll(mySingleExpressionVisitor.getScriptNode().getInvokeMethods());
                }
            }
        }
        return null;
    }

    @Override
    public Object visitLogicalAndExpression(TypeScriptParser.LogicalAndExpressionContext ctx) {
        for (TypeScriptParser.SingleExpressionContext singleExpressionContext : ctx.singleExpression()) {
            MySingleExpressionVisitor mySingleExpressionVisitor = new MySingleExpressionVisitor(null);
            mySingleExpressionVisitor.visit(singleExpressionContext);
            if (mySingleExpressionVisitor.getScriptNode() != null) {
                if (CollectionUtils.isNotEmpty(mySingleExpressionVisitor.getScriptNode().getInvokeMethods())) {
                    scriptNode.getInvokeMethods().addAll(mySingleExpressionVisitor.getScriptNode().getInvokeMethods());
                }
            }
        }
        return null;
    }

    @Override
    public Object visitLogicalOrExpression(TypeScriptParser.LogicalOrExpressionContext ctx) {
        for (TypeScriptParser.SingleExpressionContext singleExpressionContext : ctx.singleExpression()) {
            MySingleExpressionVisitor mySingleExpressionVisitor = new MySingleExpressionVisitor(null);
            mySingleExpressionVisitor.visit(singleExpressionContext);
            if (mySingleExpressionVisitor.getScriptNode() != null) {
                if (CollectionUtils.isNotEmpty(mySingleExpressionVisitor.getScriptNode().getInvokeMethods())) {
                    scriptNode.getInvokeMethods().addAll(mySingleExpressionVisitor.getScriptNode().getInvokeMethods());
                }
            }
        }
        return null;
    }

    @Override
    public Object visitTernaryExpression(TypeScriptParser.TernaryExpressionContext ctx) {
        for (TypeScriptParser.SingleExpressionContext singleExpressionContext : ctx.singleExpression()) {
            MySingleExpressionVisitor mySingleExpressionVisitor = new MySingleExpressionVisitor(null);
            mySingleExpressionVisitor.visit(singleExpressionContext);
            if (mySingleExpressionVisitor.getScriptNode() != null) {
                if (CollectionUtils.isNotEmpty(mySingleExpressionVisitor.getScriptNode().getInvokeMethods())) {
                    scriptNode.getInvokeMethods().addAll(mySingleExpressionVisitor.getScriptNode().getInvokeMethods());
                }
            }
        }
        return null;
    }

    @Override
    public Object visitAssignmentExpression(TypeScriptParser.AssignmentExpressionContext ctx) {
        for (TypeScriptParser.SingleExpressionContext singleExpressionContext : ctx.singleExpression()) {
            MySingleExpressionVisitor mySingleExpressionVisitor = new MySingleExpressionVisitor(null);
            mySingleExpressionVisitor.visit(singleExpressionContext);
            if (mySingleExpressionVisitor.getScriptNode() != null) {
                if (CollectionUtils.isNotEmpty(mySingleExpressionVisitor.getScriptNode().getInvokeMethods())) {
                    scriptNode.getInvokeMethods().addAll(mySingleExpressionVisitor.getScriptNode().getInvokeMethods());
                }
            }
        }
        return null;
    }

    @Override
    public Object visitAssignmentOperatorExpression(TypeScriptParser.AssignmentOperatorExpressionContext ctx) {
        for (TypeScriptParser.SingleExpressionContext singleExpressionContext : ctx.singleExpression()) {
            MySingleExpressionVisitor mySingleExpressionVisitor = new MySingleExpressionVisitor(null);
            mySingleExpressionVisitor.visit(singleExpressionContext);
            if (mySingleExpressionVisitor.getScriptNode() != null) {
                if (CollectionUtils.isNotEmpty(mySingleExpressionVisitor.getScriptNode().getInvokeMethods())) {
                    scriptNode.getInvokeMethods().addAll(mySingleExpressionVisitor.getScriptNode().getInvokeMethods());
                }
            }
        }
        return null;
    }

    @Override
    public Object visitTemplateStringExpression(TypeScriptParser.TemplateStringExpressionContext ctx) {
        //todo 模板字符串表达式 暂不处理
        return null;
    }

    @Override
    public Object visitIteratorsExpression(TypeScriptParser.IteratorsExpressionContext ctx) {
        //迭代器块 暂不处理
        return null;
    }

    @Override
    public Object visitGeneratorsExpression(TypeScriptParser.GeneratorsExpressionContext ctx) {
        //todo 生成器块暂不处理
        return null;
    }

    @Override
    public Object visitGeneratorsFunctionExpression(TypeScriptParser.GeneratorsFunctionExpressionContext ctx) {
        //todo 生成器函数表达式暂不处理
        return null;
    }

    @Override
    public Object visitYieldExpression(TypeScriptParser.YieldExpressionContext ctx) {
        //todo yield表达式暂不处理
        return null;
    }

    @Override
    public Object visitThisExpression(TypeScriptParser.ThisExpressionContext ctx) {
        //todo this暂不处理
        return null;
    }

//    @Override
//    public Object visitIdentifierExpression(TypeScriptParser.IdentifierExpressionContext ctx) {
//        //todo 标识符表达式简单处理
//        if (ctx.singleExpression() != null) {
//            MySingleExpressionVisitor mySingleExpressionVisitor = new MySingleExpressionVisitor(scriptBaseState);
//            mySingleExpressionVisitor.visit(ctx.singleExpression());
//        }
//        return null;
//    }

    @Override
    public Object visitSuperExpression(TypeScriptParser.SuperExpressionContext ctx) {
        //todo 父类暂不处理
        return null;
    }

    @Override
    public Object visitLiteralExpression(TypeScriptParser.LiteralExpressionContext ctx) {
        //todo 暂不支持字面量表达式
        return null;
    }

    @Override
    public Object visitArrayLiteralExpression(TypeScriptParser.ArrayLiteralExpressionContext ctx) {
        //todo 暂不支持数组字面量表达式
        return null;
    }

    @Override
    public Object visitObjectLiteralExpression(TypeScriptParser.ObjectLiteralExpressionContext ctx) {
        //todo 暂不支持对象字面量
        if (CollectionUtils.isNotEmpty(ctx.objectLiteral().propertyAssignment())) {
            for (TypeScriptParser.PropertyAssignmentContext propertyAssignmentContext : ctx.objectLiteral().propertyAssignment()) {
//                propertyAssignment
//                : propertyName (':' | Assign) singleExpression     # PropertyExpressionAssignment
//                        | '[' singleExpression ']' ':' singleExpression # ComputedPropertyExpressionAssignment
//                        | getAccessor                                   # PropertyGetter
//                        | setAccessor                                   # PropertySetter
//                        | generatorMethod                               # MethodProperty
//                        | identifierOrKeyWord                           # PropertyShorthand
//                        | Ellipsis? singleExpression                    # SpreadOperator
//                        | restParameter                                 # RestParameterInObject
//                ;

                if (propertyAssignmentContext instanceof TypeScriptParser.PropertyExpressionAssignmentContext) {
                    TypeScriptParser.PropertyExpressionAssignmentContext propertyExpressionAssignmentContext = (TypeScriptParser.PropertyExpressionAssignmentContext) propertyAssignmentContext;
                    ScriptVariable scriptVariable = new ScriptVariable();
                    scriptVariable.setStartLine(propertyExpressionAssignmentContext.getStart().getLine());
                    scriptVariable.setEndLine(propertyExpressionAssignmentContext.getStop().getLine());
                    scriptVariable.setBody(propertyExpressionAssignmentContext.getText());
                    scriptVariable.setName(propertyExpressionAssignmentContext.propertyName().getText());
                    MySingleExpressionVisitor mySingleExpressionVisitor = new MySingleExpressionVisitor(null);
                    mySingleExpressionVisitor.visit(propertyExpressionAssignmentContext.singleExpression());
                    if (CollectionUtils.isNotEmpty(mySingleExpressionVisitor.getScriptNode().getMethods())) {
                        scriptVariable.setMethods(mySingleExpressionVisitor.getScriptNode().getMethods());
                    }
                    scriptNode.getVariables().add(scriptVariable);
                }

                if (propertyAssignmentContext instanceof TypeScriptParser.MethodPropertyContext) {
                    TypeScriptParser.MethodPropertyContext methodPropertyContext = (TypeScriptParser.MethodPropertyContext) propertyAssignmentContext;
//                    generatorMethod
//                    : (Async {this.notLineTerminator()}?)? '*'? propertyName '(' formalParameterList? ')' OpenBrace functionBody CloseBrace
//                    ;

                    TypeScriptParser.GeneratorMethodContext generatorMethodContext = methodPropertyContext.generatorMethod();
                    ScriptMethod scriptMethod = new ScriptMethod();
                    scriptMethod.setMethodName(generatorMethodContext.propertyName().getText());
                    scriptMethod.setStartLine(generatorMethodContext.getStart().getLine());
                    scriptMethod.setEndLine(generatorMethodContext.getStop().getLine());
                    scriptMethod.setBody(generatorMethodContext.getText());

                    //todo 参数暂不处理 formalParameterList
                    MyFunctionBodyVisitor myFunctionBodyVisitor = new MyFunctionBodyVisitor();
                    myFunctionBodyVisitor.visitFunctionBody(generatorMethodContext.functionBody(), scriptMethod);
                    scriptNode.getMethods().add(scriptMethod);
                }
            }
        }
        return null;
    }


    @Override
    public Object visitParenthesizedExpression(TypeScriptParser.ParenthesizedExpressionContext ctx) {
        if (ctx.expressionSequence() != null) {
            for (TypeScriptParser.SingleExpressionContext singleExpressionContext : ctx.expressionSequence().singleExpression()) {
                MySingleExpressionVisitor mySingleExpressionVisitor = new MySingleExpressionVisitor(null);
                mySingleExpressionVisitor.visit(singleExpressionContext);
                if (mySingleExpressionVisitor.getScriptNode() != null) {
                    if (CollectionUtils.isNotEmpty(mySingleExpressionVisitor.getScriptNode().getInvokeMethods())) {
                        scriptNode.getInvokeMethods().addAll(mySingleExpressionVisitor.getScriptNode().getInvokeMethods());
                    }
                    if (CollectionUtils.isNotEmpty(mySingleExpressionVisitor.getScriptNode().getMethods())) {
                        scriptNode.getMethods().addAll(mySingleExpressionVisitor.getScriptNode().getMethods());
                    }
                    if (CollectionUtils.isNotEmpty(mySingleExpressionVisitor.getScriptNode().getVariables())) {
                        scriptNode.getVariables().addAll(mySingleExpressionVisitor.getScriptNode().getVariables());
                    }
                    if (CollectionUtils.isNotEmpty(mySingleExpressionVisitor.getScriptNode().getHtmlNodes())) {
                        scriptNode.getHtmlNodes().addAll(mySingleExpressionVisitor.getScriptNode().getHtmlNodes());
                    }
                }
            }
        }
        return null;
    }

    @Override
    public Object visitGenericTypes(TypeScriptParser.GenericTypesContext ctx) {
        List<ScriptGenerics> generics = new ArrayList<>();
        if (ctx.typeArguments() != null) {
            for (TypeScriptParser.TypeArgumentContext typeArgumentContext : ctx.typeArguments().typeArgumentList().typeArgument()) {
                ScriptGenerics scriptGenerics = new ScriptGenerics();
                scriptGenerics.setName(typeArgumentContext.getText());
                generics.add(scriptGenerics);
            }
        }
        if (ctx.expressionSequence() != null) {
            for (TypeScriptParser.SingleExpressionContext singleExpressionContext : ctx.expressionSequence().singleExpression()) {
                MySingleExpressionVisitor mySingleExpressionVisitor = new MySingleExpressionVisitor(null);
                mySingleExpressionVisitor.visit(singleExpressionContext);
                if (mySingleExpressionVisitor.getScriptNode() != null) {
                    if (CollectionUtils.isNotEmpty(mySingleExpressionVisitor.getScriptNode().getInvokeMethods())) {
                        for (ScriptMethod scriptMethod : mySingleExpressionVisitor.getScriptNode().getInvokeMethods()) {
                            scriptMethod.setGenerics(generics);
                            scriptNode.getInvokeMethods().add(scriptMethod);
                        }
                    }
                }
            }
        }

        return null;
    }

    @Override
    public Object visitCastAsExpression(TypeScriptParser.CastAsExpressionContext ctx) {
        //todo 先不处理as
        MySingleExpressionVisitor mySingleExpressionVisitor = new MySingleExpressionVisitor(null);
        mySingleExpressionVisitor.visit(ctx.singleExpression());
        if (mySingleExpressionVisitor.getScriptNode() != null) {
            if (CollectionUtils.isNotEmpty(mySingleExpressionVisitor.getScriptNode().getInvokeMethods())) {
                scriptNode.getInvokeMethods().addAll(mySingleExpressionVisitor.getScriptNode().getInvokeMethods());
            }
        }
        return null;
    }

    @Override
    public Object visitNonNullAssertionExpression(TypeScriptParser.NonNullAssertionExpressionContext ctx) {
        MySingleExpressionVisitor mySingleExpressionVisitor = new MySingleExpressionVisitor(null);
        mySingleExpressionVisitor.visit(ctx.singleExpression());
        if (mySingleExpressionVisitor.getScriptNode() != null) {
            if (CollectionUtils.isNotEmpty(mySingleExpressionVisitor.getScriptNode().getInvokeMethods())) {
                scriptNode.getInvokeMethods().addAll(mySingleExpressionVisitor.getScriptNode().getInvokeMethods());
            }
        }
        return null;
    }

    @Override
    public Object visitJsxElements(TypeScriptParser.JsxElementsContext ctx) {
        MyJsxElementsVisitor myJsxElementsVisitor = new MyJsxElementsVisitor();
        myJsxElementsVisitor.visitJsxElement(ctx, scriptNode);
        return null;
    }


}
