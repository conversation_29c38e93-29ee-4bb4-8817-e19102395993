package com.sankuai.deepcode.ast.model.foreend;

import com.sankuai.deepcode.ast.model.typescript.ScriptMethodParam;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
public class JsMethodNodeInfo extends ForeEndNodeInfo {
    private boolean isAsync = false;
    private boolean isAwait = false;
    private boolean isYield = false;
    private boolean isAbstract = false;
    private boolean isCallSignature = false;
    private String methodName;
    private String modifier;
    private List<ScriptMethodParam> params = new ArrayList<>();
}
