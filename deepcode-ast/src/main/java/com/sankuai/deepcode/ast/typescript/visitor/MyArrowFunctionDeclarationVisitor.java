package com.sankuai.deepcode.ast.typescript.visitor;

import com.sankuai.deepcode.ast.model.typescript.*;
import com.sankuai.deepcode.ast.typescript.gen.TypeScriptParser;
import com.sankuai.deepcode.ast.typescript.gen.TypeScriptParserBaseVisitor;
import lombok.Getter;
import org.apache.commons.collections4.CollectionUtils;

public class MyArrowFunctionDeclarationVisitor extends TypeScriptParserBaseVisitor<Object> {

    @Getter
    private ScriptMethod scriptMethod;

    private ScriptNode scriptNode;

    MyArrowFunctionDeclarationVisitor(ScriptNode myScriptNode) {
        scriptMethod = new ScriptMethod();
        if (myScriptNode == null) {
            scriptNode = new ScriptNode();
        } else {
            scriptNode = myScriptNode;
        }
        if (scriptNode.isExport()) {
            scriptMethod.setExport(true);
        }
        scriptNode.getMethods().add(scriptMethod);
    }

    @Override
    public Object visitArrowFunctionDeclaration(TypeScriptParser.ArrowFunctionDeclarationContext ctx) {
        scriptMethod.setStartLine(ctx.getStart().getLine());
        scriptMethod.setEndLine(ctx.getStop().getLine());
        scriptMethod.setBody(ctx.getText());
        if (ctx.Async() != null) {
            scriptMethod.setAsync(true);
        }
        //todo  简单处理
        if (ctx.arrowFunctionParameters().propertyName() != null) {
            scriptMethod.setMethodName(ctx.arrowFunctionParameters().propertyName().getText());
        }
        if (ctx.arrowFunctionParameters().formalParameterList() != null) {
            //todo 参数暂不处理 formalParameterList
        }

        if (ctx.typeAnnotation() != null) {
            ScriptMethodReturn scriptMethodReturn = new ScriptMethodReturn();
            scriptMethodReturn.setType(ctx.typeAnnotation().type_().getText());
            scriptMethod.setMethodReturn(scriptMethodReturn);
        }

        if (ctx.arrowFunctionBody() != null) {
            if (ctx.arrowFunctionBody().singleExpression() != null) {
                MySingleExpressionVisitor mySingleExpressionVisitor = new MySingleExpressionVisitor(null);
                mySingleExpressionVisitor.visit(ctx.arrowFunctionBody().singleExpression());
                if (mySingleExpressionVisitor.getScriptNode() != null) {
                    if (mySingleExpressionVisitor.getScriptNode().getInvokeMethods() != null) {
                        scriptMethod.getInvokeMethods().addAll(mySingleExpressionVisitor.getScriptNode().getInvokeMethods());
                    }
                    if (CollectionUtils.isNotEmpty(mySingleExpressionVisitor.getScriptNode().getVariables())) {
                        scriptMethod.getVariables().addAll(mySingleExpressionVisitor.getScriptNode().getVariables());
                    }
                    if (CollectionUtils.isNotEmpty(mySingleExpressionVisitor.getScriptNode().getHtmlNodes())) {
                        scriptMethod.getHtmlNodes().addAll(mySingleExpressionVisitor.getScriptNode().getHtmlNodes());
                    }
                }
            } else if (ctx.arrowFunctionBody().functionBody() != null) {
                MyFunctionBodyVisitor.visitFunctionBody(ctx.arrowFunctionBody().functionBody(), scriptMethod);
            }
        }

        return null;
    }
}
