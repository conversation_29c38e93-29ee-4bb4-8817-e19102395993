package com.sankuai.deepcode.ast.scss.visitor;

import com.sankuai.deepcode.ast.enums.scss.ScssSelectorTypeEnum;
import com.sankuai.deepcode.ast.model.scss.ScssRule;
import com.sankuai.deepcode.ast.model.scss.ScssSelector;
import com.sankuai.deepcode.ast.scss.gen.ScssParser;
import com.sankuai.deepcode.ast.scss.gen.ScssParserBaseVisitor;
import com.sankuai.deepcode.ast.scss.visitor.MyBlockVisitor;
import lombok.Getter;
import org.antlr.v4.runtime.tree.ParseTree;
import org.apache.commons.collections4.CollectionUtils;

public class MyRulesetVisitor extends ScssParserBaseVisitor<Object> {

    @Getter
    private ScssRule scssRule;

    public MyRulesetVisitor(ScssRule myScssRule) {
        scssRule = new ScssRule();
        if (myScssRule != null) {
            myScssRule.getChildren().add(scssRule);
        }
    }


    @Override
    public Object visitRuleset(ScssParser.RulesetContext ctx) {
        scssRule.setStartLine(ctx.getStart().getLine());
        scssRule.setEndLine(ctx.getStop().getLine());
        scssRule.setBody(ctx.getText());
        visitSelectorGroup(ctx.selectorGroup());
        com.sankuai.deepcode.ast.scss.visitor.MyBlockVisitor visitor = new MyBlockVisitor(scssRule);
        visitor.visitBlock(ctx.block());
        return null;
    }


    @Override
    public Object visitSelectorGroup(ScssParser.SelectorGroupContext ctx) {
        for (ScssParser.SelectorContext selectorContext : ctx.selector()) {
            visitSelector(selectorContext);
        }
        return null;
    }

    @Override
    public Object visitSelector(ScssParser.SelectorContext ctx) {
        if(CollectionUtils.isNotEmpty(ctx.children)){
            for (int i = 0; i < ctx.children.size(); i++) {
                ParseTree parseTree = ctx.children.get(i);
                ScssParser.CombinatorContext combinatorContext = null;
                ScssParser.SelectorSequenceContext selectorSequenceContext = null;
                ScssSelector scssSelector = new ScssSelector();
                if (i == 0) {
                    if (parseTree instanceof ScssParser.CombinatorContext) {
                        combinatorContext = (ScssParser.CombinatorContext) parseTree;
                        selectorSequenceContext = (ScssParser.SelectorSequenceContext) ctx.children.get(i + 1);
                        i++;
                    } else if (parseTree instanceof ScssParser.SelectorSequenceContext) {
                        selectorSequenceContext = (ScssParser.SelectorSequenceContext) parseTree;
                    }
                } else {
                    combinatorContext = (ScssParser.CombinatorContext) parseTree;
                    selectorSequenceContext = (ScssParser.SelectorSequenceContext) ctx.children.get(i + 1);
                    i++;
                }
                if (null != combinatorContext) {
                    if (combinatorContext.Plus() != null) {
                        if (ctx.selectorSequence().size() > 1) {
                            scssRule.setType(ScssSelectorTypeEnum.PLUS.getCode());
                        }
                    } else if (combinatorContext.Greater() != null) {
                        scssRule.setType(ScssSelectorTypeEnum.GREATER.getCode());
                    } else if (combinatorContext.Tilde() != null) {
                        scssRule.setType(ScssSelectorTypeEnum.TILDE.getCode());
                    } else if (combinatorContext.Space() != null) {
                        scssRule.setType(ScssSelectorTypeEnum.SPACE.getCode());
                    }
                }
                visitSelectorSequence(selectorSequenceContext, scssSelector);
                scssRule.getSelectors().add(scssSelector);
            }
        }
        return null;
    }

    public Object visitSelectorSequence(ScssParser.SelectorSequenceContext ctx, ScssSelector scssSelector) {
        //todo 简单处理成string
        scssSelector.setName(ctx.getText());
//        for (int i = 0; i < ctx.children.size(); i++) {
//            ParseTree parseTree = ctx.children.get(i);
//            if (parseTree instanceof ScssParser.TypeSelectorContext) {
//
//            } else if (parseTree instanceof ScssParser.UniversalContext) {
//
//            } else if (parseTree instanceof ScssParser.IdContext) {
//
//            } else if (parseTree instanceof ScssParser.ClassNameContext) {
//
//            } else if (parseTree instanceof ScssParser.AttribContext) {
//
//            } else if (parseTree instanceof ScssParser.PseudoContext) {
//
//            } else if (parseTree instanceof ScssParser.NegationContext) {
//
//            }else if (parseTree instanceof ScssParser.InterpolationContext){
//
//            }else if (parseTree instanceof ScssParser.ParentRefContext){
//
//            }
//        }
        return null;
    }

}
