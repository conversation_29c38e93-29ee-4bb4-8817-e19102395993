package com.sankuai.deepcode.ast.model.python3;

import com.google.common.collect.Lists;
import com.sankuai.deepcode.ast.model.base.LocationInfo;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

/**
 * Package: com.sankuai.deepcode.ast.model.python3
 * Description:
 *
 * <AUTHOR>
 * @since 2024/12/9 13:18
 */
@Setter
@Getter
@ToString(callSuper = true)
@Accessors(chain = true)
public class PythonBlockComment extends PythonComment {
    /**
     * 块级注释时，所属块的路径
     * 可以是模块，类、方法完整路径
     */
    String blockModulePath;

    public PythonBlockComment setComment(String comment) {
        this.comment = comment;
        return this;
    }

    public PythonBlockComment setStart(LocationInfo start) {
        this.start = start;
        return this;
    }

    public PythonBlockComment setEnd(LocationInfo end) {
        this.end = end;
        return this;
    }

}
