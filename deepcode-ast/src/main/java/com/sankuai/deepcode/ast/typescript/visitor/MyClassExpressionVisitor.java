package com.sankuai.deepcode.ast.typescript.visitor;

import com.sankuai.deepcode.ast.model.typescript.*;
import com.sankuai.deepcode.ast.typescript.gen.TypeScriptParser;
import com.sankuai.deepcode.ast.typescript.gen.TypeScriptParserBaseVisitor;
import lombok.Getter;
import org.apache.commons.collections4.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

public class MyClassExpressionVisitor extends TypeScriptParserBaseVisitor<Object> {

    @Getter
    private ScriptClass scriptClass;

    @Override
    public Object visitClassExpression(TypeScriptParser.ClassExpressionContext ctx) {
        scriptClass = new ScriptClass();
        scriptClass.setClassName(ctx.identifier().getText());
        scriptClass.setStartLine(ctx.getStart().getLine());
        scriptClass.setEndLine(ctx.getStop().getLine());
        //todo extend implement 简单处理
        if (ctx.classHeritage().classExtendsClause() != null) {
            ScriptExtends superClass = new ScriptExtends();
            TypeScriptParser.TypeNameContext typeNameContext = ctx.classHeritage().classExtendsClause().typeReference().typeName();
            if (typeNameContext.identifier() != null) {
                superClass.setName(typeNameContext.identifier().getText());
            } else if (typeNameContext.namespaceName() != null) {
                superClass.setNameSpace(typeNameContext.namespaceName().getText());
            }
            TypeScriptParser.TypeGenericContext typeGenericContext = ctx.classHeritage().classExtendsClause().typeReference().typeGeneric();
            if (typeGenericContext != null) {
                List<ScriptGenerics> generics = new ArrayList<>();
                for (TypeScriptParser.TypeArgumentContext typeArgumentContext : typeGenericContext.typeArgumentList().typeArgument()) {
                    ScriptGenerics scriptGenerics = new ScriptGenerics();
                    scriptGenerics.setName(typeArgumentContext.getText());
                    generics.add(scriptGenerics);
                }
                if (typeGenericContext.typeGeneric() != null) {
                    ScriptGenerics scriptGenerics = new ScriptGenerics();
                    scriptGenerics.setName(typeGenericContext.typeGeneric().getText());
                    generics.add(scriptGenerics);
                }
                superClass.setGenerics(generics);
            }
        }

        if (ctx.classHeritage().implementsClause() != null) {
            List<ScriptImplements> interfaces = new ArrayList<>();
            List<TypeScriptParser.TypeReferenceContext> typeReferenceContexts = ctx.classHeritage().implementsClause().classOrInterfaceTypeList().typeReference();
            for (TypeScriptParser.TypeReferenceContext typeReferenceContext : typeReferenceContexts) {
                ScriptImplements scriptImplements = new ScriptImplements();
                if (typeReferenceContext.typeName().identifier() != null) {
                    scriptImplements.setName(typeReferenceContext.typeName().identifier().getText());
                } else if (typeReferenceContext.typeName().namespaceName() != null) {
                    scriptImplements.setNameSpace(typeReferenceContext.typeName().namespaceName().getText());
                }

                if (typeReferenceContext.typeGeneric() != null) {
                    List<ScriptGenerics> generics = new ArrayList<>();
                    for (TypeScriptParser.TypeArgumentContext typeArgumentContext : typeReferenceContext.typeGeneric().typeArgumentList().typeArgument()) {
                        ScriptGenerics scriptGenerics = new ScriptGenerics();
                        scriptGenerics.setName(typeArgumentContext.getText());
                        generics.add(scriptGenerics);
                    }
                    if (typeReferenceContext.typeGeneric().typeGeneric() != null) {
                        ScriptGenerics scriptGenerics = new ScriptGenerics();
                        scriptGenerics.setName(typeReferenceContext.typeGeneric().typeGeneric().getText());
                        generics.add(scriptGenerics);
                    }
                    scriptImplements.setGenerics(generics);
                }
                interfaces.add(scriptImplements);
            }
        }
        visitClassTail(ctx.classTail());
        return null;
    }


    @Override
    public Object visitClassTail(TypeScriptParser.ClassTailContext ctx) {
        for (TypeScriptParser.ClassElementContext classElementContext : ctx.classElement()) {
            visitClassElement(classElementContext);
        }
        return null;
    }

    @Override
    public Object visitClassElement(TypeScriptParser.ClassElementContext ctx) {
        if (ctx.constructorDeclaration() != null) {
            visitConstructorDeclaration(ctx.constructorDeclaration());
        } else if (ctx.propertyMemberDeclaration() != null) {
            //todo 属性成员声明 简单处理
            List<String> decorators = new ArrayList<>();
            if (ctx.decoratorList() != null) {
                for (TypeScriptParser.DecoratorContext decoratorContext : ctx.decoratorList().decorator()) {
                    decorators.add(decoratorContext.getText());
                }
            }
            MyPropertyMemberDeclarationVisitor myPropertyMemberDeclarationVisitor = new MyPropertyMemberDeclarationVisitor(scriptClass, decorators);
            myPropertyMemberDeclarationVisitor.visit(ctx.propertyMemberDeclaration());
        } else if (ctx.indexMemberDeclaration() != null) {
            //todo 索引成员声明 暂不处理


        } else if (ctx.statement() != null) {
            com.sankuai.deepcode.ast.typescript.visitor.MyStatementVisitor myStatementVisitor = new MyStatementVisitor(null);
            myStatementVisitor.visit(ctx.statement());
            //todo 方法statement暂时只处理方法
            ScriptNode scriptNode = myStatementVisitor.getScriptNode();
            if (CollectionUtils.isNotEmpty(scriptNode.getInvokeMethods())) {
                for (ScriptMethod method : scriptNode.getInvokeMethods()) {
                    scriptClass.getMethods().add(method);
                }
            }
        }
        return null;
    }


    @Override
    public Object visitConstructorDeclaration(TypeScriptParser.ConstructorDeclarationContext ctx) {
        ScriptMethod scriptMethod = new ScriptMethod();
        scriptMethod.setStartLine(ctx.getStart().getLine());
        scriptMethod.setEndLine(ctx.getStop().getLine());
        scriptMethod.setBody(ctx.getText());
        if (ctx.accessibilityModifier() != null) {
            scriptMethod.setModifier(ctx.accessibilityModifier().getText());
        }
        scriptMethod.setMethodName("constructor");
        //todo 构造函数参数暂不处理 formalParameterList
        scriptClass.setConstructor(scriptMethod);

        MyFunctionBodyVisitor.visitFunctionBody(ctx.functionBody(), scriptMethod);
        return null;
    }
}
