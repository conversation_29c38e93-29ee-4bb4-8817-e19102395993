package com.sankuai.deepcode.ast.model.java;

import lombok.Data;

@Data
public class CountComment implements Comparable<CountComment> {
    private int startLine;
    private int endLine;
    private String type;     //class method field inClass inClassMethod inClassField
    private ClassNode classNode;
    private FieldNode fieldNode;
    private MethodNode methodNode;

    @Override
    public int compareTo(CountComment other) {
        return Integer.compare(this.startLine, other.startLine);
    }
}
