package com.sankuai.deepcode.ast.typescript.visitor;

import com.sankuai.deepcode.ast.model.typescript.*;
import com.sankuai.deepcode.ast.typescript.gen.TypeScriptParser;
import com.sankuai.deepcode.ast.typescript.gen.TypeScriptParserBaseVisitor;
import lombok.Getter;
import org.apache.commons.collections4.CollectionUtils;

public class MyTypeScriptVisitor extends TypeScriptParserBaseVisitor<Object> {


    @Getter
    private ScriptNode scriptNode;

    @Override
    public Object visitProgram(TypeScriptParser.ProgramContext ctx) {
        scriptNode = new ScriptNode();
        scriptNode.setStartLine(ctx.getStart().getLine());
        scriptNode.setEndLine(ctx.getStop().getLine());
        if (ctx.sourceElements() != null && CollectionUtils.isNotEmpty(ctx.sourceElements().sourceElement())) {
            for (TypeScriptParser.SourceElementContext sourceElement : ctx.sourceElements().sourceElement()) {
                visitSourceElement(sourceElement);
            }
        }
        return null;
    }

    private boolean isExport = false;

    @Override
    public Object visitSourceElement(TypeScriptParser.SourceElementContext ctx) {
        if (ctx.Export() != null) {
            isExport = true;
        } else {
            isExport = false;
        }
        visitStatement(ctx.statement());
        return null;
    }


    @Override
    public Object visitStatement(TypeScriptParser.StatementContext ctx) {
        if (ctx == null) {
            return null;
        }
        MyStatementVisitor myStatementVisitor = new MyStatementVisitor(null);
        myStatementVisitor.visit(ctx);
        ScriptNode stateNode = myStatementVisitor.getScriptNode();
        if (CollectionUtils.isNotEmpty(stateNode.getImports())) {
            scriptNode.getImports().addAll(stateNode.getImports());
        }
        if (CollectionUtils.isNotEmpty(stateNode.getMethods())) {
            for (ScriptMethod scriptMethod : stateNode.getMethods()) {
                scriptMethod.setExport(isExport);
                scriptNode.getMethods().add(scriptMethod);
            }
        }
        if (CollectionUtils.isNotEmpty(stateNode.getClasses())) {
            for (ScriptClass scriptClass : stateNode.getClasses()) {
                scriptClass.setExport(isExport);
                scriptNode.getClasses().add(scriptClass);
            }
        }
        if (CollectionUtils.isNotEmpty(stateNode.getVariables())) {
            for (ScriptVariable scriptVariable : stateNode.getVariables()) {
                scriptVariable.setExport(isExport);
                scriptNode.getVariables().add(scriptVariable);
            }
        }
        if (CollectionUtils.isNotEmpty(stateNode.getNamespaces())) {
            for (ScriptNameSpace scriptNamespace : stateNode.getNamespaces()) {
                scriptNamespace.setExport(isExport);
                scriptNode.getNamespaces().add(scriptNamespace);
            }
        }
        if (CollectionUtils.isNotEmpty(stateNode.getTypeAliases())) {
            for (ScriptTypeAlias scriptTypeAlias : stateNode.getTypeAliases()) {
                scriptTypeAlias.setExport(isExport);
                scriptNode.getTypeAliases().add(scriptTypeAlias);
            }
        }
        if (CollectionUtils.isNotEmpty(stateNode.getInvokeMethods())) {
            for (ScriptMethod scriptMethod : stateNode.getInvokeMethods()) {
                scriptNode.getInvokeMethods().add(scriptMethod);
            }
        }
        return null;
    }

}
