package com.sankuai.deepcode.ast.python3.visitor;

import com.sankuai.deepcode.ast.model.base.LocationInfo;
import com.sankuai.deepcode.ast.model.python3.PythonImportNode;
import com.sankuai.deepcode.ast.model.python3.ScopeManager;
import com.sankuai.deepcode.ast.python3.gen.PythonParser;
import com.sankuai.deepcode.ast.python3.gen.PythonParserBaseVisitor;
import lombok.Getter;

import java.util.*;

/**
 * Package: com.sankuai.deepcode.ast.python3.visitor
 * Description:
 *
 * <AUTHOR>
 * @since 2024/12/4 21:47
 */
@Getter
public class PythonImportVisitor extends PythonParserBaseVisitor<Object> {
    private final String fileName;
    private final String moduleName;
    private final String modulePath;
    private final ScopeManager scopeManager;

    private final List<PythonImportNode> importNodes = new ArrayList<>();


    public PythonImportVisitor(String fileName, String moduleName, String modulePath, ScopeManager scopeManager) {
        this.fileName = fileName;
        this.moduleName = moduleName;
        this.modulePath = modulePath;
        this.scopeManager = scopeManager;
    }

    /**
     * 访问导入语句节点
     *
     * @param ctx 导入语句上下文
     * @return 访问结果
     */
    @Override
    public Object visitImport_stmt(PythonParser.Import_stmtContext ctx) {
        if (ctx.import_name() != null) {
            handleImportName(ctx.import_name());
        } else if (ctx.import_from() != null) {
            handleImportFrom(ctx.import_from());
        }
        return super.visitImport_stmt(ctx);
    }

    private void handleImportName(PythonParser.Import_nameContext ctx) {
        for (PythonParser.Dotted_as_nameContext dotted : ctx.dotted_as_names().dotted_as_name()) {
            String name = dotted.dotted_name().getText();
            String alias = dotted.NAME() != null ? dotted.NAME().getText() : name;

            PythonImportNode importNode = new PythonImportNode();
            importNode.setCurrentModuleName(moduleName);
            importNode.setCurrentModulePath(modulePath);
            importNode.setFileName(fileName);
            importNode.setImportType(PythonImportNode.ImportType.IMPORT);
            importNode.setLocation(new LocationInfo(ctx.start.getLine(), ctx.start.getCharPositionInLine()));
            importNode.setImportedItems(Collections.singletonList(name));
            importNode.setAliases(Collections.singletonMap(name, alias));

            // 设置 fromModulePath 和 fullImportModulePath
            importNode.setFromModulePath(name);
            importNode.setFullImportModulePath(name);

            importNodes.add(importNode);

            scopeManager.addImport(alias, name, ctx.start.getLine());
            if (!alias.equals(name)) {
                scopeManager.addAlias(alias, name, ctx.start.getLine());
            }
        }
    }

    private void handleImportFrom(PythonParser.Import_fromContext ctx) {
        String importModulePath = ctx.dotted_name() != null ? ctx.dotted_name().getText() : "";
        int dotCount = (int) ctx.children.stream().filter(child -> ".".equals(child.getText())).count();

        // 处理相对导入
        String fullModulePath = getFullModulePath(importModulePath, dotCount);

        PythonImportNode importNode = new PythonImportNode();
        importNode.setCurrentModuleName(moduleName);
        // 使用当前模块的路径
        importNode.setCurrentModulePath(this.modulePath);
        importNode.setFileName(fileName);
        importNode.setImportType(PythonImportNode.ImportType.FROM);
        importNode.setLocation(new LocationInfo(ctx.start.getLine(), ctx.start.getCharPositionInLine()));
        importNode.setFromModulePath(fullModulePath);

        List<String> importedItems = new ArrayList<>();
        Map<String, String> aliases = new HashMap<>();

        PythonParser.Import_from_targetsContext targetsCtx = ctx.import_from_targets();
        if (targetsCtx.STAR() != null) {
            importedItems.add("*");
            scopeManager.addImport("*", fullModulePath + ".*", targetsCtx.getStart().getLine());
        } else if (targetsCtx.import_from_as_names() != null) {
            for (PythonParser.Import_from_as_nameContext nameCtx : targetsCtx.import_from_as_names().import_from_as_name()) {
                String name = nameCtx.NAME(0).getText();
                String alias = nameCtx.NAME().size() > 1 ? nameCtx.NAME(1).getText() : name;
                importedItems.add(name);
                aliases.put(name, alias);

                String fullPath = fullModulePath + "." + name;
                scopeManager.addImport(alias, fullPath, nameCtx.getStart().getLine());
                if (!alias.equals(name)) {
                    scopeManager.addAlias(alias, fullPath, nameCtx.getStart().getLine());
                }
            }
        }

        importNode.setImportedItems(importedItems);
        importNode.setAliases(aliases);
        importNode.setFullImportModulePath(fullModulePath);
        importNodes.add(importNode);
    }

    /**
     * 处理带点的导入名称
     *
     * @param ctx 带点的导入名称上下文
     * @return 导入项列表
     */
    private List<String> visitDottedAsNames(PythonParser.Dotted_as_namesContext ctx) {
        List<String> imports = new ArrayList<>();
        Map<String, String> aliases = new HashMap<>();
        for (PythonParser.Dotted_as_nameContext dotted : ctx.dotted_as_name()) {
            String name = dotted.dotted_name().getText();
            imports.add(name);
            if (dotted.NAME() != null) {
                aliases.put(name, dotted.NAME().getText());
            }
        }
        importNodes.get(importNodes.size() - 1).setAliases(aliases);
        return imports;
    }

    /**
     * 处理from导入语句的目标
     *
     * @param ctx from导入语句目标上下文
     * @return 导入项列表
     */
    private List<String> visitImportFromTargets(PythonParser.Import_from_targetsContext ctx) {
        List<String> imports = new ArrayList<>();
        Map<String, String> aliases = new HashMap<>();
        if (ctx.import_from_as_names() != null) {
            for (PythonParser.Import_from_as_nameContext name : ctx.import_from_as_names().import_from_as_name()) {
                String importName = name.NAME(0).getText();
                imports.add(importName);
                if (name.NAME().size() > 1) {
                    aliases.put(importName, name.NAME(1).getText());
                }
            }
        } else if (ctx.STAR() != null) {
            imports.add("*");
        }
        importNodes.get(importNodes.size() - 1).setAliases(aliases);
        return imports;
    }

    private String getFullModulePath(String importModulePath, int dotCount) {
        // 如果是绝对导入（dotCount == 0），直接返回模块路径
        if (dotCount == 0) {
            return importModulePath;
        }

        // 处理相对导入
        // 使用当前模块的完整路径来计算相对导入
        String[] currentModuleParts = this.modulePath.split("\\.");

        // 计算目标包的路径
        // dotCount = 1: 当前包 (.)
        // dotCount = 2: 父包 (..)
        // dotCount = 3: 祖父包 (...) 等等

        // 对于相对导入，我们需要从当前模块的包开始计算
        // 如果当前模块是 package.subpackage.module，那么包路径是 package.subpackage
        String[] packageParts;
        if (currentModuleParts.length > 1) {
            // 去掉模块名，只保留包路径
            packageParts = Arrays.copyOf(currentModuleParts, currentModuleParts.length - 1);
        } else {
            // 如果没有包结构，包路径为空
            packageParts = new String[0];
        }

        int levelsUp = dotCount - 1; // 需要向上几级

        if (levelsUp >= packageParts.length) {
            // 如果向上的级数超过了当前包的深度，返回相对路径标记
            StringBuilder result = new StringBuilder();
            for (int i = 0; i < dotCount; i++) {
                result.append(".");
            }
            if (!importModulePath.isEmpty()) {
                result.append(importModulePath);
            }
            return result.toString();
        }

        // 计算目标包路径
        String[] targetPackageParts = Arrays.copyOf(packageParts, packageParts.length - levelsUp);
        String targetPackage = String.join(".", targetPackageParts);

        // 构建完整的导入路径
        if (importModulePath.isEmpty()) {
            return targetPackage;
        } else {
            return targetPackage.isEmpty() ? importModulePath : targetPackage + "." + importModulePath;
        }
    }
}
