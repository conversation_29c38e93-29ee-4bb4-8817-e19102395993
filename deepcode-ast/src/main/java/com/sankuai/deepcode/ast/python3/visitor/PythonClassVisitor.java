package com.sankuai.deepcode.ast.python3.visitor;

import com.sankuai.deepcode.ast.model.base.LocationInfo;
import com.sankuai.deepcode.ast.model.python3.*;
import com.sankuai.deepcode.ast.python3.gen.PythonParser;
import com.sankuai.deepcode.ast.python3.gen.PythonParserBaseVisitor;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

import java.util.Comparator;

/**
 * Package: com.sankuai.deepcode.ast.python3.visitor
 * Description:
 *
 * <AUTHOR>
 * @since 2024/12/4 17:13
 */
@Slf4j
public class PythonClassVisitor extends PythonParserBaseVisitor<Object> {
    @Getter
    private final PythonClassNode classNode;
    private final String moduleName;
    private final ScopeManager scopeManager;

    public PythonClassVisitor(String fileName, String moduleName, ScopeManager scopeManager) {
        this.moduleName = moduleName;
        this.scopeManager = scopeManager;

        this.classNode = new PythonClassNode();
        this.classNode.setFileName(fileName);
        this.classNode.setModuleName(moduleName);
    }

    @Override
    public Object visitClass_def(PythonParser.Class_defContext ctx) {
        if (ctx.class_def_raw() != null) {
            classNode.setName(ctx.class_def_raw().NAME().getText());
            classNode.setModulePath(moduleName + "." + classNode.getName());
            scopeManager.enterScope(classNode.getName());
            // 当前类中，self 均指向当前类
            scopeManager.addToScope("self", classNode.getModulePath(), ctx.getStart().getLine());
        }
        classNode.setClassBody(ctx.getText());
        classNode.setStart(LocationInfo.of(ctx.getStart().getLine()));
        classNode.setEnd(LocationInfo.of(ctx.getStop().getLine() - 1));

        // 处理类的文档字符串和块级注释
        if (ctx.class_def_raw() != null && ctx.class_def_raw().block() != null) {
            processClassDocstringAndComments(ctx.class_def_raw().block());
            visitBlock(ctx.class_def_raw().block());
        }

        // 处理父类
        if (ctx.class_def_raw() != null && ctx.class_def_raw().arguments() != null) {
            PythonParser.ArgumentsContext argsCtx = ctx.class_def_raw().arguments();
            if (argsCtx.args() != null) {
                for (int i = 0; i < argsCtx.args().expression().size(); i++) {
                    PythonParser.ExpressionContext expr = argsCtx.args().expression(i);
                    String superClassName = expr.getText();
                    PythonClassNode.PythonSuperClass superClass = new PythonClassNode.PythonSuperClass();
                    superClass.setIndex(i);
                    superClass.setName(superClassName);

                    // 解析父类的来源
                    String sourceModulePath = resolveSuperClassPath(superClassName, argsCtx.start.getLine());
                    superClass.setSourceModulePath(sourceModulePath);

                    classNode.getSuperClasses().add(superClass);
                }
            }
        }

        // 处理装饰器
        if (ctx.decorators() != null) {
            for (PythonParser.Named_expressionContext decorator : ctx.decorators().named_expression()) {
                PythonDecoratorClass decoratorClass = new PythonDecoratorClass();
                decoratorClass.setName(decorator.getText());
                classNode.getDecorators().add(decoratorClass);
            }

            // 类上如果存在装饰器，需要将类定义的开始 以装饰器定义的结束+1
            classNode.getDecorators().stream().max(
                    Comparator.comparing(decorator -> decorator.getStart().getLine())
            ).map(PythonClassNode::getStart).map(LocationInfo::getLine).ifPresent(l -> classNode.getStart().setLine(l + 1));
        }
        // 退出类的作用域
        scopeManager.exitScope();
        return null;
    }

    /**
     * 权且只解析 module.ClassName 的这一种情况，链式的模块使用较少，暂且 不处理
     *
     * @param superClassName 父类的名称
     * @return 解析出的父类来源的模块路径
     */
    private String resolveSuperClassPath(String superClassName, int line) {
        String[] parts = superClassName.split("\\.");
        StringBuilder currentPath = new StringBuilder();
        String resolvedPath = "Unknown";

        for (String part : parts) {
            if (currentPath.length() > 0) {
                currentPath.append(".");
            }
            currentPath.append(part);
            String tempPath = scopeManager.resolveSymbol(currentPath.toString(), line);
            if (!"Unknown".equals(tempPath)) {
                resolvedPath = tempPath;
            }
        }

        return resolvedPath;
    }


    @Override
    public Object visitBlock(PythonParser.BlockContext ctx) {
        if (ctx.statements() != null) {
            for (PythonParser.StatementContext stmt : ctx.statements().statement()) {
                visitStatement(stmt);
            }
        }

        if (ctx.simple_stmts() != null) {
            for (PythonParser.Simple_stmtContext simpleStmt : ctx.simple_stmts().simple_stmt()) {
                if (simpleStmt.assignment() != null) {
                    visitAssignment(simpleStmt.assignment());
                }
            }
        }

        // 处理类内的单行注释
        if (ctx.statements() != null) {
            for (PythonParser.StatementContext stmt : ctx.statements().statement()) {
                if (stmt.getText().trim().startsWith("#")) {
                    classNode.getComments().add(new PythonComment().setComment(stmt.getText().trim().replace("#", "")));
                }
            }
        }
        return null;
    }

    @Override
    public Object visitStatement(PythonParser.StatementContext ctx) {
        if (ctx.compound_stmt() != null) {
            if (ctx.compound_stmt().function_def() != null) {
                visitFunction_def(ctx.compound_stmt().function_def());
            } else if (ctx.compound_stmt().class_def() != null) {
                // 处理嵌套类
                log.warn("nested class def: {} not supported.", ctx.compound_stmt().class_def().getText());
                /*
                * 暂且不解析 类定义内部嵌套的类定义
                PythonClassVisitor nestedClassVisitor = new PythonClassVisitor(classNode.getFileName(), classNode.getModulePath(), scopeManager);
                PythonClassNode nestedClassNode = (PythonClassNode) nestedClassVisitor.visitClass_def(ctx.compound_stmt().class_def());
                scopeManager.exitScope();
                classNode.getInnerClasses().add(nestedClassNode);
                 */
            }
        } else if (ctx.simple_stmts() != null) {
            for (PythonParser.Simple_stmtContext simpleStmt : ctx.simple_stmts().simple_stmt()) {
                if (simpleStmt.assignment() != null) {
                    visitAssignment(simpleStmt.assignment());
                }
            }
        }
        return null;
    }

    @Override
    public Object visitFunction_def(PythonParser.Function_defContext ctx) {
        PythonMethodDefVisitor functionVisitor = new PythonMethodDefVisitor(classNode.getFileName(), classNode.getModuleName(), classNode.getName(), scopeManager);
        functionVisitor.visit(ctx);
        PythonMethodNode functionNode = functionVisitor.getMethodNode();
        scopeManager.addToScope(functionNode.getName(), classNode.getModulePath() + "." + functionNode.getName(), ctx.getStart().getLine());
        classNode.getMethods().add(functionNode);
        return null;
    }

    @Override
    public Object visitAssignment(PythonParser.AssignmentContext ctx) {
        // 处理类属性
        PythonParameterNode paramNode = new PythonParameterNode();
        if (ctx.NAME() != null) {
            paramNode.setName(ctx.NAME().getText());
        } else if (ctx.single_target() != null && ctx.single_target().NAME() != null) {
            paramNode.setName(ctx.single_target().NAME().getText());
        }
        if (ctx.expression() != null) {
            paramNode.setType(inferType(ctx.expression()));
        }
        if (ctx.annotated_rhs() != null) {
            // 提取实际的字面量值
            String literalValue = PythonLiteralValueExtractor.extractLiteralValue(ctx.annotated_rhs());
            paramNode.setDefaultValue(literalValue);
        }
        scopeManager.addToScope(paramNode.getName(), classNode.getModulePath() + "." + paramNode.getName(), ctx.getStart().getLine());
        classNode.getParameters().add(paramNode);
        return null;
    }

    private String inferType(PythonParser.ExpressionContext ctx) {
        if (ctx.disjunction() != null) {
            PythonParser.DisjunctionContext disj = ctx.disjunction().get(0);
            if (disj.conjunction() != null && !disj.conjunction().isEmpty()) {
                PythonParser.ConjunctionContext conj = disj.conjunction(0);
                if (conj.inversion() != null && !conj.inversion().isEmpty()) {
                    PythonParser.InversionContext inv = conj.inversion(0);
                    if (inv.NOT() == null && inv.comparison() != null) {
                        PythonParser.ComparisonContext comp = inv.comparison();
                        if (comp.bitwise_or() != null && !comp.bitwise_or().isEmpty()) {
                            PythonParser.Bitwise_orContext bitOr = comp.bitwise_or();
                            return inferTypeFromBitwiseOr(bitOr);
                        }
                    }
                }
            }
        }
        return "unknown";
    }

    private String inferTypeFromBitwiseOr(PythonParser.Bitwise_orContext ctx) {
        if (ctx.bitwise_xor() != null && !ctx.bitwise_xor().isEmpty()) {
            PythonParser.Bitwise_xorContext xor = ctx.bitwise_xor();
            if (xor.bitwise_and() != null && !xor.bitwise_and().isEmpty()) {
                PythonParser.Bitwise_andContext and = xor.bitwise_and();
                if (and.shift_expr() != null && !and.shift_expr().isEmpty()) {
                    PythonParser.Shift_exprContext shift = and.shift_expr();
                    if (shift.sum() != null && !shift.sum().isEmpty()) {
                        PythonParser.SumContext sum = shift.sum();
                        if (sum.term() != null && !sum.term().isEmpty()) {
                            PythonParser.TermContext term = sum.term();
                            if (term.factor() != null && !term.factor().isEmpty()) {
                                PythonParser.FactorContext factor = term.factor();
                                if (factor.power() != null) {
                                    return inferTypeFromPower(factor.power());
                                }
                            }
                        }
                    }
                }
            }
        }
        return "unknown";
    }

    private String inferTypeFromPower(PythonParser.PowerContext ctx) {
        if (ctx.await_primary() != null) {
            PythonParser.Await_primaryContext awaitPrimary = ctx.await_primary();
            if (awaitPrimary.primary() != null) {
                PythonParser.PrimaryContext primary = awaitPrimary.primary();
                if (primary.atom() != null) {
                    PythonParser.AtomContext atom = primary.atom();
                    if (atom.NAME() != null) {
                        return atom.NAME().getText();
                    } else if (atom.NUMBER() != null) {
                        return atom.NUMBER().getText().contains(".") ? "float" : "int";
                    } else if (atom.strings() != null) {
                        return "str";
                    } else if (atom.TRUE() != null || atom.FALSE() != null) {
                        return "bool";
                    } else if (atom.NONE() != null) {
                        return "None";
                    } else if (atom.tuple() != null) {
                        return "tuple";
                    } else if (atom.list() != null) {
                        return "list";
                    } else if (atom.dict() != null || atom.set() != null) {
                        return atom.dict() != null ? "dict" : "set";
                    }
                }
            }
        }
        return "unknown";
    }

    /**
     * 处理类的文档字符串和其他块级注释
     * 第一个三引号字符串作为类的文档字符串，其他的作为其他块级注释
     */
    private void processClassDocstringAndComments(PythonParser.BlockContext blockCtx) {
        if (blockCtx == null) {
            return;
        }

        if (blockCtx.statements() != null) {
            boolean foundFirstDocstring = false;

            for (PythonParser.StatementContext stmt : blockCtx.statements().statement()) {
                // 检查是否是简单语句且只包含一个表达式（可能是字符串字面量）
                if (stmt.simple_stmts() != null && stmt.simple_stmts().simple_stmt().size() == 1) {
                    PythonParser.Simple_stmtContext simpleStmt = stmt.simple_stmts().simple_stmt(0);

                    // 检查是否是表达式语句
                    if (simpleStmt.star_expressions() != null) {
                        String possibleDocstring = extractStringLiteral(simpleStmt.star_expressions());

                        if (possibleDocstring != null && isMultilineString(possibleDocstring)) {
                            PythonBlockComment blockComment = new PythonBlockComment()
                                    .setStart(LocationInfo.of(stmt.getStart().getLine()))
                                    .setEnd(LocationInfo.of(stmt.getStop().getLine()))
                                    .setComment(cleanDocstring(possibleDocstring));

                            if (!foundFirstDocstring) {
                                // 第一个三引号字符串作为类的文档字符串
                                classNode.setBlockComment(blockComment);
                                foundFirstDocstring = true;
                            } else {
                                // 其他的作为其他块级注释
                                classNode.getOtherBlockComments().add(blockComment);
                            }
                        }
                    }
                }
            }
        }
    }

    /**
     * 从表达式中提取字符串字面量
     */
    private String extractStringLiteral(PythonParser.Star_expressionsContext ctx) {
        // 简化的字符串提取逻辑，实际可能需要更复杂的解析
        String text = ctx.getText();
        if (text.startsWith("\"\"\"") || text.startsWith("'''")) {
            return text;
        }
        return null;
    }

    /**
     * 检查是否是多行字符串（三引号字符串）
     */
    private boolean isMultilineString(String str) {
        return str.startsWith("'''") || str.startsWith("\"\"\"");
    }

    /**
     * 清理文档字符串，移除三引号
     */
    private String cleanDocstring(String docstring) {
        if (docstring.startsWith("'''") && docstring.endsWith("'''")) {
            return docstring.substring(3, docstring.length() - 3).trim();
        } else if (docstring.startsWith("\"\"\"") && docstring.endsWith("\"\"\"")) {
            return docstring.substring(3, docstring.length() - 3).trim();
        }
        return docstring;
    }
}
