package com.sankuai.deepcode.ast.scss.visitor;

import com.sankuai.deepcode.ast.model.scss.ScssVariable;
import com.sankuai.deepcode.ast.scss.gen.ScssParser;
import com.sankuai.deepcode.ast.scss.gen.ScssParserBaseVisitor;
import lombok.Getter;

public class MyErrorDeclarationVisitor extends ScssParserBaseVisitor<Object> {

    @Getter
    private ScssVariable scssVariable;


    @Override
    public Object visitErrorDeclaration(ScssParser.ErrorDeclarationContext ctx) {
        scssVariable = new ScssVariable();
        scssVariable.setStartLine(ctx.getStart().getLine());
        scssVariable.setEndLine(ctx.getStop().getLine());
        scssVariable.setName(ctx.Error().getText());
        scssVariable.setValue(ctx.String_().getText());
        return null;
    }

}
