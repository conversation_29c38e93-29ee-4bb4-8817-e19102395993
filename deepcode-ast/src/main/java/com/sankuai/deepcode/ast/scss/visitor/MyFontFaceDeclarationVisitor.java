package com.sankuai.deepcode.ast.scss.visitor;

import com.sankuai.deepcode.ast.model.scss.ScssVariable;
import com.sankuai.deepcode.ast.scss.gen.ScssParser;
import com.sankuai.deepcode.ast.scss.gen.ScssParserBaseVisitor;
import lombok.Getter;

public class MyFontFaceDeclarationVisitor extends ScssParserBaseVisitor<Object> {

    @Getter
    private ScssVariable scssVariable;


    @Override
    public Object visitFontFaceDeclaration(ScssParser.FontFaceDeclarationContext ctx) {
        //todo 自定义字体
        return null;
    }


}
