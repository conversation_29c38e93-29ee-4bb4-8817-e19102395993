package com.sankuai.deepcode.ast.model.typescript;

import com.sankuai.deepcode.ast.enums.ChangeTypeEnum;
import com.sankuai.deepcode.ast.model.html.HtmlNode;
import lombok.Data;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

@Data
public class ScriptMethod {
    private boolean isExport;
    private int startLine;
    private int endLine;
    private int changeType = ChangeTypeEnum.DEFAULT.getCode();
    private List<Integer> changeLines = new ArrayList<>();
    private String body="";
    private List<Integer> commentLines = new ArrayList<>();
    private boolean isAsync = false;
    private boolean isAwait = false;
    private boolean isYield = false;
    private boolean isAbstract = false;
    private boolean isCallSignature = false;
    private String methodName;
    private String filePath;
    private String modifier;
    private List<String> methodTypes = new ArrayList<>(Arrays.asList("default"));
    private List<ScriptGenerics> generics = new ArrayList<>();
    private List<String> decorators = new ArrayList<>();
    private List<ScriptMethodParam> params = new ArrayList<>();
    private ScriptMethodReturn methodReturn;
    private List<ScriptMethod> invokeMethods = new ArrayList<>();
    private List<ScriptVariable> variables = new ArrayList<>();
    private  List<HtmlNode> htmlNodes = new ArrayList<>();

    private String defineType;
}
