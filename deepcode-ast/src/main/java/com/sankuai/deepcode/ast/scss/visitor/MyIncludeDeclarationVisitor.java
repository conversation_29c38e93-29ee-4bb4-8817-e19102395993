package com.sankuai.deepcode.ast.scss.visitor;

import com.sankuai.deepcode.ast.model.scss.ScssVariable;
import com.sankuai.deepcode.ast.scss.gen.ScssParser;
import com.sankuai.deepcode.ast.scss.gen.ScssParserBaseVisitor;
import lombok.Getter;

public class MyIncludeDeclarationVisitor extends ScssParserBaseVisitor<Object> {

    @Getter
    private ScssVariable scssVariable;


    @Override
    public Object visitIncludeDeclaration(ScssParser.IncludeDeclarationContext ctx) {
        //todo 引入混入（mixin）
        return null;
    }


}
