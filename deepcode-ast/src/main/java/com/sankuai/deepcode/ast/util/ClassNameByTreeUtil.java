package com.sankuai.deepcode.ast.util;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class ClassNameByTreeUtil {

    private class TrieNode {
        Map<String, TrieNode> children = new HashMap<>();
        List<String> classPaths = new ArrayList<>();
    }

    private TrieNode root;

    public ClassNameByTreeUtil() {
        root = new TrieNode();
    }

    public void insert(String classPath) {
        String[] tokens = classPath.split("\\.");
        TrieNode current = root;
        // 反向插入，使得Trie按类路径的后缀进行组织
        for (int i = tokens.length - 1; i >= 0; i--) {
            String token = tokens[i];
            current = current.children.computeIfAbsent(token, k -> new TrieNode());
        }
        // 在叶节点存储完整的类路径
        current.classPaths.add(classPath);
    }

    public List<String> searchClassName(String name) {
        String[] tokens = name.split("\\.");
        TrieNode current = root;
        // 反向遍历className的标记，并在Trie中查找
        for (int i = tokens.length - 1; i >= 0; i--) {
            String token = tokens[i];
            current = current.children.get(token);
            if (current == null) {
                return new ArrayList<>();
            }
        }
        List<String> result = new ArrayList<>();
        collectClassPaths(current, result);
        return result;
    }

    public void collectClassPaths(TrieNode node, List<String> result){
        result.addAll(node.classPaths);
        for(TrieNode child : node.children.values()){
            collectClassPaths(child, result);
        }
    }
}
