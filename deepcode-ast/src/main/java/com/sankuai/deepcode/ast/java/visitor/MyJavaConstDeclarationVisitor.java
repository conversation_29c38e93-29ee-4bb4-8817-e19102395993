package com.sankuai.deepcode.ast.java.visitor;

import com.sankuai.deepcode.ast.java.gen.JavaParser;
import com.sankuai.deepcode.ast.java.gen.JavaParserBaseVisitor;
import com.sankuai.deepcode.ast.model.java.ClassNode;
import com.sankuai.deepcode.ast.model.java.FieldNode;
import com.sankuai.deepcode.ast.model.java.JavaGenerics;
import com.sankuai.deepcode.ast.util.DeepCopy;
import lombok.Getter;

import java.util.ArrayList;
import java.util.List;

public class MyJavaConstDeclarationVisitor extends JavaParserBaseVisitor<Object> {
    private ClassNode classNode = new ClassNode();

    public MyJavaConstDeclarationVisitor(ClassNode myClassNode) {
        classNode = myClassNode;
    }

    @Getter
    private List<FieldNode> fieldNodes = new ArrayList<>();

    private FieldNode baseFieldNode = new FieldNode();

    private String typeStr = "";

    int index = 0;

    @Override
    public Object visitConstDeclaration(JavaParser.ConstDeclarationContext ctx) {
        if (null != ctx.typeType()) {
            typeStr = ctx.typeType().getText();
            com.sankuai.deepcode.ast.java.visitor.MyJavaTypeTypeVistor myJavaTypeTypeVistor = new MyJavaTypeTypeVistor();
            myJavaTypeTypeVistor.visit(ctx.typeType());
            baseFieldNode.setFieldType(myJavaTypeTypeVistor.getType());
            List<String> generics = new ArrayList<>();
            for (JavaGenerics javaGenerics : myJavaTypeTypeVistor.getGenerics()) {
                generics.add(javaGenerics.getName());
            }
            baseFieldNode.setSignatures(generics);
        }
        baseFieldNode.setClassName(classNode.getClassName());
        baseFieldNode.setInClassName(classNode.getInClassName());
        baseFieldNode.setStartLine(ctx.getStart().getLine());
        baseFieldNode.setEndLine(ctx.getStop().getLine());
        for (JavaParser.ConstantDeclaratorContext constantDeclaratorContext : ctx.constantDeclarator()) {
            visitConstantDeclarator(constantDeclaratorContext);
        }
        return null;
    }

    @Override
    public Object visitConstantDeclarator(JavaParser.ConstantDeclaratorContext ctx) {
        if (index == 0) {
            baseFieldNode.setFieldName(ctx.identifier().getText());
            if (null != ctx.variableInitializer()) {
                baseFieldNode.setValue(ctx.variableInitializer().getText());
            }
            baseFieldNode.setBody(typeStr + ctx.getText());
            fieldNodes.add(baseFieldNode);
        } else {
            FieldNode fieldNode = DeepCopy.deepCopy(baseFieldNode, FieldNode.class);
            fieldNode.setFieldName(ctx.identifier().getText());
            if (null != ctx.variableInitializer()) {
                fieldNode.setValue(ctx.variableInitializer().getText());
            }
            fieldNode.setBody(typeStr + ctx.getText());
            fieldNodes.add(fieldNode);
        }
        index++;
        return null;
    }


}
