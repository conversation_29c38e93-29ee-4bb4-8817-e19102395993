package com.sankuai.deepcode.ast.enums.typescript;

public enum StatementTypeEnum {
    BLOCK("block", "代码块"),
    VARIABLE("variable", "变量声明"),
    IMPORT("import", "导入语句"),
    EXPORT("export", "导出语句"),
    EMPTY("empty", "空语句"),
    ABSTRACT("abstract", "抽象声明"),
    ClASS("class", "类声明"),
    FUNCTION("function", "函数声明"),
    EXPRESSION("expression", "表达式语句"),
    INTERFACE("interface", "接口声明"),
    NAMESPACE("namespace", "命名空间声明"),
    IF("if", "条件语句"),
    CONTINUE("continue", "继续语句"),
    BREAK("break", "中断语句"),
    RETURN("return", "返回语句"),
    YIELD("yield", "生成器函数中的 yield 语句，用于暂停和恢复生成器函数的执行"),
    WITH("with", "用于扩展作用域链"),
    LABELLED("labelled", "语句添加标签"),
    SWITCH("switch", "多分支条件判断"),
    THROW("throw", "用于抛出异常"),
    TYR("try", "用于捕获异常"),
    DEBUGGER("debugger", "在代码中设置断点"),
    ARROWFUNCTION("arrowFunction", "箭头函数声明"),
    GENERATORFUNCTION("generatorFunction", "生成器函数声明，使用 function* 关键字定义的函数"),
    TYPEALIAS("typeAlias", "类型别名声明"),
    ENUM("enum", "枚举声明");

    private String code;

    private String desc;

    StatementTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return this.code;
    }


    public String getDesc() {
        return desc;
    }

    @Override
    public String toString() {
        return "ErrorCodeEnum{" +
                "code=" + code +
                ", desc='" + desc + '\'' +
                '}';
    }
}
