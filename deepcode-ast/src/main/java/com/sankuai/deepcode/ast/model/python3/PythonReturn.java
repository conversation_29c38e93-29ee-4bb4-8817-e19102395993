package com.sankuai.deepcode.ast.model.python3;

import com.sankuai.deepcode.ast.model.base.LocationInfo;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 * Package: com.sankuai.deepcode.ast.model.python3
 * Description:
 *
 * <AUTHOR>
 * @since 2024/12/4 11:39
 */
@Setter
@Getter
@Accessors(chain = true)
@ToString
public class PythonReturn {
    /**
     * 返回语句所在的行号列表
     */
    private LocationInfo returnLine;

    /**
     * 返回值的类型
     */
    private String type;
    /**
     * 返回的原始值
     */
    private String rawReturn;

}
