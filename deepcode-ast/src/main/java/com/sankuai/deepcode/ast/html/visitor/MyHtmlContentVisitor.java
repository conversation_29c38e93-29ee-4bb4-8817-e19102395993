package com.sankuai.deepcode.ast.html.visitor;

import com.sankuai.deepcode.ast.enums.html.ElementTypeEnum;
import com.sankuai.deepcode.ast.html.gen.HTMLParser;
import com.sankuai.deepcode.ast.html.gen.HTMLParserBaseVisitor;
import com.sankuai.deepcode.ast.model.html.ElementValue;
import com.sankuai.deepcode.ast.model.html.HtmlNode;
import lombok.Getter;
import org.antlr.v4.runtime.tree.TerminalNode;
import org.apache.commons.collections4.CollectionUtils;

public class MyHtmlContentVisitor extends HTMLParserBaseVisitor<Object> {

    @Getter
    private HtmlNode htmlNode;

    public MyHtmlContentVisitor(HtmlNode myHtmlNode) {
        htmlNode = myHtmlNode;
    }

    @Override
    public Object visitHtmlContent(HTMLParser.HtmlContentContext ctx) {
        htmlNode.setTagType(ElementTypeEnum.ELEMENT.getCode());
        htmlNode.setStartLine(ctx.getStart().getLine());
        htmlNode.setEndLine(ctx.getStop().getLine());
        if (CollectionUtils.isNotEmpty(ctx.htmlChardata())) {
            for (HTMLParser.HtmlChardataContext htmlChardataContext : ctx.htmlChardata()) {
                if (htmlChardataContext.HTML_TEXT() != null) {
                    ElementValue elementValue = new ElementValue();
                    elementValue.setLine(htmlChardataContext.HTML_TEXT().getSymbol().getLine());
                    elementValue.setValue(htmlChardataContext.HTML_TEXT().getText());
                    htmlNode.getElementValues().add(elementValue);
                }
            }
        }

        if (CollectionUtils.isNotEmpty(ctx.htmlElement())) {
            for (HTMLParser.HtmlElementContext htmlElementContext : ctx.htmlElement()) {
                HtmlNode myHtmlNode = new HtmlNode();
                myHtmlNode.setTagDepth(htmlNode.getTagDepth() + 1);
                myHtmlNode.setTagIndex(htmlNode.getChildren().size());
                myHtmlNode.setBody(htmlElementContext.getText());
                MyHtmlElementVisitor myHtmlElementVisitor = new MyHtmlElementVisitor(myHtmlNode);
                myHtmlElementVisitor.visit(htmlElementContext);
                htmlNode.getChildren().add(myHtmlNode);
            }
        }
        if (CollectionUtils.isNotEmpty(ctx.htmlComment())) {
            for (HTMLParser.HtmlCommentContext htmlCommentContext : ctx.htmlComment()) {
                if (htmlCommentContext.HTML_COMMENT() != null) {
                    htmlNode.getCommentLines().add(htmlCommentContext.HTML_COMMENT().getSymbol().getLine());
                } else if (htmlCommentContext.HTML_CONDITIONAL_COMMENT() != null) {
                    htmlNode.getCommentLines().add(htmlCommentContext.HTML_CONDITIONAL_COMMENT().getSymbol().getLine());
                }
            }
        }
        if (CollectionUtils.isNotEmpty(ctx.CDATA())) {
            for (TerminalNode cdataContext : ctx.CDATA()) {
//                HtmlNode cdataNode = new HtmlNode();
//                cdataNode.setTagType(ElementTypeEnum.CDATA.getCode());
//                cdataNode.setStartLine(cdataContext.getSymbol().getLine());
//                cdataNode.setEndLine(cdataContext.getSymbol().getLine());
                ElementValue elementValue = new ElementValue();
                elementValue.setLine(cdataContext.getSymbol().getLine());
                elementValue.setValue(cdataContext.getText());
//                cdataNode.getElementValues().add(elementValue);
//                cdataNode.setBody(cdataContext.getText());
                htmlNode.getElementValues().add(elementValue);
            }
        }
        return null;
    }


}
