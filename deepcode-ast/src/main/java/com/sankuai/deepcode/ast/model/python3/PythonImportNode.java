package com.sankuai.deepcode.ast.model.python3;

import com.sankuai.deepcode.ast.model.base.LocationInfo;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;
import java.util.Map;

/**
 * Package: com.sankuai.deepcode.ast.model.python3
 * Description:
 *
 * <AUTHOR>
 * @since 2024/12/4 15:18
 */
@Setter
@Getter
@ToString
public class PythonImportNode {
    // 导入语句所在的文件路径
    private String fileName;
    // 导入语句所在的模块名称
    private String currentModuleName;
    // 导入语句所在的模块路径
    private String currentModulePath;
    // 导入类型：'import' 或 'from'
    private ImportType importType;

    private String modulePath;
    // 导入的模块路径（用于from语句）
    private String fromModulePath;
    // 导入模块的全路径
    private String fullImportModulePath;
    // 导入的模块或对象列表
    private List<String> importedItems;
    // 别名映射（原名 -> 别名）
    private Map<String, String> aliases;
    // 导入语句的位置信息
    private LocationInfo location;

    @Getter
    public enum ImportType {
        IMPORT("import"),
        FROM("from");

        private final String type;

        ImportType(String type) {
            this.type = type;
        }
    }
}

