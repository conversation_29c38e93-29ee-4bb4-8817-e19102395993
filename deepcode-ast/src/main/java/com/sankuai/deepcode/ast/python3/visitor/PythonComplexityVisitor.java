package com.sankuai.deepcode.ast.python3.visitor;

import com.sankuai.deepcode.ast.python3.gen.PythonParser;
import com.sankuai.deepcode.ast.python3.gen.PythonParserBaseVisitor;
import lombok.Getter;
import org.apache.commons.collections4.CollectionUtils;

/**
 * Package: com.sankuai.deepcode.ast.python3.visitor
 * Description:
 *
 * <AUTHOR>
 * @since 2024/12/23 20:20
 */

@Getter
public class PythonComplexityVisitor extends PythonParserBaseVisitor<Void> {
    private int complexity = 1;

    @Override
    public Void visitIf_stmt(PythonParser.If_stmtContext ctx) {
        complexity++;
        return super.visitIf_stmt(ctx);
    }

    @Override
    public Void visitElif_stmt(PythonParser.Elif_stmtContext ctx) {
        complexity++;
        return super.visitElif_stmt(ctx);
    }

    @Override
    public Void visitElse_block(PythonParser.Else_blockContext ctx) {
        complexity++;
        return super.visitElse_block(ctx);
    }

    @Override
    public Void visitFinally_block(PythonParser.Finally_blockContext ctx) {
        complexity++;
        return super.visitFinally_block(ctx);
    }

    @Override
    public Void visitWhile_stmt(PythonParser.While_stmtContext ctx) {
        complexity++;
        return super.visitWhile_stmt(ctx);
    }

    @Override
    public Void visitFor_stmt(PythonParser.For_stmtContext ctx) {
        complexity++;
        return super.visitFor_stmt(ctx);
    }


    @Override
    public Void visitExcept_block(PythonParser.Except_blockContext ctx) {
        complexity++;
        return super.visitExcept_block(ctx);
    }

    @Override
    public Void visitSimple_stmt(PythonParser.Simple_stmtContext ctx) {
        if (ctx.BREAK() != null) {
            complexity++;
        } else if (ctx.CONTINUE() != null) {
            complexity++;
        }
        return super.visitSimple_stmt(ctx);
    }

    @Override
    public Void visitCase_block(PythonParser.Case_blockContext ctx) {
        complexity++;
        return super.visitCase_block(ctx);
    }

    @Override
    public Void visitDisjunction(PythonParser.DisjunctionContext ctx) {
        if (CollectionUtils.isNotEmpty(ctx.OR())) {
            complexity++;
        }
        return super.visitDisjunction(ctx);
    }

    @Override
    public Void visitConjunction(PythonParser.ConjunctionContext ctx) {
        if (CollectionUtils.isNotEmpty(ctx.AND())) {
            complexity++;
        }
        return super.visitConjunction(ctx);
    }
}
