package com.sankuai.deepcode.ast.java.visitor;//package com.sankuai.deepcode.ast.java.visitor;
//
//import com.sankuai.deepcode.ast.enums.java.ClassTypeEnum;
//import com.sankuai.deepcode.ast.java.gen.JavaParser;
//import com.sankuai.deepcode.ast.java.gen.JavaParserBaseVisitor;
//import com.sankuai.deepcode.ast.model.java.*;
//import lombok.Getter;
//import lombok.Setter;
//import org.antlr.v4.runtime.ParserRuleContext;
//import org.antlr.v4.runtime.Token;
//import org.antlr.v4.runtime.tree.ParseTree;
//import org.antlr.v4.runtime.tree.TerminalNodeImpl;
//import org.apache.commons.collections.CollectionUtils;
//import org.apache.commons.lang3.StringUtils;
//
//import java.util.*;
//
//public class MyJavaClassVisitor extends JavaParserBaseVisitor<Object> {
//
//    private boolean initial = false;
//    private String classPath;
//    private static Map<String, List<MethodNode>> allClassAndMethodMap = new HashMap<>();
//    private static List<String> allClassList = new ArrayList<>();
//    private Map<String, FieldNode> classFields = new HashMap<>();
//    private Map<Object, List<FieldNode>> fieldNames = new HashMap<>();
//
//    public MyJavaClassVisitor(String path, boolean init, Map<String, List<MethodNode>> classAndMethodMap, List<String> classList, Map<Object, List<FieldNode>> nameFieldMap) {
//        initial = init;
//        allClassAndMethodMap = classAndMethodMap;
//        allClassList = classList;
//        classPath = path;
//        fieldNames = nameFieldMap;
//        classNode.setClassPath(path);
//    }
//
//    private int classVistorCount = 0;
//
//    @Getter
//    private ClassNode classNode = new ClassNode();
//
//    @Getter
//    private List<ClassNode> inClassNode = new ArrayList<>();
//
//    private int inClassNameNum = 1;
//
//    @Setter
//    private String inClassName = "";
//    @Setter
//    private String className = "";
//    @Setter
//    private String packageName = "";
//
//    private Map<String, MethodNode> methodNameMap = new HashMap<>();
//
//    private boolean initStaticMethod = false;
//
//    @Override
//    public Object visitClassDeclaration(JavaParser.ClassDeclarationContext ctx) {
//        classVistorCount++;
//        Object object = handleDeclaration(ctx, ClassTypeEnum.CLASS);
//        classVistorCount--;
//        return object;
//    }
//
//    @Override
//    public Object visitInterfaceDeclaration(JavaParser.InterfaceDeclarationContext ctx) {
//        classVistorCount++;
//        Object object = handleDeclaration(ctx, ClassTypeEnum.INTERFACE);
//        classVistorCount--;
//        return object;
//    }
//
//    @Override
//    public Object visitEnumDeclaration(JavaParser.EnumDeclarationContext ctx) {
//        classVistorCount++;
//        Object object = handleDeclaration(ctx, ClassTypeEnum.ENUM);
//        classVistorCount--;
//        return object;
//    }
//
//    @Override
//    public Object visitAnnotationTypeDeclaration(JavaParser.AnnotationTypeDeclarationContext ctx) {
//        classVistorCount++;
//        Object object = handleDeclaration(ctx, ClassTypeEnum.ANNOTATION);
//        classVistorCount--;
//        return object;
//    }
//
//    private Object handleDeclaration(ParserRuleContext ctx, ClassTypeEnum classType) {
//        if (classVistorCount == 1) {
//            Token start = ctx.start;
//            Token stop = ctx.stop;
//            if (null != start) {
//                classNode.setStartLine(start.getLine());
//            }
//            if (null != stop) {
//                classNode.setEndLine(stop.getLine());
//            }
//            if (StringUtils.isEmpty(inClassName)) {
//                if (ctx instanceof JavaParser.AnnotationTypeDeclarationContext) {
//                    if (StringUtils.isNotEmpty(packageName)) {
//                        classNode.setClassName(packageName + "." + ((JavaParser.AnnotationTypeDeclarationContext) ctx).identifier().getText());
//                    } else {
//                        classNode.setClassName(((JavaParser.AnnotationTypeDeclarationContext) ctx).identifier().getText());
//                    }
//                } else {
//                    if (StringUtils.isNotEmpty(packageName)) {
//                        classNode.setClassName(packageName + "." + ctx.getChild(1).getText());
//                    } else {
//                        classNode.setClassName(ctx.getChild(1).getText());
//                    }
//                }
//                className = classNode.getClassName();
//            } else {
//                classNode.setClassName(className);
//                classNode.setInClassName(inClassName);
//            }
//
//            initClassAccessAndAnn(ctx, classNode);
//
//            classNode.setClassType(classType.getCode());
//            if (ctx instanceof JavaParser.ClassDeclarationContext) {
//                JavaParser.ClassDeclarationContext classCtx = (JavaParser.ClassDeclarationContext) ctx;
//                if (classCtx.typeParameters() != null) {
//                    for (JavaParser.TypeParameterContext typeParameterContext : classCtx.typeParameters().typeParameter()) {
//                        String typeName = typeParameterContext.identifier().getText();
//                        JavaGenerics javaGenerics = new JavaGenerics();
//                        javaGenerics.setName(typeName);
//                        classNode.getGenerics().add(javaGenerics);
//                    }
//                }
//                if (classCtx.typeType() != null) {
//                    MyJavaTypeTypeVistor visitor = new MyJavaTypeTypeVistor();
//                    visitor.visit(classCtx.typeType());
//                    JavaExtends javaExtends = new JavaExtends();
//                    javaExtends.setName(visitor.getType());
//                    javaExtends.setGenerics(visitor.getGenerics());
//                    classNode.setSuperClass(javaExtends);
//                }
//                if (classCtx.typeList() != null) {
//                    for (JavaParser.TypeListContext typeListContext : classCtx.typeList()) {
//                        MyJavaTypeTypeVistor visitor = new MyJavaTypeTypeVistor();
//                        visitor.visit(typeListContext);
//                        JavaImplements javaImplements = new JavaImplements();
//                        javaImplements.setName(visitor.getType());
//                        javaImplements.setGenerics(visitor.getGenerics());
//                        classNode.getInterfaces().add(javaImplements);
//                    }
//                }
//            } else if (ctx instanceof JavaParser.InterfaceDeclarationContext) {
//                JavaParser.InterfaceDeclarationContext interfaceCtx = (JavaParser.InterfaceDeclarationContext) ctx;
//                if (interfaceCtx.typeParameters() != null) {
//                    for (JavaParser.TypeParameterContext typeParameterContext : interfaceCtx.typeParameters().typeParameter()) {
//                        String typeName = typeParameterContext.identifier().getText();
//                        JavaGenerics javaGenerics = new JavaGenerics();
//                        javaGenerics.setName(typeName);
//                        classNode.getGenerics().add(javaGenerics);
//                    }
//                }
//            } else if (ctx instanceof JavaParser.EnumDeclarationContext) {
//                JavaParser.EnumDeclarationContext enumCtx = (JavaParser.EnumDeclarationContext) ctx;
//                JavaParser.TypeListContext typeListContext = enumCtx.typeList();
//                if (typeListContext != null) {
//                    MyJavaTypeTypeVistor visitor = new MyJavaTypeTypeVistor();
//                    visitor.visit(typeListContext);
//                    JavaImplements javaImplements = new JavaImplements();
//                    javaImplements.setName(visitor.getType());
//                    javaImplements.setGenerics(visitor.getGenerics());
//                    classNode.getInterfaces().add(javaImplements);
//                }
//
//                MyJavaEnumVisitor visitor = new MyJavaEnumVisitor();
//                visitor.visit(enumCtx);
//                for (FieldNode fieldNode : visitor.getFieldNodes()) {
//                    fieldNode.setClassName(classNode.getClassName());
//                    fieldNode.setInClassName(classNode.getInClassName());
//                    classNode.getFieldNodes().add(fieldNode);
//                }
//
//            }
//            Object object = visitChildren(ctx);
//            return object;
//        } else {
//            MyJavaClassVisitor visitor = new MyJavaClassVisitor(classPath, initial, allClassAndMethodMap, allClassList, fieldNames);
//            visitor.setPackageName(packageName);
//            visitor.setClassName(classNode.getClassName());
//            if (StringUtils.isEmpty(inClassName)) {
//                if (StringUtils.isNotEmpty(ctx.getChild(1).getText())) {
//                    visitor.setInClassName(ctx.getChild(1).getText());
//                } else {
//                    visitor.setInClassName(String.valueOf(inClassNameNum));
//                    inClassNameNum++;
//                }
//            } else {
//                if (StringUtils.isNotEmpty(ctx.getChild(1).getText())) {
//                    visitor.setInClassName(inClassName + "$" + ctx.getChild(1).getText());
//                } else {
//                    visitor.setInClassName(inClassName + "$" + inClassNameNum);
//                    inClassNameNum++;
//                }
//            }
//            visitor.visit(ctx);
//            Token start = ctx.start;
//            Token stop = ctx.stop;
//            if (null != start) {
//                visitor.getClassNode().setStartLine(start.getLine());
//            }
//            if (null != stop) {
//                visitor.getClassNode().setEndLine(stop.getLine());
//            }
//            initClassAccessAndAnn(ctx, visitor.getClassNode());
//            inClassNode.add(visitor.getClassNode());
//            return visitChildren(ctx);
//        }
//    }
//
//
//    public void initClassAccessAndAnn(ParserRuleContext ctx, ClassNode classNode) {
//        if (ctx.parent instanceof JavaParser.TypeDeclarationContext) {
//            List<JavaParser.ClassOrInterfaceModifierContext> classOrInterfaceModifierContexts =
//                    ((JavaParser.TypeDeclarationContext) ctx.parent).classOrInterfaceModifier();
//            if (classOrInterfaceModifierContexts != null) {
//                List<JavaAnnotation> annotations = new ArrayList<>();
//                String access = "";
//                for (JavaParser.ClassOrInterfaceModifierContext classOrInterfaceModifierContext : classOrInterfaceModifierContexts) {
//                    if (null != classOrInterfaceModifierContext.annotation()) {
//                        MyJavaAnnotationVisitor visitor = new MyJavaAnnotationVisitor();
//                        visitor.visit(classOrInterfaceModifierContext.annotation());
//                        annotations.add(visitor.getAnnotation());
//                    } else {
//                        access += " " + classOrInterfaceModifierContext.getText();
//                    }
//                }
//                classNode.setAccess(access.trim());
//                classNode.getAnnotations().addAll(annotations);
//            }
//        } else if (ctx.parent instanceof JavaParser.InterfaceMemberDeclarationContext
//                || ctx.parent instanceof JavaParser.MemberDeclarationContext) {
//            List<JavaParser.ModifierContext> modifierContexts = null;
//            if (ctx.parent instanceof JavaParser.InterfaceMemberDeclarationContext) {
//                modifierContexts = ((JavaParser.InterfaceBodyDeclarationContext) ctx.parent.parent).modifier();
//            } else {
//                modifierContexts = ((JavaParser.ClassBodyDeclarationContext) ctx.parent.parent).modifier();
//            }
//            if (modifierContexts != null) {
//                List<JavaAnnotation> annotations = new ArrayList<>();
//                String access = "";
//                for (JavaParser.ModifierContext modifierContext : modifierContexts) {
//                    if (null != modifierContext.classOrInterfaceModifier()) {
//                        if (null != modifierContext.classOrInterfaceModifier().annotation()) {
//                            MyJavaAnnotationVisitor visitor = new MyJavaAnnotationVisitor();
//                            visitor.visit(modifierContext.classOrInterfaceModifier().annotation());
//                            visitor.getAnnotation().setStartLine(ctx.getStart().getLine());
//                            visitor.getAnnotation().setEndLine(ctx.getStop().getLine());
//                            annotations.add(visitor.getAnnotation());
//                        } else {
//                            access += " " + modifierContext.classOrInterfaceModifier().getText();
//                        }
//                    }
//                }
//                classNode.setAccess(access.trim());
//                classNode.getAnnotations().addAll(annotations);
//            }
//        }
//    }
//
//
//    @Override
//    public Object visitGenericConstructorDeclaration(JavaParser.GenericConstructorDeclarationContext ctx) {
//        MyJavaMethodVisitor visitor = new MyJavaMethodVisitor(initial, classNode, allClassAndMethodMap, allClassList, classFields, fieldNames);
//        visitor.putLocalFieldName(classNode.getFieldNodes());
//        visitor.visit(ctx);
//        MethodNode methodNode = visitor.getMethodNode();
//        initMethodAccessAndAnn(ctx, methodNode);
//        if (CollectionUtils.isNotEmpty(methodNode.getAnnotations())) {
//            methodNode.setStartLine(methodNode.getAnnotations().get(0).getStartLine());
//        } else {
//            methodNode.setStartLine(ctx.getStart().getLine());
//        }
//        methodNode.setEndLine(ctx.getStop().getLine());
//        methodNode.setClassName(classNode.getClassName());
//        methodNode.setInClassName(inClassName);
//        idOverloading(methodNode);
//        classNode.getMethodNodes().add(methodNode);
//        return visitChildren(ctx);
//    }
//
//    @Override
//    public Object visitConstructorDeclaration(JavaParser.ConstructorDeclarationContext ctx) {
//        MyJavaMethodVisitor visitor = new MyJavaMethodVisitor(initial, classNode, allClassAndMethodMap, allClassList, classFields, fieldNames);
//        visitor.putLocalFieldName(classNode.getFieldNodes());
//        visitor.visit(ctx);
//        MethodNode methodNode = visitor.getMethodNode();
//        initMethodAccessAndAnn(ctx, methodNode);
//        if (CollectionUtils.isNotEmpty(methodNode.getAnnotations())) {
//            methodNode.setStartLine(methodNode.getAnnotations().get(0).getStartLine());
//        } else {
//            methodNode.setStartLine(ctx.getStart().getLine());
//        }
//        methodNode.setEndLine(ctx.getStop().getLine());
//        methodNode.setClassName(classNode.getClassName());
//        methodNode.setInClassName(inClassName);
//        idOverloading(methodNode);
//        classNode.getMethodNodes().add(methodNode);
//        return visitChildren(ctx);
//    }
//
//
//    @Override
//    public Object visitPackageDeclaration(JavaParser.PackageDeclarationContext ctx) {
//        packageName = ctx.qualifiedName().getText();
//        return visitChildren(ctx);
//    }
//
//    @Override
//    public Object visitImportDeclaration(JavaParser.ImportDeclarationContext ctx) {
//        if (!classNode.getImports().contains(ctx.qualifiedName().getText())) {
//            classNode.getImports().add(ctx.qualifiedName().getText());
//        }
//        return visitChildren(ctx);
//    }
//
////    @Override
////    public Object visitFieldDeclaration(JavaParser.FieldDeclarationContext ctx) {
////        if (classVistorCount == 1) {
////            MyJavaFieldVisitor visitor = new MyJavaFieldVisitor();
////            visitor.visit(ctx);
////            FieldNode fieldNode = visitor.getFieldNodes();
////            fieldNode.setClassName(classNode.getClassName());
////            fieldNode.setInClassName(inClassName);
////            if (ctx.parent instanceof JavaParser.MemberDeclarationContext) {
////                List<JavaParser.ModifierContext> modifierContexts = ((JavaParser.ClassBodyDeclarationContext) ctx.parent.parent).modifier();
////                if (modifierContexts != null) {
////                    List<JavaAnnotation> annotations = new ArrayList<>();
////                    String body = "";
////                    String access = "";
////                    for (JavaParser.ModifierContext modifierContext : modifierContexts) {
////                        body += modifierContext.getText();
////                        if (null != modifierContext.classOrInterfaceModifier()) {
////                            if (null != modifierContext.classOrInterfaceModifier().annotation()) {
////                                MyJavaAnnotationVisitor annotationVisitor = new MyJavaAnnotationVisitor();
////                                annotationVisitor.visit(modifierContext.classOrInterfaceModifier().annotation());
////                                annotationVisitor.getAnnotation().setStartLine(ctx.getStart().getLine());
////                                annotationVisitor.getAnnotation().setEndLine(ctx.getStop().getLine());
////                                annotations.add(annotationVisitor.getAnnotation());
////                            } else {
////                                access += " " + modifierContext.classOrInterfaceModifier().getText();
////                            }
////                        }
////                    }
////                    fieldNode.setAccess(access.trim());
////                    fieldNode.getAnnotations().addAll(annotations);
////                    fieldNode.setBody(body + fieldNode.getBody());
////                }
////            }
////            if (CollectionUtils.isNotEmpty(fieldNode.getAnnotations())) {
////                fieldNode.setStartLine(fieldNode.getAnnotations().get(0).getStartLine());
////            } else {
////                fieldNode.setStartLine(ctx.getStart().getLine());
////            }
////            fieldNode.setEndLine(ctx.getStop().getLine());
////            classNode.getFieldNodes().add(fieldNode);
////            classFields.put(fieldNode.getFieldName(), fieldNode);
////            return visitChildren(ctx);
////        } else {
////            return visitChildren(ctx);
////        }
////    }
//
//
//    @Override
//    public Object visitMethodDeclaration(JavaParser.MethodDeclarationContext ctx) {
//        if (classVistorCount == 1) {
//            MyJavaMethodVisitor visitor = new MyJavaMethodVisitor(initial, classNode, allClassAndMethodMap, allClassList, classFields, fieldNames);
//            visitor.putLocalFieldName(classNode.getFieldNodes());
//            visitor.visit(ctx);
//            MethodNode methodNode = visitor.getMethodNode();
//            initMethodAccessAndAnn(ctx, methodNode);
//            if (CollectionUtils.isNotEmpty(methodNode.getAnnotations())) {
//                methodNode.setStartLine(methodNode.getAnnotations().get(0).getStartLine());
//            } else {
//                methodNode.setStartLine(ctx.getStart().getLine());
//            }
//            methodNode.setEndLine(ctx.getStop().getLine());
//            methodNode.setClassName(classNode.getClassName());
//            methodNode.setInClassName(inClassName);
//            idOverloading(methodNode);
//            classNode.getMethodNodes().add(methodNode);
//            return visitChildren(ctx);
//        } else {
//            return visitChildren(ctx);
//        }
//    }
//
//    @Override
//    public Object visitInterfaceMethodDeclaration(JavaParser.InterfaceMethodDeclarationContext ctx) {
//        if (classVistorCount == 1) {
//            MyJavaMethodVisitor visitor = new MyJavaMethodVisitor(initial, classNode, allClassAndMethodMap, allClassList, classFields, fieldNames);
//            visitor.putLocalFieldName(classNode.getFieldNodes());
//            visitor.visit(ctx);
//            MethodNode methodNode = visitor.getMethodNode();
//            initMethodAccessAndAnn(ctx, methodNode);
//            if (CollectionUtils.isNotEmpty(methodNode.getAnnotations())) {
//                methodNode.setStartLine(methodNode.getAnnotations().get(0).getStartLine());
//            } else {
//                methodNode.setStartLine(ctx.getStart().getLine());
//            }
//            methodNode.setEndLine(ctx.getStop().getLine());
//            methodNode.setClassName(classNode.getClassName());
//            methodNode.setInClassName(inClassName);
//            idOverloading(methodNode);
//            classNode.getMethodNodes().add(methodNode);
//            return visitChildren(ctx);
//        } else {
//            return visitChildren(ctx);
//        }
//    }
//
//    @Override
//    public Object visitGenericInterfaceMethodDeclaration(JavaParser.GenericInterfaceMethodDeclarationContext ctx) {
//        if (classVistorCount == 1) {
//            MyJavaMethodVisitor visitor = new MyJavaMethodVisitor(initial, classNode, allClassAndMethodMap, allClassList, classFields, fieldNames);
//            visitor.putLocalFieldName(classNode.getFieldNodes());
//            visitor.visit(ctx);
//            MethodNode methodNode = visitor.getMethodNode();
//            initMethodAccessAndAnn(ctx, methodNode);
//            if (CollectionUtils.isNotEmpty(methodNode.getAnnotations())) {
//                methodNode.setStartLine(methodNode.getAnnotations().get(0).getStartLine());
//            } else {
//                methodNode.setStartLine(ctx.getStart().getLine());
//            }
//            methodNode.setEndLine(ctx.getStop().getLine());
//            methodNode.setClassName(classNode.getClassName());
//            methodNode.setInClassName(inClassName);
//            idOverloading(methodNode);
//            classNode.getMethodNodes().add(methodNode);
//            return visitChildren(ctx);
//        } else {
//            return visitChildren(ctx);
//        }
//    }
//
//
//    public void initMethodAccessAndAnn(ParserRuleContext ctx, MethodNode methodNode) {
//        if (ctx.parent instanceof JavaParser.MemberDeclarationContext) {
//            List<JavaParser.ModifierContext> modifierContexts = ((JavaParser.ClassBodyDeclarationContext) ctx.parent.parent).modifier();
//            if (modifierContexts != null) {
//                List<JavaAnnotation> annotations = new ArrayList<>();
//                StringBuilder access = new StringBuilder();
//                String body = "";
//                for (JavaParser.ModifierContext modifierContext : modifierContexts) {
//                    body += modifierContext.getText();
//                    if (null != modifierContext.classOrInterfaceModifier()) {
//                        if (null != modifierContext.classOrInterfaceModifier().annotation()) {
//                            MyJavaAnnotationVisitor visitor = new MyJavaAnnotationVisitor();
//                            visitor.visit(modifierContext.classOrInterfaceModifier().annotation());
//                            visitor.getAnnotation().setStartLine(ctx.getStart().getLine());
//                            visitor.getAnnotation().setEndLine(ctx.getStop().getLine());
//                            annotations.add(visitor.getAnnotation());
//                        } else {
//                            access.append(" ").append(modifierContext.classOrInterfaceModifier().getText());
//                        }
//                    }
//                }
//                methodNode.setAccess(access.toString().trim());
//                methodNode.getAnnotations().addAll(annotations);
//                methodNode.setBody(body + methodNode.getBody());
//            }
//        }
//    }
//
//    public void idOverloading(MethodNode methodNode) {
//        MethodNode overMethod = methodNameMap.get(methodNode.getMethodName());
//        if (null == overMethod) {
//            methodNameMap.put(methodNode.getMethodName(), methodNode);
//        } else {
//            overMethod.setOverloading(true);
//            methodNode.setOverloading(true);
//        }
//    }
//
//    @Override
//    public Object visitClassBodyDeclaration(JavaParser.ClassBodyDeclarationContext ctx) {
//        //todo 这样写只能处理一个 static  拼接终止行
//        if (CollectionUtils.isNotEmpty(ctx.children)) {
//            ParseTree parseTree = ctx.getChild(0);
//            if (parseTree instanceof TerminalNodeImpl) {
//                if ("static".equals(parseTree.getText())) {
//                    if (!initStaticMethod) {
//                        MethodNode methodNode = new MethodNode();
//                        methodNode.setClassName(className);
//                        methodNode.setInClassName(inClassName);
//                        methodNode.setMethodName("static");
//                        methodNode.setStartLine(ctx.getStart().getLine());
//                        methodNode.setEndLine(ctx.getStop().getLine());
//                        methodNode.setBody(ctx.getText());
//                        classNode.getMethodNodes().add(methodNode);
//                        initStaticMethod = true;
//                    } else {
//                        for (MethodNode methodNode : classNode.getMethodNodes()) {
//                            if ("static".equals(methodNode.getMethodName())) {
//                                methodNode.setBody(methodNode.getBody() + ctx.getText());
//                                methodNode.setEndLine(ctx.getStop().getLine());
//                                break;
//                            }
//                        }
//                    }
//                }
//            }
//        }
//        return visitChildren(ctx);
//    }
//}
