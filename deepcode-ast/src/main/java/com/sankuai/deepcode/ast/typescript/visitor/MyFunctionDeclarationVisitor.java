package com.sankuai.deepcode.ast.typescript.visitor;

import com.sankuai.deepcode.ast.model.typescript.*;
import com.sankuai.deepcode.ast.typescript.gen.TypeScriptParser;
import com.sankuai.deepcode.ast.typescript.gen.TypeScriptParserBaseVisitor;
import lombok.Getter;

import java.util.ArrayList;
import java.util.List;

public class MyFunctionDeclarationVisitor extends TypeScriptParserBaseVisitor<Object> {

    @Getter
    private ScriptMethod scriptMethod;

    private ScriptNode scriptNode;

    MyFunctionDeclarationVisitor(ScriptNode myScriptNode) {
        scriptMethod = new ScriptMethod();
        if (myScriptNode == null) {
            scriptNode = new ScriptNode();
        } else {
            scriptNode = myScriptNode;
        }
        if (scriptNode.isExport()) {
            scriptMethod.setExport(true);
        }
        scriptNode.getMethods().add(scriptMethod);
    }

    @Override
    public Object visitFunctionDeclaration(TypeScriptParser.FunctionDeclarationContext ctx) {
        scriptMethod.setStartLine(ctx.getStart().getLine());
        scriptMethod.setEndLine(ctx.getStop().getLine());
        scriptMethod.setBody(ctx.getText());
        if (ctx.Async() != null) {
            scriptMethod.setAsync(true);
        }
        if (ctx.Multiply() != null) {
            scriptMethod.setYield(true);
        }
        scriptMethod.setMethodName(ctx.identifier().getText());
        visitCallSignature(ctx.callSignature());
        MyFunctionBodyVisitor.visitFunctionBody(ctx.functionBody(), scriptMethod);
        return null;
    }

    @Override
    public Object visitCallSignature(TypeScriptParser.CallSignatureContext ctx) {
        //todo   callSignature 简单处理
        if (ctx.typeParameters() != null && ctx.typeParameters().typeParameterList() != null) {
            for (TypeScriptParser.TypeParameterContext typeParameterContext : ctx.typeParameters().typeParameterList().typeParameter()) {
                ScriptGenerics scriptGenerics = new ScriptGenerics();
                scriptGenerics.setName(typeParameterContext.getText());
                scriptMethod.getGenerics().add(scriptGenerics);
            }
        }

        if (ctx.parameterList() != null) {
            if (ctx.parameterList().restParameter() != null) {
                ScriptMethodParam scriptMethodParam1 = new ScriptMethodParam();
                scriptMethodParam1.setName("...");
                scriptMethod.getParams().add(scriptMethodParam1);
                ScriptMethodParam scriptMethodParam2 = new ScriptMethodParam();
                scriptMethodParam2.setName("...");
                if (ctx.parameterList().restParameter().typeAnnotation() != null) {
                    scriptMethodParam2.setType(ctx.parameterList().restParameter().typeAnnotation().type_().getText());
                }
                scriptMethod.getParams().add(scriptMethodParam2);
            } else if (ctx.parameterList().parameter() != null) {
                for (TypeScriptParser.ParameterContext parameterContext : ctx.parameterList().parameter()) {
                    ScriptMethodParam scriptMethodParam = new ScriptMethodParam();
                    if (parameterContext.requiredParameter() != null) {
                        scriptMethodParam.setRequired(true);
                        if (parameterContext.requiredParameter().decoratorList() != null) {
                            List<String> decorators = new ArrayList<>();
                            for (TypeScriptParser.DecoratorContext decoratorContext : parameterContext.requiredParameter().decoratorList().decorator()) {
                                decorators.add(decoratorContext.getText());
                            }
                            scriptMethodParam.setDecorators(decorators);
                        }
                        if (parameterContext.requiredParameter().accessibilityModifier() != null) {
                            scriptMethodParam.setModifier(parameterContext.requiredParameter().accessibilityModifier().getText());
                        }
                        scriptMethodParam.setName(parameterContext.requiredParameter().identifierOrPattern().getText());
                        if (parameterContext.requiredParameter().typeAnnotation() != null) {
                            scriptMethodParam.setType(parameterContext.requiredParameter().typeAnnotation().type_().getText());
                        }
                    } else {
                        if (parameterContext.optionalParameter().decoratorList() != null) {
                            List<String> decorators = new ArrayList<>();
                            for (TypeScriptParser.DecoratorContext decoratorContext : parameterContext.optionalParameter().decoratorList().decorator()) {
                                decorators.add(decoratorContext.getText());
                            }
                            scriptMethodParam.setDecorators(decorators);
                        }
                        if (parameterContext.optionalParameter().accessibilityModifier() != null) {
                            scriptMethodParam.setModifier(parameterContext.optionalParameter().accessibilityModifier().getText());
                        }
                        scriptMethodParam.setName(parameterContext.optionalParameter().identifierOrPattern().getText());
                        if (parameterContext.optionalParameter().initializer() != null) {
                            scriptMethodParam.setDefaultValue(parameterContext.optionalParameter().initializer().getText());
                        }
                        if (parameterContext.optionalParameter().typeAnnotation() != null) {
                            scriptMethodParam.setType(parameterContext.optionalParameter().typeAnnotation().type_().getText());
                        }
                    }
                    scriptMethod.getParams().add(scriptMethodParam);
                }
            }
        }

        if (ctx.typeAnnotation() != null) {
            ScriptMethodReturn scriptMethodReturn = new ScriptMethodReturn();
            scriptMethodReturn.setType(ctx.typeAnnotation().type_().getText());
            scriptMethod.setMethodReturn(scriptMethodReturn);
        } else {
            ScriptMethodReturn scriptMethodReturn = new ScriptMethodReturn();
            scriptMethodReturn.setType("void");
            scriptMethod.setMethodReturn(scriptMethodReturn);
        }
        return null;
    }
}
