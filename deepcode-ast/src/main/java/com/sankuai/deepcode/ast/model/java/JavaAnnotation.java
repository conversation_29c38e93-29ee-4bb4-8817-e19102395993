package com.sankuai.deepcode.ast.model.java;

import com.sankuai.deepcode.ast.model.java.JavaAnnotationValue;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
public class JavaAnnotation {
    private String className;
    private String name;
    private List<JavaAnnotationValue> values = new ArrayList<>();
    private int startLine = -1;
    private int endLine = -1;
}
