package com.sankuai.deepcode.ast.python3.visitor;

import com.sankuai.deepcode.ast.model.python3.PythonFunctionCallNode;
import com.sankuai.deepcode.ast.model.python3.PythonMethodNode;
import com.sankuai.deepcode.ast.model.python3.PythonParameterNode;
import com.sankuai.deepcode.ast.model.python3.ScopeManager;
import com.sankuai.deepcode.ast.python3.gen.PythonParser;
import lombok.Getter;

import java.util.List;

/**
 * Package: com.sankuai.deepcode.ast.python3.visitor
 * Description:
 *
 * <AUTHOR>
 * @since 2024/12/5 14:00
 */
@Getter
public class PythonMethodDefVisitor extends PythonFunctionDefVisitor {
    // 方法定义可以没有 类
    private final String fileName;
    private final String currentClassName;
    private final ScopeManager scopeManager;
    private PythonMethodNode methodNode;

    public PythonMethodDefVisitor(String fileName, String moduleName, String currentClassName, ScopeManager scopeManager) {
        super(fileName, moduleName + "." + currentClassName, scopeManager);
        this.fileName = fileName;
        this.currentClassName = currentClassName;
        this.scopeManager = scopeManager;
        this.methodNode = new PythonMethodNode();

        this.methodNode.setClassName(currentClassName);
        this.methodNode.setModulePath(moduleName + "." + currentClassName);

    }

    @Override
    public Object visitFunction_def(PythonParser.Function_defContext ctx) {
        super.visitFunction_def(ctx);
        // 类的实例方法定义，复用方法定义，只补齐 className 即可
        methodNode = new PythonMethodNode(super.getFunctionNode(), this.currentClassName);

        // 重新进入方法作用域以便解析局部变量
        scopeManager.enterScope(methodNode.getName());

        for (PythonFunctionCallNode functionCallNode : methodNode.getCallNodes()) {
            String fullCallExpression = functionCallNode.getRawCode();
            String[] callParts = smartSplit(fullCallExpression);



            if (callParts.length > 1) {
                // 处理链式调用
                processChainedCall(functionCallNode, callParts);
            } else {
                // 处理单个方法调用
                processSingleCall(functionCallNode);
            }
        }

        // 退出方法作用域
        scopeManager.exitScope();
        return null;
    }

    @Override
    public void processParameters(PythonParser.ParametersContext parametersCtx,
                                  List<PythonParameterNode> parameters,
                                  boolean hasArgs,
                                  boolean hasKwargs,
                                  int slashIndex,
                                  int starIndex) {
        super.processParameters(parametersCtx, parameters, hasArgs, hasKwargs, slashIndex, starIndex);

        // 特殊处理 self 参数
        if (!parameters.isEmpty() && parameters.get(0).getName().equals("self")) {
            parameters.get(0).setType(currentClassName);
            parameters.get(0).setTypeModule(methodNode.getModulePath());
        }
    }

    private void processChainedCall(PythonFunctionCallNode functionCallNode, String[] callParts) {
        StringBuilder sourcePath = new StringBuilder();
        StringBuilder functionName = new StringBuilder();
        boolean isSuperCall = false;



        for (int i = 0; i < callParts.length; i++) {
            String part = callParts[i].replaceAll("\\(.*\\)", ""); // 移除括号内的内容
            if (i == 0) {
                if (part.equals("self")) {
                    // self方法调用应该指向当前类，而不是当前方法
                    // 从methodNode.getModulePath()中提取类路径（去掉方法名）
                    String classPath = methodNode.getModulePath();
                    // methodNode.getModulePath() 格式为 "utils.StringMatcher.__init__"
                    // 我们需要提取 "utils.StringMatcher"
                    int lastDotIndex = classPath.lastIndexOf(".");
                    if (lastDotIndex > 0) {
                        sourcePath.append(classPath.substring(0, lastDotIndex));
                    } else {
                        sourcePath.append(classPath);
                    }
                } else if (part.equals("super")) {
                    // 标记为super调用
                    isSuperCall = true;
                    sourcePath.append("SUPER_CLASS_TO_BE_RESOLVED");
                } else {
                    String resolvedPath = scopeManager.resolveSymbol(part, functionCallNode.getStart().getLine());
                    if (resolvedPath != null) {
                        // 如果解析到的是内置类型，需要添加 BUILTIN 前缀
                        if (isBuiltinType(resolvedPath)) {
                            sourcePath.append("BUILTIN.").append(resolvedPath);
                        } else {
                            sourcePath.append(resolvedPath);
                        }
                    } else {
                        // 无法解析的变量，可能是方法参数或未知变量
                        sourcePath.append("Unknown");
                    }
                }
            } else {
                if (i == callParts.length - 1) {
                    functionName.append(part);
                } else {
                    // 如果sourcePath已经是"Unknown"，就不再添加更多的路径部分
                    if (!sourcePath.toString().equals("Unknown")) {
                        sourcePath.append(".").append(part);
                    }
                }
            }
        }

        functionCallNode.setSourceModulePath(sourcePath.toString());
        if (isSuperCall) {
            functionCallNode.setSuperCall(functionName.toString());
            functionCallNode.setFunctionName(functionName.toString());
        } else {
            // 对于非super调用，函数名应该只是最后一部分（方法名）
            // 例如：re.finditer -> functionName = "finditer"
            functionCallNode.setFunctionName(functionName.toString());
        }
    }

    private void processSingleCall(PythonFunctionCallNode functionCallNode) {
        String originalFunctionName = functionCallNode.getFunctionName();
        String resolvedPath = scopeManager.resolveSymbol(originalFunctionName, functionCallNode.getStart().getLine());

        if (resolvedPath != null) {
            functionCallNode.setSourceModulePath(resolvedPath);
            // 函数名保持不变，因为单个调用的函数名已经是正确的
        } else if (originalFunctionName.startsWith("self.")) {
            String methodName = originalFunctionName.substring(5);
            functionCallNode.setSourceModulePath(methodNode.getModulePath() + "." + methodName);
            functionCallNode.setFunctionName(methodName); // 只保留方法名
        } else if (originalFunctionName.startsWith("super().")) {
            String methodName = originalFunctionName.substring(8);
            functionCallNode.setSuperCall(methodName);
            functionCallNode.setFunctionName(methodName); // 只保留方法名
            // TODO: 需要获取父类信息来解析 super() 调用
            functionCallNode.setSourceModulePath("SUPER_CLASS_TO_BE_RESOLVED");
        } else {
            // 对于无法解析的函数调用，需要判断是否为方法参数的调用
            // 如果是链式调用（包含点号），则很可能是方法参数或未知对象的方法调用
            if (originalFunctionName.contains(".")) {
                functionCallNode.setSourceModulePath("Unknown");
            } else {
                // 可能是局部函数或全局函数
                functionCallNode.setSourceModulePath(methodNode.getModulePath());
            }
            // 函数名保持不变
        }
    }

    /**
     * 智能分割函数调用表达式，正确处理括号内的内容
     * 例如：re.finditer(self._GLOB_PATTERN, pattern) -> ["re", "finditer(self._GLOB_PATTERN, pattern)"]
     */
    private String[] smartSplit(String expression) {
        if (expression == null || expression.isEmpty()) {
            return new String[]{expression};
        }

        java.util.List<String> parts = new java.util.ArrayList<>();
        StringBuilder currentPart = new StringBuilder();
        int parenthesesLevel = 0;
        int bracketLevel = 0;

        for (int i = 0; i < expression.length(); i++) {
            char c = expression.charAt(i);

            if (c == '(') {
                parenthesesLevel++;
                currentPart.append(c);
            } else if (c == ')') {
                parenthesesLevel--;
                currentPart.append(c);
            } else if (c == '[') {
                bracketLevel++;
                currentPart.append(c);
            } else if (c == ']') {
                bracketLevel--;
                currentPart.append(c);
            } else if (c == '.' && parenthesesLevel == 0 && bracketLevel == 0) {
                // 只有在括号和方括号外的点才作为分隔符
                if (currentPart.length() > 0) {
                    parts.add(currentPart.toString());
                    currentPart = new StringBuilder();
                }
            } else {
                currentPart.append(c);
            }
        }

        // 添加最后一部分
        if (currentPart.length() > 0) {
            parts.add(currentPart.toString());
        }

        return parts.toArray(new String[0]);
    }

    /**
     * 检查是否为内置类型
     */
    private boolean isBuiltinType(String typeName) {
        if (typeName == null) {
            return false;
        }
        // Python内置类型列表
        return typeName.equals("int") || typeName.equals("float") || typeName.equals("str") ||
               typeName.equals("bool") || typeName.equals("list") || typeName.equals("tuple") ||
               typeName.equals("dict") || typeName.equals("set") || typeName.equals("bytes") ||
               typeName.equals("bytearray") || typeName.equals("complex") || typeName.equals("frozenset") ||
               typeName.equals("object") || typeName.equals("type");
    }
}

