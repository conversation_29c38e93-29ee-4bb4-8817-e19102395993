package com.sankuai.deepcode.ast.model.java;

import com.sankuai.deepcode.ast.model.base.BaseNode;
import com.sankuai.deepcode.ast.model.base.CodeView;
import com.sankuai.deepcode.ast.model.java.FieldNode;
import com.sankuai.deepcode.ast.model.java.JavaAnnotation;
import com.sankuai.deepcode.ast.model.java.JavaGenerics;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
public class ClassNode extends BaseNode {
    private String moduleName = "";
    private String fileVid = "";
    private String classPath;
    private String className;
    private String inClassName = "";
    private String classType;
    private List<JavaGenerics> generics = new ArrayList<>();
    private JavaExtends superClass = new JavaExtends();
    private List<JavaImplements> interfaces = new ArrayList<>();
    private String access;
    private List<JavaAnnotation> annotations = new ArrayList<>();
    private int changeType = 0;
    private List<Integer> changeLines = new ArrayList<>();
    private int checkType = 0;
    private List<Integer> checkLines = new ArrayList<>();
    private int startLine = -1;
    private int endLine = -1;
    private List<Integer> commentLines = new ArrayList<>();
    private List<String> imports = new ArrayList<>();

    private List<FieldNode> fieldNodes = new ArrayList<>();
    private List<MethodNode> methodNodes = new ArrayList<>();
    private List<ClassNode> inClassNodes = new ArrayList<>();

    private List<CodeView> codeViews = new ArrayList<>();
    private int commitCount = 0;
}
