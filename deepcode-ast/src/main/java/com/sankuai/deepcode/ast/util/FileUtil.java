package com.sankuai.deepcode.ast.util;

import com.sankuai.deepcode.ast.util.LinuxCmdUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.io.filefilter.RegexFileFilter;
import org.apache.commons.io.filefilter.TrueFileFilter;

import java.io.File;
import java.util.*;

@Slf4j
public class FileUtil {

    public static final String SRC_MAIN = "/src/main/java/";

    public static final String TEST_FILE = "/src/test/";

    public static final String SRC_TEST_FILE = "src/test/";

    public static final String GIT = "/.git/";

    public static Map<String, File> getAllFiles(String path) {
        Map<String, File> fileMap = new HashMap<>();
        Collection<File> files = FileUtils.listFiles(new File(path), new RegexFileFilter(".*"), TrueFileFilter.INSTANCE);
        for (File file : files) {
            if (file.getPath().contains(GIT)) {
                continue;
            }
            if (file.getPath().contains(TEST_FILE)) {
                continue;
            }
            String key = file.getPath().split(path + "/")[1];
            fileMap.put(key, file);
        }
        return fileMap;
    }

    public static List<File> getJavaFiles(String path) {
        File file = new File(path);
        Collection<File> files = FileUtils.listFiles(file, new RegexFileFilter(".*\\.java"), TrueFileFilter.INSTANCE);
        List<File> result = new ArrayList<>();
        for (File f : files) {
            if (!f.getPath().contains(TEST_FILE)) {
                result.add(f);
            }
        }
        return result;
    }

    public static void createDir(String dirPath) {
        File file = new File(dirPath);
        if (!file.exists() && !file.isDirectory()) {
            log.info("创建目录：{}", dirPath);
            file.mkdirs();
        }
    }

    public static List<File> getPomXmls(String path) {
        File file = new File(path);
        Collection<File> files = FileUtils.listFiles(file, new RegexFileFilter("pom.xml"), TrueFileFilter.INSTANCE);
        if (CollectionUtils.isEmpty(files)) {
            return null;
        } else {
            return (List<File>) files;
        }
    }


    public static void deleteFile(String filePath, String fileName) {
        List<String> command1 = new ArrayList<>();
        command1.add("rm");
        command1.add("-rf");
        command1.add(fileName);
        LinuxCmdUtil.execCmd(command1, filePath);
    }


    public static String isAllowed(File file) {
        long fileSizeInBytes = file.length();
        if (fileSizeInBytes > 1024 * 1024 * 3) {
            return "单文件超过3M";
        }
        String reason = "不支持的文件类型";
        List<String> extensions = new ArrayList<>();
        extensions.add("java");

        //前端
        extensions.add("html");
        extensions.add("wxml");
        extensions.add("vue");
        extensions.add("js");
        extensions.add("jsx");
        extensions.add("ts");
        extensions.add("tsx");
        extensions.add("css");
        extensions.add("less");
        extensions.add("scss");
        extensions.add("wxss");

        // 添加Python文件扩展名
        extensions.add("py");
        extensions.add("toml");
        extensions.add("env");
        // TODO *.ipynb 【jupyter notebook 的文件解析？？】

        //其他
        extensions.add("xml");
        extensions.add("text");
        extensions.add("txt");
        extensions.add("md");
        extensions.add("conf");
        extensions.add("cfg");
        extensions.add("yaml");
        extensions.add("properties");
        extensions.add("json");

        String fileName = file.getName();
        int lastDotIndex = fileName.lastIndexOf('.');
        if (lastDotIndex == -1 || lastDotIndex == fileName.length() - 1) {
            reason = "文件没有扩展名或扩展名为空";
        }
        String fileExtension = fileName.substring(lastDotIndex + 1).toLowerCase();
        if (extensions.contains(fileExtension)) {
            return null;
        } else {
            return reason;
        }
    }
}
