package com.sankuai.deepcode.ast.model.base;

import lombok.Data;

import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;

@Data
public class CompareRes {
    private String fromCommit;
    private String toCommit;
    private String buildCommit;
    private String uniquePath = "";
    private boolean diff = false;
    private boolean check = false;
    private String gitPath = "";
    private String gitToPath = "";
    private Set<String> filterPath = new HashSet<>();
    private Map<String, GitDiffInfo> gitJavaDiffInfoMap = new HashMap<>();
    private Map<String, GitDiffInfo> gitPythonDiffInfoMap = new HashMap<>();
    private Map<String, GitDiffInfo> gitForeEndDiffInfoMap = new HashMap<>();
    private Map<String, GitDiffInfo> gitNoSourceInfoMap = new HashMap<>();
}
