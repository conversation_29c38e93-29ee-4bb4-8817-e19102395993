package com.sankuai.deepcode.ast.java.visitor;//package com.sankuai.deepcode.ast.java.visitor;
//
//import com.sankuai.deepcode.ast.java.gen.JavaParser;
//import com.sankuai.deepcode.ast.java.gen.JavaParserBaseVisitor;
//import com.sankuai.deepcode.ast.model.java.ClassNode;
//import com.sankuai.deepcode.ast.model.java.FieldNode;
//import com.sankuai.deepcode.ast.model.java.JavaAnnotation;
//import lombok.Getter;
//
//import java.util.ArrayList;
//import java.util.List;
//
//public class MyMemberDeclarationVisitor extends JavaParserBaseVisitor<Object> {
//
//    private ClassNode parentClassNode;
//    private String packageName;
//    private List<JavaAnnotation> annotations = new ArrayList<>();
//    private String access = "";
//
//    public MyMemberDeclarationVisitor(ClassNode myParentClassNode, ClassNode myClassNode, String myPackageName, List<JavaAnnotation> myAnnotations, String myAccess) {
//        parentClassNode = myParentClassNode;
//        classNode = myClassNode;
//        packageName = myPackageName;
//        annotations = myAnnotations;
//        access = myAccess;
//    }
//
//    @Getter
//    private ClassNode classNode;
//
//    @Override
//    public Object visitMemberDeclaration(JavaParser.MemberDeclarationContext ctx) {
//        if (ctx.methodDeclaration() != null) {
//            MyJavaMethodDeclarationVisitor visitor = new MyJavaMethodDeclarationVisitor(classNode);
//            visitor.visit(ctx.methodDeclaration());
//            visitor.getMethodNode().setAccess(access);
//            visitor.getMethodNode().setAnnotations(annotations);
//            classNode.getMethodNodes().add(visitor.getMethodNode());
//        } else if (ctx.genericMethodDeclaration() != null) {
//            MyJavaGenericMethodDeclarationVisitor visitor = new MyJavaGenericMethodDeclarationVisitor(classNode);
//            visitor.visit(ctx.genericMethodDeclaration());
//            visitor.getMethodNode().setAccess(access);
//            visitor.getMethodNode().setAnnotations(annotations);
//            classNode.getMethodNodes().add(visitor.getMethodNode());
//        } else if (ctx.fieldDeclaration() != null) {
//            MyJavaFieldVisitor visitor = new MyJavaFieldVisitor(classNode);
//            visitor.visit(ctx.fieldDeclaration());
//            for (FieldNode fieldNode : visitor.getFieldNodes()) {
//                fieldNode.setAccess(access);
//                fieldNode.setAnnotations(annotations);
//                classNode.getFieldNodes().add(fieldNode);
//            }
//        } else if (ctx.constructorDeclaration() != null) {
//            MyJavaConstructorDeclarationVisitor visitor = new MyJavaConstructorDeclarationVisitor(classNode);
//            visitor.visit(ctx.constructorDeclaration());
//            visitor.getMethodNode().setAccess(access);
//            visitor.getMethodNode().setAnnotations(annotations);
//            classNode.getMethodNodes().add(visitor.getMethodNode());
//        } else if (ctx.genericConstructorDeclaration() != null) {
//            MyJavaGenericConstructorDeclarationVisitor visitor = new MyJavaGenericConstructorDeclarationVisitor(classNode);
//            visitor.visit(ctx.genericConstructorDeclaration());
//            visitor.getMethodNode().setAccess(access);
//            visitor.getMethodNode().setAnnotations(annotations);
//            classNode.getMethodNodes().add(visitor.getMethodNode());
//        } else if (ctx.interfaceDeclaration() != null) {
//            classNode.setAccess(access);
//            classNode.setAnnotations(annotations);
//            if (parentClassNode == null) {
//                MyJavaInterfaceDeclarationVisitor visitor = new MyJavaInterfaceDeclarationVisitor(null, classNode, packageName);
//                visitor.visit(ctx.interfaceDeclaration());
//            } else {
//                MyJavaInterfaceDeclarationVisitor visitor = new MyJavaInterfaceDeclarationVisitor(parentClassNode, classNode, packageName);
//                visitor.visit(ctx.interfaceDeclaration());
//                parentClassNode.getInClassNodes().add(classNode);
//            }
//        } else if (ctx.annotationTypeDeclaration() != null) {
//            classNode.setAccess(access);
//            classNode.setAnnotations(annotations);
//            if (parentClassNode == null) {
//                MyAnnotationTypeDeclarationVisitor visitor = new MyAnnotationTypeDeclarationVisitor(null, classNode, packageName);
//                visitor.visit(ctx.annotationTypeDeclaration());
//            } else {
//                MyAnnotationTypeDeclarationVisitor visitor = new MyAnnotationTypeDeclarationVisitor(parentClassNode, classNode, packageName);
//                visitor.visit(ctx.annotationTypeDeclaration());
//                parentClassNode.getInClassNodes().add(classNode);
//            }
//        } else if (ctx.classDeclaration() != null) {
//            classNode.setAccess(access);
//            classNode.setAnnotations(annotations);
//            if (parentClassNode == null) {
//                MyJavaClassDeclarationVisitor visitor = new MyJavaClassDeclarationVisitor(null, classNode, packageName);
//                visitor.visit(ctx.classDeclaration());
//            } else {
//                MyJavaClassDeclarationVisitor visitor = new MyJavaClassDeclarationVisitor(parentClassNode, classNode, packageName);
//                visitor.visit(ctx.classDeclaration());
//                parentClassNode.getInClassNodes().add(classNode);
//            }
//        } else if (ctx.enumDeclaration() != null) {
//            classNode.setAccess(access);
//            classNode.setAnnotations(annotations);
//            if (parentClassNode == null) {
//                MyJavaEnumDeclarationVisitor visitor = new MyJavaEnumDeclarationVisitor(null, classNode, packageName);
//                visitor.visit(ctx.enumDeclaration());
//            } else {
//                MyJavaEnumDeclarationVisitor visitor = new MyJavaEnumDeclarationVisitor(parentClassNode, classNode, packageName);
//                visitor.visit(ctx.enumDeclaration());
//                parentClassNode.getInClassNodes().add(classNode);
//            }
//        } else {
//            //todo recordDeclaration Java17 暂不处理
//        }
//        return null;
//    }
//
//}
