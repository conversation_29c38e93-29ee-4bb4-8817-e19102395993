package com.sankuai.deepcode.ast.enums;

public enum DiffTypeEnum {
    SAM(1, "same"),

    ADD(2, "add"),

    DEL(3, "delete"),

    CHECK(-1, "check"),

    CHECKDEL(-2, "checkdel"),

    CHECKADD(-3, "checkadd");

    private int code;
    private String msg;


    DiffTypeEnum(int code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public int getCode() {
        return this.code;
    }

    public String getMsg() {
        return this.msg;
    }


    @Override
    public String toString() {
        return "DiffTypeEnum{" +
                "code=" + code +
                ", msg='" + msg + '\'' +
                '}';
    }
}
