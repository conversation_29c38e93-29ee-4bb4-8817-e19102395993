package com.sankuai.deepcode.ast.typescript.visitor;

import com.sankuai.deepcode.ast.model.typescript.ScriptNode;
import com.sankuai.deepcode.ast.model.typescript.ScriptClass;
import com.sankuai.deepcode.ast.model.typescript.ScriptVariable;
import com.sankuai.deepcode.ast.typescript.gen.TypeScriptParser;
import com.sankuai.deepcode.ast.typescript.gen.TypeScriptParserBaseVisitor;

public class MyEnumDeclarationVisitor extends TypeScriptParserBaseVisitor<Object> {

    private ScriptClass scriptClass;

    private ScriptNode scriptNode;

    MyEnumDeclarationVisitor(ScriptNode myScriptNode) {
        scriptClass = new ScriptClass();
        scriptNode = myScriptNode;
        scriptNode.getClasses().add(scriptClass);
    }


    @Override
    public Object visitEnumDeclaration(TypeScriptParser.EnumDeclarationContext ctx) {
        if (ctx.Const() != null) {
            scriptClass.setConst(true);
        }
        scriptClass.setStartLine(ctx.getStart().getLine());
        scriptClass.setEndLine(ctx.getStop().getLine());
        scriptClass.setType("enum");
        scriptClass.setClassName(ctx.identifier().getText());
        visitEnumBody(ctx.enumBody());
        return null;
    }

    @Override
    public Object visitEnumBody(TypeScriptParser.EnumBodyContext ctx) {
        if (ctx.enumMemberList() != null && ctx.enumMemberList().enumMember() != null) {
            for (TypeScriptParser.EnumMemberContext enumMemberContext : ctx.enumMemberList().enumMember()) {
                visitEnumMember(enumMemberContext);
            }
        }
        return null;
    }


    @Override
    public Object visitEnumMember(TypeScriptParser.EnumMemberContext ctx) {
        ScriptVariable scriptVariable = new ScriptVariable();
        scriptVariable.setStartLine(ctx.getStart().getLine());
        scriptVariable.setEndLine(ctx.getStop().getLine());
        scriptVariable.setBody(ctx.getText());
        scriptVariable.setName(ctx.propertyName().getText());
        //todo enum value简单处理
        if (ctx.singleExpression() != null) {
            scriptVariable.setValue(ctx.singleExpression().getText());
        }
        scriptVariable.setStartLine(ctx.getStart().getLine());
        scriptVariable.setEndLine(ctx.getStop().getLine());
        scriptClass.getVariables().add(scriptVariable);
        return null;
    }


}
