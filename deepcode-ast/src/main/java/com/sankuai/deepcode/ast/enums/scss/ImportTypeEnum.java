package com.sankuai.deepcode.ast.enums.scss;

public enum ImportTypeEnum {
    IMPORT("import", "引用"),
    USE("use", "使用"),
    FORWARD("forward", "转发"),
    REQUIRE("require", "引用 js语法");

    private String code;

    private String desc;

    ImportTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return this.code;
    }


    public String getDesc() {
        return desc;
    }

    @Override
    public String toString() {
        return "ErrorCodeEnum{" +
                "code=" + code +
                ", desc='" + desc + '\'' +
                '}';
    }
}
