package com.sankuai.deepcode.ast.model.typescript;

import com.sankuai.deepcode.ast.enums.ChangeTypeEnum;
import com.sankuai.deepcode.ast.model.html.HtmlNode;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
public class ScriptVariable {
    private boolean isExport;
    private boolean isReadOnly;
    private boolean isDecare;
    private int startLine;
    private int endLine;
    private int changeType = ChangeTypeEnum.DEFAULT.getCode();
    private List<Integer> changeLines = new ArrayList<>();
    private String body="";
    private List<Integer> commentLines = new ArrayList<>();
    private List<String> decorators = new ArrayList<>();
    private List<ScriptLiteral> literal = new ArrayList<>();
    private String type;  //array object
    private String filePath;
    private String modifier;
    private String name;
    private String value;
    private boolean questionMark;
    private ScriptGenerics generics;

    private List<ScriptMethod> methods = new ArrayList<>();
    private List<ScriptClass> classes = new ArrayList<>();
    private List<HtmlNode> htmlNodes = new ArrayList<>();
}
