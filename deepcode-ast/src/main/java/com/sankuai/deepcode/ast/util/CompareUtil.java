package com.sankuai.deepcode.ast.util;

import com.sankuai.deepcode.ast.enums.ChangeTypeEnum;
import com.sankuai.deepcode.ast.enums.CheckTypeEnum;
import com.sankuai.deepcode.ast.enums.DiffTypeEnum;
import com.sankuai.deepcode.ast.model.base.CodeView;
import com.sankuai.deepcode.ast.model.base.CompareRes;
import com.sankuai.deepcode.ast.model.base.GitDiffInfo;
import com.sankuai.deepcode.ast.model.base.LcsNode;
import com.sankuai.deepcode.ast.model.code.GitChangeFile;
import com.sankuai.deepcode.ast.model.xml.XmlElement;
import com.sankuai.deepcode.ast.xml.gen.XMLLexer;
import com.sankuai.deepcode.ast.xml.gen.XMLParser;
import com.sankuai.deepcode.ast.xml.visitor.MyXmlVisitor;
import lombok.extern.slf4j.Slf4j;
import org.antlr.v4.runtime.CharStreams;
import org.antlr.v4.runtime.CommonTokenStream;
import org.antlr.v4.runtime.tree.ParseTree;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.*;

import static com.sankuai.deepcode.ast.util.FileUtil.SRC_TEST_FILE;


@Slf4j
public class CompareUtil {

    public static final String WORKSPACE_PATH = "/opt/meituan/code_workspace";


    public static CompareRes getDiffInfos(long uniqueId, String gitUrl, String repos, String fromBranch, String toBranch, String buildBranch, String buildCommit) throws Exception {
        CompareRes compareRes = new CompareRes();
        Set<String> filterPath = new HashSet<>();
        compareRes.setFilterPath(filterPath);
        Map<String, GitDiffInfo> result = new HashMap<>();
        String uniquePath = WORKSPACE_PATH + "/" + uniqueId + "/" + repos + "/analysis_src";
        compareRes.setUniquePath(uniquePath);
        FileUtil.createDir(uniquePath);
        //下载master
        GitUtil.gitCloneByAlias(uniquePath, gitUrl, repos, "_to", 600);
        String lastToCommit = "";
        if (StringUtils.isEmpty(buildCommit)) {
            lastToCommit = GitUtil.gitResetHard(uniquePath, repos + "_to", toBranch);
        } else {
            lastToCommit = GitUtil.gitResetHard(uniquePath, repos + "_to", buildCommit);
        }
        compareRes.setToCommit(lastToCommit);
        List<String> modules = getModules(uniquePath + "/" + repos + "_to");

        boolean diff = false;
        if (!fromBranch.equals(toBranch)) {
            diff = true;
        }
        String lastFromCommit = "";
        String baseCommit = "";
        String lastCommit = "";
        if (diff) {
            compareRes.setDiff(true);
            compareRes.setGitPath(uniquePath + "/" + repos + "_from");
            compareRes.setGitToPath(uniquePath + "/" + repos + "_to");
            //copy
            GitUtil.copyR(uniquePath, repos + "_to", repos + "_from");
            lastFromCommit = GitUtil.gitResetHard(uniquePath, repos + "_from", fromBranch);
            compareRes.setFromCommit(lastFromCommit);
            lastCommit = lastFromCommit;
            baseCommit = GitUtil.gitBaseByCommit(uniquePath, repos + "_from", lastFromCommit, lastToCommit);
        } else {
            compareRes.setGitPath(uniquePath + "/" + repos + "_to");
            lastCommit = lastToCommit;
            compareRes.setFromCommit(lastCommit);
        }
        boolean check = false;
        String lastBuildCommit = "";
        if (!fromBranch.equals(buildBranch)) {
            compareRes.setGitPath(uniquePath + "/" + repos + "_build");
            compareRes.setCheck(true);
            check = true;
            GitUtil.copyR(uniquePath, repos + "_to", repos + "_build");
            lastBuildCommit = GitUtil.gitResetHard(uniquePath, repos + "_build", buildBranch);
            compareRes.setBuildCommit(lastBuildCommit);
        } else {
            compareRes.setBuildCommit(lastCommit);
        }
        Map<String, GitDiffInfo> stringGitDiffInfoMap = getDiffFileInfos(modules, repos, baseCommit, lastCommit, uniquePath, diff, check, filterPath);
        for (Map.Entry<String, GitDiffInfo> entry : stringGitDiffInfoMap.entrySet()) {
            if (entry.getValue().getFileType().equals("java")) {
                compareRes.getGitJavaDiffInfoMap().put(entry.getKey(), entry.getValue());
            } else if (entry.getValue().getFileType().equals("py")) {
                compareRes.getGitPythonDiffInfoMap().put(entry.getKey(), entry.getValue());
            } else if (entry.getValue().getFileType().equals("html")
                    || entry.getValue().getFileType().equals("wxml")
                    || entry.getValue().getFileType().equals("vue")
                    || entry.getValue().getFileType().equals("js")
                    || entry.getValue().getFileType().equals("jsx")
                    || entry.getValue().getFileType().equals("ts")
                    || entry.getValue().getFileType().equals("tsx")
                    || entry.getValue().getFileType().equals("css")
                    || entry.getValue().getFileType().equals("less")
                    || entry.getValue().getFileType().equals("scss")
                    || entry.getValue().getFileType().equals("wxss")) {
                compareRes.getGitForeEndDiffInfoMap().put(entry.getKey(), entry.getValue());
            } else {
                compareRes.getGitNoSourceInfoMap().put(entry.getKey(), entry.getValue());
            }
        }
        return compareRes;
    }

    public static List<String> getModules(String path) {
        List<String> modules = new ArrayList<>();
        List<File> files = FileUtil.getPomXmls(path);
        if (CollectionUtils.isEmpty(files)) {
            return modules;
        }
        if (1 == files.size()) {
            return modules;
        } else {
            for (File f : files) {
                if (f.getPath().equals(path + "/pom.xml")) {
                    modules = getModulesByXml(f);
                }
            }
            return modules;
        }
    }

    public static List<String> getModulesByXml(File file) {
        List<String> modules = new ArrayList<>();
        try {
            String code = new String(Files.readAllBytes(Paths.get(file.getPath())));
            XMLLexer lexer = new XMLLexer(CharStreams.fromString(code));
            CommonTokenStream tokens = new CommonTokenStream(lexer);
            XMLParser parser = new XMLParser(tokens);
            ParseTree tree = parser.document();

            MyXmlVisitor visitor = new MyXmlVisitor();
            visitor.visit(tree);

            XmlElement xmlElement = visitor.getElement();
            if (null != xmlElement) {
                List<XmlElement> children = xmlElement.getChildElement();
                if (CollectionUtils.isNotEmpty(children)) {
                    for (XmlElement child : children) {
                        if ("modules".equals(child.getName())) {
                            for (XmlElement module : child.getChildElement()) {
                                modules.add(module.getContent());
                            }
                        }
                    }
                }
            }
        } catch (Exception e) {
        }
        return modules;
    }

    public static String getModule(String path) {
        String module = null;
        List<File> files = FileUtil.getPomXmls(path);
        if (CollectionUtils.isEmpty(files)) {
            return module;
        }
        if (1 == files.size()) {
            File f = files.get(0);
            module = getArtifactIdByXml(f);
        }
        return module;
    }

    public static String getArtifactIdByXml(File file) {
        String artifactId = "";
        try {
            String code = new String(Files.readAllBytes(Paths.get(file.getPath())));
            XMLLexer lexer = new XMLLexer(CharStreams.fromString(code));
            CommonTokenStream tokens = new CommonTokenStream(lexer);
            XMLParser parser = new XMLParser(tokens);
            ParseTree tree = parser.document();

            MyXmlVisitor visitor = new MyXmlVisitor();
            visitor.visit(tree);

            XmlElement xmlElement = visitor.getElement();
            if (null != xmlElement) {
                List<XmlElement> children = xmlElement.getChildElement();
                if (CollectionUtils.isNotEmpty(children)) {
                    for (XmlElement child : children) {
                        if ("artifactId".equals(child.getName())) {
                            artifactId = child.getContent();
                            break;
                        }
                    }
                }
            }
        } catch (Exception e) {
            return null;
        }
        return artifactId;
    }


    public static Map<String, GitDiffInfo> getDiffFileInfos(List<String> modules, String repos, String baseCommit, String lastCommit, String uniquePath, Boolean diff, Boolean check, Set<String> filterPath) throws Exception {
        Map<String, GitDiffInfo> result = new HashMap<>();
        try {
            Map<String, Integer> pathCountMap = new HashMap<>();
            if (diff) {
                pathCountMap = GitUtil.getCommitCount(uniquePath, repos + "_from");
                if (check) {
                    pathCountMap = GitUtil.getCommitCount(uniquePath, repos + "_build");
                }
            } else {
                pathCountMap = GitUtil.getCommitCount(uniquePath, repos + "_to");
            }
            //master代码和资源文件
            Map<String, File> toFiles = FileUtil.getAllFiles(uniquePath + "/" + repos + "_to");
            if (diff) {
                List<GitChangeFile> gitChangeFiles = getGitChangeFiles(uniquePath, repos + "_from", baseCommit, lastCommit);
                List<GitDiffInfo> add = new ArrayList<>();
                List<GitDiffInfo> change = new ArrayList<>();
                Map<String, File> fromFiles = FileUtil.getAllFiles(uniquePath + "/" + repos + "_from");
                if (CollectionUtils.isNotEmpty(gitChangeFiles)) {
                    for (GitChangeFile gitChangeFile : gitChangeFiles) {
                        fromFiles.remove(gitChangeFile.getBFile());
                        File fromFile = new File(uniquePath + "/" + repos + "_from/" + gitChangeFile.getBFile());
                        if (!fromFile.exists()) {
                            continue;
                        }
                        if (null == toFiles.get(gitChangeFile.getAFile())) {
                            //新增文件
                            GitDiffInfo from = initFileCode(fromFile, ChangeTypeEnum.ADD, modules, repos + "_from", uniquePath, filterPath);
                            if (null != from) {
                                add.add(from);
                                from.setChangeType(ChangeTypeEnum.ADD.getCode());
                                Integer commitCount = pathCountMap.get(from.getPath());
                                if (null != commitCount) {
                                    from.setCommitCount(commitCount);
                                }
                                result.put(from.getPath(), from);

                            }
                        } else {
                            //有变更文件
                            GitDiffInfo from = initFileCode(fromFile, ChangeTypeEnum.DEFAULT, modules, repos + "_from", uniquePath, filterPath);
                            if (null != from) {
                                File toFile = new File(uniquePath + "/" + repos + "_to/" + gitChangeFile.getAFile());
                                GitDiffInfo to = initFileCode(toFile, ChangeTypeEnum.DEFAULT, modules, repos + "_to", uniquePath, filterPath);
                                if (null != to) {
                                    diffFileCode(from, to);
                                    change.add(from);
                                    from.setChangeType(ChangeTypeEnum.CHANGE.getCode());
                                    Integer commitCount = pathCountMap.get(from.getPath());
                                    if (null != commitCount) {
                                        from.setCommitCount(commitCount);
                                    }
                                    result.put(from.getPath(), from);
                                }
                            }
                        }
                    }
                }

//                if (check) {
//                    if (CollectionUtils.isNotEmpty(add) || CollectionUtils.isNotEmpty(change)) {
//                        Map<String, File> buildFiles = FileUtil.getAllFiles(uniquePath + "/" + repos + "_build");
//                        if (CollectionUtils.isNotEmpty(add)) {
//                            for (GitDiffInfo gitDiffInfo : add) {
//                                if (null == buildFiles.get(gitDiffInfo.getPath())) {
//                                    gitDiffInfo.setCheckType(CheckTypeEnum.DELETE.getCode());
//                                } else {
//                                    GitDiffInfo buildFile = initFileCode(buildFiles.get(gitDiffInfo.getPath()), ChangeTypeEnum.DEFAULT, modules, repos + "_build", uniquePath, filterPath);
//                                    if (null != buildFile) {
//                                        checkFileCode(buildFile, gitDiffInfo);
//                                        if (gitDiffInfo.getCheckLines().size() > 0) {
//                                            gitDiffInfo.setCheckType(CheckTypeEnum.CHANGE.getCode());
//                                        }
//
//                                    }
//                                }
//                            }
//                        }
//                        if (CollectionUtils.isNotEmpty(change)) {
//                            for (GitDiffInfo gitDiffInfo : change) {
//                                if (null == buildFiles.get(gitDiffInfo.getPath())) {
//                                    gitDiffInfo.setCheckType(CheckTypeEnum.DELETE.getCode());
//                                } else {
//                                    GitDiffInfo buildFile = initFileCode(buildFiles.get(gitDiffInfo.getPath()), ChangeTypeEnum.DEFAULT, modules, repos + "_build", uniquePath, filterPath);
//                                    if (null != buildFile) {
//                                        checkFileCode(buildFile, gitDiffInfo);
//                                        if (gitDiffInfo.getCheckLines().size() > 0) {
//                                            gitDiffInfo.setCheckType(CheckTypeEnum.CHANGE.getCode());
//                                        }
//                                    }
//                                }
//                            }
//                        }
//                        for (Map.Entry<String, File> entry : buildFiles.entrySet()) {
//                            if (!result.containsKey(entry.getKey())) {
//                                GitDiffInfo build = initFileCode(entry.getValue(), ChangeTypeEnum.DEFAULT, modules, repos + "_build", uniquePath, filterPath);
//                                Integer commitCount = pathCountMap.get(build.getPath());
//                                if (null != commitCount) {
//                                    build.setCommitCount(commitCount);
//                                }
//                                result.put(entry.getKey(), build);
//                            }
//                        }
//                    }
//                }

                if (!fromFiles.isEmpty()) {
                    for (Map.Entry<String, File> entry : fromFiles.entrySet()) {
                        GitDiffInfo from = initFileCode(entry.getValue(), ChangeTypeEnum.DEFAULT, modules, repos + "_from", uniquePath, filterPath);
                        if (null != from) {
                            Integer commitCount = pathCountMap.get(from.getPath());
                            if (null != commitCount) {
                                from.setCommitCount(commitCount);
                            }
                            result.put(entry.getKey(), from);
                        }
                    }
                }
            } else {
                for (Map.Entry<String, File> entry : toFiles.entrySet()) {
                    GitDiffInfo gitDiffInfo = initFileCode(entry.getValue(), ChangeTypeEnum.DEFAULT, modules, repos + "_to", uniquePath, filterPath);
                    Integer commitCount = pathCountMap.get(gitDiffInfo.getPath());
                    if (null != commitCount) {
                        gitDiffInfo.setCommitCount(commitCount);
                    }
                    result.put(entry.getKey(), gitDiffInfo);
                }
            }
        } catch (Exception e) {
            System.out.println("Git exception: " + e);
            throw new Exception("Git getDiffFileInfos exception e:" + e.getMessage());
        }
        return result;
    }

    public static List<GitChangeFile> getGitChangeFiles(String path, String repos, String baseCommit, String lastCommit) {
        List<GitChangeFile> changeFiles = new ArrayList<>();
        List<String> fileInfos = GitUtil.getDiffFileInfos(path, repos, baseCommit, lastCommit);
        if (CollectionUtils.isNotEmpty(fileInfos)) {
            for (String info : fileInfos) {
                if (StringUtils.isEmpty(info)) {
                    continue;
                }
                List<String> showViews = Arrays.asList(info.split("\t"));
                if (showViews.size() < 3) {
                    continue;
                }
                String baseString = showViews.get(2).trim();
                if (baseString.contains(SRC_TEST_FILE)) {
                    continue;
                }
                String aFile = "";
                String bFile = "";
                GitChangeFile gitChangeFile = new GitChangeFile();
                if (baseString.contains("=>")) {
                    aFile = baseString.split("=>")[0].replace("{", "").trim();
                    bFile = baseString.split("\\{")[0] + baseString.split("=>")[1].split("}")[0].trim();
                    gitChangeFile.setAFile(aFile);
                    gitChangeFile.setBFile(bFile);
                    changeFiles.add(gitChangeFile);
                } else {
                    aFile = baseString;
                    gitChangeFile.setAFile(aFile);
                    gitChangeFile.setBFile(aFile);
                    changeFiles.add(gitChangeFile);
                }
            }
        }
        return changeFiles;
    }


    public static GitDiffInfo initFileCode(File file, ChangeTypeEnum changeTypeEnum, List<String> moduleNames, String repos, String uniquePath, Set<String> filterPath) throws Exception {
        GitDiffInfo gitDiffInfo = new GitDiffInfo();
        if (!file.exists()) {
            return null;
        }
        String path = file.getPath().split(uniquePath + "/" + repos + "/")[1];
        gitDiffInfo.setPath(path);
        gitDiffInfo.setFileName(file.getName());
        if (file.getName().contains(".")) {
            gitDiffInfo.setFileType(file.getName().split("\\.")[1]);
        } else {
            gitDiffInfo.setFileType("unknown");
        }
        if (CollectionUtils.isNotEmpty(moduleNames)) {
            String firstName = path.split("/")[0];
            if (moduleNames.contains(firstName)) {
                gitDiffInfo.setModuleName(firstName);
            }
            if (StringUtils.isEmpty(gitDiffInfo.getModuleName()) && !firstName.equals(file.getName())) {
                String moduleName = getModule(uniquePath + "/" + repos + "/" + firstName);
                if (StringUtils.isNotEmpty(moduleName)) {
                    gitDiffInfo.setModuleName(moduleName);
                }
            }
        }
        try {
            String reason = FileUtil.isAllowed(file);
            gitDiffInfo.setChangeType(changeTypeEnum.getCode());
            List<CodeView> codeViews = new ArrayList<>();
            if (null == reason) {
                String code = new String(Files.readAllBytes(Paths.get(file.getPath())));
                //todo  这里有很多文件存储过滤逻辑
                codeViews = initFileCodeView(code);
                if (codeViews.size() > 10000) {
                    codeViews.clear();
                    CodeView codeView = new CodeView();
                    codeView.setId(0);
                    codeView.setLine(1);
                    codeView.setView("单文件行数超过1w行,默认过滤不解析");
                    codeViews.add(codeView);
                    filterPath.add(path);
                } else {
                    int lineNum = 0;
                    //todo 过滤thrift自动生成文件   protocol自动生成文件
                    boolean isThriftAuto = false;
                    boolean isProtocol = false;
                    for (CodeView codeView : codeViews) {
                        if (codeView.getView().contains("Autogenerated by Thrift Compiler")) {
                            isThriftAuto = true;
                            break;
                        }
                        if (codeView.getView().contains("Generated by the protocol buffer compiler")) {
                            isProtocol = true;
                            break;
                        }
                        if (ChangeTypeEnum.ADD == changeTypeEnum) {
                            codeView.setType(DiffTypeEnum.ADD.getCode());
                        }
                        lineNum++;
                        if (ChangeTypeEnum.ADD == changeTypeEnum) {
                            gitDiffInfo.getChangeLines().add(lineNum);
                        }
                    }
                    if (isThriftAuto) {
                        codeViews.clear();
                        CodeView codeView = new CodeView();
                        codeView.setId(0);
                        codeView.setLine(1);
                        codeView.setView("thrift自动生成文件,默认过滤不解析");
                        codeViews.add(codeView);
                        filterPath.add(path);
                    }
                    if (isProtocol) {
                        codeViews.clear();
                        CodeView codeView = new CodeView();
                        codeView.setId(0);
                        codeView.setLine(1);
                        codeView.setView("protocol自动生成文件,默认过滤不解析");
                        codeViews.add(codeView);
                        filterPath.add(path);
                    }
                }
            } else {
                CodeView codeView = new CodeView();
                codeView.setId(0);
                codeView.setLine(1);
                codeView.setView(reason);
                codeViews.add(codeView);
                filterPath.add(path);
            }
            gitDiffInfo.setCodeViews(codeViews);
        } catch (Exception e) {
            System.out.println("file:" + file + "解析异常e:" + e);
            throw new Exception("解析异常e:" + e.getMessage());
        }
        return gitDiffInfo;
    }

    public static List<CodeView> initFileCodeView(String code) throws IOException {
        List<String> rows = Arrays.asList(code.split("\\r?\\n|\\n?\\r"));
        List<CodeView> codeViews = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(rows)) {
            int line = 1;
            boolean longView = false;
            for (String row : rows) {
                if (row.length() > 8192) {
                    longView = true;
                    break;
                }
                CodeView codeView = new CodeView();
                codeView.setId(line);
                codeView.setLine(line);
                codeView.setView(row);
                line++;
                codeViews.add(codeView);
            }
            if (longView) {
                CodeView codeView = new CodeView();
                codeView.setId(0);
                codeView.setLine(1);
                codeView.setView("文件内存在单行数超过8192字符默认过滤不解析");
                codeViews.add(codeView);
                return codeViews;
            }
        }
        return codeViews;
    }


    public static void diffFileCode(GitDiffInfo from, GitDiffInfo to) {
        if (CollectionUtils.isEmpty(to.getCodeViews())) {
            to.setCodeViews(new ArrayList<>());
        }
        if (CollectionUtils.isEmpty(from.getCodeViews())) {
            from.setCodeViews(new ArrayList<>());
        }
        List<LcsNode> list = LcsUtil.LCS(to.getCodeViews(), from.getCodeViews());
        List<CodeView> codeViews = new ArrayList<>();
        List<Integer> changeLines = new ArrayList<>();
        int id = 1;
        for (LcsNode lcsNode : list) {
            if (CollectionUtils.isNotEmpty(lcsNode.getValue())) {
                for (CodeView codeView : lcsNode.getValue()) {
                    //copy一个新的出来
                    CodeView codeViewTmp = DeepCopy.deepCopy(codeView, CodeView.class);
                    codeViewTmp.setId(id);
                    id++;
                    codeViewTmp.setType(lcsNode.getType().getCode());
                    codeViews.add(codeViewTmp);
                    if (lcsNode.getType() == DiffTypeEnum.ADD) {
                        changeLines.add(codeView.getLine());
                    }
                }
            }
        }
        from.setChangeLines(changeLines);
        from.setCodeViews(codeViews);
    }

//    public static void checkFileCode(GitDiffInfo build, GitDiffInfo from) {
//        if (CollectionUtils.isEmpty(build.getCodeViews())) {
//            build.setCodeViews(new ArrayList<>());
//        }
//        if (CollectionUtils.isEmpty(from.getCodeViews())) {
//            from.setCodeViews(new ArrayList<>());
//        }
//        Set<Integer> fromAdd = new HashSet<>();
//        Set<Integer> fromDel = new HashSet<>();
//        int line = 1;
//        for (CodeView f : from.getCodeViews()) {
//            if (f.getType() == DiffTypeEnum.ADD.getCode()) {
//                fromAdd.add(line);
//            }
//            if (f.getType() == DiffTypeEnum.DEL.getCode()) {
//                fromDel.add(line);
//            }
//            line++;
//        }
//        List<LcsNode> list = LcsUtil.LCS(from.getCodeViews(), build.getCodeViews());
//        List<CodeView> codeViews = new ArrayList<>();
//        List<Integer> checkLines = new ArrayList<>();
//        List<Integer> changeLines = new ArrayList<>();
//        int id = 1;
//        for (LcsNode lcsNode : list) {
//            if (CollectionUtils.isNotEmpty(lcsNode.getValue())) {
//                for (CodeView codeView : lcsNode.getValue()) {
//                    //copy一个新的出来
//                    CodeView codeViewTmp = DeepCopy.deepCopy(codeView, CodeView.class);
//                    codeViewTmp.setId(id);
//                    id++;
//                    codeViewTmp.setType(lcsNode.getType().getCode());
//                    codeViews.add(codeViewTmp);
//                    if (lcsNode.getType() == DiffTypeEnum.ADD) {
//                        checkLines.add(codeView.getLine());
//                    }
//                }
//            }
//        }
//        int oldLine = 1;
//        int sourceline = 1;
//        for (CodeView f : codeViews) {
//            if (fromAdd.contains(oldLine)) {
//                if (f.getType() == DiffTypeEnum.ADD.getCode()) {
//                    f.setType(DiffTypeEnum.CHECKADD.getCode());
//                    sourceline++;
//                } else if (f.getType() == DiffTypeEnum.DEL.getCode()) {
//                    f.setType(DiffTypeEnum.CHECKDEL.getCode());
//                    oldLine++;
//                } else if (f.getType() == DiffTypeEnum.SAM.getCode()) {
//                    f.setType(DiffTypeEnum.ADD.getCode());
//                    changeLines.add(sourceline);
//                    oldLine++;
//                    sourceline++;
//                }
//            } else if (fromDel.contains(oldLine)) {
//                if (f.getType() == DiffTypeEnum.ADD.getCode()) {
//                    f.setType(DiffTypeEnum.ADD.getCode());
//                    sourceline++;
//                } else if (f.getType() == DiffTypeEnum.DEL.getCode()) {
//                    f.setType(DiffTypeEnum.DEL.getCode());
//                    oldLine++;
//                } else if (f.getType() == DiffTypeEnum.SAM.getCode()) {
//                    f.setType(DiffTypeEnum.SAM.getCode());
//                    oldLine++;
//                    sourceline++;
//                }
//            } else {
//                if (f.getType() == DiffTypeEnum.ADD.getCode()) {
//                    f.setType(DiffTypeEnum.CHECKADD.getCode());
//                    sourceline++;
//                } else if (f.getType() == DiffTypeEnum.DEL.getCode()) {
//                    f.setType(DiffTypeEnum.CHECKDEL.getCode());
//                    oldLine++;
//                } else {
//                    oldLine++;
//                    sourceline++;
//                }
//            }
//        }
//        from.setCheckLines(checkLines);
//        from.setChangeLines(changeLines);
//        from.setCodeViews(codeViews);
//    }

}
