package com.sankuai.deepcode.ast.util;

import com.sankuai.deepcode.ast.model.html.HtmlNode;
import com.sankuai.deepcode.ast.model.java.ClassNode;
import com.sankuai.deepcode.ast.model.java.FieldNode;
import com.sankuai.deepcode.ast.model.java.FileNode;
import com.sankuai.deepcode.ast.model.java.MethodNode;
import com.sankuai.deepcode.ast.model.scss.ScssNode;
import com.sankuai.deepcode.ast.model.scss.ScssRule;
import com.sankuai.deepcode.ast.model.scss.ScssSelector;
import com.sankuai.deepcode.ast.model.typescript.ScriptMethod;
import com.sankuai.deepcode.ast.model.typescript.ScriptNode;
import com.sankuai.deepcode.ast.model.typescript.ScriptVariable;
import org.apache.commons.codec.digest.DigestUtils;

public class Md5Util {
    public static String stringToMd5(String plainText) {
        return DigestUtils.md5Hex(plainText);
    }

    public static String classNameToMd5(String className, String inClassName) {
        return DigestUtils.md5Hex(className + "$" + inClassName);
    }

    public static String classNodeToMd5(ClassNode classNode) {
        return DigestUtils.md5Hex(classNode.getClassName() + "$" + classNode.getInClassName());
    }

    public static String fieldNodeToMd5(FieldNode fieldNode) {
        return DigestUtils.md5Hex(fieldNode.getClassName() + "$" + fieldNode.getInClassName() + "." + fieldNode.getFieldName());
    }

    public static String methodNodeToMd5(MethodNode methodNode, boolean invoke) {
        if (invoke) {
            methodNode.setVidByNum(methodNodeToMd5ByNum(methodNode));
            methodNode.setVidByName(methodNodeToMd5ByName(methodNode));
        } else {
            if (methodNode.isOverloading()) {
                methodNode.setVidByNum(methodNodeToMd5ByNum(methodNode));
            } else {
                methodNode.setVidByName(methodNodeToMd5ByName(methodNode));
            }
        }
        return DigestUtils.md5Hex(methodNode.getClassName() + "$" + methodNode.getInClassName() + "."
                + methodNode.getMethodName() + "(" + methodNode.getParamTypeStr() + ")");
    }

    public static String methodNodeToMd5ByName(String className, String inClassName, String methodName) {
        return DigestUtils.md5Hex(className + "$" + inClassName + "."
                + methodName);
    }

    public static String methodNodeToMd5ByName(MethodNode methodNode) {
        return DigestUtils.md5Hex(methodNode.getClassName() + "$" + methodNode.getInClassName() + "."
                + methodNode.getMethodName());
    }

    public static String methodNodeToMd5ByNum(MethodNode methodNode) {
        return DigestUtils.md5Hex(methodNode.getClassName() + "$" + methodNode.getInClassName() + "."
                + methodNode.getMethodName() + "(" + methodNode.getParams().size() + ")");
    }

    public static String methodNodeToMd5ByNum(String className, String inClassName, String methodName, int paramSize) {
        return DigestUtils.md5Hex(className + "$" + inClassName + "."
                + methodName + "(" + paramSize + ")");
    }


    public static String fieldBodyToMd5(FieldNode fieldNode) {
        return DigestUtils.md5Hex(fieldNode.getBody());
    }

    public static String methodBodyToMd5(MethodNode methodNode) {
        return DigestUtils.md5Hex(methodNode.getAnnotations() + methodNode.getAccess() + methodNode.getBody());
    }

    public static String fileNodeToMd5(FileNode fileNode) {
        return DigestUtils.md5Hex(fileNode.getPath());
    }


    public static String filePathToMd5(String filePath) {
        return DigestUtils.md5Hex(filePath);
    }

    public static String htmlNodeToMd5(HtmlNode htmlNode) {
        return DigestUtils.md5Hex(htmlNode.getFilePath() + htmlNode.getTagDepth() + htmlNode.getTagIndex() + htmlNode.getTagName());
    }

    public static String scssRuleToMd5(ScssRule scssRule, String filePath) {
        scssRule.initPathName();
        return DigestUtils.md5Hex(filePath + scssRule.getPathName());
    }

    public static String scriptMethodToMd5(ScriptMethod scriptMethod, String filePath) {
        return DigestUtils.md5Hex(filePath + scriptMethod.getMethodName());
    }

    public static String scriptMethodNameToMd5(String methodName, String filePath) {
        return DigestUtils.md5Hex(filePath + methodName);
    }


    public static String scriptVariablesToMd5(ScriptVariable scriptVariable, String filePath) {
        return DigestUtils.md5Hex(filePath + scriptVariable.getName());
    }

    public static String bodyToMd5(String body) {
        return DigestUtils.md5Hex(body);
    }
}
