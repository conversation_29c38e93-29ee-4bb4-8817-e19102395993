package com.sankuai.deepcode.ast.python3.visitor;

import com.sankuai.deepcode.ast.model.base.LocationInfo;
import com.sankuai.deepcode.ast.model.python3.PythonDecoratorFunc;
import com.sankuai.deepcode.ast.python3.gen.PythonParser;
import com.sankuai.deepcode.ast.python3.gen.PythonParserBaseVisitor;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.antlr.v4.runtime.tree.TerminalNode;

import java.util.ArrayList;
import java.util.List;

/**
 * Package: com.sankuai.deepcode.ast.python3.visitor
 * Description:
 *
 * <AUTHOR>
 * @since 2024/12/6 15:54
 */
@Setter
@Getter
@ToString
public class PythonDecoratorVisitor extends PythonParserBaseVisitor<Object> {
    private List<PythonDecoratorFunc> decoratorFuncList = new ArrayList<>();
    private final String fileName;

    public PythonDecoratorVisitor(String fileName) {
        this.fileName = fileName;
    }

    @Override
    public Object visitDecorators(PythonParser.DecoratorsContext ctx) {
        int decoratorCount = ctx.named_expression().size();
        for (int i = 0; i < decoratorCount; i++) {
            PythonParser.Named_expressionContext namedExprCtx = ctx.named_expression(i);
            PythonDecoratorFunc decoratorFunc = new PythonDecoratorFunc();
            visitNamedExpression(namedExprCtx, decoratorFunc);
            decoratorFunc.setLevel(decoratorCount - i); // 设置装饰器层级
            decoratorFuncList.add(decoratorFunc);
        }
        return null;
    }

    private void visitNamedExpression(PythonParser.Named_expressionContext ctx, PythonDecoratorFunc decoratorFunc) {
        if (ctx.expression() != null) {
            if (ctx.expression().function_call() != null) {
                visitFunctionCall(ctx.expression().function_call(), decoratorFunc);
            } else {
                // 处理简单名称形式的装饰器
                decoratorFunc.setFunctionName(ctx.getText());
            }
        }
        setLocationInfo(ctx, decoratorFunc);
    }

    private void visitFunctionCall(PythonParser.Function_callContext ctx, PythonDecoratorFunc decoratorFunc) {
        StringBuilder fullCallChain = new StringBuilder();

        // 新的function_call规则只包含primary_call_chain
        if (ctx.primary_call_chain() != null) {
            PythonParser.Primary_call_chainContext chainCtx = ctx.primary_call_chain();

            // 处理atom部分
            if (chainCtx.atom() != null) {
                String atomText = chainCtx.atom().getText();
                decoratorFunc.setFunctionName(atomText);
                fullCallChain.append(atomText);
            }

            // 处理call_suffix部分
            List<PythonParser.Call_suffixContext> callSuffixes = chainCtx.call_suffix();
            for (PythonParser.Call_suffixContext suffix : callSuffixes) {
                if (suffix.DOT() != null && suffix.NAME() != null) {
                    // 属性访问后的函数调用
                    String methodName = suffix.NAME().getText();
                    fullCallChain.append(".").append(methodName).append("(");

                    if (suffix.arguments() != null) {
                        visitArguments(suffix.arguments(), decoratorFunc);
                        fullCallChain.append(decoratorFunc.getOriginCallArgs());
                    }
                    fullCallChain.append(")");

                    // 更新函数名为完整的调用链
                    decoratorFunc.setFunctionName(decoratorFunc.getFunctionName() + "." + methodName);
                } else if (suffix.LPAR() != null) {
                    // 简单的函数调用
                    fullCallChain.append("(");
                    if (suffix.arguments() != null) {
                        visitArguments(suffix.arguments(), decoratorFunc);
                        fullCallChain.append(decoratorFunc.getOriginCallArgs());
                    }
                    fullCallChain.append(")");
                }
            }
        }

        decoratorFunc.setOriginCallArgs(fullCallChain.toString());
    }

    private String getPrimaryName(PythonParser.PrimaryContext ctx) {
        StringBuilder name = new StringBuilder();
        if (ctx.atom() != null) {
            name.append(ctx.atom().getText());
        }
        for (int i = 0; i < ctx.getChildCount(); i++) {
            if (ctx.getChild(i) instanceof TerminalNode) {
                String text = ctx.getChild(i).getText();
                if (".".equals(text) && i + 1 < ctx.getChildCount() && ctx.getChild(i + 1) instanceof TerminalNode) {
                    name.append(".").append(ctx.getChild(i + 1).getText());
                    // Skip the next child as we've already appended it
                    i++;
                }
            }
        }
        return name.toString();
    }

    private void visitArguments(PythonParser.ArgumentsContext ctx, PythonDecoratorFunc decoratorFunc) {
        if (ctx.args() != null) {
            List<String> args = new ArrayList<>();
            StringBuilder argsString = new StringBuilder();

            for (PythonParser.Starred_expressionContext starredExpr : ctx.args().starred_expression()) {
                args.add("*" + starredExpr.expression().getText());
                argsString.append("*").append(starredExpr.expression().getText()).append(", ");
            }
            for (PythonParser.ExpressionContext expr : ctx.args().expression()) {
                args.add(expr.getText());
                argsString.append(expr.getText()).append(", ");
            }
            for (PythonParser.Assignment_expressionContext assignExpr : ctx.args().assignment_expression()) {
                args.add(assignExpr.getText());
                argsString.append(assignExpr.getText()).append(", ");
            }
            if (ctx.args().kwargs() != null) {
                processKwargs(ctx.args().kwargs(), args, argsString);
            }

            // Remove trailing comma and space
            if (argsString.length() > 2) {
                argsString.setLength(argsString.length() - 2);
            }

            decoratorFunc.setArguments(args);
            decoratorFunc.setOriginCallArgs(argsString.toString());
        }
    }

    private void processKwargs(PythonParser.KwargsContext ctx, List<String> args, StringBuilder argsString) {
        for (PythonParser.Kwarg_or_starredContext kwargCtx : ctx.kwarg_or_starred()) {
            if (kwargCtx.NAME() != null) {
                String arg = kwargCtx.NAME().getText() + "=" + kwargCtx.expression().getText();
                args.add(arg);
                argsString.append(arg).append(", ");
            } else if (kwargCtx.starred_expression() != null) {
                String arg = "*" + kwargCtx.starred_expression().expression().getText();
                args.add(arg);
                argsString.append(arg).append(", ");
            }
        }
        for (PythonParser.Kwarg_or_double_starredContext kwargCtx : ctx.kwarg_or_double_starred()) {
            if (kwargCtx.NAME() != null) {
                String arg = kwargCtx.NAME().getText() + "=" + kwargCtx.expression().getText();
                args.add(arg);
                argsString.append(arg).append(", ");
            } else {
                String arg = "**" + kwargCtx.expression().getText();
                args.add(arg);
                argsString.append(arg).append(", ");
            }
        }
    }


    private void setLocationInfo(PythonParser.Named_expressionContext ctx, PythonDecoratorFunc decoratorFunc) {
        LocationInfo location = new LocationInfo();
        location.setLine(ctx.getStart().getLine());
        location.setColumn(ctx.getStart().getCharPositionInLine());
        decoratorFunc.setStart(location);
    }
}
