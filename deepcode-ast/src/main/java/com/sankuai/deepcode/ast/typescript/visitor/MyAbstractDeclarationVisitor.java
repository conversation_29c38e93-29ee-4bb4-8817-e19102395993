package com.sankuai.deepcode.ast.typescript.visitor;

import com.sankuai.deepcode.ast.model.typescript.ScriptNode;
import com.sankuai.deepcode.ast.model.typescript.ScriptClass;
import com.sankuai.deepcode.ast.model.typescript.ScriptMethod;
import com.sankuai.deepcode.ast.typescript.gen.TypeScriptParser;
import com.sankuai.deepcode.ast.typescript.gen.TypeScriptParserBaseVisitor;
import lombok.Getter;

public class MyAbstractDeclarationVisitor extends TypeScriptParserBaseVisitor<Object> {

    @Getter
    private ScriptClass scriptClass;
    @Getter
    private ScriptMethod scriptMethod;

    private ScriptNode scriptNode;

    MyAbstractDeclarationVisitor(ScriptNode myScriptNode) {
        if (myScriptNode == null) {
            scriptNode = new ScriptNode();
        } else {
            scriptNode = myScriptNode;
        }
    }

    @Override
    public Object visitAbstractDeclaration(TypeScriptParser.AbstractDeclarationContext ctx) {
        //todo 简单处理abstract
        if (ctx.identifier() != null) {
            scriptMethod = new ScriptMethod();
            scriptMethod.setAbstract(true);
            scriptNode.getMethods().add(scriptMethod);
            scriptMethod.setStartLine(ctx.getStart().getLine());
            scriptMethod.setEndLine(ctx.getStop().getLine());
            scriptMethod.setBody(ctx.getText());
            scriptMethod.setMethodName(ctx.identifier().getText());
        } else {
            scriptClass = new ScriptClass();
            scriptNode.getClasses().add(scriptClass);
            scriptClass.setStartLine(ctx.getStart().getLine());
            scriptClass.setEndLine(ctx.getStop().getLine());
            scriptClass.setType("abstract");

            com.sankuai.deepcode.ast.typescript.visitor.MyVariableStatementVisitor myVariableStatementVisitor = new MyVariableStatementVisitor(scriptNode);
            myVariableStatementVisitor.visit(ctx.variableStatement());
            //todo 抽象类内方法未处理
//            myVariableStatementVisitor.getScriptVariable();

        }
        return null;
    }


}
