package com.sankuai.deepcode.ast.enums;


public enum NodeSourceEnum {

    LOCAL("local", "本地"),

    UNKNOWN("unknown", "未知");

    private String code;

    private String desc;

    NodeSourceEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return this.code;
    }


    public String getDesc() {
        return desc;
    }

    @Override
    public String toString() {
        return "NodeSourceEnum{" +
                "code=" + code +
                ", desc='" + desc + '\'' +
                '}';
    }
}