package com.sankuai.deepcode.ast.model.base;

import com.sankuai.deepcode.ast.enums.DiffTypeEnum;
import lombok.Data;

import java.util.Collections;
import java.util.Comparator;
import java.util.List;

@Data
public class CodeView {
    private int id;
    private int line;
    private int type = DiffTypeEnum.SAM.getCode();
    private String view;

    public static void sortCodeViews(List<CodeView> codeViews, List<Integer> addLines) {
        Collections.sort(codeViews, new Comparator<CodeView>() {
            @Override
            public int compare(CodeView cv1, CodeView cv2) {
                if (cv1.getLine() != cv2.getLine()) {
                    return Integer.compare(cv1.getLine(), cv2.getLine());
                } else {
                    return Integer.compare(cv2.getType(), cv1.getType());
                }
            }
        });

        int idCounter = 1;
        for (CodeView codeView : codeViews) {
            if (codeView.getType() == DiffTypeEnum.SAM.getCode() && addLines.contains(codeView.getLine())) {
                codeView.setType(DiffTypeEnum.ADD.getCode());
            }
            codeView.setId(idCounter++);
        }
    }
}
