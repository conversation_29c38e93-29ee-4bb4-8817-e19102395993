package com.sankuai.deepcode.ast.typescript.visitor;

import com.sankuai.deepcode.ast.html.gen.HTMLLexer;
import com.sankuai.deepcode.ast.html.gen.HTMLParser;
import com.sankuai.deepcode.ast.html.visitor.MyHtmlVisitor;
import com.sankuai.deepcode.ast.model.typescript.ScriptNode;
import com.sankuai.deepcode.ast.typescript.gen.TypeScriptParser;
import org.antlr.v4.runtime.CharStreams;
import org.antlr.v4.runtime.CommonTokenStream;
import org.antlr.v4.runtime.tree.ParseTree;
import org.apache.commons.collections4.CollectionUtils;

public class MyJsxElementsVisitor {

    public void visitJsxElement(TypeScriptParser.JsxElementsContext ctx, ScriptNode scriptNode) {
        int startLine = ctx.getStart().getLine();
        String code = ctx.getText();
        for (int i = 1; i < startLine - 1; i++) {
            code = "\n" + code;
        }

        HTMLLexer lexer = new HTMLLexer(CharStreams.fromString(code));
        CommonTokenStream tokens = new CommonTokenStream(lexer);
        HTMLParser parser = new HTMLParser(tokens);
        ParseTree tree = parser.htmlDocument();

        MyHtmlVisitor visitor = new MyHtmlVisitor();
        visitor.visit(tree);

        if (CollectionUtils.isNotEmpty(visitor.getHtmlNodes())) {
            scriptNode.getHtmlNodes().addAll(visitor.getHtmlNodes());
        }

    }
}
