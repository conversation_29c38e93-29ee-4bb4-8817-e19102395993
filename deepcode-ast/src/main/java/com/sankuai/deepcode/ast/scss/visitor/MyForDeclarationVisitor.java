package com.sankuai.deepcode.ast.scss.visitor;

import com.sankuai.deepcode.ast.model.scss.ScssVariable;
import com.sankuai.deepcode.ast.scss.gen.ScssParser;
import com.sankuai.deepcode.ast.scss.gen.ScssParserBaseVisitor;
import lombok.Getter;

public class MyForDeclarationVisitor extends ScssParserBaseVisitor<Object> {

    @Getter
    private ScssVariable scssVariable;


    @Override
    public Object visitForDeclaration(ScssParser.ForDeclarationContext ctx) {
        //todo 条件控制
//        ifDeclaration
//        : AtIf expression block elseIfStatement* elseStatement?
//        ;
        return null;
    }


}
