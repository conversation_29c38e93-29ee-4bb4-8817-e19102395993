package com.sankuai.deepcode.ast.model.typescript;

import com.sankuai.deepcode.ast.model.html.HtmlNode;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
public class ScriptNode {
    private int startLine;
    private int endLine;
    private List<ScriptImport> imports = new ArrayList<>();
    private List<ScriptDeclareModule> declareModules = new ArrayList<>();
    private List<ScriptMethod> methods = new ArrayList<>();
    private List<ScriptClass> classes = new ArrayList<>();
    private List<ScriptVariable> variables = new ArrayList<>();
    private List<ScriptNameSpace> namespaces = new ArrayList<>();
    private List<ScriptTypeAlias> typeAliases = new ArrayList<>();
    private List<ScriptMethod> invokeMethods = new ArrayList<>();
    private List<HtmlNode> htmlNodes = new ArrayList<>();
    private List<Integer> commentLines = new ArrayList<>();
    private boolean isExport = false;
    private String filePath;


    public ScriptNode deepCopy() {
        ScriptNode scriptNode = new ScriptNode();
        scriptNode.setStartLine(this.startLine);
        scriptNode.setEndLine(this.endLine);
        scriptNode.setImports(this.imports);
        scriptNode.setDeclareModules(this.declareModules);
        scriptNode.setMethods(this.methods);
        scriptNode.setClasses(this.classes);
        scriptNode.setVariables(this.variables);
        scriptNode.setNamespaces(this.namespaces);
        scriptNode.setTypeAliases(this.typeAliases);
        scriptNode.setInvokeMethods(this.invokeMethods);
        scriptNode.setHtmlNodes(this.htmlNodes);
        scriptNode.setCommentLines(this.commentLines);
        scriptNode.setFilePath(this.filePath);
        scriptNode.setExport(this.isExport);
        return scriptNode;
    }
}
