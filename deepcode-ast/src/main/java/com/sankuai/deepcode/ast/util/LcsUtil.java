package com.sankuai.deepcode.ast.util;


import com.sankuai.deepcode.ast.enums.DiffTypeEnum;
import com.sankuai.deepcode.ast.model.base.CodeView;
import com.sankuai.deepcode.ast.model.base.LcsNode;
import org.apache.commons.collections4.CollectionUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

public class LcsUtil {

    public static List<LcsNode> LCS(List<CodeView> to, List<CodeView> from) {
        List<LcsNode> res = new ArrayList<>();
        int limit = 5000;
        int max = to.size() > from.size() ? to.size() : from.size();
        if (max > 1) {
            for (int i = 0; i < max; i += limit) {
                List<CodeView> tmpTo = new ArrayList<>();
                if (to.size() > i) {
                    if (i + limit <= to.size()) {
                        tmpTo = to.subList(i, i + limit);
                    } else {
                        tmpTo = to.subList(i, to.size());
                    }
                }

                List<CodeView> tmpFrom = new ArrayList<>();
                if (from.size() > i) {
                    if (i + limit <= from.size()) {
                        tmpFrom = from.subList(i, i + limit);
                    } else {
                        tmpFrom = from.subList(i, from.size());
                    }
                }
                List<LcsNode> lcsNodes = LCS(tmpTo, tmpFrom, 0);
                res.addAll(lcsNodes);
            }
        }
        return res;
    }

    public static List<LcsNode> LCS(List<CodeView> to, List<CodeView> from, int strSt) {
        if (CollectionUtils.isEmpty(to) && CollectionUtils.isEmpty(from)) {
            return new ArrayList<>();
        } else if (CollectionUtils.isEmpty(to)) {
            return new ArrayList<>(Collections.singletonList(new LcsNode(strSt, from.size(), DiffTypeEnum.ADD, from)));
        } else if (CollectionUtils.isEmpty(from)) {
            return new ArrayList<>(Collections.singletonList(new LcsNode(strSt, to.size(), DiffTypeEnum.DEL, to)));
        }

        int[][] dp = new int[to.size() + 1][from.size() + 1];
        int maxi, maxj, maxk;
        maxi = maxj = maxk = 0;
        for (int i = 1; i <= to.size(); i++) {
            for (int j = 1; j <= from.size(); j++) {
                if (to.get(i - 1).getView().equals(from.get(j - 1).getView())) {
                    dp[i][j] = dp[i - 1][j - 1] + 1;
                    if (dp[i][j] >= maxk) {
                        maxk = dp[i][j];
                        maxi = i;
                        maxj = j;
                    }
                }
            }
        }

        List<LcsNode> list = new ArrayList<>();
        if (maxk == 0) {
            list.add(new LcsNode(strSt, to.size(), DiffTypeEnum.DEL, to));
            list.add(new LcsNode(strSt, from.size(), DiffTypeEnum.ADD, from));
        } else {
            list.addAll(LCS(to.subList(0, maxi - maxk), from.subList(0, maxj - maxk), strSt));
            list.add(new LcsNode(maxj - maxk, maxk, DiffTypeEnum.SAM, from.subList(maxj - maxk, maxj - maxk + maxk)));
            list.addAll(LCS(to.subList(maxi, to.size()), from.subList(maxj, from.size()), maxj));
        }
        return list;
    }
}
