package com.sankuai.deepcode.ast.typescript.visitor;

import com.sankuai.deepcode.ast.model.typescript.ScriptNode;
import com.sankuai.deepcode.ast.model.typescript.ScriptMethod;
import com.sankuai.deepcode.ast.typescript.gen.TypeScriptParser;
import com.sankuai.deepcode.ast.typescript.gen.TypeScriptParserBaseVisitor;
import lombok.Getter;

public class MyGeneratorFunctionDeclarationVisitor extends TypeScriptParserBaseVisitor<Object> {

    @Getter
    private ScriptMethod scriptMethod;

    private ScriptNode scriptNode;

    MyGeneratorFunctionDeclarationVisitor(ScriptNode myScriptNode) {
        scriptMethod = new ScriptMethod();
        if (myScriptNode == null) {
            scriptNode = new ScriptNode();
        } else {
            scriptNode = myScriptNode;
        }
        if (scriptNode.isExport()) {
            scriptMethod.setExport(true);
        }
        scriptNode.getMethods().add(scriptMethod);
    }

    @Override
    public Object visitGeneratorFunctionDeclaration(TypeScriptParser.GeneratorFunctionDeclarationContext ctx) {
        scriptMethod.setStartLine(ctx.getStart().getLine());
        scriptMethod.setEndLine(ctx.getStop().getLine());
        scriptMethod.setBody(ctx.getText());
        if (ctx.Async() != null) {
            scriptMethod.setAsync(true);
        }

        if (ctx.identifier() != null) {
            scriptMethod.setMethodName(ctx.identifier().getText());
        }

        if (ctx.formalParameterList() != null) {
            //todo 参数暂不处理 formalParameterList
        }
        MyFunctionBodyVisitor.visitFunctionBody(ctx.functionBody(), scriptMethod);
        return null;
    }
}
