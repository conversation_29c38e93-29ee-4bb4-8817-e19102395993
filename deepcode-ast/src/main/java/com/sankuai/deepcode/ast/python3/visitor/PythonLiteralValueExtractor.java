package com.sankuai.deepcode.ast.python3.visitor;

import com.sankuai.deepcode.ast.python3.gen.PythonParser;

/**
 * Python字面量值提取器
 * 用于从Python AST节点中提取实际的字面量值
 *
 * <AUTHOR>
 * @since 2024/12/4
 */
public class PythonLiteralValueExtractor {

    /**
     * 从annotated_rhs节点中提取字面量值
     *
     * @param ctx annotated_rhs上下文
     * @return 提取的字面量值，如果无法提取则返回原始文本
     */
    public static String extractLiteralValue(PythonParser.Annotated_rhsContext ctx) {
        if (ctx == null) {
            return null;
        }

        // 简单实现：直接从文本中提取基本字面量
        String text = ctx.getText();
        return extractSimpleLiteralValue(text);
    }

    /**
     * 从star_expressions节点中提取字面量值
     *
     * @param ctx star_expressions上下文
     * @return 提取的字面量值，如果无法提取则返回原始文本
     */
    public static String extractLiteralValue(PythonParser.Star_expressionsContext ctx) {
        if (ctx == null) {
            return null;
        }

        // 简单实现：直接从文本中提取基本字面量
        String text = ctx.getText();
        return extractSimpleLiteralValue(text);
    }

    /**
     * 从文本中提取简单的字面量值
     */
    public static String extractSimpleLiteralValue(String text) {
        if (text == null || text.trim().isEmpty()) {
            return text;
        }

        text = text.trim();

        // 处理字符串字面量
        if (isStringLiteral(text)) {
            return extractStringValueFromText(text);
        }

        // 处理数字字面量
        if (isNumberLiteral(text)) {
            return text;
        }

        // 处理布尔值和None
        if ("True".equals(text) || "False".equals(text) || "None".equals(text)) {
            return text;
        }

        // 其他情况返回原始文本
        return text;
    }

    /**
     * 检查是否是字符串字面量
     */
    private static boolean isStringLiteral(String text) {
        return (text.startsWith("'") && text.endsWith("'")) ||
               (text.startsWith("\"") && text.endsWith("\"")) ||
               (text.startsWith("'''") && text.endsWith("'''")) ||
               (text.startsWith("\"\"\"") && text.endsWith("\"\"\"")) ||
               (text.length() > 2 &&
                (text.charAt(0) == 'r' || text.charAt(0) == 'R' ||
                 text.charAt(0) == 'u' || text.charAt(0) == 'U' ||
                 text.charAt(0) == 'b' || text.charAt(0) == 'B' ||
                 text.charAt(0) == 'f' || text.charAt(0) == 'F') &&
                (text.charAt(1) == '\'' || text.charAt(1) == '"'));
    }

    /**
     * 检查是否是数字字面量
     */
    private static boolean isNumberLiteral(String text) {
        try {
            // 尝试解析为数字
            if (text.contains(".")) {
                Double.parseDouble(text);
            } else {
                Long.parseLong(text);
            }
            return true;
        } catch (NumberFormatException e) {
            return false;
        }
    }

    /**
     * 从字符串文本中提取实际的字符串值
     */
    private static String extractStringValueFromText(String text) {
        if (text == null) {
            return null;
        }

        // 处理三引号字符串
        if (text.startsWith("'''") && text.endsWith("'''")) {
            return text.substring(3, text.length() - 3);
        } else if (text.startsWith("\"\"\"") && text.endsWith("\"\"\"")) {
            return text.substring(3, text.length() - 3);
        }
        // 处理单引号字符串
        else if (text.startsWith("'") && text.endsWith("'")) {
            return text.substring(1, text.length() - 1);
        }
        // 处理双引号字符串
        else if (text.startsWith("\"") && text.endsWith("\"")) {
            return text.substring(1, text.length() - 1);
        }
        // 处理带前缀的字符串（如 r'string', u'string' 等）
        else if (text.length() > 2) {
            char prefix = text.charAt(0);
            if ((prefix == 'r' || prefix == 'R' || prefix == 'u' || prefix == 'U' ||
                 prefix == 'b' || prefix == 'B' || prefix == 'f' || prefix == 'F') &&
                (text.charAt(1) == '\'' || text.charAt(1) == '"')) {

                String content = text.substring(1);
                if (content.startsWith("'''") && content.endsWith("'''")) {
                    return content.substring(3, content.length() - 3);
                } else if (content.startsWith("\"\"\"") && content.endsWith("\"\"\"")) {
                    return content.substring(3, content.length() - 3);
                } else if (content.startsWith("'") && content.endsWith("'")) {
                    return content.substring(1, content.length() - 1);
                } else if (content.startsWith("\"") && content.endsWith("\"")) {
                    return content.substring(1, content.length() - 1);
                }
            }
        }

        // 如果无法解析，返回原始文本
        return text;
    }

}
