package com.sankuai.deepcode.ast.java.visitor;

import com.sankuai.deepcode.ast.java.gen.JavaParser;
import com.sankuai.deepcode.ast.java.gen.JavaParserBaseVisitor;
import com.sankuai.deepcode.ast.java.visitor.MyJavaFormalParametersVistor;
import com.sankuai.deepcode.ast.java.visitor.MyJavaTypeTypeVistor;
import com.sankuai.deepcode.ast.model.java.*;
import lombok.Getter;
import org.antlr.v4.runtime.ParserRuleContext;

import java.util.ArrayList;
import java.util.List;

public class MyJavaMethodDeclarationVisitor extends JavaParserBaseVisitor<Object> {
    private ClassNode classNode = new ClassNode();

    public MyJavaMethodDeclarationVisitor(ClassNode myClassNode) {
        classNode = myClassNode;
    }

    @Getter
    private MethodNode methodNode;


    @Override
    public Object visitMethodDeclaration(JavaParser.MethodDeclarationContext ctx) {
        methodNode = new MethodNode();
        methodNode.setClassName(classNode.getClassName());
        methodNode.setInClassName(classNode.getInClassName());
        methodNode.setMethodName(ctx.identifier().getText());
        visitTypeTypeOrVoid(ctx.typeTypeOrVoid());
        visitFormalParameters(ctx.formalParameters());
        methodNode.setStartLine(ctx.getStart().getLine());
        methodNode.setEndLine(ctx.getStop().getLine());
        methodNode.setBody(ctx.methodBody().getText());
        List<ParserRuleContext> methodCtxs = new ArrayList<>();
        methodCtxs.add(ctx);
        methodNode.setMethodCtxs(methodCtxs);
        return null;
    }


    @Override
    public Object visitTypeTypeOrVoid(JavaParser.TypeTypeOrVoidContext ctx) {
        JavaReturn javaReturn = new JavaReturn();
        if (null != ctx.typeType()) {
            com.sankuai.deepcode.ast.java.visitor.MyJavaTypeTypeVistor vistor = new MyJavaTypeTypeVistor();
            vistor.visit(ctx.typeType());
            javaReturn.setType(vistor.getType());
            List<String> signatures = new ArrayList<>();
            for (JavaGenerics javaGenerics : vistor.getGenerics()) {
                signatures.add(javaGenerics.getName());
            }
            javaReturn.setSignatures(signatures);
            javaReturn.setAnnotations(vistor.getAnnotations());
        } else {
            javaReturn.setType(ctx.getText());
        }
        methodNode.setReturnInfo(javaReturn);
        return null;
    }


    @Override
    public Object visitFormalParameters(JavaParser.FormalParametersContext ctx) {
        com.sankuai.deepcode.ast.java.visitor.MyJavaFormalParametersVistor vistor = new MyJavaFormalParametersVistor();
        vistor.visitFormalParameters(ctx);
        methodNode.setParams(vistor.getParams());
        return null;
    }


}
