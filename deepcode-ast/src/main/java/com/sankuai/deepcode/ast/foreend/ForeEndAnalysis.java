package com.sankuai.deepcode.ast.foreend;

import com.sankuai.deepcode.ast.enums.ChangeTypeEnum;
import com.sankuai.deepcode.ast.enums.NodeSourceEnum;
import com.sankuai.deepcode.ast.enums.foreend.ForeEndEgdeTypeEnum;
import com.sankuai.deepcode.ast.enums.html.ElementTypeEnum;
import com.sankuai.deepcode.ast.html.gen.HTMLLexer;
import com.sankuai.deepcode.ast.html.gen.HTMLParser;
import com.sankuai.deepcode.ast.html.visitor.MyHtmlVisitor;
import com.sankuai.deepcode.ast.model.base.CompareRes;
import com.sankuai.deepcode.ast.model.base.GitDiffInfo;
import com.sankuai.deepcode.ast.model.foreend.ForeEndAnalysisRes;
import com.sankuai.deepcode.ast.model.foreend.ForeEndBaseEdge;
import com.sankuai.deepcode.ast.model.foreend.ForeEndChangeNum;
import com.sankuai.deepcode.ast.model.html.ElementValue;
import com.sankuai.deepcode.ast.model.html.HtmlAttribute;
import com.sankuai.deepcode.ast.model.html.HtmlNode;
import com.sankuai.deepcode.ast.model.java.FileNode;
import com.sankuai.deepcode.ast.model.scss.ScssNode;
import com.sankuai.deepcode.ast.model.scss.ScssRule;
import com.sankuai.deepcode.ast.model.scss.ScssSelector;
import com.sankuai.deepcode.ast.model.typescript.*;
import com.sankuai.deepcode.ast.scss.gen.ScssLexer;
import com.sankuai.deepcode.ast.scss.gen.ScssParser;
import com.sankuai.deepcode.ast.scss.visitor.MyScssVisitor;
import com.sankuai.deepcode.ast.typescript.gen.TypeScriptLexer;
import com.sankuai.deepcode.ast.typescript.gen.TypeScriptParser;
import com.sankuai.deepcode.ast.typescript.visitor.MyTypeScriptVisitor;
import com.sankuai.deepcode.ast.util.CompareUtil;
import com.sankuai.deepcode.ast.util.FileUtil;
import com.sankuai.deepcode.ast.util.Md5Util;
import org.antlr.v4.runtime.CharStreams;
import org.antlr.v4.runtime.CommonTokenStream;
import org.antlr.v4.runtime.tree.ParseTree;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.io.File;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.*;
import java.util.stream.Collectors;

import static com.sankuai.deepcode.ast.util.CompareUtil.WORKSPACE_PATH;

public class ForeEndAnalysis {

    public static ForeEndAnalysisRes analysis(long uniqueId, String gitUrl, String gitName, String fromBranch, String toBranch, String buildBranch, String buildCommit) throws Exception {
        long start = System.currentTimeMillis();
        if (StringUtils.isEmpty(fromBranch)) {
            throw new Exception("fromBranch源分支不能为空");
        }
        if (StringUtils.isEmpty(fromBranch)) {
            fromBranch = "master";
            System.out.println("toBranch目标分支为空,默认master");
        }

        long startTime = System.currentTimeMillis();
        CompareRes compareRes = CompareUtil.getDiffInfos(uniqueId, gitUrl, gitName, fromBranch, toBranch, buildBranch, buildCommit);
        long end = System.currentTimeMillis();
        System.out.println("diff耗时:" + (end - startTime) + "ms");

        ForeEndAnalysisRes foreEndAnalysisRes = foreEndAnalysis(gitName, compareRes);
        end = System.currentTimeMillis();
        FileUtil.deleteFile(WORKSPACE_PATH, String.valueOf(uniqueId));
        System.out.println("解析总耗时:" + (end - start) + "ms");
        return foreEndAnalysisRes;
    }


    public static ForeEndAnalysisRes foreEndAnalysis(String gitName, CompareRes compareRes) throws Exception {
        long startTime = System.currentTimeMillis();
        Map<String, GitDiffInfo> gitDiffInfoMap = compareRes.getGitForeEndDiffInfoMap();
        Set<String> filterPath = compareRes.getFilterPath();
        String alias = "";
        if (compareRes.isDiff()) {
            alias = "_from";
            if (compareRes.isCheck()) {
                alias = "_build";
            }
        } else {
            alias = "_to";
        }
        ForeEndAnalysisRes foreEndAnalysisRes = initForeEndAnalysis(gitName + alias, compareRes.getGitPath(), filterPath);
        long end = System.currentTimeMillis();
        System.out.println("初始化" + gitName + alias + "耗时:" + (end - startTime) + "ms");
        startTime = System.currentTimeMillis();
        foreEndAnalysisRes.setToCommit(compareRes.getToCommit());
        foreEndAnalysisRes.setFromCommit(compareRes.getFromCommit());
        foreEndAnalysisRes.setBuildCommit(compareRes.getBuildCommit());
        if (compareRes.isDiff()) {
            for (Map.Entry<String, GitDiffInfo> entry : gitDiffInfoMap.entrySet()) {
                GitDiffInfo gitDiffInfo = entry.getValue();
                if (gitDiffInfo.getChangeType() == ChangeTypeEnum.DEFAULT.getCode()) {
                    filterPath.add(gitDiffInfo.getPath());
                }
            }
            ForeEndAnalysisRes toBranchRes = initForeEndAnalysis(gitName + "_to", compareRes.getGitToPath(), filterPath);
            end = System.currentTimeMillis();
            System.out.println("初始化" + gitName + "_to" + "耗时:" + (end - startTime) + "ms");

            ForeEndChangeNum foreEndChangeNum = new ForeEndChangeNum();
            for (Map.Entry<String, Map<String, HtmlNode>> entry : foreEndAnalysisRes.getHtmlNodeMap().entrySet()) {
                GitDiffInfo gitDiffInfo = gitDiffInfoMap.get(entry.getKey());
                if (gitDiffInfo != null) {
                    if (gitDiffInfo.getChangeType() != ChangeTypeEnum.DEFAULT.getCode()) {
                        List<Integer> commentList = foreEndAnalysisRes.getCommentList().get(entry.getKey());
                        for (Map.Entry<String, HtmlNode> nodeEntry : entry.getValue().entrySet()) {
                            diffHtmlNode(nodeEntry.getKey(), nodeEntry.getValue(), gitDiffInfo, toBranchRes, foreEndChangeNum, commentList);
                        }
                    }
                }
            }

            for (Map.Entry<String, Map<String, ScriptMethod>> entry : foreEndAnalysisRes.getScriptMethodMap().entrySet()) {
                GitDiffInfo gitDiffInfo = gitDiffInfoMap.get(entry.getKey());
                if (gitDiffInfo != null) {
                    if (gitDiffInfo.getChangeType() != ChangeTypeEnum.DEFAULT.getCode()) {
                        List<Integer> commentList = foreEndAnalysisRes.getCommentList().get(entry.getKey());
                        for (Map.Entry<String, ScriptMethod> nodeEntry : entry.getValue().entrySet()) {
                            diffScriptMethodNode(nodeEntry.getKey(), nodeEntry.getValue(), gitDiffInfo, toBranchRes, foreEndChangeNum, commentList);
                        }
                    }
                }
            }

            for (Map.Entry<String, Map<String, ScriptVariable>> entry : foreEndAnalysisRes.getScriptVariableMap().entrySet()) {
                GitDiffInfo gitDiffInfo = gitDiffInfoMap.get(entry.getKey());
                if (gitDiffInfo != null) {
                    if (gitDiffInfo.getChangeType() != ChangeTypeEnum.DEFAULT.getCode()) {
                        List<Integer> commentList = foreEndAnalysisRes.getCommentList().get(entry.getKey());
                        for (Map.Entry<String, ScriptVariable> nodeEntry : entry.getValue().entrySet()) {
                            diffScriptVariableNode(nodeEntry.getKey(), nodeEntry.getValue(), gitDiffInfo, toBranchRes, foreEndChangeNum, commentList);
                        }
                    }
                }
            }

            for (Map.Entry<String, Map<String, ScssRule>> entry : foreEndAnalysisRes.getScssRuleMap().entrySet()) {
                GitDiffInfo gitDiffInfo = gitDiffInfoMap.get(entry.getKey());
                if (gitDiffInfo != null) {
                    if (gitDiffInfo.getChangeType() != ChangeTypeEnum.DEFAULT.getCode()) {
                        List<Integer> commentList = foreEndAnalysisRes.getCommentList().get(entry.getKey());
                        for (Map.Entry<String, ScssRule> nodeEntry : entry.getValue().entrySet()) {
                            diffScssRuleNode(nodeEntry.getKey(), nodeEntry.getValue(), gitDiffInfo, toBranchRes, foreEndChangeNum, commentList);
                        }
                    }
                }
            }

            for (GitDiffInfo gitDiffInfo : gitDiffInfoMap.values()) {
                FileNode fileNode = new FileNode();
                fileNode.setCommitCount(gitDiffInfo.getCommitCount());
                fileNode.setModuleName(gitDiffInfo.getModuleName());
                fileNode.setFileName(gitDiffInfo.getFileName());
                fileNode.setPath(gitDiffInfo.getPath());
                fileNode.setFileType(gitDiffInfo.getFileType());
                fileNode.setChangeType(gitDiffInfo.getChangeType());
                fileNode.setChangeLines(gitDiffInfo.getChangeLines());
                fileNode.setStartLine(1);
                fileNode.setEndLine(gitDiffInfo.getCodeViews().size() - 1);
                fileNode.setCodeViews(gitDiffInfo.getCodeViews());
                fileNode.setVid(Md5Util.fileNodeToMd5(fileNode));

                boolean fileChange = false;
                for (int i = fileNode.getStartLine(); i <= fileNode.getEndLine(); i++) {
                    if (gitDiffInfo.getChangeLines().contains(i)) {
                        fileNode.getChangeLines().add(i);
                        fileChange = true;
                    }
                }
                if (fileChange) {
                    FileNode toFile = toBranchRes.getFileNodeMap().get(fileNode.getVid());
                    if (null == toFile) {
                        fileNode.setChangeType(ChangeTypeEnum.ADD.getCode());
                    } else {
                        fileNode.setChangeType(ChangeTypeEnum.CHANGE.getCode());
                    }
                }
                foreEndAnalysisRes.getFileNodeMap().put(fileNode.getPath(), fileNode);
            }
        } else {
            for (GitDiffInfo gitDiffInfo : gitDiffInfoMap.values()) {
                FileNode fileNode = new FileNode();
                fileNode.setModuleName(gitDiffInfo.getModuleName());
                fileNode.setFileName(gitDiffInfo.getFileName());
                fileNode.setPath(gitDiffInfo.getPath());
                fileNode.setFileType(gitDiffInfo.getFileType());
                fileNode.setStartLine(1);
                fileNode.setEndLine(gitDiffInfo.getCodeViews().size() - 1);
                fileNode.setCodeViews(gitDiffInfo.getCodeViews());
                fileNode.setVid(Md5Util.fileNodeToMd5(fileNode));
                foreEndAnalysisRes.getFileNodeMap().put(fileNode.getPath(), fileNode);
            }
        }

        end = System.currentTimeMillis();
        System.out.println("解析" + gitName + alias + "耗时:" + (end - startTime) + "ms");
        return foreEndAnalysisRes;
    }

    public static void diffHtmlNode(String vid, HtmlNode htmlNode, GitDiffInfo gitDiffInfo, ForeEndAnalysisRes toBranchRes, ForeEndChangeNum foreEndChangeNum, List<Integer> commentList) {
        boolean isChange = false;
        for (int i = htmlNode.getStartLine(); i <= htmlNode.getEndLine(); i++) {
            if (commentList.contains(i)) {
                continue;
            }
            if (gitDiffInfo.getChangeLines().contains(i)) {
                htmlNode.getChangeLines().add(i);
                isChange = true;
            }
        }
        if (isChange) {
            if (toBranchRes.getHtmlNodeMap().get(gitDiffInfo.getPath()) == null) {
                htmlNode.setChangeType(ChangeTypeEnum.ADD.getCode());
                foreEndChangeNum.setAddHtmlCount(foreEndChangeNum.getAddHtmlCount() + 1);
            } else {
                HtmlNode toHtmlNode = toBranchRes.getHtmlNodeMap().get(gitDiffInfo.getPath()).get(vid);
                if (toHtmlNode == null) {
                    htmlNode.setChangeType(ChangeTypeEnum.ADD.getCode());
                    foreEndChangeNum.setAddHtmlCount(foreEndChangeNum.getAddHtmlCount() + 1);
                } else {
                    htmlNode.setChangeType(ChangeTypeEnum.CHANGE.getCode());
                    String toMd5 = Md5Util.bodyToMd5(toHtmlNode.getBody());
                    String fromMd5 = Md5Util.bodyToMd5(htmlNode.getBody());
                    if (!toMd5.equals(fromMd5)) {
                        htmlNode.setChangeType(ChangeTypeEnum.CHANGE.getCode());
                        foreEndChangeNum.setChangeHtmlCount(foreEndChangeNum.getChangeHtmlCount() + 1);
                    }
                }
            }
        }
    }

    public static void diffScriptMethodNode(String vid, ScriptMethod scriptMethod, GitDiffInfo gitDiffInfo, ForeEndAnalysisRes toBranchRes, ForeEndChangeNum foreEndChangeNum, List<Integer> commentList) {
        boolean isChange = false;
        for (int i = scriptMethod.getStartLine(); i <= scriptMethod.getEndLine(); i++) {
            if (commentList.contains(i)) {
                scriptMethod.getCommentLines().add(i);
                continue;
            }
            if (gitDiffInfo.getChangeLines().contains(i)) {
                scriptMethod.getChangeLines().add(i);
                isChange = true;
            }
        }
        if (isChange) {
            if (toBranchRes.getScriptMethodMap().get(gitDiffInfo.getPath()) == null) {
                scriptMethod.setChangeType(ChangeTypeEnum.ADD.getCode());
                foreEndChangeNum.setAddHtmlCount(foreEndChangeNum.getAddHtmlCount() + 1);
            } else {
                ScriptMethod toScriptMethod = toBranchRes.getScriptMethodMap().get(gitDiffInfo.getPath()).get(vid);
                if (toScriptMethod == null) {
                    scriptMethod.setChangeType(ChangeTypeEnum.ADD.getCode());
                    foreEndChangeNum.setAddHtmlCount(foreEndChangeNum.getAddHtmlCount() + 1);
                } else {
                    scriptMethod.setChangeType(ChangeTypeEnum.CHANGE.getCode());
                    String toMd5 = Md5Util.bodyToMd5(toScriptMethod.getBody());
                    String fromMd5 = Md5Util.bodyToMd5(scriptMethod.getBody());
                    if (!toMd5.equals(fromMd5)) {
                        scriptMethod.setChangeType(ChangeTypeEnum.CHANGE.getCode());
                        foreEndChangeNum.setChangeHtmlCount(foreEndChangeNum.getChangeHtmlCount() + 1);
                    }
                }
            }
        }
    }

    public static void diffScriptVariableNode(String vid, ScriptVariable scriptVariable, GitDiffInfo gitDiffInfo, ForeEndAnalysisRes toBranchRes, ForeEndChangeNum foreEndChangeNum, List<Integer> commentList) {
        boolean isChange = false;
        for (int i = scriptVariable.getStartLine(); i <= scriptVariable.getEndLine(); i++) {
            if (commentList.contains(i)) {
                scriptVariable.getCommentLines().add(i);
                continue;
            }
            if (gitDiffInfo.getChangeLines().contains(i)) {
                scriptVariable.getChangeLines().add(i);
                isChange = true;
            }
        }
        if (isChange) {
            if (toBranchRes.getScriptVariableMap().get(gitDiffInfo.getPath()) == null) {
                scriptVariable.setChangeType(ChangeTypeEnum.ADD.getCode());
                foreEndChangeNum.setAddHtmlCount(foreEndChangeNum.getAddHtmlCount() + 1);
            } else {
                ScriptVariable toScriptVariable = toBranchRes.getScriptVariableMap().get(gitDiffInfo.getPath()).get(vid);
                if (toScriptVariable == null) {
                    scriptVariable.setChangeType(ChangeTypeEnum.ADD.getCode());
                    foreEndChangeNum.setAddHtmlCount(foreEndChangeNum.getAddHtmlCount() + 1);
                } else {
                    scriptVariable.setChangeType(ChangeTypeEnum.CHANGE.getCode());
                    String toMd5 = Md5Util.bodyToMd5(toScriptVariable.getBody());
                    String fromMd5 = Md5Util.bodyToMd5(scriptVariable.getBody());
                    if (!toMd5.equals(fromMd5)) {
                        scriptVariable.setChangeType(ChangeTypeEnum.CHANGE.getCode());
                        foreEndChangeNum.setChangeHtmlCount(foreEndChangeNum.getChangeHtmlCount() + 1);
                    }
                }
            }
        }
    }

    public static void diffScssRuleNode(String vid, ScssRule scssRule, GitDiffInfo gitDiffInfo, ForeEndAnalysisRes toBranchRes, ForeEndChangeNum foreEndChangeNum, List<Integer> commentList) {
        boolean isChange = false;
        for (int i = scssRule.getStartLine(); i <= scssRule.getEndLine(); i++) {
            if (commentList.contains(i)) {
                scssRule.getCommentLines().add(i);
                continue;
            }
            if (gitDiffInfo.getChangeLines().contains(i)) {
                scssRule.getChangeLines().add(i);
                isChange = true;
            }
        }
        if (isChange) {
            if (toBranchRes.getScssRuleMap().get(gitDiffInfo.getPath()) == null) {
                scssRule.setChangeType(ChangeTypeEnum.ADD.getCode());
                foreEndChangeNum.setAddHtmlCount(foreEndChangeNum.getAddHtmlCount() + 1);
            } else {
                ScssRule toScssRule = toBranchRes.getScssRuleMap().get(gitDiffInfo.getPath()).get(vid);
                if (toScssRule == null) {
                    scssRule.setChangeType(ChangeTypeEnum.ADD.getCode());
                    foreEndChangeNum.setAddHtmlCount(foreEndChangeNum.getAddHtmlCount() + 1);
                } else {
                    scssRule.setChangeType(ChangeTypeEnum.CHANGE.getCode());
                    String toMd5 = Md5Util.bodyToMd5(toScssRule.getBody());
                    String fromMd5 = Md5Util.bodyToMd5(scssRule.getBody());
                    if (!toMd5.equals(fromMd5)) {
                        scssRule.setChangeType(ChangeTypeEnum.CHANGE.getCode());
                        foreEndChangeNum.setChangeHtmlCount(foreEndChangeNum.getChangeHtmlCount() + 1);
                    }
                }
            }
        }
    }


    public static ForeEndAnalysisRes initForeEndAnalysis(String gitName, String gitPath, Set<String> filterPath) throws Exception {
        ForeEndAnalysisRes foreEndAnalysisRes = new ForeEndAnalysisRes();

        Map<String, Map<String, HtmlNode>> htmlNodeMap = new HashMap<>();
        Map<String, Map<String, ScssRule>> scssRuleMap = new HashMap<>();
        Map<String, Map<String, ScriptMethod>> scriptMethodMap = new HashMap<>();
        Map<String, Map<String, ScriptVariable>> scriptVariableMap = new HashMap<>();
        Map<String, Set<String>> fileImportFile = new HashMap<>();
        Map<String, Set<String>> fileDefineMethod = new HashMap<>();
        Map<String, Set<String>> fileDefineField = new HashMap<>();
        //todo 简单处理  下面三个未去重
        Map<String, List<ForeEndBaseEdge>> htmlInvokeMethod = new HashMap<>();
        Map<String, List<ForeEndBaseEdge>> methodInvokeMethod = new HashMap<>();
        Map<String, List<ForeEndBaseEdge>> htmlQuoteScss = new HashMap<>();
        Map<String, List<Integer>> commentList = new HashMap<>();
        foreEndAnalysisRes.setHtmlNodeMap(htmlNodeMap);
        foreEndAnalysisRes.setScssRuleMap(scssRuleMap);
        foreEndAnalysisRes.setScriptMethodMap(scriptMethodMap);
        foreEndAnalysisRes.setScriptVariableMap(scriptVariableMap);
        foreEndAnalysisRes.setCommentList(commentList);
        foreEndAnalysisRes.setFileImportFile(fileImportFile);
        foreEndAnalysisRes.setFileDefineMethod(fileDefineMethod);
        foreEndAnalysisRes.setFileDefineField(fileDefineField);
        foreEndAnalysisRes.setHtmlInvokeMethod(htmlInvokeMethod);
        foreEndAnalysisRes.setMethodInvokeMethod(methodInvokeMethod);
        foreEndAnalysisRes.setHtmlQuoteScss(htmlQuoteScss);

        Map<String, File> fileMap = FileUtil.getAllFiles(gitPath);
        List<File> firstFiles = new ArrayList<>();
        for (File file : fileMap.values()) {
            if (file.getName().endsWith(".html")
                    || file.getName().endsWith(".wxml")
                    || file.getName().endsWith(".vue")
                    || file.getName().endsWith(".js")
                    || file.getName().endsWith(".jsx")
                    || file.getName().endsWith(".ts")
                    || file.getName().endsWith(".tsx")
                    || file.getName().endsWith(".css")
                    || file.getName().endsWith(".less")
                    || file.getName().endsWith(".scss")
                    || file.getName().endsWith(".wxss")) {
                firstFiles.add(file);
            }
        }

        Map<String, List<ScriptImport>> fileImportFileMap = new HashMap<>();
        for (File file : firstFiles) {
            String filePath = file.getPath().split(gitName + "/")[1];
            if (filterPath.contains(filePath)) {
                continue;
            }
            String code = new String(Files.readAllBytes(Paths.get(file.getPath())));
            List<HtmlNode> htmlNodes = new ArrayList<>();
            List<ScssNode> scssNodes = new ArrayList<>();
            List<ScriptNode> scriptNodes = new ArrayList<>();
            List<Integer> comments = new ArrayList<>();
            if (file.getName().endsWith(".html") || file.getName().endsWith(".wxml") || file.getName().endsWith(".vue")) {
                HTMLLexer lexer = new HTMLLexer(CharStreams.fromString(code));
                CommonTokenStream tokens = new CommonTokenStream(lexer);
                HTMLParser parser = new HTMLParser(tokens);
                ParseTree tree = parser.htmlDocument();
                MyHtmlVisitor visitor = new MyHtmlVisitor();
                visitor.visit(tree);
                List<HtmlNode> htmlNodeList = visitor.getHtmlNodes();
                initListByHtmlNodes(htmlNodeList, htmlNodes, scssNodes, scriptNodes, comments, filePath, fileImportFileMap);
            } else if (file.getName().endsWith(".js") || file.getName().endsWith(".jsx") || file.getName().endsWith(".ts") || file.getName().endsWith(".tsx")) {
                TypeScriptLexer lexer = new TypeScriptLexer(CharStreams.fromString(code));
                CommonTokenStream tokens = new CommonTokenStream(lexer);
                TypeScriptParser parser = new TypeScriptParser(tokens);
                ParseTree tree = parser.program();
                MyTypeScriptVisitor visitor = new MyTypeScriptVisitor();
                visitor.visit(tree);
                ScriptNode scriptNode = visitor.getScriptNode();
                initListByScriptNode(scriptNode, htmlNodes, scssNodes, scriptNodes, comments, filePath, fileImportFileMap);
                InitForeEndCommon.initCommonByToken(tokens, comments);
            } else if (file.getName().endsWith(".css") || file.getName().endsWith(".less") || file.getName().endsWith(".scss") || file.getName().endsWith(".wxss")) {
                ScssLexer lexer = new ScssLexer(CharStreams.fromString(code));
                CommonTokenStream tokens = new CommonTokenStream(lexer);
                ScssParser parser = new ScssParser(tokens);
                ParseTree tree = parser.stylesheet();
                MyScssVisitor visitor = new MyScssVisitor();
                visitor.visit(tree);
                ScssNode scssNode = visitor.getScssNode();
                initListByScssNode(scssNode, scssNodes, filePath);
                InitForeEndCommon.initCommonByToken(tokens, comments);
            }
            Map<String, HtmlNode> htmlNodeVidMap = new HashMap<>();
            for (HtmlNode htmlNode : htmlNodes) {
                comments.addAll(htmlNode.getCommentLines());
                htmlNodeVidMap.put(Md5Util.htmlNodeToMd5(htmlNode), htmlNode);
            }
            htmlNodeMap.put(filePath, htmlNodeVidMap);

            Map<String, ScssRule> scssRuleVidMap = new HashMap<>();
            for (ScssNode scssNode : scssNodes) {
                comments.addAll(scssNode.getCommentLines());
                for (ScssRule scssRule : scssNode.getScssRules()) {
                    scssRuleVidMap.put(Md5Util.scssRuleToMd5(scssRule, scssNode.getFilePath()), scssRule);
                }
            }
            scssRuleMap.put(filePath, scssRuleVidMap);

            Map<String, ScriptMethod> scriptMethodVidMap = new HashMap<>();
            Map<String, ScriptVariable> scriptVariableVidMap = new HashMap<>();
            for (ScriptNode scriptNode : scriptNodes) {
                comments.addAll(scriptNode.getCommentLines());
                initScriptNodeMap(scriptNode, filePath, scriptMethodVidMap, scriptVariableVidMap, foreEndAnalysisRes);
            }
            scriptMethodMap.put(filePath, scriptMethodVidMap);
            scriptVariableMap.put(filePath, scriptVariableVidMap);

            comments = comments.stream().distinct().sorted().collect(Collectors.toList());
            commentList.put(filePath, comments);
        }

        // 初始化importMap
        for (Map.Entry<String, List<ScriptImport>> entry : fileImportFileMap.entrySet()) {
            String importFilePath = entry.getKey();
            List<ScriptImport> scriptImports = entry.getValue();
            for (ScriptImport scriptImport : scriptImports) {
                String fromName = scriptImport.getFromName();
                if (StringUtils.isNotEmpty(fromName)) {
                    fromName = trimMarks(fromName);
                    String[] split = importFilePath.split("/");
                    String fileName = split[split.length - 1];
                    if (fromName.startsWith("@")) {
                        if (fromName.startsWith("@/")) {
                            fromName = fromName.replace("@/", "");
                            if (importFilePath.contains("src/")) {
                                String[] splitSrc = importFilePath.split("src/");
                                fromName = splitSrc[0] + "src/" + fromName;
                            }
                        } else {
                            fromName = fromName.substring(1);
                        }
                    } else if (fromName.startsWith("./")) {
                        String dirPath = importFilePath.replace(fileName, "");
                        fromName = dirPath + fromName.substring(2);
                    } else if (fromName.startsWith("../")) {
                        String dirPath = importFilePath.replace(fileName, "");
                        int dotCount = countParentReferences(fromName);
                        fromName = fromName.substring(dotCount * 3, fromName.length());
                        String[] dirPathSplit = dirPath.split("/");
                        StringBuilder newDirPath = new StringBuilder();
                        for (int i = 0; i < dirPathSplit.length; i++) {
                            if (i >= dotCount - 1) {
                                break;
                            } else {
                                newDirPath.append(dirPathSplit[i]).append("/");
                            }
                        }
                        fromName = newDirPath + fromName;
                    }
                    if (null != fileMap.get(fromName)) {
                        foreEndAnalysisRes.addFileImportFile(importFilePath, fromName);
                    } else {
                        if (!fromName.contains(".")) {
                            if (null != fileMap.get(fromName + ".js") ||
                                    null != fileMap.get(fromName + ".jsx") ||
                                    null != fileMap.get(fromName + ".ts") ||
                                    null != fileMap.get(fromName + ".tsx")) {
                                foreEndAnalysisRes.addFileImportFile(importFilePath, fromName);
                            }
                        }
                    }
                }
            }
        }

        //初始化 map关系数据
        Map<String, Map<String, String>> pathAndMethodNameAndVidMap = new HashMap<>();
        for (Map.Entry<String, Map<String, ScriptMethod>> entry : scriptMethodMap.entrySet()) {
            Map<String, String> nameAndVidMap = new HashMap<>();
            for (Map.Entry<String, ScriptMethod> entryNode : entry.getValue().entrySet()) {
                nameAndVidMap.put(entryNode.getValue().getMethodName(), entryNode.getKey());
            }
            pathAndMethodNameAndVidMap.put(entry.getKey(), nameAndVidMap);
        }
        Map<String, Map<String, String>> pathAndScssNameAndVidMap = new HashMap<>();
        for (Map.Entry<String, Map<String, ScssRule>> entry : scssRuleMap.entrySet()) {
            Map<String, String> nameAndVidMap = new HashMap<>();
            for (Map.Entry<String, ScssRule> entryNode : entry.getValue().entrySet()) {
                if (CollectionUtils.isNotEmpty(entryNode.getValue().getSelectors())) {
                    for (ScssSelector scssSelector : entryNode.getValue().getSelectors()) {
                        if (StringUtils.isNotEmpty(scssSelector.getName())) {
                            nameAndVidMap.put(scssSelector.getName(), entryNode.getKey());
                        }
                    }
                }
            }
            pathAndScssNameAndVidMap.put(entry.getKey(), nameAndVidMap);
        }

        Map<String, ForeEndBaseEdge> methodMethodEdgesMap = new HashMap<>();
        for (Map.Entry<String, Map<String, HtmlNode>> entry : htmlNodeMap.entrySet()) {
            List<ForeEndBaseEdge> htmlMethodEdges = new ArrayList<>();
            List<ForeEndBaseEdge> htmlScssEdges = new ArrayList<>();
            for (Map.Entry<String, HtmlNode> entryNode : entry.getValue().entrySet()) {
                String htmlVid = entryNode.getKey();
                HtmlNode htmlNode = entryNode.getValue();
                if (CollectionUtils.isNotEmpty(htmlNode.getHtmlAttributes())) {
                    for (HtmlAttribute htmlAttribute : htmlNode.getHtmlAttributes()) {
                        //todo 样式先只解析一层
                        if ("id".equals(htmlAttribute.getTagName()) || "class".equals(htmlAttribute.getTagName())) {
                            String tagValue = htmlAttribute.getTagValue();
                            if (StringUtils.isNotEmpty(tagValue)) {
                                tagValue = trimMarks(tagValue);
                                Map<String, String> scssNameAndVid = pathAndScssNameAndVidMap.get(entry.getKey());
                                if (null != scssNameAndVid) {
                                    String targetVid = scssNameAndVid.get("." + tagValue);
                                    if (StringUtils.isEmpty(targetVid)) {
                                        targetVid = scssNameAndVid.get("#" + tagValue);
                                    }
                                    if (StringUtils.isNotEmpty(targetVid)) {
                                        String key = htmlVid + "->" + targetVid;
                                        if (!methodMethodEdgesMap.containsKey(key)) {
                                            ForeEndBaseEdge foreEndBaseEdge = new ForeEndBaseEdge();
                                            foreEndBaseEdge.setSource(htmlVid);
                                            foreEndBaseEdge.setSourceType(NodeSourceEnum.LOCAL.getCode());
                                            foreEndBaseEdge.setTarget(targetVid);
                                            foreEndBaseEdge.setTargetType(NodeSourceEnum.LOCAL.getCode());
                                            foreEndBaseEdge.setType(ForeEndEgdeTypeEnum.HTML_QUOTE_STYLE.getCode());
                                            methodMethodEdgesMap.put(key, foreEndBaseEdge);
                                            htmlScssEdges.add(foreEndBaseEdge);
                                        } else {
                                            methodMethodEdgesMap.get(key).getLines().add(htmlAttribute.getStartLine());
                                        }
                                    }
                                }
                            }
                        } else {
                            Map<String, String> fileMethodMap = pathAndMethodNameAndVidMap.get(entry.getKey());
                            if (null != fileMethodMap) {
                                if (StringUtils.isNotEmpty(htmlAttribute.getTagValue())) {
                                    for (String methodName : fileMethodMap.keySet()) {
                                        if (htmlAttribute.getTagValue().matches(".*\\b" + methodName + "\\s*\\(.*")) {
                                            String key = htmlVid + "->" + fileMethodMap.get(methodName);
                                            if (!methodMethodEdgesMap.containsKey(key)) {
                                                ForeEndBaseEdge foreEndBaseEdge = new ForeEndBaseEdge();
                                                foreEndBaseEdge.setSource(htmlVid);
                                                foreEndBaseEdge.setTargetType(NodeSourceEnum.LOCAL.getCode());
                                                foreEndBaseEdge.setTarget(fileMethodMap.get(methodName));
                                                foreEndBaseEdge.setTargetType(NodeSourceEnum.LOCAL.getCode());
                                                foreEndBaseEdge.setType(ForeEndEgdeTypeEnum.HTML_INVOKE_METHOD.getCode());
                                                methodMethodEdgesMap.put(key, foreEndBaseEdge);
                                                htmlMethodEdges.add(foreEndBaseEdge);
                                            } else {
                                                methodMethodEdgesMap.get(key).getLines().add(htmlAttribute.getStartLine());
                                            }
                                        }
                                    }
                                }

                                if (CollectionUtils.isNotEmpty(htmlNode.getElementValues())) {
                                    for (ElementValue htmlElementValue : htmlNode.getElementValues()) {
                                        if (StringUtils.isNotEmpty(htmlElementValue.getValue())
                                                && htmlElementValue.getValue().contains("(")) {
                                            for (String methodName : fileMethodMap.keySet()) {
                                                if (htmlElementValue.getValue().matches(".*\\b" + methodName + "\\s*\\(.*")) {
                                                    String key = htmlVid + "->" + fileMethodMap.get(methodName);
                                                    if (!methodMethodEdgesMap.containsKey(key)) {
                                                        ForeEndBaseEdge foreEndBaseEdge = new ForeEndBaseEdge();
                                                        foreEndBaseEdge.setSource(htmlVid);
                                                        foreEndBaseEdge.setSourceType(NodeSourceEnum.LOCAL.getCode());
                                                        foreEndBaseEdge.setTarget(fileMethodMap.get(methodName));
                                                        foreEndBaseEdge.setTargetType(NodeSourceEnum.LOCAL.getCode());
                                                        foreEndBaseEdge.setType(ForeEndEgdeTypeEnum.HTML_INVOKE_METHOD.getCode());
                                                        methodMethodEdgesMap.put(key, foreEndBaseEdge);
                                                        htmlMethodEdges.add(foreEndBaseEdge);
                                                    } else {
                                                        methodMethodEdgesMap.get(key).getLines().add(htmlElementValue.getLine());
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
            if (CollectionUtils.isNotEmpty(htmlMethodEdges)) {
                htmlInvokeMethod.put(entry.getKey(), htmlMethodEdges);
            }
            if (CollectionUtils.isNotEmpty(htmlScssEdges)) {
                htmlQuoteScss.put(entry.getKey(), htmlScssEdges);
            }
        }

        for (Map.Entry<String, Map<String, ScriptMethod>> entry : scriptMethodMap.entrySet()) {
            List<ForeEndBaseEdge> methodMethodEdges = new ArrayList<>();
            String filePath = entry.getKey();
            Map<String, ScriptMethod> methodMap = entry.getValue();
            for (Map.Entry<String, ScriptMethod> entryNode : methodMap.entrySet()) {
                String methodVid = entryNode.getKey();
                ScriptMethod scriptMethod = entryNode.getValue();
                if (CollectionUtils.isNotEmpty(scriptMethod.getInvokeMethods())) {
                    //todo  方法调用先简单处理
                    for (ScriptMethod invoke : scriptMethod.getInvokeMethods()) {
                        String targetVid = Md5Util.scriptMethodNameToMd5(invoke.getMethodName(), filePath);
                        String key = methodVid + "->" + targetVid;
                        if (!methodMethodEdgesMap.containsKey(key)) {
                            ForeEndBaseEdge foreEndBaseEdge = new ForeEndBaseEdge();
                            foreEndBaseEdge.setSource(methodVid);
                            foreEndBaseEdge.setSourceType(NodeSourceEnum.LOCAL.getCode());
                            foreEndBaseEdge.setType(ForeEndEgdeTypeEnum.METHOD_INVOKE_METHOD.getCode());
                            foreEndBaseEdge.getLines().add(invoke.getStartLine());
                            foreEndBaseEdge.setTarget(targetVid);
                            ScriptMethod targetMethod = methodMap.get(targetVid);
                            if (targetMethod != null) {
                                foreEndBaseEdge.setTargetType(NodeSourceEnum.LOCAL.getCode());
                            } else {
                                Set<String> importFilePath = fileImportFile.get(filePath);
                                boolean find = false;
                                if (CollectionUtils.isNotEmpty(importFilePath)) {
                                    for (String path : importFilePath) {
                                        targetVid = Md5Util.scriptMethodNameToMd5(invoke.getMethodName(), path);
                                        targetMethod = methodMap.get(targetVid);
                                        if (targetMethod != null) {
                                            find = true;
                                            foreEndBaseEdge.setTargetType(NodeSourceEnum.LOCAL.getCode());
                                            break;
                                        }
                                    }
                                }
                                if (!find) {
                                    foreEndBaseEdge.setTargetType(NodeSourceEnum.UNKNOWN.getCode());
                                }
                            }
                            methodMethodEdgesMap.put(key, foreEndBaseEdge);
                            methodMethodEdges.add(foreEndBaseEdge);
                        } else {
                            methodMethodEdgesMap.get(key).addLine(invoke.getStartLine());
                        }
                    }
                }
            }
            if (CollectionUtils.isNotEmpty(methodMethodEdges)) {
                methodInvokeMethod.put(filePath, methodMethodEdges);
            }
        }

        return foreEndAnalysisRes;
    }

    public static String trimMarks(String str) {
        char first = str.charAt(0);
        char last = str.charAt(str.length() - 1);
        if ((first == '"' || first == '\'') && first == last) {
            str = str.substring(1, str.length() - 1);
        }
        return str;
    }

    public static int countParentReferences(String input) {
        if (input == null || input.isEmpty()) {
            return 0;
        }
        int count = 0;
        int searchIndex = 0;
        String target = "../";
        int step = target.length();
        while (true) {
            int foundIndex = input.indexOf(target, searchIndex);
            if (foundIndex == -1) {
                break;
            }
            count++;
            searchIndex = foundIndex + step;
        }
        return count;
    }

    public static void initScriptNodeMap(ScriptNode scriptNode, String filePath,
                                         Map<String, ScriptMethod> scriptMethodMap,
                                         Map<String, ScriptVariable> stringVariableMap,
                                         ForeEndAnalysisRes foreEndAnalysisRes) {
        for (ScriptVariable scriptVariable : scriptNode.getVariables()) {
            stringVariableMap.put(Md5Util.scriptVariablesToMd5(scriptVariable, filePath), scriptVariable);
            foreEndAnalysisRes.addFileDefineField(filePath, Md5Util.scriptVariablesToMd5(scriptVariable, filePath));
            for (ScriptMethod scriptMethod : scriptVariable.getMethods()) {
                scriptMethodMap.put(Md5Util.scriptMethodToMd5(scriptMethod, filePath), scriptMethod);
                foreEndAnalysisRes.addFileDefineMethod(filePath, Md5Util.scriptMethodToMd5(scriptMethod, filePath));
            }
        }

        for (ScriptMethod scriptMethod : scriptNode.getMethods()) {
            scriptMethodMap.put(Md5Util.scriptMethodToMd5(scriptMethod, filePath), scriptMethod);
            foreEndAnalysisRes.addFileDefineMethod(filePath, Md5Util.scriptMethodToMd5(scriptMethod, filePath));
        }

        for (ScriptClass scriptClass : scriptNode.getClasses()) {
            for (ScriptMethod scriptMethod : scriptClass.getMethods()) {
                scriptMethodMap.put(Md5Util.scriptMethodToMd5(scriptMethod, filePath), scriptMethod);
                foreEndAnalysisRes.addFileDefineMethod(filePath, Md5Util.scriptMethodToMd5(scriptMethod, filePath));
            }
            for (ScriptVariable scriptVariable : scriptClass.getVariables()) {
                stringVariableMap.put(Md5Util.scriptVariablesToMd5(scriptVariable, filePath), scriptVariable);
                foreEndAnalysisRes.addFileDefineField(filePath, Md5Util.scriptVariablesToMd5(scriptVariable, filePath));
            }
        }
    }


    public static void initListByHtmlNodes(List<HtmlNode> analysisHtmlNodes,
                                           List<HtmlNode> htmlNodesRes,
                                           List<ScssNode> scssNodesRes,
                                           List<ScriptNode> scriptNodesRes,
                                           List<Integer> commentLines,
                                           String filePath,
                                           Map<String, List<ScriptImport>> fileImportFileMap) {
        for (HtmlNode htmlNode : analysisHtmlNodes) {
            HtmlNode copyHtmlNode = htmlNode.deepCopy();
            copyHtmlNode.setFilePath(filePath);
            if (copyHtmlNode.getTagType() != null && copyHtmlNode.getTagType().equals(ElementTypeEnum.ELEMENT.getCode())) {
                htmlNodesRes.add(copyHtmlNode);
            }
            if (CollectionUtils.isNotEmpty(htmlNode.getScssNodes())) {
                if (CollectionUtils.isNotEmpty(htmlNode.getCommentLines())) {
                    commentLines.addAll(htmlNode.getCommentLines());
                }
                for (ScssNode scssNode : htmlNode.getScssNodes()) {
                    ScssNode copyScssNode = scssNode.deepCopy();
                    copyScssNode.setFilePath(filePath);
                    scssNodesRes.add(copyScssNode);
                }
            }
            if (CollectionUtils.isNotEmpty(htmlNode.getScriptStates())) {
                if (CollectionUtils.isNotEmpty(htmlNode.getCommentLines())) {
                    commentLines.addAll(htmlNode.getCommentLines());
                }
                for (ScriptNode scriptNode : htmlNode.getScriptStates()) {
                    ScriptNode copyScriptNode = scriptNode.deepCopy();
                    copyScriptNode.setFilePath(filePath);
                    scriptNodesRes.add(copyScriptNode);
                    if (CollectionUtils.isNotEmpty(copyScriptNode.getImports())) {
                        fileImportFileMap.put(filePath, copyScriptNode.getImports());
                    }

                    if (CollectionUtils.isNotEmpty(scriptNode.getHtmlNodes())) {
                        initListByHtmlNodes(scriptNode.getHtmlNodes(), htmlNodesRes, scssNodesRes, scriptNodesRes, commentLines, filePath, fileImportFileMap);
                    }
                }
            }
            if (CollectionUtils.isNotEmpty(htmlNode.getChildren())) {
                initListByHtmlNodes(htmlNode.getChildren(), htmlNodesRes, scssNodesRes, scriptNodesRes, commentLines, filePath, fileImportFileMap);
            }
        }
    }

    public static void initListByScriptNode(ScriptNode analysisScriptNode,
                                            List<HtmlNode> htmlNodesRes,
                                            List<ScssNode> scssNodesRes,
                                            List<ScriptNode> scriptNodesRes,
                                            List<Integer> commentLines,
                                            String filePath,
                                            Map<String, List<ScriptImport>> fileImportFileMap) {
        ScriptNode copyScriptNode = analysisScriptNode.deepCopy();
        copyScriptNode.setFilePath(filePath);
        scriptNodesRes.add(copyScriptNode);
        if (CollectionUtils.isNotEmpty(copyScriptNode.getImports())) {
            fileImportFileMap.put(filePath, copyScriptNode.getImports());
        }
        if (CollectionUtils.isNotEmpty(analysisScriptNode.getHtmlNodes())) {
            initListByHtmlNodes(analysisScriptNode.getHtmlNodes(), htmlNodesRes, scssNodesRes, scriptNodesRes, commentLines, filePath, fileImportFileMap);
        }
    }

    public static void initListByScssNode(ScssNode analysisScssNode,
                                          List<ScssNode> scssNodesRes,
                                          String filePath) {
        ScssNode copyScssNode = analysisScssNode.deepCopy();
        copyScssNode.setFilePath(filePath);
        scssNodesRes.add(copyScssNode);
    }

}
