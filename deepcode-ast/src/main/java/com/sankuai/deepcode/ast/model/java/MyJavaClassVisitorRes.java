package com.sankuai.deepcode.ast.model.java;

import com.sankuai.deepcode.ast.model.base.CodeView;
import com.sankuai.deepcode.ast.model.java.ClassNode;
import lombok.Data;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

@Data
public class MyJavaClassVisitorRes {
    private com.sankuai.deepcode.ast.model.java.ClassNode classNode = new com.sankuai.deepcode.ast.model.java.ClassNode();
    private List<ClassNode> inClassNode = new ArrayList<>();
    private Set<String> imports = new HashSet<>();
    private List<CodeView> codeViews = new ArrayList<>();
}
