package com.sankuai.deepcode.ast.model.python3;

import com.sankuai.deepcode.ast.model.python3.PythonClassNode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 * Package: com.sankuai.deepcode.ast.model.python3
 * Description:
 *
 * <AUTHOR>
 * @since 2024/12/4 15:30
 */
@Setter
@Getter
@ToString(callSuper = true)
@Accessors(chain = true)
public class PythonDecoratorClass extends PythonClassNode {
    private Boolean isDecorator = Boolean.TRUE;
}
