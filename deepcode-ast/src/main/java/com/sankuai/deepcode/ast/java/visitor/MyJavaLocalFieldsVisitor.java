package com.sankuai.deepcode.ast.java.visitor;

import com.sankuai.deepcode.ast.java.gen.JavaParser;
import com.sankuai.deepcode.ast.java.gen.JavaParserBaseVisitor;
import com.sankuai.deepcode.ast.java.visitor.MyJavaFormalParametersVistor;
import com.sankuai.deepcode.ast.java.visitor.MyJavaLocalVariableVistor;
import com.sankuai.deepcode.ast.java.visitor.MyJavaTypeTypeVistor;
import com.sankuai.deepcode.ast.model.java.*;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class MyJavaLocalFieldsVisitor extends JavaParserBaseVisitor<Object> {

    private Map<String, FieldNode> classFields = new HashMap<>();
    private Map<String, List<FieldNode>> fieldNames = new HashMap<>();
    private MethodNode methodNode;

    public MyJavaLocalFieldsVisitor(ClassNode myClassNode, MethodNode myMethodNode, Map<String, List<FieldNode>> nameFieldMap) {
        methodNode = myMethodNode;
        fieldNames = nameFieldMap;
        putLocalFieldName(myClassNode.getFieldNodes());
    }

    @Getter
    private List<LocalField> localFields = new ArrayList<>();

    @Getter
    private int complexity = 1;

    public void putLocalFieldName(List<FieldNode> fieldNodes) {
        for (FieldNode fieldNode : fieldNodes) {
            classFields.put(fieldNode.getFieldName(), fieldNode);
            LocalField localField = new LocalField();
            localField.setName(fieldNode.getFieldName());
            localField.setType(fieldNode.getFieldType());
            localField.setStartLine(fieldNode.getStartLine());
            localFields.add(localField);
        }
    }


    @Override
    public Object visitFormalParameters(JavaParser.FormalParametersContext ctx) {
        MyJavaFormalParametersVistor vistor = new MyJavaFormalParametersVistor();
        vistor.visitFormalParameters(ctx);
        for (JavaParam paramNode : vistor.getParams()) {
            LocalField localField = new LocalField();
            localField.setName(paramNode.getName());
            localField.setType(paramNode.getType());
            localField.setStartLine(ctx.getStart().getLine());
            localFields.add(localField);
        }
        return visitChildren(ctx);
    }

    @Override
    public Object visitLocalVariableDeclaration(JavaParser.LocalVariableDeclarationContext ctx) {
        MyJavaLocalVariableVistor visitor = new MyJavaLocalVariableVistor();
        visitor.visit(ctx);
        for (LocalFieldNode localFieldNode : visitor.getLocalFieldNodes()) {
            LocalField localField = new LocalField();
            localField.setName(localFieldNode.getFieldName());
            localField.setType(localFieldNode.getFieldType());
            localField.setStartLine(localFieldNode.getStartLine());
            localFields.add(localField);
        }
        return visitChildren(ctx);
    }

    @Override
    public Object visitVariableDeclaratorId(JavaParser.VariableDeclaratorIdContext ctx) {
        if (ctx.parent instanceof JavaParser.EnhancedForControlContext) {
            JavaParser.TypeTypeContext typeTypeContext = ((JavaParser.EnhancedForControlContext) ctx.parent).typeType();
            if (null != typeTypeContext) {
                com.sankuai.deepcode.ast.java.visitor.MyJavaTypeTypeVistor vistor = new MyJavaTypeTypeVistor();
                vistor.visit(typeTypeContext);
                LocalField localField = new LocalField();
                localField.setName(ctx.getText());
                localField.setType(vistor.getType());
                localField.setStartLine(ctx.getStart().getLine());
                localFields.add(localField);
            }
        }
        return visitChildren(ctx);
    }

    @Override
    public Object visitCatchClause(JavaParser.CatchClauseContext ctx) {
        LocalField localField = new LocalField();
        localField.setName(ctx.getText());
        localField.setType(ctx.catchType().getText());
        localField.setStartLine(ctx.getStart().getLine());
        localFields.add(localField);
        return visitChildren(ctx);
    }


    @Override
    public Object visitIdentifier(JavaParser.IdentifierContext ctx) {
        if (!ctx.getText().equals(methodNode.getMethodName())) {
            //todo 未添加静态变量和类型的判断
            String text = ctx.getText();
            FieldNode fieldNode = classFields.get(text);
            if (null != fieldNode) {
                fieldNode.getQuotedLines().add(ctx.getStart().getLine());
                methodNode.getQuoteFields().add(fieldNode);
            } else {
                List<FieldNode> fieldNodes = fieldNames.get(text);
                if (null != fieldNodes) {
                    for (FieldNode field : fieldNodes) {
                        field.getQuotedLines().add(ctx.getStart().getLine());
                    }
                    methodNode.getQuoteFields().addAll(fieldNodes);
                }
            }
        }
        return visitChildren(ctx);
    }

    @Override
    public Object visitStatement(JavaParser.StatementContext ctx) {
        if (null != ctx.start) {
            String text = ctx.start.getText();
            //todo  三元递归 else if 等重复
            if (StringUtils.isNotEmpty(text)) {
                if (text.equals("if")) {
                    complexity++;
                } else if (text.equals("else")) {
                    complexity++;
                } else if (text.equals("for")) {
                    complexity++;
                } else if (text.equals("while")) {
                    complexity++;
                } else if (text.equals("do")) {
                    complexity++;
                } else if (text.equals("case")) {
                    complexity++;
                } else if (text.equals("&&")) {
                    complexity++;
                } else if (text.equals("||")) {
                    complexity++;
                } else if (text.equals("catch")) {
                    complexity++;
                } else if (text.equals("finally")) {
                    complexity++;
                } else if (text.equals("break")) {
                    complexity++;
                } else if (text.equals("continue")) {
                    complexity++;
                }
            }
        }
        return visitChildren(ctx);
    }


}
