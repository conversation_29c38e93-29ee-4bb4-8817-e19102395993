package com.sankuai.deepcode.ast.scss.visitor;

import com.sankuai.deepcode.ast.enums.scss.ImportTypeEnum;
import com.sankuai.deepcode.ast.model.scss.ScssImport;
import com.sankuai.deepcode.ast.model.scss.ScssUse;
import com.sankuai.deepcode.ast.scss.gen.ScssParser;
import com.sankuai.deepcode.ast.scss.gen.ScssParserBaseVisitor;
import com.sankuai.deepcode.ast.scss.visitor.MyParametersVisitor;
import lombok.Getter;

public class MyImportDeclarationVisitor extends ScssParserBaseVisitor<Object> {

    @Getter
    private ScssImport scssImport;

    @Override
    public Object visitImportDeclaration(ScssParser.ImportDeclarationContext ctx) {
        scssImport = new ScssImport();
        if (ctx.Import() != null) {
            scssImport.setType(ImportTypeEnum.IMPORT.getCode());
            scssImport.setImportPath(ctx.importPath().getText());
        } else if (ctx.Use() != null) {
            scssImport.setType(ImportTypeEnum.USE.getCode());
            scssImport.setImportPath(ctx.importPath().getText());
            ScssUse scssUse = new ScssUse();
            if (ctx.asClause() != null) {
                if (ctx.asClause().Times() != null) {
                    scssUse.setAsName(ctx.asClause().Times().getText());
                } else {
                    scssUse.setAsName(ctx.asClause().identifier().getText());
                }
            }
            if (ctx.withClause() != null && ctx.withClause().parameters() != null) {
                com.sankuai.deepcode.ast.scss.visitor.MyParametersVisitor visitor = new MyParametersVisitor();
                visitor.visitParameters(ctx.withClause().parameters());
                scssUse.setParameters(visitor.getParameters());
            }
            scssImport.setScssUse(scssUse);
        } else if (ctx.Forward() != null) {
            scssImport.setType(ImportTypeEnum.FORWARD.getCode());
            scssImport.setImportPath(ctx.importPath().getText());
        } else if (ctx.Require() != null) {
            scssImport.setType(ImportTypeEnum.REQUIRE.getCode());
            scssImport.setImportPath(ctx.importPath().getText());
        }
        return null;
    }


}
