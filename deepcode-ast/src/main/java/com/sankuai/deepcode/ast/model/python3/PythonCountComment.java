package com.sankuai.deepcode.ast.model.python3;

import lombok.Data;

/**
 * Package: com.sankuai.deepcode.ast.model.python3
 * Description: Python注释计数和分配辅助类，用于管理注释到各个节点的分配逻辑
 *
 * <AUTHOR>
 * @since 2024/12/9 15:00
 */
@Data
public class PythonCountComment implements Comparable<PythonCountComment> {
    private int startLine;
    private int endLine;
    /**
     * 注释类型：
     * - module: 模块级注释
     * - class: 类级注释
     * - method: 方法级注释（包括模块级函数）
     * - classMethod: 类内方法注释
     * - classField: 类内字段注释
     * - moduleField: 模块级字段注释
     */
    private String type;
    
    private PythonModuleNode moduleNode;
    private PythonClassNode classNode;
    private PythonFunctionNode functionNode;
    private PythonMethodNode methodNode;
    private PythonParameterNode parameterNode;

    @Override
    public int compareTo(PythonCountComment other) {
        return Integer.compare(this.startLine, other.startLine);
    }
}
