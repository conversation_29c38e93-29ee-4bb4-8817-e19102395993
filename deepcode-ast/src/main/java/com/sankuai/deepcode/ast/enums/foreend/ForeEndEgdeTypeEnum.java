package com.sankuai.deepcode.ast.enums.foreend;

public enum ForeEndEgdeTypeEnum {
    HTML_INVOKE_METHOD(0, "html调用方法"),
    HTML_QUOTE_FIELD(1, "html引用变量"),
    METHOD_INVOKE_METHOD(2, "方法调用方法"),
    HTML_QUOTE_STYLE(3, "html引用样式");

    private int code;
    private String msg;

    ForeEndEgdeTypeEnum(int code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public int getCode() {
        return this.code;
    }

    public String getMsg() {
        return this.msg;
    }


    @Override
    public String toString() {
        return "DiffTypeEnum{" +
                "code=" + code +
                ", msg='" + msg + '\'' +
                '}';
    }
}
