package com.sankuai.deepcode.ast.java.visitor;

import com.sankuai.deepcode.ast.enums.java.ClassTypeEnum;
import com.sankuai.deepcode.ast.java.gen.JavaParser;
import com.sankuai.deepcode.ast.java.gen.JavaParserBaseVisitor;
import com.sankuai.deepcode.ast.java.visitor.MyJavaAnnotationVisitor;
import com.sankuai.deepcode.ast.java.visitor.MyJavaClassDeclarationVisitor;
import com.sankuai.deepcode.ast.java.visitor.MyJavaEnumDeclarationVisitor;
import com.sankuai.deepcode.ast.java.visitor.MyJavaInterfaceDeclarationVisitor;
import com.sankuai.deepcode.ast.model.java.ClassNode;
import com.sankuai.deepcode.ast.model.java.JavaAnnotation;
import com.sankuai.deepcode.ast.util.Md5Util;
import lombok.Getter;
import org.antlr.v4.runtime.ParserRuleContext;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;

public class MyAnnotationTypeDeclarationVisitor extends JavaParserBaseVisitor<Object> {

    private ClassNode parentClassNode;
    @Getter
    private ClassNode classNode = new ClassNode();
    private String packageName;

    public MyAnnotationTypeDeclarationVisitor(ClassNode myParentClassNode, ClassNode myClassNode, String myPackageName) {
        parentClassNode = myParentClassNode;
        classNode = myClassNode;
        packageName = myPackageName;
    }

    private String access = "";
    private List<JavaAnnotation> annotations = new ArrayList<>();

    @Override
    public Object visitAnnotationTypeDeclaration(JavaParser.AnnotationTypeDeclarationContext ctx) {
        classNode.setClassType(ClassTypeEnum.ANNOTATION.getCode());
        classNode.setStartLine(ctx.getStart().getLine());
        classNode.setEndLine(ctx.getStop().getLine());
        if (null == parentClassNode) {
            if (StringUtils.isEmpty(packageName)) {
                classNode.setClassName(ctx.identifier().getText());
            } else {
                classNode.setClassName(packageName + "." + ctx.identifier().getText());
            }
        } else {
            classNode.setClassName(parentClassNode.getClassName());
            if (StringUtils.isEmpty(parentClassNode.getInClassName())) {
                classNode.setInClassName(ctx.identifier().getText());
            } else {
                classNode.setInClassName(parentClassNode.getInClassName() + "$" + ctx.identifier().getText());
            }
        }
        if (ctx.annotationTypeBody() != null) {
            visitAnnotationTypeBody(ctx.annotationTypeBody());
        }
        return null;
    }

    @Override
    public Object visitAnnotationTypeBody(JavaParser.AnnotationTypeBodyContext ctx) {
        for (JavaParser.AnnotationTypeElementDeclarationContext annotationTypeElementDeclarationContext : ctx.annotationTypeElementDeclaration()) {
            visitAnnotationTypeElementDeclaration(annotationTypeElementDeclarationContext);
        }
        return null;
    }

    @Override
    public Object visitAnnotationTypeElementDeclaration(JavaParser.AnnotationTypeElementDeclarationContext ctx) {
        if (CollectionUtils.isNotEmpty(ctx.modifier())) {
            for (JavaParser.ModifierContext modifierContext : ctx.modifier()) {
                if (null != modifierContext.classOrInterfaceModifier()) {
                    if (null != modifierContext.classOrInterfaceModifier().annotation()) {
                        com.sankuai.deepcode.ast.java.visitor.MyJavaAnnotationVisitor visitor = new MyJavaAnnotationVisitor();
                        visitor.visit(modifierContext.classOrInterfaceModifier().annotation());
                        annotations.add(visitor.getAnnotation());
                    } else {
                        access += " " + modifierContext.classOrInterfaceModifier().getText();
                    }
                }
            }
            access = access.trim();
            visitAnnotationTypeElementRest(ctx.annotationTypeElementRest());
        }
        return null;
    }

    @Override
    public Object visitAnnotationTypeElementRest(JavaParser.AnnotationTypeElementRestContext ctx) {
        if (ctx.typeType() != null && ctx.annotationTypeDeclaration() != null) {
            ClassNode inClassNode = initInClassNode(classNode, ctx.annotationTypeDeclaration());
            MyAnnotationTypeDeclarationVisitor visitor = new MyAnnotationTypeDeclarationVisitor(classNode, inClassNode, packageName);
            visitor.visit(ctx.annotationTypeDeclaration());
            classNode.getInClassNodes().add(inClassNode);
        } else if (ctx.classDeclaration() != null) {
            ClassNode inClassNode = initInClassNode(classNode, ctx.classDeclaration());
            com.sankuai.deepcode.ast.java.visitor.MyJavaClassDeclarationVisitor visitor = new MyJavaClassDeclarationVisitor(classNode, inClassNode, packageName);
            visitor.visit(ctx.classDeclaration());
            classNode.getInClassNodes().add(inClassNode);
        } else if (ctx.interfaceDeclaration() != null) {
            ClassNode inClassNode = initInClassNode(classNode, ctx.interfaceDeclaration());
            com.sankuai.deepcode.ast.java.visitor.MyJavaInterfaceDeclarationVisitor visitor = new MyJavaInterfaceDeclarationVisitor(classNode, inClassNode, packageName);
            visitor.visit(ctx.interfaceDeclaration());
            classNode.getInClassNodes().add(inClassNode);
        } else if (ctx.enumDeclaration() != null) {
            ClassNode inClassNode = initInClassNode(classNode, ctx.enumDeclaration());
            com.sankuai.deepcode.ast.java.visitor.MyJavaEnumDeclarationVisitor visitor = new MyJavaEnumDeclarationVisitor(classNode, inClassNode, packageName);
            visitor.visit(ctx.enumDeclaration());
            classNode.getInClassNodes().add(inClassNode);
        } else if (ctx.annotationTypeDeclaration() != null) {
            ClassNode inClassNode = initInClassNode(classNode, ctx.annotationTypeDeclaration());
            MyAnnotationTypeDeclarationVisitor visitor = new MyAnnotationTypeDeclarationVisitor(classNode, inClassNode, packageName);
            visitor.visit(ctx.annotationTypeDeclaration());
            classNode.getInClassNodes().add(inClassNode);
        }
        //todo   recordDeclaration Java17暂不处理
        return null;
    }

    public ClassNode initInClassNode(ClassNode classNode, ParserRuleContext parserRuleContext) {
        ClassNode inClassNode = new ClassNode();
        inClassNode.setFileVid(Md5Util.filePathToMd5(classNode.getClassPath()));
        inClassNode.setClassPath(classNode.getClassPath());
        inClassNode.setClassName(classNode.getClassName());
        inClassNode.setAccess(access);
        inClassNode.setAnnotations(annotations);
        inClassNode.setStartLine(parserRuleContext.start.getLine());
        inClassNode.setEndLine(parserRuleContext.stop.getLine());
        return inClassNode;
    }

}
