package com.sankuai.deepcode.ast.model.python3;

import com.google.common.collect.Lists;
import com.sankuai.deepcode.ast.enums.ChangeTypeEnum;
import com.sankuai.deepcode.ast.model.base.BaseNode;
import com.sankuai.deepcode.ast.model.base.LocationInfo;
import com.sankuai.deepcode.ast.util.Md5Util;
import lombok.*;
import lombok.experimental.Accessors;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

/**
 * Package: com.sankuai.deepcode.ast.model.python3
 * Description: Python 函数节点定义
 *
 * <AUTHOR>
 * @since 2024/12/4 11:09
 */
@Getter
@Setter
@Accessors(chain = true)
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class PythonFunctionNode extends BaseNode {
    // 文件名称
    String fileName;
    // 模块名称
    String moduleName;
    //  模块路径【会包含当前方法名称】
    String modulePath;
    // 函数名
    String name;
    // 函数注释
    String docString;
    // 是否为异步函数
    Boolean async;
    // 参数数量
    Integer parameterCount;
    // 函数开始行号
    LocationInfo start;
    // 函数结束行号
    LocationInfo end;
    // 是否有不定参数
    Boolean hasArgs;
    // 是否有关键字参数
    Boolean hasKwargs;
    // 是否为生成器函数
    Boolean isGenerator;
    // 位置参数索引
    Integer slashIndex = -1;
    // 星号参数索引
    Integer starIndex = -1;
    // 函数圈复杂度
    Integer complexity = 1;
    // 函数体
    String funcBody;

    PythonBlockComment functionComment;

    // 其他块级注释
    List<PythonBlockComment> otherBlockComments = Lists.newArrayList();

    // 行级注释
    List<PythonComment> comments = Lists.newArrayList();

    // 函数装饰器列表
    List<PythonDecoratorFunc> decorators = Lists.newArrayList();
    // 函数参数列表
    List<PythonParameterNode> parameters = Lists.newArrayList();
    // 内部导入列表
    List<PythonImportNode> innerImports = Lists.newArrayList();
    // 调用节点列表
    List<PythonFunctionCallNode> callNodes = Lists.newArrayList();
    // 内部函数列表
    List<PythonFunctionNode> innerFunctions = Lists.newArrayList();
    // 函数返回值列表
    List<PythonReturn> returnNode = Lists.newArrayList();
    // 函数定义时的返回值类型
    private String functionReturnType;

    /**
     * FIXME: 暂时参照 Java 变更内容进行定义，后期需要依据实际情况进行调整
     */
    private ChangeTypeEnum changeType = ChangeTypeEnum.DEFAULT;
    private List<Integer> changeLines;

    public String getFullPath() {
        return moduleName + "." + name;
    }

    /**
     * 拷贝构造函数
     *
     * @param node
     */
    public PythonFunctionNode(PythonFunctionNode node) {
        if (node == null) {
            return;
        }
        this.moduleName = node.moduleName;
        this.name = node.name;
        this.docString = node.docString;
        this.async = node.async;
        this.parameterCount = node.parameterCount;
        this.start = node.start;
        this.end = node.end;
        this.hasArgs = node.hasArgs;
        this.hasKwargs = node.hasKwargs;
        this.isGenerator = node.isGenerator;
        this.slashIndex = node.slashIndex;
        this.starIndex = node.starIndex;
        this.decorators = node.decorators;
        this.parameters = node.parameters;
        this.innerImports = node.innerImports;
        this.returnNode = node.returnNode;
        this.callNodes = node.callNodes;
        this.funcBody = node.funcBody;
        this.complexity = node.complexity;

        this.fileName = node.fileName;
        this.modulePath = node.modulePath;
        this.functionComment = node.functionComment;
        this.otherBlockComments = node.otherBlockComments != null ? Lists.newArrayList(node.otherBlockComments) : Lists.newArrayList();
        this.comments = node.comments != null ? Lists.newArrayList(node.comments) : Lists.newArrayList();
        this.innerFunctions = node.innerFunctions != null ? Lists.newArrayList(node.innerFunctions) : Lists.newArrayList();
        this.functionReturnType = node.functionReturnType;

        this.changeType = node.changeType;
        this.changeLines = node.changeLines;
    }

    @Override
    public String getVid() {
        if (StringUtils.isNotEmpty(getModulePath())) {

            return Md5Util.stringToMd5(getModulePath());
        }
        return StringUtils.EMPTY;
    }
}
