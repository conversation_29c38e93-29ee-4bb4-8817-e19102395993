package com.sankuai.deepcode.ast.enums;

public enum CheckTypeEnum {
    DEFAULT(0, "默认无变更"),

    DELETE(1, "不存在"),

    CHANGE(2, "有变更");

    private int code;
    private String msg;


    CheckTypeEnum(int code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public int getCode() {
        return this.code;
    }

    public String getMsg() {
        return this.msg;
    }


    @Override
    public String toString() {
        return "DiffTypeEnum{" +
                "code=" + code +
                ", msg='" + msg + '\'' +
                '}';
    }
}
