package com.sankuai.deepcode.ast.java.visitor;

import com.sankuai.deepcode.ast.java.gen.JavaParser;
import com.sankuai.deepcode.ast.java.gen.JavaParserBaseVisitor;
import com.sankuai.deepcode.ast.java.visitor.MyJavaConstructorDeclarationVisitor;
import com.sankuai.deepcode.ast.model.java.ClassNode;
import com.sankuai.deepcode.ast.model.java.MethodNode;
import lombok.Getter;

public class MyJavaGenericConstructorDeclarationVisitor extends JavaParserBaseVisitor<Object> {
    private ClassNode classNode = new ClassNode();

    public MyJavaGenericConstructorDeclarationVisitor(ClassNode myClassNode) {
        classNode = myClassNode;
    }

    @Getter
    private MethodNode methodNode;

    @Override
    public Object visitGenericConstructorDeclaration(JavaParser.GenericConstructorDeclarationContext ctx) {
        com.sankuai.deepcode.ast.java.visitor.MyJavaConstructorDeclarationVisitor visitor = new MyJavaConstructorDeclarationVisitor(classNode);
        visitor.visit(ctx);
        methodNode = visitor.getMethodNode();
        return null;
    }

}
