package com.sankuai.deepcode.ast.scss.visitor;

import com.sankuai.deepcode.ast.model.scss.ScssVariable;
import com.sankuai.deepcode.ast.scss.gen.ScssParser;
import com.sankuai.deepcode.ast.scss.gen.ScssParserBaseVisitor;
import lombok.Getter;

public class MyContentDeclarationVisitor extends ScssParserBaseVisitor<Object> {

    @Getter
    private ScssVariable scssVariable;


    @Override
    public Object visitContentDeclaration(ScssParser.ContentDeclarationContext ctx) {
        //todo内容块 声明

//        contentDeclaration
//        : Content (Lparen parameters Rparen)? Semi?
//        ;
        return null;
    }


}
