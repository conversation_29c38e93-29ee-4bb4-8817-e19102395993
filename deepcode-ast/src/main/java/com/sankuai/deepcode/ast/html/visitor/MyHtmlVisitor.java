package com.sankuai.deepcode.ast.html.visitor;

import com.sankuai.deepcode.ast.enums.html.ElementTypeEnum;
import com.sankuai.deepcode.ast.html.gen.HTMLParser;
import com.sankuai.deepcode.ast.html.gen.HTMLParserBaseVisitor;
import com.sankuai.deepcode.ast.model.html.HtmlNode;
import lombok.Getter;
import org.apache.commons.collections4.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

public class MyHtmlVisitor extends HTMLParserBaseVisitor<Object> {

    @Getter
    private List<HtmlNode> htmlNodes = new ArrayList<>();

    @Override
    public Object visitHtmlDocument(HTMLParser.HtmlDocumentContext ctx) {
        if (ctx.scriptletOrSeaWs() != null && CollectionUtils.isNotEmpty(ctx.scriptletOrSeaWs())) {
            for (HTMLParser.ScriptletOrSeaWsContext scriptletOrSeaWsContext : ctx.scriptletOrSeaWs()) {
                if (scriptletOrSeaWsContext.SEA_WS() != null) {
                    continue;
                } else if (scriptletOrSeaWsContext.SCRIPTLET() != null) {
                    HtmlNode htmlNode = new HtmlNode();
                    htmlNode.setTagType(ElementTypeEnum.SCRIPTLET.getCode());
                    htmlNode.setTagDepth(0);
                    htmlNode.setTagIndex(htmlNodes.size());
                    htmlNode.setStartLine(scriptletOrSeaWsContext.start.getLine());
                    htmlNode.setEndLine(scriptletOrSeaWsContext.stop.getLine());
                    htmlNode.setBody(ctx.getText());
                    htmlNodes.add(htmlNode);
                }
            }
        }
        if (ctx.XML() != null) {
            HtmlNode htmlNode = new HtmlNode();
            htmlNode.setTagType(ElementTypeEnum.XMLDESC.getCode());
            htmlNode.setTagDepth(0);
            htmlNode.setTagIndex(htmlNodes.size());
            htmlNode.setStartLine(ctx.XML().getSymbol().getLine());
            htmlNode.setEndLine(ctx.XML().getSymbol().getLine());
            htmlNode.setBody(ctx.getText());
            htmlNodes.add(htmlNode);
        }
        if (ctx.DTD() != null) {
            HtmlNode htmlNode = new HtmlNode();
            htmlNode.setTagType(ElementTypeEnum.DTDDESC.getCode());
            htmlNode.setTagDepth(0);
            htmlNode.setTagIndex(htmlNodes.size());
            htmlNode.setStartLine(ctx.DTD().getSymbol().getLine());
            htmlNode.setEndLine(ctx.DTD().getSymbol().getLine());
            htmlNode.setBody(ctx.getText());
            htmlNodes.add(htmlNode);
        }

        if (ctx.htmlElements() != null && CollectionUtils.isNotEmpty(ctx.htmlElements())) {
            for (HTMLParser.HtmlElementsContext htmlElementsContext : ctx.htmlElements()) {
                HtmlNode htmlNode = new HtmlNode();
                htmlNode.setTagDepth(0);
                htmlNode.setTagIndex(htmlNodes.size());
                htmlNode.setBody(ctx.getText());
                if (htmlElementsContext.htmlElement() != null) {
                    if (CollectionUtils.isNotEmpty(htmlElementsContext.htmlElement().TAG_NAME())) {
                        htmlNode.setTagName(htmlElementsContext.htmlElement().TAG_NAME().get(0).getText());
                    }
                    htmlNode.setStartLine(htmlElementsContext.htmlElement().getStart().getLine());
                    htmlNode.setEndLine(htmlElementsContext.htmlElement().getStop().getLine());
                    MyHtmlElementVisitor myHtmlElementVisitor = new MyHtmlElementVisitor(htmlNode);
                    myHtmlElementVisitor.visit(htmlElementsContext.htmlElement());
                }
                if (CollectionUtils.isNotEmpty(htmlElementsContext.htmlMisc())) {
                    for (HTMLParser.HtmlMiscContext htmlMiscContext : htmlElementsContext.htmlMisc()) {
                        if (htmlMiscContext.SEA_WS() != null) {
                            continue;
                        } else if (htmlMiscContext.htmlComment() != null) {
                            if (htmlMiscContext.htmlComment().HTML_COMMENT() != null) {
                                htmlNode.getCommentLines().add(htmlMiscContext.htmlComment().HTML_COMMENT().getSymbol().getLine());
                            } else if (htmlMiscContext.htmlComment().HTML_CONDITIONAL_COMMENT() != null) {
                                htmlNode.getCommentLines().add(htmlMiscContext.htmlComment().HTML_CONDITIONAL_COMMENT().getSymbol().getLine());
                            }
                        }
                    }
                }
                htmlNodes.add(htmlNode);
            }
        }
        return null;
    }

}
