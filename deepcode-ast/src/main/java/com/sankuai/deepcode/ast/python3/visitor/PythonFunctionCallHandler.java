package com.sankuai.deepcode.ast.python3.visitor;

import com.sankuai.deepcode.ast.model.python3.PythonFunctionCallNode;
import com.sankuai.deepcode.ast.model.python3.ScopeManager;
import com.sankuai.deepcode.ast.python3.gen.PythonParser;

import java.util.List;

/**
 * Python函数调用统一处理器
 * 用于在各个visitor中复用函数调用解析逻辑，避免重复代码
 * 
 * <AUTHOR>
 * @since 2025/1/13
 */
public class PythonFunctionCallHandler {
    
    private final String fileName;
    private final String modulePath;
    private final ScopeManager scopeManager;

    public PythonFunctionCallHandler(String fileName, String modulePath, ScopeManager scopeManager) {
        this.fileName = fileName;
        this.modulePath = modulePath;
        this.scopeManager = scopeManager;
    }
    
    /**
     * 处理函数调用上下文，返回解析出的函数调用节点列表
     * 
     * @param ctx 函数调用上下文
     * @return 函数调用节点列表
     */
    public List<PythonFunctionCallNode> handleFunctionCall(PythonParser.Function_callContext ctx) {
        PythonFunctionCallVisitor callVisitor = new PythonFunctionCallVisitor(fileName, modulePath, scopeManager);
        callVisitor.visit(ctx);
        return callVisitor.getFunctionCallNodes();
    }
    
    /**
     * 处理Primary上下文中的函数调用，返回解析出的函数调用节点列表
     * 
     * @param ctx Primary上下文
     * @return 函数调用节点列表
     */
    public List<PythonFunctionCallNode> handlePrimaryFunctionCall(PythonParser.PrimaryContext ctx) {
        PythonFunctionCallVisitor callVisitor = new PythonFunctionCallVisitor(fileName, modulePath, scopeManager);
        callVisitor.visitPrimary(ctx);
        return callVisitor.getFunctionCallNodes();
    }
    
    /**
     * 检查Primary上下文是否包含函数调用
     * 
     * @param ctx Primary上下文
     * @return 是否包含函数调用
     */
    public static boolean containsFunctionCall(PythonParser.PrimaryContext ctx) {
        if (ctx.primary_suffix() == null) {
            return false;
        }
        
        for (PythonParser.Primary_suffixContext suffix : ctx.primary_suffix()) {
            if (suffix.LPAR() != null) {
                return true;
            }
        }
        return false;
    }
    
    /**
     * 从函数调用节点列表中获取最后一个函数调用的信息
     * 用于变量类型推断等场景
     * 
     * @param functionCallNodes 函数调用节点列表
     * @return 最后一个函数调用节点，如果列表为空则返回null
     */
    public static PythonFunctionCallNode getLastFunctionCall(List<PythonFunctionCallNode> functionCallNodes) {
        if (functionCallNodes == null || functionCallNodes.isEmpty()) {
            return null;
        }
        return functionCallNodes.get(functionCallNodes.size() - 1);
    }
    
    /**
     * 从函数调用信息中推断变量类型
     * 
     * @param functionCall 函数调用节点
     * @param currentModulePath 当前模块路径
     * @return 推断出的类型，如果无法推断则返回null
     */
    public static String inferTypeFromFunctionCall(PythonFunctionCallNode functionCall, String currentModulePath) {
        if (functionCall == null) {
            return null;
        }
        
        String functionName = functionCall.getFunctionName();
        String sourceModulePath = functionCall.getSourceModulePath();
        
        // 优先使用函数调用的sourceModulePath来确定类型
        if (sourceModulePath != null && !sourceModulePath.equals("Unknown") && !sourceModulePath.equals("BUILTIN")) {
            if (sourceModulePath.contains(".") && !functionName.contains(".")) {
                return sourceModulePath + "." + functionName;
            } else {
                return sourceModulePath;
            }
        }
        
        // 如果sourceModulePath不可用，使用当前模块路径
        if (currentModulePath != null) {
            return currentModulePath + "." + functionName;
        }
        
        return null;
    }
}
