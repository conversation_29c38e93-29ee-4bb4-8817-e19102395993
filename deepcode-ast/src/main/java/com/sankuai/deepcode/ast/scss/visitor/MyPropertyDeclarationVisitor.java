package com.sankuai.deepcode.ast.scss.visitor;

import com.sankuai.deepcode.ast.model.scss.ScssVariable;
import com.sankuai.deepcode.ast.scss.gen.ScssParser;
import com.sankuai.deepcode.ast.scss.gen.ScssParserBaseVisitor;
import lombok.Getter;

import java.util.ArrayList;
import java.util.List;

public class MyPropertyDeclarationVisitor extends ScssParserBaseVisitor<Object> {

    @Getter
    private ScssVariable scssVariable;

    @Override
    public Object visitPropertyDeclaration(ScssParser.PropertyDeclarationContext ctx) {
        scssVariable = new ScssVariable();
        scssVariable.setStartLine(ctx.getStart().getLine());
        scssVariable.setEndLine(ctx.getStop().getLine());
        scssVariable.setName(ctx.identifier().getText());
        visitPropertyValue(ctx.propertyValue());
        return null;
    }


    @Override
    public Object visitPropertyValue(ScssParser.PropertyValueContext ctx) {
        scssVariable.setValue(ctx.getText());
//todo 简单处理成string
//        propertyValue
//        : (
//                value
//                        | value? prio? block
//                        | variableName
//                        | listSpaceSeparated
//                        | listCommaSeparated
//                        | expression
//                        | functionCall
//        ) prio?
//        ;
        return null;
    }


}
