package com.sankuai.deepcode.ast.model.python3;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.deepcode.ast.enums.ChangeTypeEnum;
import com.sankuai.deepcode.ast.model.base.BaseNode;
import com.sankuai.deepcode.ast.util.Md5Util;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * Package: com.sankuai.deepcode.ast.python3.visitor
 * Description:
 *
 * <AUTHOR>
 * @since 2024/12/4 15:11
 */
@Setter
@Getter
@ToString(callSuper = true)
@Accessors(chain = true)
public class PythonModuleNode extends BaseNode {
    /**
     * 模块名称
     */
    private String moduleName;
    /**
     * 模块路径【包含当前模块名称】
     */
    private String modulePath;
    /**
     * 模块文件路径【相对于项目根目录】
     */
    private String fileName;
    /**
     * 模块注释
     */
    private String comment;
    /**
     * 父模块名称
     */
    private String parentModule;
    /**
     * 子模块节点列表【只包含一级子模块 名称】
     * 子模块可能是个文件模块，也可能是另一个目录模块
     */
    private List<String> subModuleNodes = new ArrayList<>();
    /**
     * 模块中包含的导入节点列表
     */
    private List<PythonImportNode> importNodes = new ArrayList<>();
    /**
     * 模块中包含的参数节点列表
     */
    private List<PythonParameterNode> moduleParamNodes = new ArrayList<>();
    /**
     * 模块中包含的类节点列表
     */
    private List<PythonClassNode> classNodes = new ArrayList<>();

    /**
     * 模块中包含的函数节点列表
     */
    private List<PythonFunctionNode> functionNodes = new ArrayList<>();
    /**
     * 模块中包含的函数调用节点列表
     */
    List<PythonFunctionCallNode> functionCallNodes = new ArrayList<>();

    List<PythonComment> comments = new ArrayList<>();

    /**
     * FIXME: 暂时参照 Java 变更内容进行定义，后期需要依据实际情况进行调整
     */
    private ChangeTypeEnum changeType = ChangeTypeEnum.DEFAULT;
    private List<Integer> changeLines;

    public PythonModuleNode setParentModule(String parentModule) {
        this.parentModule = parentModule;
        this.modulePath = (StringUtils.isNotEmpty(parentModule) ? (parentModule + ".") : "") + moduleName;
        return this;
    }

    @Override
    public String getVid() {
        if (StringUtils.isNotEmpty(getModulePath())) {
            return Md5Util.stringToMd5(getModulePath());
        }
        return StringUtils.EMPTY;
    }


    @JsonIgnore
    // 模块内的所有类节点
    private Map<String, PythonClassNode> classNodeMap = Maps.newHashMap();

    @JsonIgnore
    // 模块内的所有函数和方法节点
    private Map<String, PythonFunctionNode> functionNodeMap = Maps.newHashMap();

    @JsonIgnore
    // 模块内的所有函数调用节点
    private Map<String, List<PythonFunctionCallNode>> functionCallNodeMap = Maps.newHashMap();

    @JsonIgnore
    // 模块内的所有导入节点
    private Map<String, List<PythonImportNode>> importNodeMap = Maps.newHashMap();

    public void populateNodeMaps() {
        // 填充导入节点
        importNodeMap.put(this.modulePath, this.importNodes);

        // 填充类节点及其相关信息
        classNodes.forEach(classNode -> {
            classNodeMap.put(classNode.getModulePath(), classNode);
            importNodeMap.computeIfAbsent(classNode.getModulePath(), k -> Lists.newArrayList())
                    .addAll(classNode.getInnerImports());

            classNode.getMethods().forEach(methodNode -> {
                functionNodeMap.put(methodNode.getModulePath(), methodNode);
                importNodeMap.computeIfAbsent(methodNode.getModulePath(), k -> Lists.newArrayList())
                        .addAll(methodNode.getInnerImports());
                populateFunctionCallNodeMap(methodNode);
            });
        });

        // 填充函数节点及其相关信息
        functionNodes.forEach(functionNode -> {
            functionNodeMap.put(functionNode.getModulePath(), functionNode);
            importNodeMap.computeIfAbsent(functionNode.getModulePath(), k -> Lists.newArrayList())
                    .addAll(functionNode.getInnerImports());
            populateFunctionCallNodeMap(functionNode);
        });

        this.functionCallNodes.forEach(callNode -> {
            functionCallNodeMap.computeIfAbsent(this.modulePath, k -> new ArrayList<>()).add(callNode);
        });
    }

    private void populateFunctionCallNodeMap(PythonFunctionNode functionNode) {
        List<PythonFunctionCallNode> callNodes = functionNode.getCallNodes();
        functionCallNodeMap.put(functionNode.getModulePath(), callNodes);
    }

}
