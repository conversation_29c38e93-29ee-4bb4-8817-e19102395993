package com.sankuai.deepcode.ast.java.visitor;

import com.sankuai.deepcode.ast.java.gen.JavaParser;
import com.sankuai.deepcode.ast.java.gen.JavaParserBaseVisitor;
import com.sankuai.deepcode.ast.java.visitor.MyJavaFormalParametersVistor;
import com.sankuai.deepcode.ast.model.java.ClassNode;
import com.sankuai.deepcode.ast.model.java.MethodNode;
import lombok.Getter;
import org.antlr.v4.runtime.ParserRuleContext;

import java.util.ArrayList;
import java.util.List;

public class MyJavaConstructorDeclarationVisitor extends JavaParserBaseVisitor<Object> {
    private ClassNode classNode = new ClassNode();

    public MyJavaConstructorDeclarationVisitor(ClassNode myClassNode) {
        classNode = myClassNode;
    }

    @Getter
    private MethodNode methodNode;

    @Override
    public Object visitConstructorDeclaration(JavaParser.ConstructorDeclarationContext ctx) {
        methodNode = new MethodNode();
        methodNode.setClassName(classNode.getClassName());
        methodNode.setInClassName(classNode.getInClassName());
        methodNode.setMethodName(ctx.identifier().getText());
        visitFormalParameters(ctx.formalParameters());
        methodNode.setStartLine(ctx.getStart().getLine());
        methodNode.setEndLine(ctx.getStop().getLine());
        methodNode.setBody(ctx.block().getText());
        List<ParserRuleContext> methodCtxs = new ArrayList<>();
        methodCtxs.add(ctx.block());
        methodNode.setMethodCtxs(methodCtxs);
        //todo 构造函数{} body 未解析
        return null;
    }

    @Override
    public Object visitFormalParameters(JavaParser.FormalParametersContext ctx) {
        com.sankuai.deepcode.ast.java.visitor.MyJavaFormalParametersVistor vistor = new MyJavaFormalParametersVistor();
        vistor.visitFormalParameters(ctx);
        methodNode.setParams(vistor.getParams());
        return null;
    }

}
