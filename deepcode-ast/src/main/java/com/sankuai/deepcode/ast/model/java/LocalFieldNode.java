package com.sankuai.deepcode.ast.model.java;

import com.sankuai.deepcode.ast.model.base.BaseNode;
import com.sankuai.deepcode.ast.model.java.JavaReturn;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
public class LocalFieldNode extends BaseNode {
    private String className;
    private String inClassName = "";
    private String methodName;
    private String fieldName;
    private String access;
    private JavaReturn returnInfo;
    private String annotationInfos;
    private List<String> signatures = new ArrayList<>();
    private String fieldType;
    private Object value;
    private int startLine = -1;
    private int endLine = -1;
    private List<Integer> commentLines = new ArrayList<>();
}
