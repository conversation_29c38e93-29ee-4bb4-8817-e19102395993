package com.sankuai.deepcode.ast.java.visitor;

import com.sankuai.deepcode.ast.java.gen.JavaParser;
import com.sankuai.deepcode.ast.java.gen.JavaParserBaseVisitor;
import com.sankuai.deepcode.ast.java.visitor.MyJavaTypeTypeVistor;
import com.sankuai.deepcode.ast.model.java.JavaGenerics;
import com.sankuai.deepcode.ast.model.java.LocalFieldNode;
import lombok.Getter;

import java.util.ArrayList;
import java.util.List;

public class MyJavaLocalVariableVistor extends JavaParserBaseVisitor<Object> {
    @Getter
    private List<LocalFieldNode> localFieldNodes = new ArrayList<>();

    @Override
    public Object visitLocalVariableDeclaration(JavaParser.LocalVariableDeclarationContext ctx) {
        com.sankuai.deepcode.ast.java.visitor.MyJavaTypeTypeVistor vistor = new MyJavaTypeTypeVistor();
        vistor.visit(ctx);
        List<String> generics = new ArrayList<>();
        for (JavaGenerics javaGenerics : vistor.getGenerics()) {
            generics.add(javaGenerics.getName());
        }
        if (null != ctx.variableDeclarators()) {
            for (JavaParser.VariableDeclaratorContext variableDeclaratorContext : ctx.variableDeclarators().variableDeclarator()) {
                LocalFieldNode localFieldNode = new LocalFieldNode();
                localFieldNode.setStartLine(ctx.getStart().getLine());
                localFieldNode.setEndLine(ctx.getStop().getLine());
                localFieldNode.setFieldName(variableDeclaratorContext.variableDeclaratorId().getText());
                if (null != variableDeclaratorContext.variableInitializer()) {
                    localFieldNode.setValue(variableDeclaratorContext.variableInitializer().getText());
                }
                localFieldNode.setFieldType(vistor.getType());
                localFieldNode.setSignatures(generics);
                localFieldNodes.add(localFieldNode);
            }
        }
        return null;
    }

}
