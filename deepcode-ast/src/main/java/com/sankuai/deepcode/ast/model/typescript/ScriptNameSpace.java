package com.sankuai.deepcode.ast.model.typescript;

import com.sankuai.deepcode.ast.model.typescript.ScriptClass;
import com.sankuai.deepcode.ast.model.typescript.ScriptMethod;
import com.sankuai.deepcode.ast.model.typescript.ScriptVariable;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
public class ScriptNameSpace {
    private boolean isExport;
    private boolean isDecare;
    private int startLine;
    private int endLine;
    private String name;
    private String filePath;

    private List<com.sankuai.deepcode.ast.model.typescript.ScriptMethod> methods = new ArrayList<>();
    private List<ScriptClass> classes = new ArrayList<>();
    private List<ScriptVariable> variables = new ArrayList<>();
    private List<ScriptNameSpace> namespaces = new ArrayList<>();
    private List<ScriptTypeAlias> typeAliases = new ArrayList<>();
    private List<ScriptMethod> invokeMethods = new ArrayList<>();
}
