package com.sankuai.deepcode.ast.model.python3;

import com.sankuai.deepcode.ast.model.base.BaseNode;
import com.sankuai.deepcode.ast.model.base.LocationInfo;
import com.sankuai.deepcode.ast.util.Md5Util;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 * Package: com.sankuai.deepcode.ast.model.python3
 * Description:
 *
 * <AUTHOR>
 * @since 2024/12/4 11:34
 */
@Setter
@Getter
@ToString
@Accessors(chain = true)
public class PythonParameterNode extends BaseNode {
    private String modulePath;
    private Integer paramIndex;
    // 参数名称
    private String name;
    // 类型的原始名称
    private String type;
    // 参数 数据类型 模块来源
    private String typeModule;
    // 初始、默认值
    private String defaultValue;
    //
    private LocationInfo location;

    @Override
    public String getVid() {
        return Md5Util.stringToMd5(
                modulePath + "." + name
        );
    }
}
