package com.sankuai.deepcode.ast.model.java;

import com.sankuai.deepcode.ast.model.base.BaseNode;
import com.sankuai.deepcode.ast.model.java.JavaAnnotation;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
public class FieldNode extends BaseNode {
    private String source = "unknown"; //方法来源 local本地 unknown未知
    private String moduleName = "";
    private String className;
    private String inClassName = "";
    private String fieldName;
    private String access;
    private List<JavaAnnotation> annotations = new ArrayList<>();
    private List<String> signatures = new ArrayList<>();
    private String fieldType;

    public void setValue(Object value) {
        if (value != null) {
            if (value.toString().length() > 4096) {
                this.value = "##单号内容过程截断##" + value.toString().substring(0, 4096) + "...";
            } else {
                this.value = value;
            }
        } else {
            this.value = value;
        }
    }

    private Object value = null;
    private int changeType = 0;
    private List<Integer> changeLines = new ArrayList<>();
    private int checkType = 0;
    private List<Integer> checkLines = new ArrayList<>();
    private int startLine = -1;
    private int endLine = -1;
    private List<Integer> commentLines = new ArrayList<>();

    private List<Integer> quotedLines = new ArrayList<>();
    private String body = "";
}
