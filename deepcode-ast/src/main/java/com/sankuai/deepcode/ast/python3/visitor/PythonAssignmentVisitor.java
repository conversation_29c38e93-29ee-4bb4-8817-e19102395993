package com.sankuai.deepcode.ast.python3.visitor;

import com.sankuai.deepcode.ast.model.python3.PythonFunctionCallNode;
import com.sankuai.deepcode.ast.model.python3.PythonParameterNode;
import com.sankuai.deepcode.ast.model.python3.ScopeManager;
import com.sankuai.deepcode.ast.python3.gen.PythonParser;
import com.sankuai.deepcode.ast.python3.gen.PythonParserBaseVisitor;
import lombok.Getter;
import org.apache.commons.collections4.CollectionUtils;

import java.util.List;

/**
 * Package: com.sankuai.deepcode.ast.python3.visitor
 * Description:
 *
 * <AUTHOR>
 * @since 2024/12/4 21:58
 */
@Getter
public class PythonAssignmentVisitor extends PythonParserBaseVisitor<Object> {

    private PythonParameterNode paramNode = new PythonParameterNode();
    private final String fileName;
    private final String currentModulePath;
    private final ScopeManager scopeManager;

    public PythonAssignmentVisitor(String fileName, String currentModulePath, ScopeManager scopeManager) {
        this.fileName = fileName;
        this.currentModulePath = currentModulePath;
        this.scopeManager = scopeManager;
    }


    @Override
    public Object visitAssignment(PythonParser.AssignmentContext ctx) {
        if (ctx.getParent() instanceof PythonParser.Simple_stmtContext) {
            String variableName = null;
            String annotationType = null;

            // 解析变量名和类型注解
            if (ctx.NAME() != null) {
                variableName = ctx.NAME().getText();
                if (ctx.expression() != null) {
                    annotationType = ctx.expression().getText();
                }
            } else if (ctx.single_target() != null && ctx.single_target().NAME() != null) {
                variableName = ctx.single_target().NAME().getText();
            }

            paramNode.setName(variableName);

            // 优先处理类型注解
            if (annotationType != null) {
                String resolvedType = scopeManager.resolveSymbol(annotationType, ctx.getStart().getLine());
                if (resolvedType != null && !resolvedType.equals("Unknown")) {
                    paramNode.setType(resolvedType);
                } else {
                    paramNode.setType(currentModulePath + "." + annotationType);
                }
            } else {
                // 解析赋值表达式
                if (ctx.star_expressions() != null) {
                    visit(ctx.star_expressions());
                    // 提取字面量值
                    String literalValue = PythonLiteralValueExtractor.extractLiteralValue(ctx.star_expressions());
                    paramNode.setDefaultValue(literalValue);
                } else if (ctx.annotated_rhs() != null) {
                    visit(ctx.annotated_rhs());
                    // 提取字面量值
                    String literalValue = PythonLiteralValueExtractor.extractLiteralValue(ctx.annotated_rhs());
                    paramNode.setDefaultValue(literalValue);
                }
            }

            // 如果类型还未确定，尝试从赋值表达式推断
            if (paramNode.getType() == null) {
                inferTypeFromRightHandSide(ctx);
            }
            paramNode.setModulePath(currentModulePath);

            // 更新作用域
            if (paramNode.getType() != null) {
                scopeManager.addToScope(variableName, paramNode.getType(), ctx.getStart().getLine());
            } else {
                scopeManager.addToScope(variableName, currentModulePath + "." + variableName, ctx.getStart().getLine());
            }

        }
        return null;
    }

    private void inferTypeFromRightHandSide(PythonParser.AssignmentContext ctx) {
        if (ctx.star_expressions() != null && ctx.star_expressions().star_expression().size() == 1) {
            PythonParser.Star_expressionContext starExpr = ctx.star_expressions().star_expression(0);
            if (starExpr.expression() != null) {
                starExpr.expression().accept(new PythonParserBaseVisitor<Void>() {
                    @Override
                    public Void visitAtom(PythonParser.AtomContext ctx) {
                        if (ctx.NAME() != null) {
                            String typeName = ctx.NAME().getText();
                            String resolvedType = scopeManager.resolveSymbol(typeName, ctx.getStart().getLine());
                            if (resolvedType != null && !resolvedType.equals("Unknown")) {
                                paramNode.setType(resolvedType);
                            } else {
                                paramNode.setType(currentModulePath + "." + typeName);
                            }
                        } else if (ctx.strings() != null) {
                            paramNode.setType("str");
                        } else {
                            PythonAssignmentVisitor.this.visitAtom(ctx);
                        }
                        return null;
                    }

                });
            }
        }
    }

    @Override
    public Object visitFunction_call(PythonParser.Function_callContext ctx) {
        PythonFunctionCallVisitor callVisitor = new PythonFunctionCallVisitor(fileName, currentModulePath, scopeManager);
        callVisitor.visit(ctx);
        List<PythonFunctionCallNode> functionCallNodes = callVisitor.getFunctionCallNodes();

        if (CollectionUtils.isNotEmpty(functionCallNodes)) {
            // 获取最后一个函数，尝试查的返回值？
            PythonFunctionCallNode lastFunctionCall = functionCallNodes.get(functionCallNodes.size() - 1);
            String functionName = lastFunctionCall.getFunctionName();
            String resolvedType = scopeManager.resolveSymbol(functionName, ctx.getStart().getLine());

            if (resolvedType != null && !resolvedType.equals("Unknown")) {
                // 解析作用域不为空时，直接设置类型  【可能是类类型，可能是方法类型】
                paramNode.setType(resolvedType);
            } else {
                paramNode.setType(currentModulePath + "." + functionName);
            }
        } else {
            paramNode.setType("Unknown");
        }
        return null;
    }


    @Override
    public Object visitYield_expr(PythonParser.Yield_exprContext ctx) {
        paramNode.setType("Generator");
        return null;
    }

    @Override
    public Object visitStar_expressions(PythonParser.Star_expressionsContext ctx) {
        if (ctx.star_expression().size() > 1) {
            paramNode.setType("Tuple");
        } else {
            visit(ctx.star_expression(0));
        }
        return null;
    }

    @Override
    public Object visitStar_expression(PythonParser.Star_expressionContext ctx) {
        if (ctx.expression() != null) {
            visit(ctx.expression());
        } else if (ctx.bitwise_or() != null) {
            paramNode.setType("Iterable");
        }
        return null;
    }

    @Override
    public Object visitExpression(PythonParser.ExpressionContext ctx) {
        if (ctx.lambdef() != null) {
            paramNode.setType("function");
        } else if (ctx.disjunction() != null && !ctx.disjunction().isEmpty()) {
            visit(ctx.disjunction().get(0));
        } else if (ctx.function_call() != null) {
            visit(ctx.function_call());
        }
        return null;
    }

    @Override
    public Object visitAtom(PythonParser.AtomContext ctx) {
        if (ctx.NUMBER() != null) {
            paramNode.setType(ctx.NUMBER().getText().contains(".") ? "float" : "int");
        } else if (ctx.strings() != null) {
            paramNode.setType("str");
        } else if (ctx.TRUE() != null || ctx.FALSE() != null) {
            paramNode.setType("bool");
        } else if (ctx.NONE() != null) {
            paramNode.setType("None");
        } else if (ctx.tuple() != null) {
            paramNode.setType("tuple");
        } else if (ctx.list() != null) {
            paramNode.setType("list");
        } else if (ctx.dict() != null) {
            paramNode.setType("dict");
        } else if (ctx.set() != null) {
            paramNode.setType("set");
        }
        return null;
    }


}
