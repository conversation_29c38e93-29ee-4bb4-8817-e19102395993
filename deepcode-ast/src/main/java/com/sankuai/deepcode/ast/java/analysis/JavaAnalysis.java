package com.sankuai.deepcode.ast.java.analysis;

import com.sankuai.deepcode.ast.enums.ChangeTypeEnum;
import com.sankuai.deepcode.ast.enums.NodeSourceEnum;
import com.sankuai.deepcode.ast.java.gen.JavaLexer;
import com.sankuai.deepcode.ast.java.gen.JavaParser;
import com.sankuai.deepcode.ast.java.visitor.*;
import com.sankuai.deepcode.ast.model.base.CompareRes;
import com.sankuai.deepcode.ast.model.base.GitDiffInfo;
import com.sankuai.deepcode.ast.model.java.*;
import com.sankuai.deepcode.ast.util.CompareUtil;
import com.sankuai.deepcode.ast.util.FileUtil;
import com.sankuai.deepcode.ast.util.Md5Util;
import org.antlr.v4.runtime.CharStreams;
import org.antlr.v4.runtime.CommonTokenStream;
import org.antlr.v4.runtime.ParserRuleContext;
import org.antlr.v4.runtime.tree.ParseTree;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.io.File;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.*;

import static com.sankuai.deepcode.ast.util.CompareUtil.WORKSPACE_PATH;

public class JavaAnalysis {
    private static final String JAVAPATH = "/src/main/java/";

    public static JavaAnalysisRes analysis(long uniqueId, String gitUrl, String gitName, String fromBranch, String toBranch, String buildBranch, String buildCommit) throws Exception {
        long start = System.currentTimeMillis();
        if (StringUtils.isEmpty(fromBranch)) {
            throw new Exception("fromBranch源分支不能为空");
        }
        if (StringUtils.isEmpty(fromBranch)) {
            fromBranch = "master";
            System.out.println("toBranch目标分支为空,默认master");
        }

        long startTime = System.currentTimeMillis();
        CompareRes compareRes = CompareUtil.getDiffInfos(uniqueId, gitUrl, gitName, fromBranch, toBranch, buildBranch, buildCommit);
        long end = System.currentTimeMillis();
        System.out.println("diff耗时:" + (end - startTime) + "ms");

        JavaAnalysisRes javaAnalysisRes = javaAnalysis(gitName, compareRes);
        end = System.currentTimeMillis();
        FileUtil.deleteFile(WORKSPACE_PATH, String.valueOf(uniqueId));
        System.out.println("解析总耗时:" + (end - start) + "ms");
        return javaAnalysisRes;
    }


    public static JavaAnalysisRes javaAnalysis(String gitName, CompareRes compareRes) throws Exception {
        long startTime = System.currentTimeMillis();
        Map<String, GitDiffInfo> gitDiffInfoMap = compareRes.getGitJavaDiffInfoMap();
        Set<String> filterPath = compareRes.getFilterPath();
        String alias = "";
        if (compareRes.isDiff()) {
            alias = "_from";
            if (compareRes.isCheck()) {
                alias = "_build";
            }
        } else {
            alias = "_to";
        }
        JavaAnalysisRes javaAnalysisRes = initJavaAnalys(gitName + alias, compareRes.getGitPath(), filterPath, compareRes.getGitJavaDiffInfoMap());
        long end = System.currentTimeMillis();
        System.out.println("初始化" + gitName + alias + "耗时:" + (end - startTime) + "ms");
        startTime = System.currentTimeMillis();
        javaAnalysisRes.setToCommit(compareRes.getToCommit());
        javaAnalysisRes.setFromCommit(compareRes.getFromCommit());
        javaAnalysisRes.setBuildCommit(compareRes.getBuildCommit());
        if (compareRes.isDiff()) {
            JavaAnalysisRes toBranchRes = new JavaAnalysisRes();
            for (Map.Entry<String, GitDiffInfo> entry : gitDiffInfoMap.entrySet()) {
                GitDiffInfo gitDiffInfo = entry.getValue();
                if (gitDiffInfo.getChangeType() == ChangeTypeEnum.DEFAULT.getCode()) {
                    filterPath.add(gitDiffInfo.getPath());
                }
            }
            toBranchRes = initJavaAnalys(gitName + "_to", compareRes.getGitToPath(), filterPath, gitDiffInfoMap);
            end = System.currentTimeMillis();
            System.out.println("初始化" + gitName + "_to" + "耗时:" + (end - startTime) + "ms");
            for (ClassNode classNode : javaAnalysisRes.getClassNodeMap().values()) {
                GitDiffInfo gitDiffInfo = gitDiffInfoMap.get(classNode.getClassPath());
                if (null != gitDiffInfo) {
                    classNode.setCommitCount(gitDiffInfo.getCommitCount());
                    classNode.setModuleName(gitDiffInfo.getModuleName());
                    classNode.setCodeViews(gitDiffInfo.getCodeViews());
                    ClassChangeNum classChangeNum = new ClassChangeNum();
                    if (gitDiffInfo.getChangeType() != ChangeTypeEnum.DEFAULT.getCode()) {
                        for (MethodNode methodNode : classNode.getMethodNodes()) {
                            diffMethod(methodNode, gitDiffInfo, toBranchRes, classChangeNum);
                        }
                        for (FieldNode fieldNode : classNode.getFieldNodes()) {
                            diffField(fieldNode, gitDiffInfo, toBranchRes, classChangeNum);
                        }

                        boolean classChange = false;
//                        boolean classCheck = false;
                        for (int i = classNode.getStartLine(); i <= classNode.getEndLine(); i++) {
                            if (classNode.getCommentLines().contains(i)) {
                                continue;
                            }
                            if (gitDiffInfo.getChangeLines().contains(i)) {
                                classNode.getChangeLines().add(i);
                                classChange = true;
                            }
//                            if (gitDiffInfo.getCheckLines().contains(i)) {
//                                classNode.getChangeLines().add(i);
//                                classCheck = true;
//                            }
                        }
                        if (classChange) {
                            ClassNode toClass = toBranchRes.getClassNodeMap().get(classNode.getVid());
                            if (null == toClass) {
                                classNode.setChangeType(ChangeTypeEnum.ADD.getCode());
                            } else {
                                if (classChangeNum.getChangeMethodCount() + classChangeNum.getAddMethodCount()
                                        + classChangeNum.getChangeFieldCount() + classChangeNum.getAddFieldCount() > 0) {
                                    classNode.setChangeType(ChangeTypeEnum.CHANGE.getCode());
                                }
//                                if (classCheck) {
//                                    classNode.setCheckType(CheckTypeEnum.CHANGE.getCode());
//                                }
                            }
                        }
                    }
                }
            }

            for (GitDiffInfo gitDiffInfo : gitDiffInfoMap.values()) {
                FileNode fileNode = new FileNode();
                fileNode.setCommitCount(gitDiffInfo.getCommitCount());
                fileNode.setModuleName(gitDiffInfo.getModuleName());
                fileNode.setFileName(gitDiffInfo.getFileName());
                fileNode.setPath(gitDiffInfo.getPath());
                fileNode.setFileType(gitDiffInfo.getFileType());
                fileNode.setChangeType(gitDiffInfo.getChangeType());
                fileNode.setChangeLines(gitDiffInfo.getChangeLines());
                fileNode.setStartLine(1);
                fileNode.setEndLine(gitDiffInfo.getCodeViews().size() - 1);
                fileNode.setCodeViews(gitDiffInfo.getCodeViews());
                fileNode.setVid(Md5Util.fileNodeToMd5(fileNode));

                boolean fileChange = false;
//                boolean fileCheck = false;
                for (int i = fileNode.getStartLine(); i <= fileNode.getEndLine(); i++) {
                    if (gitDiffInfo.getChangeLines().contains(i)) {
                        fileNode.getChangeLines().add(i);
                        fileChange = true;
                    }
//                    if (gitDiffInfo.getCheckLines().contains(i)) {
//                        fileNode.getChangeLines().add(i);
//                        fileCheck = true;
//                    }
                }
                if (fileChange) {
                    FileNode toFile = toBranchRes.getFileNodeMap().get(fileNode.getVid());
                    if (null == toFile) {
                        fileNode.setChangeType(ChangeTypeEnum.ADD.getCode());
                    } else {
                        fileNode.setChangeType(ChangeTypeEnum.CHANGE.getCode());
//                        if (fileCheck) {
//                            fileNode.setCheckType(CheckTypeEnum.CHANGE.getCode());
//                        }
                    }
                }
                javaAnalysisRes.getFileNodeMap().put(fileNode.getPath(), fileNode);
            }
        } else {
            for (ClassNode classNode : javaAnalysisRes.getClassNodeMap().values()) {
                GitDiffInfo gitDiffInfo = gitDiffInfoMap.get(classNode.getClassPath());
                if (null != gitDiffInfo) {
                    classNode.setCodeViews(gitDiffInfo.getCodeViews());
                }
            }
            for (GitDiffInfo gitDiffInfo : gitDiffInfoMap.values()) {
                FileNode fileNode = new FileNode();
                fileNode.setModuleName(gitDiffInfo.getModuleName());
                fileNode.setFileName(gitDiffInfo.getFileName());
                fileNode.setPath(gitDiffInfo.getPath());
                fileNode.setFileType(gitDiffInfo.getFileType());
                fileNode.setStartLine(1);
                fileNode.setEndLine(gitDiffInfo.getCodeViews().size() - 1);
                fileNode.setCodeViews(gitDiffInfo.getCodeViews());
                fileNode.setVid(Md5Util.fileNodeToMd5(fileNode));
                javaAnalysisRes.getFileNodeMap().put(fileNode.getPath(), fileNode);
            }
        }
        javaAnalysisRes.setFilterPath(filterPath);
        return javaAnalysisRes;
    }


    public static void diffMethod(MethodNode methodNode, GitDiffInfo gitDiffInfo, JavaAnalysisRes toBranchRes, ClassChangeNum classChangeNum) {
        boolean isChange = false;
//        boolean isCheck = false;
        for (int i = methodNode.getStartLine(); i <= methodNode.getEndLine(); i++) {
            if (methodNode.getCommentLines().contains(i)) {
                continue;
            }
            if (gitDiffInfo.getChangeLines().contains(i)) {
                methodNode.getChangeLines().add(i);
                isChange = true;
            }
//            if (gitDiffInfo.getCheckLines().contains(i)) {
//                methodNode.getCheckLines().add(i);
//                isCheck = true;
//            }
        }
        if (isChange) {
            MethodNode toMethod = toBranchRes.getMethodNodeMap().get(methodNode.getVid());
            if (null == toMethod) {
                methodNode.setChangeType(ChangeTypeEnum.ADD.getCode());
                classChangeNum.setAddMethodCount(classChangeNum.getAddMethodCount() + 1);
            } else {
                String toMd5 = Md5Util.methodBodyToMd5(toMethod);
                String fromMd5 = Md5Util.methodBodyToMd5(methodNode);
                if (!toMd5.equals(fromMd5)) {
                    methodNode.setChangeType(ChangeTypeEnum.CHANGE.getCode());
                    classChangeNum.setChangeMethodCount(classChangeNum.getChangeMethodCount() + 1);
                }
//                if (isCheck) {
//                    methodNode.setCheckType(CheckTypeEnum.CHANGE.getCode());
//                }
            }
        }
    }

    public static void diffField(FieldNode fieldNode, GitDiffInfo gitDiffInfo, JavaAnalysisRes toBranchRes, ClassChangeNum classChangeNum) {
        boolean isChange = false;
//        boolean isCheck = false;
        for (int i = fieldNode.getStartLine(); i <= fieldNode.getEndLine(); i++) {
            if (fieldNode.getCommentLines().contains(i)) {
                continue;
            }
            if (gitDiffInfo.getChangeLines().contains(i)) {
                fieldNode.getChangeLines().add(i);
                isChange = true;
            }
//            if (gitDiffInfo.getCheckLines().contains(i)) {
//                fieldNode.getCheckLines().add(i);
//                isCheck = true;
//            }
        }
        if (isChange) {
            FieldNode toField = toBranchRes.getFieldNodeMap().get(fieldNode.getVid());
            if (null == toField) {
                fieldNode.setChangeType(ChangeTypeEnum.ADD.getCode());
                classChangeNum.setAddMethodCount(classChangeNum.getAddMethodCount() + 1);
            } else {
                String toMd5 = Md5Util.fieldBodyToMd5(toField);
                String fromMd5 = Md5Util.fieldBodyToMd5(fieldNode);
                if (!toMd5.equals(fromMd5)) {
                    fieldNode.setChangeType(ChangeTypeEnum.CHANGE.getCode());
                    classChangeNum.setChangeMethodCount(classChangeNum.getChangeMethodCount() + 1);
                }
//                if (isCheck) {
//                    fieldNode.setCheckType(CheckTypeEnum.CHANGE.getCode());
//                }
            }
        }
    }


    public static JavaAnalysisRes initJavaAnalys(String gitName, String gitPath, Set<String> filterPath, Map<String, GitDiffInfo> gitDiffInfoMap) throws Exception {
        JavaAnalysisRes javaAnalysisRes = new JavaAnalysisRes();
        Map<String, ClassNode> classNodeMap = new HashMap<>();
        Map<String, MethodNode> methodNodeMap = new HashMap<>();
        Map<String, MethodNode> methodNameNodeMap = new HashMap<>();
        Map<String, List<MethodNode>> methodNumNodeMap = new HashMap<>();

        Map<String, FieldNode> fieldNodeMap = new HashMap<>();
//        Set<String> methodVid = new HashSet<>();
//        Set<String> fieldVid = new HashSet<>();
//     List<LocalFieldNode> localFieldNodes = new ArrayList<>();

        Map<String, Set<String>> classImportClass = new HashMap<>();
        Map<String, Set<String>> classDefineMethod = new HashMap<>();
        Map<String, Set<String>> classDefineField = new HashMap<>();
        Map<String, List<MethodInvokeMethodEdge>> methodInvokeMethod = new HashMap<>();
        Map<String, List<MethodQuoteFieldEdge>> methodQuoteField = new HashMap<>();
//        Map<String, String> methodQuoteField = new HashMap<>();
//    private Map<String, String> methodDefineField = new HashMap<>();

        javaAnalysisRes.setClassNodeMap(classNodeMap);
        javaAnalysisRes.setFieldNodeMap(fieldNodeMap);
        javaAnalysisRes.setMethodNodeMap(methodNodeMap);

        javaAnalysisRes.setClassImportClass(classImportClass);
        javaAnalysisRes.setClassDefineField(classDefineField);
        javaAnalysisRes.setClassDefineMethod(classDefineMethod);
        javaAnalysisRes.setMethodInvokeMethod(methodInvokeMethod);
        javaAnalysisRes.setMethodQuoteField(methodQuoteField);

        List<File> files = FileUtil.getJavaFiles(gitPath);
        List<File> firstjavaFiles = new ArrayList<>();
//        List<File> secondJavaFiles = new ArrayList<>();
        for (File file : files) {
            if (!file.getName().endsWith(".java")) {
                continue;
            }
            if (file.getPath().contains(JAVAPATH)) {
                firstjavaFiles.add(file);
            }
        }

//        List<MyJavaCompilationUnitVisitor> visitors = new ArrayList<>();
        Map<String, ClassNode> classMap = new HashMap<>();
        Map<String, List<MethodNode>> classAndMethodMap = new HashMap<>();
        Map<String, List<FieldNode>> nameFieldMap = new HashMap<>();
        Set<String> classList = new HashSet<>();
        for (File file : firstjavaFiles) {
            String code = new String(Files.readAllBytes(Paths.get(file.getPath())));
            String classPath = file.getPath().split(gitName + "/")[1];
            MyJavaCompilationUnitVisitor myJavaCompilationUnitVisitor = new MyJavaCompilationUnitVisitor(classPath);
            JavaLexer lexer = new JavaLexer(CharStreams.fromString(code));
            CommonTokenStream tokens = new CommonTokenStream(lexer);
            JavaParser parser = new JavaParser(tokens);
            ParseTree tree = parser.compilationUnit();
            myJavaCompilationUnitVisitor.visit(tree);
            InitJavaComment.initCommon(myJavaCompilationUnitVisitor, tokens);
            for (ClassNode classNode : myJavaCompilationUnitVisitor.getClassNodes()) {
                if (filterPath.contains(classPath)) {
                    classNode.setVid(Md5Util.classNodeToMd5(classNode));
                    classNodeMap.put(classNode.getVid(), classNode);
                    GitDiffInfo gitDiffInfo = gitDiffInfoMap.get(classNode.getClassPath());
                    if (null != gitDiffInfo) {
                        classNode.setModuleName(gitDiffInfo.getModuleName());
                    }
                    if (StringUtils.isNotEmpty(classNode.getClassName())) {
                        classList.add(classNode.getClassName());
                    }
                } else {
                    InitByClassNodes.init(classNode, classMap, classAndMethodMap, classList, nameFieldMap);
                }
            }
        }

        for (ClassNode classNode : classMap.values()) {
            InitSurperAndInterfacesClass.init(classNode, classList);
            InitFieldClass.init(classNode, classNode.getFieldNodes(), classList);
            initClassRelation(classNode, classNodeMap, methodNodeMap, methodNameNodeMap, methodNumNodeMap, fieldNodeMap, javaAnalysisRes, gitDiffInfoMap);
        }

        for (ClassNode classNode : classMap.values()) {
            Map<String, String> classFieldNameAndType = new HashMap<>();
            for (FieldNode fieldNode : classNode.getFieldNodes()) {
                classFieldNameAndType.put(fieldNode.getFieldName(), fieldNode.getFieldType());
            }
            for (MethodNode methodNode : classNode.getMethodNodes()) {
                MyJavaMethodCallNewVisitor myJavaMethodCallVisitor = new MyJavaMethodCallNewVisitor(classNode, classAndMethodMap, classList);
                myJavaMethodCallVisitor.setClassFieldNameAndType(classFieldNameAndType);
                for (ParserRuleContext parserRuleContext : methodNode.getMethodCtxs()) {
                    MyJavaLocalFieldsVisitor myJavaLocalFieldsVisitor = new MyJavaLocalFieldsVisitor(classNode, methodNode, nameFieldMap);
                    myJavaLocalFieldsVisitor.visit(parserRuleContext);
                    myJavaMethodCallVisitor.setLocalFields(myJavaLocalFieldsVisitor.getLocalFields());
                    myJavaMethodCallVisitor.visit(parserRuleContext);
                    methodNode.setInvokeMethods(myJavaMethodCallVisitor.getInvokeMethodNodes());
                    methodNode.setComplexity(myJavaLocalFieldsVisitor.getComplexity());
                }
            }
        }

//        boolean init = true;
//        Map<String, List<MethodNode>> classAndMethodMap = new HashMap<>();
//        Map<Object, List<FieldNode>> nameFieldMap = new HashMap<>();
//        List<String> classList = new ArrayList<>();
//        for (File file : firstjavaFiles) {
//            String code = new String(Files.readAllBytes(Paths.get(file.getPath())));
//            String classPath = file.getPath().split(gitName + "/")[1];
//            MyJavaClassVisitor myJavaClassVisitor = parseFile(classPath, code, init, classAndMethodMap, classList, nameFieldMap);
//            if (filterPath.contains(classPath)) {
//                ClassNode classNode = myJavaClassVisitor.getClassNode();
//                classNode.setVid(Md5Util.classNodeToMd5(classNode));
//                classNodeMap.put(classNode.getVid(), classNode);
//                GitDiffInfo gitDiffInfo = gitDiffInfoMap.get(classNode.getClassPath());
//                if (null != gitDiffInfo) {
//                    classNode.setModuleName(gitDiffInfo.getModuleName());
//                }
//                if (StringUtils.isNotEmpty(myJavaClassVisitor.getClassNode().getClassName())) {
//                    classList.add(myJavaClassVisitor.getClassNode().getClassName());
//                }
//                continue;
//            }
//            if (StringUtils.isNotEmpty(myJavaClassVisitor.getClassNode().getClassName())) {
//                classList.add(myJavaClassVisitor.getClassNode().getClassName());
//            }
//            if (CollectionUtils.isNotEmpty(myJavaClassVisitor.getClassNode().getFieldNodes())) {
//                for (FieldNode fieldNode : myJavaClassVisitor.getClassNode().getFieldNodes()) {
//                    boolean addField = false;
//                    //todo 先不处理枚举
////                    if (myJavaClassVisitor.getClassNode().getClassType().equals(ClassTypeEnum.ENUM.getCode())) {
////                        addField = true;
////                    } else {
//                    if (StringUtils.isNotEmpty(fieldNode.getAccess())) {
//                        if (fieldNode.getAccess().contains("public") && fieldNode.getAccess().contains("static")) {
//                            addField = true;
//                        }
//                    }
////                    }
//                    if (addField) {
//                        if (null == nameFieldMap.get(fieldNode.getFieldName())) {
//                            List<FieldNode> fieldNodes = new ArrayList<>();
//                            fieldNodes.add(fieldNode);
//                            nameFieldMap.put(fieldNode.getFieldName(), fieldNodes);
//                        } else {
//                            nameFieldMap.get(fieldNode.getFieldName()).add(fieldNode);
//                        }
//                    }
//                }
//            }
//            secondJavaFiles.add(file);
//            classAndMethodMap.put(myJavaClassVisitor.getClassNode().getClassName(), myJavaClassVisitor.getClassNode().getMethodNodes());
//            for (ClassNode classNode : myJavaClassVisitor.getInClassNode()) {
//                classAndMethodMap.put(classNode.getClassName() + "$" + classNode.getInClassName(), classNode.getMethodNodes());
//            }
//        }
//
//        init = false;
//        for (File file : secondJavaFiles) {
//            String code = new String(Files.readAllBytes(Paths.get(file.getPath())));
//            String classPath = file.getPath().split(gitName + "/")[1];
//            if (filterPath.contains(classPath)) {
//                continue;
//            }
//            MyJavaClassVisitor myJavaClassVisitor = parseFile(classPath, code, init, classAndMethodMap, classList, nameFieldMap);
//
//            visitors.add(myJavaClassVisitor);
//        }
//
//
//        for (MyJavaClassVisitor visitor : visitors) {
//            InitSurperAndInterfacesClass.init(visitor.getClassNode(), classList);
//            InitFieldClass.init(visitor.getClassNode(), visitor.getClassNode().getFieldNodes(), classList);
//            if (CollectionUtils.isNotEmpty(visitor.getInClassNode())) {
//                for (ClassNode inClassNode : visitor.getInClassNode()) {
//                    InitSurperAndInterfacesClass.init(inClassNode, classList);
//                    InitFieldClass.init(visitor.getClassNode(), inClassNode.getFieldNodes(), classList);
//                }
//            }
//        }
//
//        for (MyJavaClassVisitor visitor : visitors) {
//            initClassRelation(visitor.getClassNode(), classNodeMap, methodNodeMap, methodNameNodeMap, methodNumNodeMap, fieldNodeMap, javaAnalysisRes, gitDiffInfoMap);
//            if (CollectionUtils.isNotEmpty(visitor.getInClassNode())) {
//                for (ClassNode inClassNode : visitor.getInClassNode()) {
//                    initClassRelation(inClassNode, classNodeMap, methodNodeMap, methodNameNodeMap, methodNumNodeMap, fieldNodeMap, javaAnalysisRes, gitDiffInfoMap);
//                }
//            }
//        }

        Map<String, MethodInvokeMethodEdge> methodInvokeMethodEdgeMap = new HashMap<>();
        Map<String, MethodQuoteFieldEdge> methodQuoteFieldEdgeMap = new HashMap<>();
        Map<String, ClassNode> classNameNodeMap = new HashMap<>();
        for (ClassNode classNode : classNodeMap.values()) {
            if (StringUtils.isNotEmpty(classNode.getClassName())) {
                if (StringUtils.isEmpty(classNode.getInClassName())) {
                    classNameNodeMap.put(classNode.getClassName(), classNode);
                }
            }
        }
//        for (MyJavaClassVisitor visitor : visitors) {
//            initClassRelation(visitor.getClassNode(), classNodeMap, javaAnalysisRes);
//            initMethodRelation(visitor.getClassNode(), methodNodeMap, methodNameNodeMap, methodNumNodeMap, methodInvokeMethodEdgeMap, methodQuoteFieldEdgeMap, classNameNodeMap);
//            for (ClassNode classNode : visitor.getInClassNode()) {
//                initMethodRelation(classNode, methodNodeMap, methodNameNodeMap, methodNumNodeMap, methodInvokeMethodEdgeMap, methodQuoteFieldEdgeMap, classNameNodeMap);
//            }
//        }
        for (ClassNode classNode : classNodeMap.values()) {
            initClassRelation(classNode, classNodeMap, javaAnalysisRes);
            initMethodRelation(classNode, methodNodeMap, methodNameNodeMap, methodNumNodeMap, methodInvokeMethodEdgeMap, methodQuoteFieldEdgeMap, classNameNodeMap);
        }
        for (MethodInvokeMethodEdge methodInvokeMethodEdge : methodInvokeMethodEdgeMap.values()) {
            List<MethodInvokeMethodEdge> methodInvokeMethodEdges = methodInvokeMethod.get(methodInvokeMethodEdge.getSource());
            if (CollectionUtils.isEmpty(methodInvokeMethodEdges)) {
                methodInvokeMethodEdges = new ArrayList<>();
                methodInvokeMethodEdges.add(methodInvokeMethodEdge);
                methodInvokeMethod.put(methodInvokeMethodEdge.getSource(), methodInvokeMethodEdges);
            } else {
                methodInvokeMethodEdges.add(methodInvokeMethodEdge);
            }
        }
        for (MethodQuoteFieldEdge methodQuoteFieldEdge : methodQuoteFieldEdgeMap.values()) {
            List<MethodQuoteFieldEdge> methodQuoteFieldEdges = methodQuoteField.get(methodQuoteFieldEdge.getSource());
            if (CollectionUtils.isEmpty(methodQuoteFieldEdges)) {
                methodQuoteFieldEdges = new ArrayList<>();
                methodQuoteFieldEdges.add(methodQuoteFieldEdge);
                methodQuoteField.put(methodQuoteFieldEdge.getSource(), methodQuoteFieldEdges);
            } else {
                methodQuoteFieldEdges.add(methodQuoteFieldEdge);
            }
        }
        return javaAnalysisRes;
    }


    public static void initClassRelation(ClassNode classNode,
                                         Map<String, ClassNode> classNodeMap,
                                         JavaAnalysisRes javaAnalysisRes) {
        for (String importStr : classNode.getImports()) {
            if (importStr.endsWith(".*")) {
                for (Map.Entry<String, ClassNode> entry : classNodeMap.entrySet()) {
                    String startStr = importStr.substring(0, importStr.length() - 1);
                    if (entry.getValue().getClassName().startsWith(startStr)) {
                        javaAnalysisRes.addClassImportClass(classNode.getVid(), entry.getValue().getVid());

                    }
                }
            } else {
                String vid = Md5Util.classNameToMd5(importStr, "");
                ClassNode importClassNode = classNodeMap.get(vid);
                if (null != importClassNode) {
                    javaAnalysisRes.addClassImportClass(classNode.getVid(), importClassNode.getVid());
                }
            }
        }
    }

    public static void initMethodRelation(ClassNode classNode,
                                          Map<String, MethodNode> methodNodeMap,
                                          Map<String, MethodNode> methodNameNodeMap,
                                          Map<String, List<MethodNode>> methodNumNodeMap,
                                          Map<String, MethodInvokeMethodEdge> methodInvokeMethodEdgeMap,
                                          Map<String, MethodQuoteFieldEdge> methodQuoteFieldEdgeMap,
                                          Map<String, ClassNode> classNameNodeMap) {
        for (MethodNode methodNode : classNode.getMethodNodes()) {
            if (CollectionUtils.isNotEmpty(classNode.getInterfaces())) {
                MethodNode implementNode = null;
                for (JavaImplements impl : classNode.getInterfaces()) {
                    if (StringUtils.isNotEmpty(impl.getClassName())) {
                        ClassNode implementClassNode = classNameNodeMap.get(impl.getClassName());
                        if (null != implementClassNode) {
                            implementNode = findByImplements(implementClassNode, methodNode, methodNameNodeMap, methodNumNodeMap, classNameNodeMap);
                            if (implementNode != null) {
                                break;
                            }
                        }
                    }
                }
                if (implementNode != null) {
                    // 递归
                    if (implementNode.getVid().equals(methodNode.getVid())) {
                        continue;
                    }
                    MethodInvokeMethodEdge methodInvokeMethodEdge = methodInvokeMethodEdgeMap.get(implementNode.getVid() + "->" + methodNode.getVid());
                    if (null != methodInvokeMethodEdge) {
                        InvokeParam invokeParam = new InvokeParam();
                        invokeParam.setInvokeLine(methodNode.getStartLine());
                        methodInvokeMethodEdge.getInvokeParams().add(invokeParam);
                    } else {
                        methodInvokeMethodEdge = new MethodInvokeMethodEdge();
                        methodInvokeMethodEdge.setSource(implementNode.getVid());
                        methodInvokeMethodEdge.setSourceType(implementNode.getSource());
                        methodInvokeMethodEdge.setTarget(methodNode.getVid());
                        methodInvokeMethodEdge.setTargetType(methodNode.getSource());
                        InvokeParam invokeParam = new InvokeParam();
                        invokeParam.setInvokeLine(methodNode.getStartLine());
                        methodInvokeMethodEdge.getInvokeParams().add(invokeParam);
                        methodInvokeMethodEdgeMap.put(implementNode.getVid() + "->" + methodNode.getVid(), methodInvokeMethodEdge);
                    }
                }
            }
            ClassNode methodClass = classNameNodeMap.get(methodNode.getClassName());
            for (MethodNode invoke : methodNode.getInvokeMethods()) {
                invoke.setVid(Md5Util.methodNodeToMd5(invoke, true));
                String invokeVid = invoke.getVidByName();
                MethodNode node = methodNameNodeMap.get(invokeVid);
                if (node != null) {
                    invokeVid = node.getVid();
                    invoke.setSource(NodeSourceEnum.LOCAL.getCode());
                } else {
                    boolean find = false;
                    invokeVid = invoke.getVidByNum();
                    List<MethodNode> nodes = methodNumNodeMap.get(invokeVid);
                    if (CollectionUtils.isNotEmpty(nodes)) {
                        if (nodes.size() == 1) {
                            invokeVid = nodes.get(0).getVid();
                            invoke.setSource(NodeSourceEnum.LOCAL.getCode());
                            find = true;
                        } else {
                            for (MethodNode no : nodes) {
                                if (invoke.getParamTypeStr().equals(no.getParamTypeStr())) {
                                    invokeVid = no.getVid();
                                    find = true;
                                    invoke.setSource(NodeSourceEnum.LOCAL.getCode());
                                    break;
                                }
                            }
                        }
                    }
                    if (!find) {
                        if (null != methodClass && StringUtils.isNotEmpty(methodClass.getSuperClass().getClassName())) {
                            MethodNode surperMethodNode = findBySuperClass(methodClass, invoke, methodNameNodeMap, methodNumNodeMap, classNameNodeMap);
                            if (surperMethodNode != null) {
                                find = true;
                                invoke.setSource(NodeSourceEnum.LOCAL.getCode());
                                invokeVid = surperMethodNode.getVid();
                            }
                        }
                    }

                    if (!find) {
                        invokeVid = invoke.getVid();
                    }
                }
                invoke.setVid(invokeVid);
                if (null == methodNodeMap.get(invokeVid)) {
                    methodNodeMap.put(invokeVid, invoke);
                }
                String source = methodNode.getVid();
                String target = invokeVid;
                //递归
                if (source.equals(target)) {
                    continue;
                }
                MethodInvokeMethodEdge methodInvokeMethodEdge = methodInvokeMethodEdgeMap.get(source + "->" + target);
                if (null != methodInvokeMethodEdge) {
                    InvokeParam invokeParam = new InvokeParam();
                    invokeParam.setInvokeLine(invoke.getStartLine());
                    methodInvokeMethodEdge.getInvokeParams().add(invokeParam);
                } else {
                    methodInvokeMethodEdge = new MethodInvokeMethodEdge();
                    methodInvokeMethodEdge.setSource(source);
                    methodInvokeMethodEdge.setSourceType(methodNode.getSource());
                    methodInvokeMethodEdge.setTarget(target);
                    methodInvokeMethodEdge.setTargetType(invoke.getSource());
                    InvokeParam invokeParam = new InvokeParam();
                    invokeParam.setInvokeLine(invoke.getStartLine());
                    methodInvokeMethodEdge.getInvokeParams().add(invokeParam);
                    methodInvokeMethodEdgeMap.put(source + "->" + target, methodInvokeMethodEdge);
                }
            }

            for (FieldNode fieldNode : methodNode.getQuoteFields()) {
                String invokeVid = Md5Util.fieldNodeToMd5(fieldNode);
                String source = methodNode.getVid();
                String target = invokeVid;
                MethodQuoteFieldEdge methodQuoteFieldEdge = methodQuoteFieldEdgeMap.get(source + "->" + target);
                if (null != methodQuoteFieldEdge) {
                    //todo 这样写有问题 暂不修正
                    methodQuoteFieldEdge.setQuoteLines(fieldNode.getQuotedLines());
                } else {
                    methodQuoteFieldEdge = new MethodQuoteFieldEdge();
                    methodQuoteFieldEdge.setSource(source);
                    methodQuoteFieldEdge.setSourceType(methodNode.getSource());
                    methodQuoteFieldEdge.setTarget(target);
                    methodQuoteFieldEdge.setTargetType(fieldNode.getSource());
                    methodQuoteFieldEdge.setQuoteLines(fieldNode.getQuotedLines());
                    methodQuoteFieldEdgeMap.put(source + "->" + target, methodQuoteFieldEdge);
                }
            }
        }
    }

    public static void initClassRelation(ClassNode classNode, Map<String, ClassNode> classNodeMap,
                                         Map<String, MethodNode> methodNodeMap,
                                         Map<String, MethodNode> methodNameNodeMap,
                                         Map<String, List<MethodNode>> methodNumNodeMap,
                                         Map<String, FieldNode> fieldNodeMap,
                                         JavaAnalysisRes javaAnalysisRes,
                                         Map<String, GitDiffInfo> gitDiffInfoMap) {
        classNode.setVid(Md5Util.classNodeToMd5(classNode));
        classNodeMap.put(classNode.getVid(), classNode);
        GitDiffInfo gitDiffInfo = gitDiffInfoMap.get(classNode.getClassPath());
        if (null != gitDiffInfo) {
            classNode.setModuleName(gitDiffInfo.getModuleName());
        }

        for (FieldNode fieldNode : classNode.getFieldNodes()) {
            fieldNode.setModuleName(classNode.getModuleName());
            fieldNode.setVid(Md5Util.fieldNodeToMd5(fieldNode));
            fieldNode.setSource(NodeSourceEnum.LOCAL.getCode());
            if (null == fieldNodeMap.get(fieldNode.getVid())) {
                fieldNodeMap.put(fieldNode.getVid(), fieldNode);
                javaAnalysisRes.addClassDefineField(classNode.getVid(), fieldNode.getVid());
            }
        }

        for (MethodNode methodNode : classNode.getMethodNodes()) {
            methodNode.setModuleName(classNode.getModuleName());
            methodNode.setSource(NodeSourceEnum.LOCAL.getCode());
            methodNode.setVid(Md5Util.methodNodeToMd5(methodNode, false));
            if (null == methodNodeMap.get(methodNode.getVid())) {
                methodNodeMap.put(methodNode.getVid(), methodNode);
                javaAnalysisRes.addClassDefineMethod(classNode.getVid(), methodNode.getVid());
            }
            if (methodNode.isOverloading()) {
                if (null == methodNumNodeMap.get(methodNode.getVidByNum())) {
                    List<MethodNode> methodNodes = new ArrayList<>();
                    methodNodes.add(methodNode);
                    methodNumNodeMap.put(methodNode.getVidByNum(), methodNodes);
                } else {
                    methodNumNodeMap.get(methodNode.getVidByNum()).add(methodNode);
                }
            } else {
                if (null == methodNameNodeMap.get(methodNode.getVidByName())) {
                    methodNameNodeMap.put(methodNode.getVidByName(), methodNode);
                }
            }
        }

    }

//    public static MyJavaClassVisitor parseFile(String path, String code, boolean init, Map<String, List<MethodNode>> classAndMethodMap, List<String> classList, Map<Object, List<FieldNode>> nameFieldMap) {
//        MyJavaClassVisitor visitor = new MyJavaClassVisitor(path, init, classAndMethodMap, classList, nameFieldMap);
//        JavaLexer lexer = new JavaLexer(CharStreams.fromString(code));
//        CommonTokenStream tokens = new CommonTokenStream(lexer);
//        JavaParser parser = new JavaParser(tokens);
//        ParseTree tree = parser.compilationUnit();
//        visitor.visit(tree);
//        InitJavaCommon.initCommon(visitor, tokens);
//        return visitor;
//    }

    public static MethodNode findBySuperClass(ClassNode invokeClassNode,
                                              MethodNode invoke,
                                              Map<String, MethodNode> methodNameNodeMap,
                                              Map<String, List<MethodNode>> methodNumNodeMap,
                                              Map<String, ClassNode> classNameNodeMap) {
        //todo 临时兼容错误解析父类类型导致的递归
        if (invokeClassNode.getSuperClass().getClassName().equals(invokeClassNode.getClassName())) {
            return null;
        }
        MethodNode surperMethodNode = null;
        String vid = Md5Util.methodNodeToMd5ByName(invokeClassNode.getSuperClass().getClassName(), "", invoke.getMethodName());
        surperMethodNode = methodNameNodeMap.get(vid);
        if (null != surperMethodNode) {
            return surperMethodNode;
        } else {
            vid = Md5Util.methodNodeToMd5ByNum(invokeClassNode.getSuperClass().getClassName(), "", invoke.getMethodName(), invoke.getParams().size());
            List<MethodNode> nodes = methodNumNodeMap.get(vid);
            if (null != nodes) {
                if (nodes.size() == 1) {
                    surperMethodNode = nodes.get(0);
                } else {
                    for (MethodNode methodNode : nodes) {
                        if (methodNode.getParamTypeStr().equals(invoke.getParamTypeStr())) {
                            surperMethodNode = methodNode;
                            break;
                        }
                    }
                }
            }
        }

        ClassNode surperClassNode = classNameNodeMap.get(invokeClassNode.getSuperClass().getClassName());
        if (null != surperClassNode && StringUtils.isNotEmpty(surperClassNode.getSuperClass().getClassName())) {
            surperMethodNode = findBySuperClass(surperClassNode, invoke, methodNameNodeMap, methodNumNodeMap, classNameNodeMap);
        }
        return surperMethodNode;
    }


    public static MethodNode findByImplements(ClassNode classNode,
                                              MethodNode methodNode,
                                              Map<String, MethodNode> methodNameNodeMap,
                                              Map<String, List<MethodNode>> methodNumNodeMap,
                                              Map<String, ClassNode> classNameNodeMap) {
        MethodNode implementsMethodNode = null;
        String vid = Md5Util.methodNodeToMd5ByName(classNode.getClassName(), "", methodNode.getMethodName());
        implementsMethodNode = methodNameNodeMap.get(vid);
        if (null != implementsMethodNode) {
            return implementsMethodNode;
        } else {
            vid = Md5Util.methodNodeToMd5ByNum(classNode.getClassName(), "", methodNode.getMethodName(), methodNode.getParams().size());
            List<MethodNode> nodes = methodNumNodeMap.get(vid);
            if (null != nodes) {
                if (nodes.size() == 1) {
                    implementsMethodNode = nodes.get(0);
                } else {
                    for (MethodNode node : nodes) {
                        if (node.getParamTypeStr().equals(methodNode.getParamTypeStr())) {
                            implementsMethodNode = node;
                            break;
                        }
                    }
                }
            }
        }

        if (CollectionUtils.isNotEmpty(classNode.getInterfaces())) {
            for (JavaImplements impl : classNode.getInterfaces()) {
                if (StringUtils.isNotEmpty(impl.getClassName())) {
                    // todo 临时兼容错误解析导致的递归
                    if (!impl.getClassName().equals(methodNode.getClassName())) {
                        ClassNode implementClassNode = classNameNodeMap.get(impl.getClassName());
                        if (null != implementClassNode) {
                            implementsMethodNode = findByImplements(implementClassNode, methodNode, methodNameNodeMap, methodNumNodeMap, classNameNodeMap);
                        }
                    }
                }
            }
        }
        return implementsMethodNode;
    }
}
