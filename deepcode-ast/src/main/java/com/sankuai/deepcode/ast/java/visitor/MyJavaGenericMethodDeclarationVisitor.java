package com.sankuai.deepcode.ast.java.visitor;

import com.sankuai.deepcode.ast.java.gen.JavaParser;
import com.sankuai.deepcode.ast.java.gen.JavaParserBaseVisitor;
import com.sankuai.deepcode.ast.java.visitor.MyJavaMethodDeclarationVisitor;
import com.sankuai.deepcode.ast.model.java.ClassNode;
import com.sankuai.deepcode.ast.model.java.MethodNode;
import lombok.Getter;

public class MyJavaGenericMethodDeclarationVisitor extends JavaParserBaseVisitor<Object> {
    private ClassNode classNode = new ClassNode();

    public MyJavaGenericMethodDeclarationVisitor(ClassNode myClassNode) {
        classNode = myClassNode;
    }

    @Getter
    private MethodNode methodNode;


    @Override
    public Object visitGenericMethodDeclaration(JavaParser.GenericMethodDeclarationContext ctx) {
        com.sankuai.deepcode.ast.java.visitor.MyJavaMethodDeclarationVisitor visitor = new MyJavaMethodDeclarationVisitor(classNode);
        visitor.visit(ctx.methodDeclaration());
        methodNode = visitor.getMethodNode();
        return null;
    }

}
