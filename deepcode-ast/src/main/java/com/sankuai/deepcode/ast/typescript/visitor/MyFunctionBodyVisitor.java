package com.sankuai.deepcode.ast.typescript.visitor;

import com.sankuai.deepcode.ast.model.typescript.ScriptNode;
import com.sankuai.deepcode.ast.model.typescript.ScriptMethod;
import com.sankuai.deepcode.ast.typescript.gen.TypeScriptParser;
import com.sankuai.deepcode.ast.typescript.gen.TypeScriptParserBaseVisitor;
import org.apache.commons.collections4.CollectionUtils;

public class MyFunctionBodyVisitor extends TypeScriptParserBaseVisitor<Object> {

    public static void visitFunctionBody(TypeScriptParser.FunctionBodyContext ctx, ScriptMethod scriptMethod) {
        if (ctx != null && ctx.sourceElements() != null) {
            for (TypeScriptParser.SourceElementContext sourceElementContext : ctx.sourceElements().sourceElement()) {
                if (sourceElementContext.statement() != null) {
                    com.sankuai.deepcode.ast.typescript.visitor.MyStatementVisitor myStatementVisitor = new MyStatementVisitor(null);
                    myStatementVisitor.visit(sourceElementContext.statement());

                    ScriptNode scriptNode = myStatementVisitor.getScriptNode();
                    //todo 方法body暂时只处理方法 变量 和jsx html
                    if (CollectionUtils.isNotEmpty(scriptNode.getInvokeMethods())) {
                        for (ScriptMethod invokeMethod : scriptNode.getInvokeMethods()) {
                            scriptMethod.getInvokeMethods().add(invokeMethod);
                        }
                    }
                    if (CollectionUtils.isNotEmpty(scriptNode.getHtmlNodes())) {
                        scriptMethod.getHtmlNodes().addAll(scriptNode.getHtmlNodes());
                    }
                    if (CollectionUtils.isNotEmpty(scriptNode.getVariables())) {
                        scriptMethod.getVariables().addAll(scriptNode.getVariables());
                    }
                }
            }
        }
    }
}
