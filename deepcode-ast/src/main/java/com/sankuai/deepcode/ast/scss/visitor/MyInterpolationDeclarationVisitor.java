package com.sankuai.deepcode.ast.scss.visitor;

import com.sankuai.deepcode.ast.model.scss.ScssVariable;
import com.sankuai.deepcode.ast.scss.gen.ScssParser;
import com.sankuai.deepcode.ast.scss.gen.ScssParserBaseVisitor;
import lombok.Getter;

public class MyInterpolationDeclarationVisitor extends ScssParserBaseVisitor<Object> {

    @Getter
    private ScssVariable scssVariable;


    @Override
    public Object visitPropertyDeclaration(ScssParser.PropertyDeclarationContext ctx) {
        scssVariable = new ScssVariable();
        scssVariable.setName(ctx.identifier().getText());
        visitPropertyValue(ctx.propertyValue());
        return null;
    }


    @Override
    public Object visitPropertyValue(ScssParser.PropertyValueContext ctx) {
        scssVariable.setValue(ctx.getText());
//todo 简单处理成string
//        propertyValue
//        : (
//                value
//                        | value? prio? block
//                        | variableName
//                        | listSpaceSeparated
//                        | listCommaSeparated
//                        | expression
//                        | functionCall
//        ) prio?
//        ;
        return null;
    }


}
