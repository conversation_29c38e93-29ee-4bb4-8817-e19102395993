package com.sankuai.deepcode.ast.typescript.visitor;

import com.sankuai.deepcode.ast.model.typescript.ScriptNode;
import com.sankuai.deepcode.ast.model.typescript.ScriptImport;
import com.sankuai.deepcode.ast.model.typescript.ScriptImportModule;
import com.sankuai.deepcode.ast.model.typescript.ScriptImportNameSpace;
import com.sankuai.deepcode.ast.typescript.gen.TypeScriptParser;
import com.sankuai.deepcode.ast.typescript.gen.TypeScriptParserBaseVisitor;

import java.util.ArrayList;
import java.util.List;


public class MyExportStatementVisitor extends TypeScriptParserBaseVisitor<Object> {


    private ScriptNode scriptNode;

    MyExportStatementVisitor(ScriptNode myScriptNode) {
        scriptNode = myScriptNode;
        scriptNode.setExport(true);
    }

    @Override
    public Object visitExportDeclaration(TypeScriptParser.ExportDeclarationContext ctx) {
        if (ctx.exportFromBlock() != null) {
            ScriptImport scriptImport = new ScriptImport();
            scriptImport.setExport(true);
            if (ctx.exportFromBlock().importNamespace() != null) {
                ScriptImportNameSpace nameSpace = new ScriptImportNameSpace();
                scriptImport.setNameSpace(nameSpace);
                if (ctx.exportFromBlock().importNamespace().Multiply() != null) {
                    nameSpace.setName(ctx.exportFromBlock().importNamespace().Multiply().getText());
                } else {
                    nameSpace.setName(ctx.exportFromBlock().importNamespace().identifierName(0).getText());
                }
                if (ctx.exportFromBlock().importNamespace().As() != null) {
                    if (ctx.exportFromBlock().importNamespace().identifierName().size() == 1) {
                        nameSpace.setAsName(ctx.exportFromBlock().importNamespace().identifierName(0).getText());
                    } else {
                        nameSpace.setAsName(ctx.exportFromBlock().importNamespace().identifierName(1).getText());
                    }
                }
            } else if (ctx.exportFromBlock().exportModuleItems() != null) {
                List<ScriptImportModule> moduleNames = new ArrayList<>();
                for (TypeScriptParser.ExportAliasNameContext exportAliasNameContext : ctx.exportFromBlock().exportModuleItems().exportAliasName()) {
                    ScriptImportModule scriptImportModule = new ScriptImportModule();
                    scriptImportModule.setExportName(exportAliasNameContext.moduleExportName().get(0).getText());
                    if (exportAliasNameContext.As() != null) {
                        scriptImportModule.setAsName(exportAliasNameContext.moduleExportName().get(1).getText());
                    }
                    moduleNames.add(scriptImportModule);
                }
                scriptImport.setModuleNames(moduleNames);
            }
            if (ctx.exportFromBlock().importFrom() != null) {
                scriptImport.setFromName(ctx.exportFromBlock().importFrom().StringLiteral().getText());
            }
            scriptNode.getImports().add(scriptImport);
        } else if (ctx.declaration() != null) {
            if (ctx.declaration().variableStatement() != null) {
                MyVariableStatementVisitor myVariableStatementVisitor = new MyVariableStatementVisitor(scriptNode);
                myVariableStatementVisitor.visit(ctx.declaration().variableStatement());
            } else if (ctx.declaration().classDeclaration() != null) {
                MyClassDeclarationVisitor myClassDeclarationVisitor = new MyClassDeclarationVisitor(scriptNode);
                myClassDeclarationVisitor.visit(ctx.declaration().classDeclaration());
            } else if (ctx.declaration().functionDeclaration() != null) {
                MyFunctionDeclarationVisitor myFunctionDeclarationVisitor = new MyFunctionDeclarationVisitor(scriptNode);
                myFunctionDeclarationVisitor.visit(ctx.declaration().functionDeclaration());
            }
        }
        scriptNode.setExport(false);
        return null;
    }


    @Override
    public Object visitExportDefaultDeclaration(TypeScriptParser.ExportDefaultDeclarationContext ctx) {
        MySingleExpressionVisitor mySingleExpressionVisitor = new MySingleExpressionVisitor(scriptNode);
        mySingleExpressionVisitor.visit(ctx.singleExpression());
        scriptNode.setExport(false);
        return null;
    }

}
