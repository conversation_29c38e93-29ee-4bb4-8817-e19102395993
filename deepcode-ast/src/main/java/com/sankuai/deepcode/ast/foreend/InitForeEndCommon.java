package com.sankuai.deepcode.ast.foreend;

import com.sankuai.deepcode.ast.model.foreend.ForeEndComment;
import org.antlr.v4.runtime.CommonTokenStream;
import org.antlr.v4.runtime.Token;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

public class InitForeEndCommon {

    public static void initCommonByToken(CommonTokenStream tokens, List<Integer> commentLines) {
        List<ForeEndComment> foreEndComments = getHiddenTokens(tokens);
        Iterator<ForeEndComment> iterator = foreEndComments.iterator();
        while (iterator.hasNext()) {
            ForeEndComment foreEndComment = iterator.next();
            for (int i = foreEndComment.getStartLine(); i <= foreEndComment.getEndLine(); i++) {
                commentLines.add(i);
            }
        }
    }


    public static List<ForeEndComment> getHiddenTokens(CommonTokenStream tokens) {
        List<ForeEndComment> hiddenTokens = new ArrayList<>();
        for (Token token : tokens.getTokens()) {
            if (token.getChannel() == Token.HIDDEN_CHANNEL) {
                if (StringUtils.isNotEmpty(token.getText().trim())) {
                    ForeEndComment foreEndComment = new ForeEndComment();
                    foreEndComment.setStartLine(token.getLine());
                    Token nextToken = tokens.get(token.getTokenIndex() + 1);
                    if (null != nextToken) {
                        foreEndComment.setEndLine(nextToken.getLine());
                    } else {
                        foreEndComment.setStartLine(token.getLine());
                    }
                    hiddenTokens.add(foreEndComment);
                }
            }
        }
        return hiddenTokens;
    }
}
