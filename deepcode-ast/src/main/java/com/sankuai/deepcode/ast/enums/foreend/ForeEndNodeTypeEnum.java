package com.sankuai.deepcode.ast.enums.foreend;

public enum ForeEndNodeTypeEnum {
    HTML(0, "html", "html节点"),
    JS_METHOD(1, "jsMethod", "js方法节点"),
    JS_FIELD(2, "js<PERSON><PERSON>", "js变量节点"),
    STYLE(3, "sttle", "样式节点");

    private int code;
    private String key;
    private String msg;

    ForeEndNodeTypeEnum(int code, String key, String msg) {
        this.code = code;
        this.key = key;
        this.msg = msg;
    }

    public int getCode() {
        return this.code;
    }

    public String getKey() {
        return this.key;
    }

    public String getMsg() {
        return this.msg;
    }


    @Override
    public String toString() {
        return "DiffTypeEnum{" +
                "code=" + code +
                ", key='" + key + '\'' +
                ", msg='" + msg + '\'' +
                '}';
    }
}
