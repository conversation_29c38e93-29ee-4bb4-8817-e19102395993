package com.sankuai.deepcode.ast.python3.visitor;

import com.sankuai.deepcode.ast.model.python3.*;
import com.sankuai.deepcode.ast.python3.gen.PythonParser;
import com.sankuai.deepcode.ast.python3.gen.PythonParserBaseVisitor;
import lombok.Getter;

/**
 * <AUTHOR>
 */
@Getter
public class PythonModuleVisitor extends PythonParserBaseVisitor<Object> {
    private final PythonAnalysesResult result;
    private final PythonModuleNode moduleNode;
    private final ScopeManager scopeManager;

    public PythonModuleVisitor(PythonModuleNode moduleNode, PythonAnalysesResult result) {
        this.result = result;
        this.moduleNode = moduleNode;

        result.getScopeManagerMap().computeIfAbsent(moduleNode.getModulePath(), k -> new ScopeManager(moduleNode.getModuleName()));
        this.scopeManager = result.getScopeManagerMap().get(moduleNode.getModulePath());
    }

    @Override
    public Object visitFile_input(PythonParser.File_inputContext ctx) {
        if (ctx.statements() != null && !ctx.statements().statement().isEmpty()) {
            PythonParser.StatementContext firstStmt = ctx.statements().statement(0);
            if (firstStmt.simple_stmts() != null) {
                PythonParser.Simple_stmtContext simpleStmt = firstStmt.simple_stmts().simple_stmt(0);
                if (simpleStmt.star_expressions() != null) {
                    String possibleComment = simpleStmt.star_expressions().getText();
                    possibleComment = possibleComment.replaceAll("^[\"']{3}|[\"']{3}$", "").trim();
                    if (!possibleComment.isEmpty()) {
                        moduleNode.setComment(possibleComment);
                    }
                }
            }
        }
        return super.visitFile_input(ctx);
    }


    @Override
    public Object visitAssignment(PythonParser.AssignmentContext ctx) {
        PythonAssignmentVisitor assignmentVisitor = new PythonAssignmentVisitor(moduleNode.getFileName(), moduleNode.getModulePath(), scopeManager);
        assignmentVisitor.visit(ctx);
        PythonParameterNode paramNode = assignmentVisitor.getParamNode();
        moduleNode.getModuleParamNodes().add(paramNode);

        return super.visitAssignment(ctx);
    }


    @Override
    public Object visitClass_def(PythonParser.Class_defContext ctx) {
        PythonClassVisitor classVisitor = new PythonClassVisitor(moduleNode.getFileName(), moduleNode.getModulePath(), scopeManager);
        classVisitor.visit(ctx);
        PythonClassNode classNode = classVisitor.getClassNode();
        scopeManager.addToScope(classNode.getName(), moduleNode.getModulePath() + "." + classNode.getName(), ctx.getStart().getLine());
        moduleNode.getClassNodes().add(classNode);
        return null;
    }

    @Override
    public Object visitImport_stmt(PythonParser.Import_stmtContext ctx) {
        PythonImportVisitor importVisitor = new PythonImportVisitor(moduleNode.getFileName(), moduleNode.getModuleName(), moduleNode.getModulePath(), scopeManager);
        importVisitor.visitImport_stmt(ctx);
        moduleNode.getImportNodes().addAll(importVisitor.getImportNodes());
        return super.visitImport_stmt(ctx);
    }

    @Override
    public Object visitFunction_def(PythonParser.Function_defContext ctx) {
        PythonFunctionDefVisitor functionVisitor = new PythonFunctionDefVisitor(moduleNode.getFileName(), moduleNode.getModulePath(), scopeManager);
        functionVisitor.visit(ctx);
        scopeManager.addToScope(functionVisitor.getFunctionNode().getName(), moduleNode.getModulePath() + "." + functionVisitor.getFunctionNode().getName(), ctx.getStart().getLine());
        moduleNode.getFunctionNodes().add(functionVisitor.getFunctionNode());
        return null;
    }

    @Override
    public Object visitFunction_call(PythonParser.Function_callContext ctx) {
        // 传入当前模块所在的路径，用于解析函数调用的路径
        PythonFunctionCallVisitor functionCallVisitor = new PythonFunctionCallVisitor(moduleNode.getFileName(), moduleNode.getModulePath(), scopeManager);
        functionCallVisitor.visit(ctx);
        moduleNode.getFunctionCallNodes().addAll(functionCallVisitor.getFunctionCallNodes());
        // 返回 null，不再进行子节点的访问，只访问到当前层的节点方法调用
        return null;
    }

    @Override
    public Object visitPrimary(PythonParser.PrimaryContext ctx) {
        // 检查是否包含函数调用（即包含 '(' arguments? ')' 的primary_suffix）
        boolean hasFunctionCall = false;
        for (PythonParser.Primary_suffixContext suffix : ctx.primary_suffix()) {
            if (suffix.LPAR() != null) {
                hasFunctionCall = true;
                break;
            }
        }

        if (hasFunctionCall) {
            // 传入当前模块所在的路径，用于解析函数调用的路径
            PythonFunctionCallVisitor functionCallVisitor = new PythonFunctionCallVisitor(moduleNode.getFileName(), moduleNode.getModulePath(), scopeManager);
            functionCallVisitor.visitPrimary(ctx);
            moduleNode.getFunctionCallNodes().addAll(functionCallVisitor.getFunctionCallNodes());
        }

        return super.visitPrimary(ctx);
    }


    @Override
    public Object visitDel_stmt(PythonParser.Del_stmtContext ctx) {
        PythonDelVisitor delVisitor = new PythonDelVisitor(moduleNode.getFileName(), moduleNode.getModuleName(), moduleNode.getModulePath(), scopeManager);
        delVisitor.visitDel_stmt(ctx);
        return super.visitDel_stmt(ctx);
    }
}
