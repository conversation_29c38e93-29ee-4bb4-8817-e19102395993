package com.sankuai.deepcode.ast.model.typescript;

import com.sankuai.deepcode.ast.model.typescript.ScriptLiteral;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
public class ScriptTypeAlias {
    private boolean isExport;
    private int startLine;
    private int endLine;
    private List<ScriptLiteral> literal = new ArrayList<>();
    private String type;  //array object
    private String filePath;
}
