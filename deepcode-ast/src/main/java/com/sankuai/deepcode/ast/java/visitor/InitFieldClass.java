package com.sankuai.deepcode.ast.java.visitor;

import com.sankuai.deepcode.ast.model.java.ClassNode;
import com.sankuai.deepcode.ast.model.java.FieldNode;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Set;

public class InitFieldClass {

    public static void init(ClassNode classNode, List<FieldNode> fieldNodes, Set<String> classList) {
        if (CollectionUtils.isNotEmpty(fieldNodes)) {
            for (FieldNode fieldNode : fieldNodes) {
                if (StringUtils.isEmpty(fieldNode.getFieldType())) {
                    continue;
                }
                boolean add = false;
                for (String importStr : classNode.getImports()) {
                    if (importStr.endsWith("." + fieldNode.getFieldType())) {
                        fieldNode.setClassName(importStr);
                        add = true;
                        break;
                    }
                    if (importStr.endsWith("*")) {
                        String name = importStr.split("\\*")[0];
                        name += "." + fieldNode.getFieldType();
                        if (classList.contains(name)) {
                            fieldNode.setClassName(name);
                            add = true;
                            break;
                        }
                    }
                }
                if (!add) {
                    String name = classNode.getClassName().split("\\.")[classNode.getClassName().split("\\.").length - 1];
                    String packageName = classNode.getClassName().split("\\." + name)[0];
                    String samePackageClass = packageName + "." + fieldNode.getFieldType();
                    if (classList.contains(samePackageClass)) {
                        fieldNode.setClassName(samePackageClass);
                        add = true;
                    }
                }
            }

        }
    }

}
