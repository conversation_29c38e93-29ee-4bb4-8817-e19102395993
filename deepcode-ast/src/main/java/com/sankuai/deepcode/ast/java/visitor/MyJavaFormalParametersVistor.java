package com.sankuai.deepcode.ast.java.visitor;

import com.sankuai.deepcode.ast.java.gen.JavaParser;
import com.sankuai.deepcode.ast.java.gen.JavaParserBaseVisitor;
import com.sankuai.deepcode.ast.model.java.JavaAnnotation;
import com.sankuai.deepcode.ast.model.java.JavaGenerics;
import com.sankuai.deepcode.ast.model.java.JavaParam;
import lombok.Getter;
import org.antlr.v4.runtime.tree.TerminalNode;
import org.apache.commons.collections4.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

public class MyJavaFormalParametersVistor extends JavaParserBaseVisitor<Object> {

    @Getter
    private List<JavaParam> params = new ArrayList<>();

//    //TODO  接收者参数未支持
//    @Override
//    public Object visitReceiverParameter(JavaParser.ReceiverParameterContext ctx) {
//        return null;
//    }

    @Override
    public Object visitFormalParameters(JavaParser.FormalParametersContext ctx) {
        if (null != ctx.formalParameterList()) {
            if (CollectionUtils.isNotEmpty(ctx.formalParameterList().formalParameter())) {
                for (JavaParser.FormalParameterContext parameterContext : ctx.formalParameterList().formalParameter()) {
                    visitFormalParameter(parameterContext);
                }
            }
            if (null != ctx.formalParameterList().lastFormalParameter()) {
                visitLastFormalParameter(ctx.formalParameterList().lastFormalParameter());
            }
        }
        return null;
    }


    @Override
    public Object visitFormalParameter(JavaParser.FormalParameterContext ctx) {
        JavaParam javaParam = new JavaParam();
        String access = "";
        List<JavaAnnotation> annotations = new ArrayList<>();
        for (JavaParser.VariableModifierContext modifierCtx : ctx.variableModifier()) {
            if (null != modifierCtx.annotation()) {
                com.sankuai.deepcode.ast.java.visitor.MyJavaAnnotationVisitor visitor = new com.sankuai.deepcode.ast.java.visitor.MyJavaAnnotationVisitor();
                visitor.visit(modifierCtx.annotation());
                annotations.add(visitor.getAnnotation());
            } else {
                access += " " + modifierCtx.getText();
            }
        }
        javaParam.setAccess(access.trim());
        javaParam.setAnnotations(annotations);
        javaParam.setName(ctx.variableDeclaratorId().identifier().getText());
        if (CollectionUtils.isNotEmpty(ctx.variableDeclaratorId().LBRACK())) {
            List<JavaGenerics> generics = new ArrayList<>();
            for (TerminalNode terminalNode : ctx.variableDeclaratorId().LBRACK()) {
                JavaGenerics javaGenerics = new JavaGenerics();
                javaGenerics.setName("[]");
                generics.add(javaGenerics);
            }
            javaParam.setGenerics(generics);
        }

        com.sankuai.deepcode.ast.java.visitor.MyJavaTypeTypeVistor vistor = new com.sankuai.deepcode.ast.java.visitor.MyJavaTypeTypeVistor();
        vistor.visit(ctx.typeType());
        javaParam.setType(vistor.getType());
        if (CollectionUtils.isNotEmpty(vistor.getGenerics())) {
            javaParam.setGenerics(vistor.getGenerics());
        }
        params.add(javaParam);
        return null;
    }

    @Override
    public Object visitLastFormalParameter(JavaParser.LastFormalParameterContext ctx) {
        JavaParam javaParam = new JavaParam();
        String access = "";
        List<JavaAnnotation> annotations = new ArrayList<>();
        for (JavaParser.VariableModifierContext modifierCtx : ctx.variableModifier()) {
            if (null != modifierCtx.annotation()) {
                com.sankuai.deepcode.ast.java.visitor.MyJavaAnnotationVisitor visitor = new MyJavaAnnotationVisitor();
                visitor.visit(modifierCtx.annotation());
                annotations.add(visitor.getAnnotation());
            } else {
                access += " " + modifierCtx.getText();
            }
        }
        javaParam.setAccess(access.trim());
        javaParam.setAnnotations(annotations);
        javaParam.setName(ctx.variableDeclaratorId().identifier().getText());
        if (CollectionUtils.isNotEmpty(ctx.variableDeclaratorId().LBRACK())) {
            List<JavaGenerics> generics = new ArrayList<>();
            for (TerminalNode terminalNode : ctx.variableDeclaratorId().LBRACK()) {
                JavaGenerics javaGenerics = new JavaGenerics();
                javaGenerics.setName("[]");
                generics.add(javaGenerics);
            }
            javaParam.setGenerics(generics);
        } else if (null != ctx.ELLIPSIS()) {
            List<JavaGenerics> generics = new ArrayList<>();
            JavaGenerics javaGenerics = new JavaGenerics();
            javaGenerics.setName("...");
            generics.add(javaGenerics);
            javaParam.setGenerics(generics);
        }
        com.sankuai.deepcode.ast.java.visitor.MyJavaTypeTypeVistor vistor = new MyJavaTypeTypeVistor();
        vistor.visit(ctx.typeType());
        javaParam.setType(vistor.getType());
        if (CollectionUtils.isNotEmpty(vistor.getGenerics())) {
            javaParam.setGenerics(vistor.getGenerics());
        }
        params.add(javaParam);
        return null;
    }

}
