package com.sankuai.deepcode.ast.scss.visitor;

import com.sankuai.deepcode.ast.model.scss.ScssNode;
import com.sankuai.deepcode.ast.scss.gen.ScssParser;
import com.sankuai.deepcode.ast.scss.gen.ScssParserBaseVisitor;
import com.sankuai.deepcode.ast.scss.visitor.MyImportDeclarationVisitor;
import lombok.Getter;
import org.apache.commons.collections4.CollectionUtils;

public class MyScssVisitor extends ScssParserBaseVisitor<Object> {

    @Getter
    private ScssNode scssNode;

    @Override
    public Object visitStylesheet(ScssParser.StylesheetContext ctx) {
        scssNode = new ScssNode();
        if (CollectionUtils.isNotEmpty(ctx.statement())) {
            for (ScssParser.StatementContext statementContext : ctx.statement()) {
                visitStatement(statementContext);
            }
        }
        return null;
    }


    @Override
    public Object visitStatement(ScssParser.StatementContext ctx) {
        if (ctx.importDeclaration() != null) {
            MyImportDeclarationVisitor visitor = new MyImportDeclarationVisitor();
            visitor.visitImportDeclaration(ctx.importDeclaration());
            scssNode.getImports().add(visitor.getScssImport());
        }
        if (ctx.variableDeclaration() != null) {
            MyVariableDeclarationVisitor visitor = new MyVariableDeclarationVisitor();
            visitor.visitVariableDeclaration(ctx.variableDeclaration());
            scssNode.getVariables().add(visitor.getScssVariable());
        }
        if (ctx.ruleset() != null) {
            MyRulesetVisitor visitor = new MyRulesetVisitor(null);
            visitor.visitRuleset(ctx.ruleset());
            scssNode.getScssRules().add(visitor.getScssRule());
        }
        if (ctx.mixinDeclaration() != null) {
            MyMixinDeclarationVisitor visitor = new MyMixinDeclarationVisitor();
            visitor.visitMixinDeclaration(ctx.mixinDeclaration());
            scssNode.getScssRules().add(visitor.getScssRule());
        }
        if (ctx.mediaDeclaration() != null) {
            MyMediaDeclarationVisitor visitor = new MyMediaDeclarationVisitor();
            visitor.visitMediaDeclaration(ctx.mediaDeclaration());
            scssNode.getScssRules().add(visitor.getScssRule());
        }
        if (ctx.functionDeclaration() != null) {
            MyFunctionDeclarationVisitor visitor = new MyFunctionDeclarationVisitor();
            visitor.visitFunctionDeclaration(ctx.functionDeclaration());
            scssNode.getScssRules().add(visitor.getScssRule());
        }
        if (ctx.errorDeclaration() != null) {
            MyErrorDeclarationVisitor visitor = new MyErrorDeclarationVisitor();
            visitor.visitErrorDeclaration(ctx.errorDeclaration());
            scssNode.getVariables().add(visitor.getScssVariable());
        }
        if (ctx.warndingDeclaration() != null) {
            MyWarndingDeclarationVisitor visitor = new MyWarndingDeclarationVisitor();
            visitor.visitWarndingDeclaration(ctx.warndingDeclaration());
            scssNode.getVariables().add(visitor.getScssVariable());
        }
        return null;
    }


}
