package com.sankuai.deepcode.ast.enums.java;

public enum ClassTypeEnum {
    CLASS("class", "普通类"),
    ENUM("enum", "枚举类"),
    INTERFACE("interface", "接口类"),
    ANNOTATION("annotation", "注解类");

    private String code;

    private String desc;

    ClassTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return this.code;
    }


    public String getDesc() {
        return desc;
    }

    @Override
    public String toString() {
        return "ErrorCodeEnum{" +
                "code=" + code +
                ", desc='" + desc + '\'' +
                '}';
    }
}
