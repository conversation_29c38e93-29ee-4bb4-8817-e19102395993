package com.sankuai.deepcode.ast.java.visitor;//package com.sankuai.deepcode.ast.java.visitor;
//
//import java.util.ArrayList;
//import java.util.HashMap;
//import java.util.List;
//import java.util.Map;
//
//import com.sankuai.deepcode.ast.model.java.*;
//import org.apache.commons.lang3.StringUtils;
//
//import com.sankuai.deepcode.ast.java.gen.JavaParser;
//import com.sankuai.deepcode.ast.java.gen.JavaParserBaseVisitor;
//
//import lombok.Getter;
//
//public class MyJavaMethodVisitor extends JavaParserBaseVisitor<Object> {
//
//    private boolean initial = false;
//    private static Map<String, List<MethodNode>> allClassAndMethodMap = new HashMap<>();
//    private static List<String> allClassList = new ArrayList<>();
//    private static ClassNode parentClassNode = new ClassNode();
//    private Map<String, FieldNode> classFields = new HashMap<>();
//    private Map<Object, List<FieldNode>> fieldNames = new HashMap<>();
//
//    public MyJavaMethodVisitor(boolean init, ClassNode classNode, Map<String, List<MethodNode>> classAndMethodMap, List<String> classList, Map<String, FieldNode> fieldMap, Map<Object, List<FieldNode>> nameFieldMap) {
//        initial = init;
//        allClassAndMethodMap = classAndMethodMap;
//        allClassList = classList;
//        parentClassNode = classNode;
//        classFields = fieldMap;
//        fieldNames = nameFieldMap;
//        putClassFieldNameAndType(classNode.getFieldNodes());
//    }
//
//    @Getter
//    private MethodNode methodNode = new MethodNode();
//
//    private Map<String, String> localFieldNameAndType = new HashMap<>();
//
//    public void putLocalFieldName(List<FieldNode> fieldNodes) {
//        for (FieldNode fieldNode : fieldNodes) {
//            localFieldNameAndType.put(fieldNode.getFieldName(), fieldNode.getFieldType());
//        }
//    }
//
//    public Map<String, String> classFieldNameAndType = new HashMap<>();
//
//    public void putClassFieldNameAndType(List<FieldNode> fieldNodes) {
//        for (FieldNode fieldNode : fieldNodes) {
//            classFieldNameAndType.put(fieldNode.getFieldName(), fieldNode.getFieldType());
//        }
//    }
//
//    @Getter
//    private int complexity = 1;
//
//
//    @Override
//    public Object visitMethodDeclaration(JavaParser.MethodDeclarationContext ctx) {
//        methodNode.setMethodName(ctx.identifier().getText());
//        Object res = visitChildren(ctx);
//        methodNode.setBody(ctx.getText());
//        methodNode.setComplexity(complexity);
//        return res;
//    }
//
//    @Override
//    public Object visitInterfaceMethodDeclaration(JavaParser.InterfaceMethodDeclarationContext ctx) {
//        methodNode.setMethodName(ctx.interfaceCommonBodyDeclaration().identifier().getText());
//        Object res = visitChildren(ctx);
//        methodNode.setBody(ctx.getText());
//        methodNode.setComplexity(complexity);
//        return res;
//    }
//
//    @Override
//    public Object visitGenericInterfaceMethodDeclaration(JavaParser.GenericInterfaceMethodDeclarationContext ctx) {
//        methodNode.setMethodName(ctx.interfaceCommonBodyDeclaration().identifier().getText());
//        Object res = visitChildren(ctx);
//        methodNode.setBody(ctx.getText());
//        methodNode.setComplexity(complexity);
//        return res;
//    }
//
//    @Override
//    public Object visitGenericConstructorDeclaration(JavaParser.GenericConstructorDeclarationContext ctx) {
//        methodNode.setMethodName(ctx.constructorDeclaration().identifier().getText());
//        Object res = visitChildren(ctx);
//        methodNode.setBody(ctx.getText());
//        methodNode.setComplexity(complexity);
//        return res;
//    }
//
//    @Override
//    public Object visitConstructorDeclaration(JavaParser.ConstructorDeclarationContext ctx) {
//        methodNode.setMethodName(ctx.identifier().getText());
//        Object res = visitChildren(ctx);
//        methodNode.setBody(ctx.getText());
//        methodNode.setComplexity(complexity);
//        return res;
//    }
//
//
//    @Override
//    public Object visitTypeTypeOrVoid(JavaParser.TypeTypeOrVoidContext ctx) {
//        JavaReturn javaReturn = new JavaReturn();
//        if (null != ctx.typeType()) {
//            MyJavaTypeTypeVistor vistor = new MyJavaTypeTypeVistor();
//            vistor.visit(ctx.typeType());
//            javaReturn.setType(vistor.getType());
//            List<String> signatures = new ArrayList<>();
//            for (JavaGenerics javaGenerics : vistor.getGenerics()) {
//                signatures.add(javaGenerics.getName());
//            }
//            javaReturn.setSignatures(signatures);
//            javaReturn.setAnnotations(vistor.getAnnotations());
//        } else {
//            javaReturn.setType(ctx.getText());
//        }
//        methodNode.setReturnInfo(javaReturn);
//        return visitChildren(ctx);
//    }
//
//    @Override
//    public Object visitTypeType(JavaParser.TypeTypeContext ctx) {
//        if (null == methodNode.getReturnInfo()) {
//            JavaReturn javaReturn = new JavaReturn();
//            javaReturn.setType(ctx.getText());
//            methodNode.setReturnInfo(javaReturn);
//        }
//        return visitChildren(ctx);
//    }
//
//    @Override
//    public Object visitPrimitiveType(JavaParser.PrimitiveTypeContext ctx) {
//        JavaReturn javaReturn = new JavaReturn();
//        javaReturn.setType(ctx.getText());
//        methodNode.setReturnInfo(javaReturn);
//        return visitChildren(ctx);
//    }
//
//
//    @Override
//    public Object visitFormalParameters(JavaParser.FormalParametersContext ctx) {
//        MyJavaFormalParametersVistor vistor = new MyJavaFormalParametersVistor();
//        vistor.visitFormalParameters(ctx);
//        methodNode.setParams(vistor.getParams());
//        for (JavaParam paramNode : vistor.getParams()) {
//            localFieldNameAndType.put(paramNode.getName(), paramNode.getType());
//        }
//        return visitChildren(ctx);
//    }
//
//
//    @Override
//    public Object visitIdentifier(JavaParser.IdentifierContext ctx) {
//        if (!ctx.getText().equals(methodNode.getMethodName())) {
//            //todo 未添加静态变量和类型的判断
//            String text = ctx.getText();
//            FieldNode fieldNode = classFields.get(text);
//            if (null != fieldNode) {
//                fieldNode.getQuotedLines().add(ctx.getStart().getLine());
//                methodNode.getQuoteFields().add(fieldNode);
//            } else {
//                List<FieldNode> fieldNodes = fieldNames.get(text);
//                if (null != fieldNodes) {
//                    for (FieldNode field : fieldNodes) {
//                        field.getQuotedLines().add(ctx.getStart().getLine());
//                    }
//                    methodNode.getQuoteFields().addAll(fieldNodes);
//                }
//            }
//        }
//        return visitChildren(ctx);
//    }
//
//
//    @Override
//    public Object visitMethodCall(JavaParser.MethodCallContext ctx) {
//        if (initial) {
//            return visitChildren(ctx);
//        } else {
//            MyJavaMethodCallVisitor visitor = new MyJavaMethodCallVisitor(parentClassNode, allClassAndMethodMap, allClassList);
//            if (methodNode.getInvokeMethods().size() > 0) {
//                visitor.setLastInvokeMethodNode(methodNode.getInvokeMethods().get(methodNode.getInvokeMethods().size() - 1));
//            }
//            visitor.setClassFieldNameAndType(classFieldNameAndType);
//            visitor.setLocalFieldNameAndType(localFieldNameAndType);
//            visitor.visit(ctx);
//            methodNode.getInvokeMethods().addAll(visitor.getInvokeMethodNodes());
//            return visitChildren(ctx);
//        }
//    }
//
//
//    @Override
//    public Object visitLocalVariableDeclaration(JavaParser.LocalVariableDeclarationContext ctx) {
//        MyJavaLocalVariableVistor visitor = new MyJavaLocalVariableVistor();
//        visitor.visit(ctx);
//        LocalFieldNode localFieldNode = visitor.getLocalFieldNode();
//        localFieldNameAndType.put(localFieldNode.getFieldName(), localFieldNode.getFieldType());
//        return visitChildren(ctx);
//    }
//
//    @Override
//    public Object visitVariableDeclaratorId(JavaParser.VariableDeclaratorIdContext ctx) {
//        if (ctx.parent instanceof JavaParser.EnhancedForControlContext) {
//            JavaParser.TypeTypeContext typeTypeContext = ((JavaParser.EnhancedForControlContext) ctx.parent).typeType();
//            if (null != typeTypeContext) {
//                MyJavaTypeTypeVistor vistor = new MyJavaTypeTypeVistor();
//                vistor.visit(typeTypeContext);
//                localFieldNameAndType.put(ctx.getText(), vistor.getType());
//            }
//        }
//        return visitChildren(ctx);
//    }
//
//    @Override
//    public Object visitCatchClause(JavaParser.CatchClauseContext ctx) {
//        localFieldNameAndType.put(ctx.identifier().getText(), ctx.catchType().getText());
//        return visitChildren(ctx);
//    }
//
//
//    @Override
//    public Object visitStatement(JavaParser.StatementContext ctx) {
//        if (null != ctx.start) {
//            String text = ctx.start.getText();
//            //todo  三元递归 else if 等重复
//            if (StringUtils.isNotEmpty(text)) {
//                if (text.equals("if")) {
//                    complexity++;
//                } else if (text.equals("else")) {
//                    complexity++;
//                } else if (text.equals("for")) {
//                    complexity++;
//                } else if (text.equals("while")) {
//                    complexity++;
//                } else if (text.equals("do")) {
//                    complexity++;
//                } else if (text.equals("case")) {
//                    complexity++;
//                } else if (text.equals("&&")) {
//                    complexity++;
//                } else if (text.equals("||")) {
//                    complexity++;
//                } else if (text.equals("catch")) {
//                    complexity++;
//                } else if (text.equals("finally")) {
//                    complexity++;
//                } else if (text.equals("break")) {
//                    complexity++;
//                } else if (text.equals("continue")) {
//                    complexity++;
//                }
//            }
//        }
//        return visitChildren(ctx);
//    }
//
//
//}
