package com.sankuai.deepcode.ast.java.visitor;

import com.sankuai.deepcode.ast.java.gen.JavaParser;
import com.sankuai.deepcode.ast.java.gen.JavaParserBaseVisitor;
import com.sankuai.deepcode.ast.java.visitor.MyJavaTypeTypeVistor;
import lombok.Getter;
import org.antlr.v4.runtime.tree.TerminalNode;
import org.apache.commons.collections4.CollectionUtils;

public class MyMethodCallExpressionVistor extends JavaParserBaseVisitor<Object> {
    //todo       复杂嵌套临时简单按照大代码块处理
    //            return  (new GuavaRetryTemplate<Map<Long, BmUserCipherView>>() {
    //                @Override
    //                protected Map<Long, BmUserCipherView> realProcess() throws Exception {
    //                    return null;
    //                }
    //            }).retryIfExceptionOfType(TException.class).retryOnFixedInterval(3, TimeUnit.MILLISECONDS, 100);

    //todo 链式调用未处理
    @Getter
    private String fieldName;

    @Override
    public Object visitPrimary(JavaParser.PrimaryContext ctx) {
        if (fieldName == null) {
            try {
                if (ctx.identifier() != null || ctx.literal() != null) {
                    if (!"\"\"".equals(ctx.getChild(0).getText())) {
                        fieldName = ctx.getChild(0).getText();
                    }
                } else if (ctx.expression() != null) {
                    if (ctx.expression().creator() != null) {
                        fieldName = ctx.expression().creator().createdName().identifier().get(0).getText();
                    } else if (ctx.expression() != null) {
                        if (CollectionUtils.isNotEmpty(ctx.expression().typeType())) {
                            //todo typeTypes未处理
                            if (CollectionUtils.isNotEmpty(ctx.expression().typeType())) {
                                com.sankuai.deepcode.ast.java.visitor.MyJavaTypeTypeVistor myJavaTypeTypeVistor = new MyJavaTypeTypeVistor();
                                myJavaTypeTypeVistor.visit(ctx.expression().typeType(0));
                                fieldName = myJavaTypeTypeVistor.getType();
                            }
                        } else {
                            fieldName = ctx.expression().getText();
                        }
                    } else {
                        fieldName = ctx.getText();
                        System.out.println("MyMethodCallExpressionVistor 未处理的类型使用兜底0ctx class:" + ctx.getClass() + " ctx:" + ctx.getText());
                    }
                } else if (ctx.getChildCount() == 1 && ctx.getChild(0) instanceof TerminalNode) {
                    fieldName = ctx.getChild(0).getText();
                } else if (ctx.typeTypeOrVoid() != null) {
                    fieldName = ctx.typeTypeOrVoid().getText();
                } else {
                    fieldName = ctx.getText();
                    System.out.println("MyMethodCallExpressionVistor 未处理的类型使用兜底1 ctx class:" + ctx.getClass() + " ctx:" + ctx.getText());
                }
            } catch (Exception e) {
                fieldName = ctx.getText();
                System.out.println("MyMethodCallExpressionVistor 处理异常使用兜底" + e);
            }
            return visitChildren(ctx);
        } else {
            return visitChildren(ctx);
        }
    }


}
