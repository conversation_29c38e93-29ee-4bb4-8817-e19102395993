package com.sankuai.deepcode.ast.model.python3;

import com.alibaba.fastjson.annotation.JSONField;
import com.google.common.collect.Maps;
import com.sankuai.deepcode.ast.model.java.FileNode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.Map;

/**
 * Package: com.sankuai.deepcode.ast.model.python3
 * Description:
 *
 * <AUTHOR>
 * @since 2024/12/4 19:48
 */
@Getter
@Setter
@ToString
public class PythonAnalysesResult {
    private String fromCommit;
    private String toCommit;
    private String buildCommit;


    // 所有模块信息
    private Map<String, PythonModuleNode> moduleMap = Maps.newConcurrentMap();

    @JSONField(serialize = false)
    // 所有文件信息, key-文件项目路径
    private Map<String, FileNode> fileMap = Maps.newConcurrentMap();

    /**
     * 整个工程下的作用域管理器
     */
    private Map<String, ScopeManager> scopeManagerMap = Maps.newConcurrentMap();

    //  分析耗时，单位为毫秒
    private Long analyseDuration;
}
