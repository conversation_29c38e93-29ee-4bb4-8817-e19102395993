package com.sankuai.deepcode.ast.python3.visitor;

import com.sankuai.deepcode.ast.model.python3.ScopeManager;
import com.sankuai.deepcode.ast.python3.gen.PythonParser;
import com.sankuai.deepcode.ast.python3.gen.PythonParserBaseVisitor;

public class PythonDelVisitor extends PythonParserBaseVisitor<Object> {
    private final ScopeManager scopeManager;
    private final String filename;
    private final String moduleName;
    private final String modulePath;


    public PythonDelVisitor(String filename, String moduleName, String modulePath, ScopeManager scopeManager) {
        this.scopeManager = scopeManager;
        this.filename = filename;
        this.moduleName = moduleName;
        this.modulePath = modulePath;
    }

    @Override
    public Object visitDel_target(PythonParser.Del_targetContext ctx) {
        if (ctx.t_primary() != null && ctx.t_primary().atom() != null && ctx.t_primary().atom().NAME() != null) {
            String name = ctx.t_primary().atom().NAME().getText();
            if (scopeManager.isInCurrentScope(name, ctx.getStart().getLine())) {
                scopeManager.removeFromScope(name);
            }
        }
        return super.visitDel_target(ctx);
    }
}
