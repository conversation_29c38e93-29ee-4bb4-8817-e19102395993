package com.sankuai.deepcode.ast.scss.visitor;

import com.sankuai.deepcode.ast.model.scss.ScssParameter;
import com.sankuai.deepcode.ast.scss.gen.ScssParser;
import com.sankuai.deepcode.ast.scss.gen.ScssParserBaseVisitor;
import lombok.Getter;
import org.apache.commons.collections4.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

public class MyParametersVisitor extends ScssParserBaseVisitor<Object> {

    @Getter
    private List<ScssParameter> parameters = new ArrayList<>();


    @Override
    public Object visitParameters(ScssParser.ParametersContext ctx) {
        if (CollectionUtils.isNotEmpty(ctx.parameter())) {
            for (ScssParser.ParameterContext parameterContext : ctx.parameter()) {
                visitParameter(parameterContext);
            }
        }
        return null;
    }


    @Override
    public Object visitParameter(ScssParser.ParameterContext ctx) {
        ScssParameter scssParameter = new ScssParameter();
        //todo  后续统一处理


        parameters.add(scssParameter);
        return null;
    }

}
