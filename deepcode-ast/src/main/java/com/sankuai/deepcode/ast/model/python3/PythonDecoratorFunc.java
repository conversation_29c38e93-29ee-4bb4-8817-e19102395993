package com.sankuai.deepcode.ast.model.python3;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * Package: com.sankuai.deepcode.ast.model.python3
 * Description:
 *
 * <AUTHOR>
 * @since 2024/12/4 11:10
 */
@Setter
@Getter
@ToString(callSuper = true)
public class PythonDecoratorFunc extends PythonFunctionCallNode {
    Boolean isDecorator = Boolean.TRUE;
    private int level;

}
