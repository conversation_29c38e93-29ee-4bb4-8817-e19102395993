package com.sankuai.deepcode.ast.java.visitor;

import com.sankuai.deepcode.ast.model.java.ClassNode;
import com.sankuai.deepcode.ast.model.java.JavaImplements;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Set;

public class InitSurperAndInterfacesClass {

    //todo 不处理多个 只保存找到的第一个
    public static void init(ClassNode classNode, Set<String> classList) {
        if (null != classNode.getSuperClass() && StringUtils.isNotEmpty(classNode.getSuperClass().getName())) {
            String superClassName = classNode.getSuperClass().getName();
            boolean add = false;
            for (String importStr : classNode.getImports()) {
                if (importStr.endsWith("." + superClassName)) {
                    classNode.getSuperClass().setClassName(importStr);
                    add = true;
                    break;
                }
                if (importStr.endsWith("*")) {
                    String name = importStr.split("\\*")[0];
                    name += "." + superClassName;
                    if (classList.contains(name)) {
                        classNode.getSuperClass().setClassName(name);
                        add = true;
                        break;
                    }
                }
            }
            if (!add) {
                for (String className : classList) {
                    if (className.endsWith("." + superClassName)) {
                        classNode.getSuperClass().setClassName(className);
                    }
                }
            }
        }


        if (CollectionUtils.isNotEmpty(classNode.getInterfaces())) {
            for (JavaImplements javaImplement : classNode.getInterfaces()) {
                String implementClassName = javaImplement.getName();
                boolean add = false;
                for (String importStr : classNode.getImports()) {
                    if (importStr.endsWith("." + implementClassName)) {
                        javaImplement.setClassName(importStr);
                        add = true;
                        break;
                    }
                    if (importStr.endsWith("*")) {
                        String name = importStr.split("\\*")[0];
                        name += "." + implementClassName;
                        if (classList.contains(name)) {
                            javaImplement.setClassName(name);
                            add = true;
                            break;
                        }
                    }
                }
                if (!add) {
                    for (String className : classList) {
                        if (className.endsWith("." + implementClassName)) {
                            javaImplement.setClassName(className);
                        }
                    }
                }
            }
        }
    }
}
