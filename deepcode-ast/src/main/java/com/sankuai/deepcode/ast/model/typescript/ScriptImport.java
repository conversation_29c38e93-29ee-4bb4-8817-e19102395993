package com.sankuai.deepcode.ast.model.typescript;

import com.sankuai.deepcode.ast.model.typescript.ScriptImportNameSpace;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
public class ScriptImport {
    private boolean isExport = false;
    private int startLine;
    private int endLine;
    private String stringName;
    private ScriptImportNameSpace nameSpace;
    private List<ScriptImportModule> moduleNames = new ArrayList<>();
    private ScriptImportAsName defaultName;
    private String fromName;
    private boolean type = false;
}
