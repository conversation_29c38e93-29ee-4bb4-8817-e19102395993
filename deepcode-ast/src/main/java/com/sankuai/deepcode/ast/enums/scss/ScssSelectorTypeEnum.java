package com.sankuai.deepcode.ast.enums.scss;

public enum ScssSelectorTypeEnum {
    PLUS("+", "相邻兄弟组合器"),
    GREATER(">", "子组合器"),
    TILDE("~", "通用兄弟组合器"),
    SPACE(" ", "后代组合器"),
    SELECTOR("selector", "元素选择器"),
    ClASS("class", "类选择器"),
    ID("id", "ID选择器"),
    COMBINATOR("combinator", "组合选择器");

    private String code;

    private String desc;

    ScssSelectorTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return this.code;
    }


    public String getDesc() {
        return desc;
    }

    @Override
    public String toString() {
        return "ErrorCodeEnum{" +
                "code=" + code +
                ", desc='" + desc + '\'' +
                '}';
    }
}
