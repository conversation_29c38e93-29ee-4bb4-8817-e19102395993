package com.sankuai.deepcode.ast.model.python3;

import com.sankuai.deepcode.ast.model.python3.PythonFunctionNode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * Package: com.sankuai.deepcode.ast.model.python3
 * Description:
 *
 * <AUTHOR>
 * @since 2024/12/4 20:25
 */
@Setter
@Getter
@ToString(callSuper = true)
public class PythonMethodNode extends PythonFunctionNode {
    // 所属类名
    String className;

    public PythonMethodNode(PythonFunctionNode functionNode, String className) {
        super(functionNode);
        this.className = className;
    }

    public PythonMethodNode() {
        super(null);
    }
}
