package com.sankuai.deepcode.ast.model.python3;

import com.google.common.collect.Lists;
import com.sankuai.deepcode.ast.model.base.LocationInfo;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

/**
 * Package: com.sankuai.deepcode.ast.model.python3
 * Description:
 *
 * <AUTHOR>
 * @since 2024/12/9 13:17
 */
@Getter
@Setter
@ToString
@Accessors(chain = true)
public class PythonComment {
    String comment;
    LocationInfo start;
    LocationInfo end;

    public List<Integer> commentLines() {
        if (start == null || end == null) {
            return Lists.newArrayList();
        }

        return IntStream.rangeClosed(start.getLine(), end.getLine()).boxed().collect(Collectors.toList());
    }
}
