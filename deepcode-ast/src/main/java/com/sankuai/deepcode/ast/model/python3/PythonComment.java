package com.sankuai.deepcode.ast.model.python3;

import com.google.common.collect.Lists;
import com.sankuai.deepcode.ast.model.base.LocationInfo;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

/**
 * Package: com.sankuai.deepcode.ast.model.python3
 * Description:
 *
 * <AUTHOR>
 * @since 2024/12/9 13:17
 */
@Getter
@Setter
@ToString
@Accessors(chain = true)
public class PythonComment {
    String comment;
    LocationInfo start;
    LocationInfo end;

    public List<Integer> commentLines() {
        if (start == null || end == null) {
            return Lists.newArrayList();
        }

        return IntStream.rangeClosed(start.getLine(), end.getLine()).boxed().collect(Collectors.toList());
    }

    /**
     * 获取注释的起始行号
     * @return 注释的起始行号，如果start为null则返回-1
     */
    public int commentLine() {
        if (start == null) {
            return -1;
        }
        return start.getLine();
    }
}
