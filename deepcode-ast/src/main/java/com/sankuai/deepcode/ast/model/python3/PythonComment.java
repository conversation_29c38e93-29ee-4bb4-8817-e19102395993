package com.sankuai.deepcode.ast.model.python3;

import com.sankuai.deepcode.ast.model.base.LocationInfo;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 * Package: com.sankuai.deepcode.ast.model.python3
 * Description:
 *
 * <AUTHOR>
 * @since 2024/12/9 13:17
 */
@Getter
@Setter
@ToString
@Accessors(chain = true)
public class PythonComment {
    String comment;
    LocationInfo start;

    public Integer commentLine() {
        if (start == null) {
            return -1;
        }
        return start.getLine();
    }
}
