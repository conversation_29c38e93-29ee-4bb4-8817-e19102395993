package com.sankuai.deepcode.ast.model.java;

import com.sankuai.deepcode.ast.model.base.BaseNode;
import com.sankuai.deepcode.ast.model.java.FieldNode;
import com.sankuai.deepcode.ast.model.java.JavaParam;
import com.sankuai.deepcode.ast.model.java.JavaReturn;
import lombok.Data;
import org.antlr.v4.runtime.ParserRuleContext;

import java.util.ArrayList;
import java.util.List;

@Data
public class MethodNode extends BaseNode {
    private String source = "unknown"; //方法来源 local本地 unknown未知
    private String moduleName = "";
    private String className; //方法类名称
    private String inClassName = ""; //方法内部类名称
    private String methodName; //方法名称
    private String access = ""; //修饰符
    private List<com.sankuai.deepcode.ast.model.java.JavaParam> params = new ArrayList<>(); //参数
    private com.sankuai.deepcode.ast.model.java.JavaReturn returnInfo = new JavaReturn(); //返回值
    private List<JavaAnnotation> annotations = new ArrayList<>(); //注解
    private Object exceptions = new Object(); //异常
    private int startLine = -1; //开始行号
    private int endLine = -1; //截止行号
    private int complexity = 1; //圈复杂度
    private int changeType = 0; //  0无变更  1新增 2变更
    private List<Integer> changeLines = new ArrayList<>(); //所有变更行
    private int checkType = 0;
    private List<Integer> checkLines = new ArrayList<>();
    private List<Integer> commentLines = new ArrayList<>(); //注释行

    private List<MethodNode> invokeMethods = new ArrayList<>();
    private List<FieldNode> quoteFields = new ArrayList<>();

    private String defineType;
    private String body = "";
    private List<ParserRuleContext> methodCtxs = new ArrayList<>();
    public boolean overloading = false;
    private String vidByName;
    private String vidByNum;

    public String getParamTypeStr() {
        StringBuilder paramType = new StringBuilder();
        for (JavaParam javaParam : params) {
            paramType.append(javaParam.getType());
        }
        return paramType.toString();
    }
}
