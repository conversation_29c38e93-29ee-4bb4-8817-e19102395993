package com.sankuai.deepcode.ast.java.visitor;

import com.sankuai.deepcode.ast.java.gen.JavaParser;
import com.sankuai.deepcode.ast.java.gen.JavaParserBaseVisitor;
import com.sankuai.deepcode.ast.java.visitor.MyMethodCallExpressionVistor;
import com.sankuai.deepcode.ast.model.java.*;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.*;

public class MyJavaMethodCallNewVisitor extends JavaParserBaseVisitor<Object> {

    @Getter
    private List<MethodNode> invokeMethodNodes = new ArrayList<>();

    @Setter
    private MethodNode lastInvokeMethodNode = new MethodNode();

    @Setter
    private List<LocalField> localFields = new ArrayList<>();

    @Setter
    private Map<String, String> classFieldNameAndType = new HashMap<>();

    private static Map<String, List<MethodNode>> allClassAndMethodMap = new HashMap<>();
    private static Set<String> allClassList = new HashSet<>();
    private static ClassNode parentClassNode = new ClassNode();

    public MyJavaMethodCallNewVisitor(ClassNode classNode, Map<String, List<MethodNode>> classAndMethodMap, Set<String> classList) {
        allClassAndMethodMap = classAndMethodMap;
        allClassList = classList;
        parentClassNode = classNode;
    }

    @Override
    public Object visitMethodCall(JavaParser.MethodCallContext ctx) {
        MethodNode invokeMethodNode = new MethodNode();
        invokeMethodNode.setStartLine(ctx.getStart().getLine());
        invokeMethodNode.setEndLine(ctx.getStop().getLine());
        invokeMethodNode.setMethodName(ctx.getStart().getText());
        String fieldName = null;
        if (ctx.getParent().getChild(0) instanceof JavaParser.ExpressionContext) {
            MyMethodCallExpressionVistor myMethodCallExpressionVistor = new MyMethodCallExpressionVistor();
            myMethodCallExpressionVistor.visit(ctx.getParent().getChild(0));
            fieldName = myMethodCallExpressionVistor.getFieldName();
        }
        String defineType = findByLocalFields(fieldName);
        if (StringUtils.isEmpty(defineType)) {
            defineType = classFieldNameAndType.get(fieldName);
        }
        if (StringUtils.isNotEmpty(defineType)) {
            invokeMethodNode.setDefineType(defineType);
        } else {
            invokeMethodNode.setDefineType(fieldName);
        }

        //todo  调用方法参数
        List<JavaParam> params = new ArrayList<>();
        if (null != ctx.arguments()) {
            if (null != ctx.arguments().expressionList() && null != ctx.arguments().expressionList().expression()) {
                for (JavaParser.ExpressionContext expressionContext : ctx.arguments().expressionList().expression()) {
                    JavaParam javaParam = new JavaParam();
                    String strValue = "";
                    for (int i = 0; i < expressionContext.getChildCount(); i++) {
                        if (i == 0) {
                            strValue = expressionContext.getChild(i).getText();
                        } else {
                            strValue += " " + expressionContext.getChild(i).getText();
                        }
                    }
                    javaParam.setValue(strValue);
                    params.add(javaParam);
                }
            }
        }

        invokeMethodNode.setParams(params);

        if (ctx.getParent().getChild(0) instanceof JavaParser.ExpressionContext) {
            JavaParser.MethodCallContext callContext = ((JavaParser.ExpressionContext) ctx.getParent().getChild(0)).methodCall();
            JavaParser.CreatorContext creatorContext = ((JavaParser.ExpressionContext) ctx.getParent().getChild(0)).creator();
            if (null != callContext && lastInvokeMethodNode != null) {
                if (null != lastInvokeMethodNode.getReturnInfo()) {
                    invokeMethodNode.setClassName(lastInvokeMethodNode.getReturnInfo().getType());
                }
            } else if (null != creatorContext) {
                invokeMethodNode.setDefineType(creatorContext.createdName().getChild(0).getText());
            } else {
                initClassName(invokeMethodNode);
            }
        } else {
            initClassName(invokeMethodNode);
        }
        initReturn(invokeMethodNode);

        invokeMethodNodes.add(invokeMethodNode);
        return visitChildren(ctx);
//        return null;
    }


    public static void initReturn(MethodNode invoke) {
        List<MethodNode> methodNodes = allClassAndMethodMap.get(invoke.getClassName() + "$" + invoke.getInClassName());
        if (CollectionUtils.isNotEmpty(methodNodes)) {
            for (MethodNode methodNode : methodNodes) {
                if (methodNode.getParams().size() == invoke.getParams().size()) {
                    invoke.setReturnInfo(methodNode.getReturnInfo());
                    break;
                }
            }
        }
    }

    public static void initClassName(MethodNode invoke) {
        if (StringUtils.isEmpty(invoke.getDefineType())) {
            invoke.setClassName(parentClassNode.getClassName());
            invoke.setInClassName(parentClassNode.getInClassName());
            return;
        }
        boolean add = false;
        String className = "";
        String inClassName = "";
        if (invoke.getDefineType().contains(".")) {
            int index = 0;
            for (String name : invoke.getDefineType().split("\\.")) {
                if (index == 0) {
                    className = name;
                } else {
                    if (StringUtils.isNotEmpty(className)) {
                        inClassName = name;
                    } else {
                        inClassName += "$" + name;
                    }
                }
                index++;
            }
        } else {
            className = invoke.getDefineType();
        }

        if ("this".equals(className)) {
            List<MethodNode> methodNodes = allClassAndMethodMap.get(parentClassNode.getClassName() + "$" + parentClassNode.getInClassName());
            if (CollectionUtils.isNotEmpty(methodNodes)) {
                int sameNameCount = 0;
                int sameParamCount = 0;
                List<MethodNode> sameNameMethodNodes = new ArrayList<>();
                List<MethodNode> sameParamMethodNodes = new ArrayList<>();
                for (MethodNode methodNode : methodNodes) {
                    if (methodNode.getMethodName().equals(invoke.getMethodName())) {
                        sameNameCount++;
                        sameNameMethodNodes.add(methodNode);
                        if (methodNode.getParams().size() == invoke.getParams().size()) {
                            sameParamCount++;
                            sameParamMethodNodes.add(methodNode);
                        }
                    }
                }
                if (sameNameCount == 1) {
                    MethodNode methodNode = sameNameMethodNodes.get(0);
                    invoke.setClassName(methodNode.getClassName());
                    invoke.setInClassName(methodNode.getInClassName());
                } else {
                    if (sameParamCount == 1) {
                        MethodNode methodNode = sameNameMethodNodes.get(0);
                        invoke.setClassName(methodNode.getClassName());
                        invoke.setInClassName(methodNode.getInClassName());
                    } else {
                        for (MethodNode methodNode : sameParamMethodNodes) {
                            if (methodNode.getParamTypeStr().equals(invoke.getParamTypeStr())) {
                                invoke.setClassName(methodNode.getClassName());
                                invoke.setInClassName(methodNode.getInClassName());
                                break;
                            }
                        }
                    }
                }
            }
        } else {
            for (String importStr : parentClassNode.getImports()) {
                if (importStr.endsWith("." + className)) {
                    invoke.setClassName(importStr);
                    add = true;
                    break;
                }
                if (importStr.endsWith("*")) {
                    String name = importStr.split("\\*")[0];
                    name += "." + className;
                    if (allClassList.contains(name)) {
                        invoke.setClassName(name);
                        add = true;
                        break;
                    }
                }
            }
            if (!add) {
                for (FieldNode fieldNode : parentClassNode.getFieldNodes()) {
                    if (className.equals(fieldNode.getFieldName())) {
                        invoke.setClassName(fieldNode.getClassName());
                        invoke.setInClassName(fieldNode.getInClassName());
                        add = true;
                        break;
                    }
                }
            }

            if (!add) {
                for (String classStr : allClassList) {
                    if (classStr.endsWith("." + className)) {
                        invoke.setClassName(classStr);
                        add = true;
                        break;
                    }
                }
            }

            if (!add) {
                String name = parentClassNode.getClassName().split("\\.")[parentClassNode.getClassName().split("\\.").length - 1];
                String packageName = parentClassNode.getClassName().split("\\." + name)[0];
                String samePackageClass = packageName + "." + className;
                if (allClassList.contains(samePackageClass)) {
                    invoke.setClassName(samePackageClass);
                    add = true;
                }
            }

            if (StringUtils.isEmpty(invoke.getClassName())) {
                invoke.setClassName(invoke.getDefineType());
            }

            if (StringUtils.isNotEmpty(inClassName)) {
                invoke.setInClassName(inClassName);
            }
        }
    }

    //todo 作用域简单实现 待优化
    public String findByLocalFields(String fieldName) {
        for (int i = localFields.size() - 1; i >= 0; i--) {
            LocalField localField = localFields.get(i);
            if (localField.getName().equals(fieldName)) {
                return localField.getType();
            }
        }
        return null;
    }
}
