package com.sankuai.deepcode.ast.java.visitor;

import com.sankuai.deepcode.ast.java.gen.JavaParser;
import com.sankuai.deepcode.ast.java.gen.JavaParserBaseVisitor;
import com.sankuai.deepcode.ast.java.visitor.MyJavaAnnotationVisitor;
import com.sankuai.deepcode.ast.java.visitor.MyJavaFormalParametersVistor;
import com.sankuai.deepcode.ast.java.visitor.MyJavaTypeTypeVistor;
import com.sankuai.deepcode.ast.model.java.*;
import lombok.Getter;
import org.antlr.v4.runtime.ParserRuleContext;
import org.apache.commons.collections4.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

public class MyJavaGenericInterfaceMethodDeclarationVisitor extends JavaParserBaseVisitor<Object> {
    private ClassNode classNode = new ClassNode();

    public MyJavaGenericInterfaceMethodDeclarationVisitor(ClassNode myClassNode) {
        classNode = myClassNode;
    }

    @Getter
    private MethodNode methodNode;


    @Override
    public Object visitGenericInterfaceMethodDeclaration(JavaParser.GenericInterfaceMethodDeclarationContext ctx) {
        methodNode = new MethodNode();
        methodNode.setClassName(classNode.getClassName());
        methodNode.setInClassName(classNode.getInClassName());
        if (CollectionUtils.isNotEmpty(ctx.interfaceMethodModifier())) {
            List<JavaAnnotation> annotations = new ArrayList<>();
            String access = "";
            for (JavaParser.InterfaceMethodModifierContext interfaceMethodModifierContext : ctx.interfaceMethodModifier()) {
                if (null != interfaceMethodModifierContext.annotation()) {
                    com.sankuai.deepcode.ast.java.visitor.MyJavaAnnotationVisitor visitor = new com.sankuai.deepcode.ast.java.visitor.MyJavaAnnotationVisitor();
                    visitor.visit(interfaceMethodModifierContext.annotation());
                    annotations.add(visitor.getAnnotation());
                } else {
                    access += " " + interfaceMethodModifierContext.getText();
                }
            }
            access = access.trim();
            methodNode.setAccess(access);

//            if (ctx.typeParameters() != null) {
//                for (JavaParser.TypeParameterContext typeParameterContext : ctx.typeParameters().typeParameter()) {
//                    String typeName = typeParameterContext.identifier().getText();
//                    JavaGenerics javaGenerics = new JavaGenerics();
//                    javaGenerics.setName(typeName);
//                    methodNode.getGenerics().add(javaGenerics);
//                }
//            }

            visitInterfaceCommonBodyDeclaration(ctx.interfaceCommonBodyDeclaration());
        }
        return null;
    }


    @Override
    public Object visitInterfaceCommonBodyDeclaration(JavaParser.InterfaceCommonBodyDeclarationContext ctx) {
        if (CollectionUtils.isNotEmpty(ctx.annotation())) {
            List<JavaAnnotation> annotations = new ArrayList<>();
            for (JavaParser.AnnotationContext annotationContext : ctx.annotation()) {
                com.sankuai.deepcode.ast.java.visitor.MyJavaAnnotationVisitor visitor = new MyJavaAnnotationVisitor();
                visitor.visit(annotationContext);
                annotations.add(visitor.getAnnotation());
            }
            methodNode.setAnnotations(annotations);
            visitTypeTypeOrVoid(ctx.typeTypeOrVoid());
            methodNode.setMethodName(ctx.identifier().getText());
            visitFormalParameters(ctx.formalParameters());
            methodNode.setBody(ctx.getText());
            List<ParserRuleContext> methodCtxs = new ArrayList<>();
            methodCtxs.add(ctx.methodBody());
            methodNode.setMethodCtxs(methodCtxs);
        }
        return null;
    }

    @Override
    public Object visitTypeTypeOrVoid(JavaParser.TypeTypeOrVoidContext ctx) {
        JavaReturn javaReturn = new JavaReturn();
        if (null != ctx.typeType()) {
            com.sankuai.deepcode.ast.java.visitor.MyJavaTypeTypeVistor vistor = new MyJavaTypeTypeVistor();
            vistor.visit(ctx.typeType());
            javaReturn.setType(vistor.getType());
            List<String> signatures = new ArrayList<>();
            for (JavaGenerics javaGenerics : vistor.getGenerics()) {
                signatures.add(javaGenerics.getName());
            }
            javaReturn.setSignatures(signatures);
            javaReturn.setAnnotations(vistor.getAnnotations());
        } else {
            javaReturn.setType(ctx.getText());
        }
        methodNode.setReturnInfo(javaReturn);
        return null;
    }

    @Override
    public Object visitFormalParameters(JavaParser.FormalParametersContext ctx) {
        com.sankuai.deepcode.ast.java.visitor.MyJavaFormalParametersVistor vistor = new MyJavaFormalParametersVistor();
        vistor.visitFormalParameters(ctx);
        methodNode.setParams(vistor.getParams());
        return null;
    }


}
