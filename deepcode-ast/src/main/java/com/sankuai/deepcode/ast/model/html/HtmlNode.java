package com.sankuai.deepcode.ast.model.html;

import com.sankuai.deepcode.ast.enums.ChangeTypeEnum;
import com.sankuai.deepcode.ast.model.base.BaseNode;
import com.sankuai.deepcode.ast.model.scss.ScssNode;
import com.sankuai.deepcode.ast.model.typescript.ScriptNode;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
public class HtmlNode extends BaseNode {
    private String tagName;
    private String tagType;
    private int tagDepth;
    private int tagIndex;
    private int startLine;
    private int endLine;
    private List<ElementValue> elementValues = new ArrayList<>();
    private List<HtmlAttribute> htmlAttributes = new ArrayList();
    private List<Integer> commentLines = new ArrayList<>();
    private String filePath;
    private String body = "";

    private int changeType = ChangeTypeEnum.DEFAULT.getCode();
    private List<Integer> changeLines = new ArrayList<>();


    private List<HtmlNode> children = new ArrayList<>();
    private List<ScriptNode> scriptStates = new ArrayList<>();
    private List<ScssNode> scssNodes = new ArrayList<>();


    public HtmlNode deepCopy() {
        HtmlNode htmlNode = new HtmlNode();
        htmlNode.setTagName(this.tagName);
        htmlNode.setTagType(this.tagType);
        htmlNode.setTagDepth(this.tagDepth);
        htmlNode.setTagIndex(this.tagIndex);
        htmlNode.setStartLine(this.startLine);
        htmlNode.setEndLine(this.endLine);
        htmlNode.setElementValues(this.elementValues);
        htmlNode.setHtmlAttributes(this.htmlAttributes);
        htmlNode.setCommentLines(this.commentLines);
        htmlNode.setFilePath(this.filePath);
        htmlNode.setBody(this.body);
        return htmlNode;
    }

}
