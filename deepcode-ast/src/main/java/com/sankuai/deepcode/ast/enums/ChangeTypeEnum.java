package com.sankuai.deepcode.ast.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

import java.util.stream.Stream;

/**
 * <AUTHOR>
 */

@Getter
@AllArgsConstructor
@ToString
public enum ChangeTypeEnum {
    DEFAULT(0, "默认无变更"),
    ADD(1, "新增"),
    CHANGE(2, "变更");

    private final int code;
    private final String msg;

    public static ChangeTypeEnum getByCode(Integer code) {
        return Stream.of(values()).filter(e -> e.getCode() == code).findFirst().orElse(DEFAULT);
    }
}
