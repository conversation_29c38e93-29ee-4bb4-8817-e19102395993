package com.sankuai.deepcode.ast.java.visitor;

import com.sankuai.deepcode.ast.enums.java.ClassTypeEnum;
import com.sankuai.deepcode.ast.java.gen.JavaParser;
import com.sankuai.deepcode.ast.java.gen.JavaParserBaseVisitor;
import com.sankuai.deepcode.ast.java.visitor.*;
import com.sankuai.deepcode.ast.java.visitor.MyJavaAnnotationVisitor;
import com.sankuai.deepcode.ast.java.visitor.MyJavaEnumDeclarationVisitor;
import com.sankuai.deepcode.ast.java.visitor.MyJavaFieldVisitor;
import com.sankuai.deepcode.ast.java.visitor.MyJavaTypeTypeVistor;
import com.sankuai.deepcode.ast.model.java.*;
import com.sankuai.deepcode.ast.util.Md5Util;
import lombok.Getter;
import org.antlr.v4.runtime.ParserRuleContext;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;

public class MyJavaClassDeclarationVisitor extends JavaParserBaseVisitor<Object> {

    private ClassNode parentClassNode;
    @Getter
    private ClassNode classNode = new ClassNode();
    private String packageName;

    public MyJavaClassDeclarationVisitor(ClassNode myParentClassNode, ClassNode myClassNode, String myPackageName) {
        parentClassNode = myParentClassNode;
        classNode = myClassNode;
        packageName = myPackageName;
    }

    private boolean initStaticMethod = false;

    @Override
    public Object visitClassDeclaration(JavaParser.ClassDeclarationContext ctx) {
        classNode.setClassType(ClassTypeEnum.CLASS.getCode());
        classNode.setStartLine(ctx.getStart().getLine());
        classNode.setEndLine(ctx.getStop().getLine());
        if (null == parentClassNode) {
            if (StringUtils.isEmpty(packageName)) {
                classNode.setClassName(ctx.identifier().getText());
            } else {
                classNode.setClassName(packageName + "." + ctx.identifier().getText());
            }
        } else {
            classNode.setClassName(parentClassNode.getClassName());
            if (StringUtils.isEmpty(parentClassNode.getInClassName())) {
                classNode.setInClassName(ctx.identifier().getText());
            } else {
                classNode.setInClassName(parentClassNode.getInClassName() + "$" + ctx.identifier().getText());
            }
        }
        if (ctx.typeParameters() != null) {
            for (JavaParser.TypeParameterContext typeParameterContext : ctx.typeParameters().typeParameter()) {
                String typeName = typeParameterContext.identifier().getText();
                JavaGenerics javaGenerics = new JavaGenerics();
                javaGenerics.setName(typeName);
                classNode.getGenerics().add(javaGenerics);
            }
        }
        if (ctx.typeType() != null) {
            com.sankuai.deepcode.ast.java.visitor.MyJavaTypeTypeVistor visitor = new com.sankuai.deepcode.ast.java.visitor.MyJavaTypeTypeVistor();
            visitor.visit(ctx.typeType());
            JavaExtends javaExtends = new JavaExtends();
            javaExtends.setName(visitor.getType());
            javaExtends.setGenerics(visitor.getGenerics());
            classNode.setSuperClass(javaExtends);
        }
        if (ctx.typeList() != null) {
            for (JavaParser.TypeListContext typeListContext : ctx.typeList()) {
                com.sankuai.deepcode.ast.java.visitor.MyJavaTypeTypeVistor visitor = new MyJavaTypeTypeVistor();
                visitor.visit(typeListContext);
                JavaImplements javaImplements = new JavaImplements();
                javaImplements.setName(visitor.getType());
                javaImplements.setGenerics(visitor.getGenerics());
                classNode.getInterfaces().add(javaImplements);
            }
        }
        //todo PERMITS typeList  Java17暂不处理
        visitClassBody(ctx.classBody());
        return null;
    }

    @Override
    public Object visitClassBody(JavaParser.ClassBodyContext ctx) {
        for (JavaParser.ClassBodyDeclarationContext classBodyDeclarationContext : ctx.classBodyDeclaration()) {
            if (classBodyDeclarationContext.STATIC() != null) {
                if (!initStaticMethod) {
                    MethodNode methodNode = new MethodNode();
                    methodNode.setClassName(classNode.getClassName());
                    methodNode.setInClassName(classNode.getInClassName());
                    methodNode.setMethodName("static");
                    methodNode.setStartLine(classBodyDeclarationContext.getStart().getLine());
                    methodNode.setEndLine(classBodyDeclarationContext.getStop().getLine());
                    methodNode.setBody(classBodyDeclarationContext.getText());
                    List<ParserRuleContext> methodCtxs = new ArrayList<>();
                    methodCtxs.add(classBodyDeclarationContext);
                    methodNode.setMethodCtxs(methodCtxs);
                    classNode.getMethodNodes().add(methodNode);
                    initStaticMethod = true;
                } else {
                    for (MethodNode methodNode : classNode.getMethodNodes()) {
                        if ("static".equals(methodNode.getMethodName())) {
                            methodNode.setBody(methodNode.getBody() + classBodyDeclarationContext.getText());
                            methodNode.setEndLine(classBodyDeclarationContext.getStop().getLine());
                            break;
                        }
                    }
                }
            } else {
                List<JavaAnnotation> annotations = new ArrayList<>();
                String access = "";
                if (CollectionUtils.isNotEmpty(classBodyDeclarationContext.modifier())) {
                    for (JavaParser.ModifierContext modifierContext : classBodyDeclarationContext.modifier()) {
                        if (null != modifierContext.classOrInterfaceModifier()) {
                            if (null != modifierContext.classOrInterfaceModifier().annotation()) {
                                com.sankuai.deepcode.ast.java.visitor.MyJavaAnnotationVisitor visitor = new MyJavaAnnotationVisitor();
                                visitor.visit(modifierContext.classOrInterfaceModifier().annotation());
                                annotations.add(visitor.getAnnotation());
                            } else {
                                access += " " + modifierContext.classOrInterfaceModifier().getText();
                            }
                        }
                    }
                }
//                if (null != classBodyDeclarationContext.memberDeclaration()) {
//                    MyMemberDeclarationVisitor visitor = new MyMemberDeclarationVisitor(parentClassNode, classNode, packageName, annotations, access);
//                    visitor.visit(classBodyDeclarationContext.memberDeclaration());
//                }
                if (null != classBodyDeclarationContext.memberDeclaration()) {
                    JavaParser.MemberDeclarationContext membered = classBodyDeclarationContext.memberDeclaration();
                    if (membered != null) {
                        if (membered.methodDeclaration() != null) {
                            MyJavaMethodDeclarationVisitor visitor = new MyJavaMethodDeclarationVisitor(classNode);
                            visitor.visit(membered.methodDeclaration());
                            visitor.getMethodNode().setAccess(access);
                            visitor.getMethodNode().setAnnotations(annotations);
                            classNode.getMethodNodes().add(visitor.getMethodNode());
                        } else if (membered.genericMethodDeclaration() != null) {
                            MyJavaGenericMethodDeclarationVisitor visitor = new MyJavaGenericMethodDeclarationVisitor(classNode);
                            visitor.visit(membered.genericMethodDeclaration());
                            visitor.getMethodNode().setAccess(access);
                            visitor.getMethodNode().setAnnotations(annotations);
                            classNode.getMethodNodes().add(visitor.getMethodNode());
                        } else if (membered.fieldDeclaration() != null) {
                            com.sankuai.deepcode.ast.java.visitor.MyJavaFieldVisitor visitor = new MyJavaFieldVisitor(classNode);
                            visitor.visit(membered.fieldDeclaration());
                            for (FieldNode fieldNode : visitor.getFieldNodes()) {
                                fieldNode.setAccess(access);
                                fieldNode.setAnnotations(annotations);
                                classNode.getFieldNodes().add(fieldNode);
                            }
                        } else if (membered.constructorDeclaration() != null) {
                            MyJavaConstructorDeclarationVisitor visitor = new MyJavaConstructorDeclarationVisitor(classNode);
                            visitor.visit(membered.constructorDeclaration());
                            visitor.getMethodNode().setAccess(access);
                            visitor.getMethodNode().setAnnotations(annotations);
                            classNode.getMethodNodes().add(visitor.getMethodNode());
                        } else if (membered.genericConstructorDeclaration() != null) {
                            MyJavaGenericConstructorDeclarationVisitor visitor = new MyJavaGenericConstructorDeclarationVisitor(classNode);
                            visitor.visit(membered.genericConstructorDeclaration());
                            visitor.getMethodNode().setAccess(access);
                            visitor.getMethodNode().setAnnotations(annotations);
                            classNode.getMethodNodes().add(visitor.getMethodNode());
                        } else if (membered.interfaceDeclaration() != null) {
                            ClassNode inClassNode = initInClassNode(classNode, access, annotations, membered.interfaceDeclaration());
                            MyJavaInterfaceDeclarationVisitor visitor = new MyJavaInterfaceDeclarationVisitor(classNode, inClassNode, packageName);
                            visitor.visit(membered.interfaceDeclaration());
                            classNode.getInClassNodes().add(inClassNode);
                        } else if (membered.annotationTypeDeclaration() != null) {
                            ClassNode inClassNode = initInClassNode(classNode, access, annotations, membered.annotationTypeDeclaration());
                            MyAnnotationTypeDeclarationVisitor visitor = new MyAnnotationTypeDeclarationVisitor(classNode, inClassNode, packageName);
                            visitor.visit(membered.annotationTypeDeclaration());
                            classNode.getInClassNodes().add(inClassNode);
                        } else if (membered.classDeclaration() != null) {
                            ClassNode inClassNode = initInClassNode(classNode, access, annotations, membered.classDeclaration());
                            MyJavaClassDeclarationVisitor visitor = new MyJavaClassDeclarationVisitor(classNode, inClassNode, packageName);
                            visitor.visit(membered.classDeclaration());
                            classNode.getInClassNodes().add(inClassNode);
                        } else if (membered.enumDeclaration() != null) {
                            ClassNode inClassNode = initInClassNode(classNode, access, annotations, membered.enumDeclaration());
                            com.sankuai.deepcode.ast.java.visitor.MyJavaEnumDeclarationVisitor visitor = new MyJavaEnumDeclarationVisitor(classNode, inClassNode, packageName);
                            visitor.visit(membered.enumDeclaration());
                            classNode.getInClassNodes().add(inClassNode);
                        } else {
                            //todo recordDeclaration Java17 暂不处理
                        }
                    }
                }
            }
        }
        return null;
    }

    public ClassNode initInClassNode(ClassNode classNode, String access, List<JavaAnnotation> annotations, ParserRuleContext parserRuleContext) {
        ClassNode inClassNode = new ClassNode();
        inClassNode.setAccess(access);
        inClassNode.setAnnotations(annotations);
        inClassNode.setFileVid(Md5Util.filePathToMd5(classNode.getClassPath()));
        inClassNode.setClassPath(classNode.getClassPath());
        inClassNode.setClassName(classNode.getClassName());
        inClassNode.setStartLine(parserRuleContext.start.getLine());
        inClassNode.setEndLine(parserRuleContext.stop.getLine());
        return inClassNode;
    }

}
