package com.sankuai.deepcode.ast.python3.visitor;

import com.sankuai.deepcode.ast.model.base.LocationInfo;
import com.sankuai.deepcode.ast.model.python3.*;
import com.sankuai.deepcode.ast.python3.gen.PythonParser;
import com.sankuai.deepcode.ast.python3.gen.PythonParserBaseVisitor;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;

/**
 * Package: com.sankuai.deepcode.ast.python3.visitor
 * Description:
 *
 * <AUTHOR>
 * @since 2024/12/4 11:00
 */
@Getter
@Slf4j
public class PythonFunctionDefVisitor extends PythonParserBaseVisitor<Object> {

    private final PythonFunctionNode functionNode;

    // 方法定义肯定有 模块，模块来自上游传入
    private final String moduleName;
    private final String filename;
    private final ScopeManager scopeManager;


    public PythonFunctionDefVisitor(String filename, String currentModuleName, ScopeManager scopeManager) {
        this.moduleName = currentModuleName;
        this.filename = filename;
        this.scopeManager = scopeManager;
        this.functionNode = new PythonFunctionNode();

        this.functionNode.setFileName(filename);
        this.functionNode.setModuleName(moduleName);
    }


    @Override
    public Object visitFunction_def(PythonParser.Function_defContext ctx) {
        // 设置方法名
        if (ctx.function_def_raw() != null && ctx.function_def_raw().NAME() != null) {
            functionNode.setName(ctx.function_def_raw().NAME().getText());
            functionNode.setModulePath(moduleName + "." + functionNode.getName());
        }
        scopeManager.enterScope(functionNode.getName());
        scopeManager.addToScope(functionNode.getName(), moduleName + "." + functionNode.getName(), ctx.getStart().getLine());

        // 解析参数
        if (ctx.function_def_raw().params() != null) {
            PythonParser.ParamsContext paramsCtx = ctx.function_def_raw().params();
            List<PythonParameterNode> parameters = new ArrayList<>();
            boolean hasArgs = false;
            boolean hasKwargs = false;
            int slashIndex = -1;
            int starIndex = -1;

            if (paramsCtx.parameters() != null) {
                PythonParser.ParametersContext parametersCtx = paramsCtx.parameters();

                // 处理所有可能的参数类型
                processParameters(parametersCtx, parameters, hasArgs, hasKwargs, slashIndex, starIndex);
            }

            functionNode.setParameters(parameters);
            functionNode.setParameterCount(parameters.size());

            // 处理参数的类型提示
            processParameterTypeHints(functionNode.getParameters());
        }

        // 处理函数的文档字符串和注释
        processFunctionDocstringAndComments(ctx.function_def_raw().block());

        com.sankuai.deepcode.ast.python3.visitor.PythonComplexityVisitor blockTreeVisitor = new PythonComplexityVisitor() {
            @Override
            public Void visitFunction_def(PythonParser.Function_defContext nestedCtx) {
                // *暂且不解析 方法定义内部嵌套的方法定义
                log.warn("nested function def:{} not supported.", nestedCtx.getText());
                /*
                PythonFunctionDefVisitor nestedVisitor = new PythonFunctionDefVisitor(filename, functionNode.getModulePath(), scopeManager);
                nestedVisitor.visitFunction_def(nestedCtx);
                functionNode.getInnerFunctions().add(nestedVisitor.getFunctionNode());
                */
                // return super.visitFunction_def(nestedCtx);
                return null;
            }

            @Override
            public Void visitReturn_stmt(PythonParser.Return_stmtContext returnCtx) {
                PythonReturn returnNode = new PythonReturn();
                returnNode.setReturnLine(LocationInfo.of(returnCtx.getStart().getLine(), returnCtx.getStart().getCharPositionInLine()));

                if (returnCtx.star_expressions() != null) {
                    returnNode.setRawReturn(returnCtx.star_expressions().getText());
                    // 注意：这里的类型推断可能需要更复杂的逻辑
                    returnNode.setType("Unknown");
                } else {
                    // 如果没有表达式，说明是空的 return 语句
                    returnNode.setRawReturn("");
                    returnNode.setType("None");
                }
                functionNode.getReturnNode().add(returnNode);
                return null;
            }

            @Override
            public Void visitAssignment(PythonParser.AssignmentContext ctx) {
                com.sankuai.deepcode.ast.python3.visitor.PythonAssignmentVisitor assignmentVisitor = new PythonAssignmentVisitor(filename, moduleName, scopeManager);
                assignmentVisitor.visit(ctx);
                return null;
            }

            @Override
            public Void visitImport_stmt(PythonParser.Import_stmtContext ctx) {
                com.sankuai.deepcode.ast.python3.visitor.PythonImportVisitor importVisitor = new PythonImportVisitor(filename, moduleName, functionNode.getModulePath(), scopeManager);
                importVisitor.visit(ctx);

                functionNode.getInnerImports().addAll(importVisitor.getImportNodes());
                return null;
            }

            @Override
            public Void visitFunction_call(PythonParser.Function_callContext ctx) {
                PythonFunctionCallVisitor callVisitor = new PythonFunctionCallVisitor(filename, moduleName, scopeManager);
                callVisitor.visit(ctx);
                functionNode.getCallNodes().addAll(callVisitor.getFunctionCallNodes());
//                return super.visitFunction_call(ctx);
                return null;
            }

            @Override
            public Void visitPrimary(PythonParser.PrimaryContext ctx) {
                // 检查是否包含函数调用（即包含 '(' arguments? ')' 的primary_suffix）
                boolean hasFunctionCall = false;
                for (PythonParser.Primary_suffixContext suffix : ctx.primary_suffix()) {
                    if (suffix.LPAR() != null) {
                        hasFunctionCall = true;
                        break;
                    }
                }

                if (hasFunctionCall) {
                    // 传入当前函数所在的模块路径，用于解析函数调用的路径
                    PythonFunctionCallVisitor callVisitor = new PythonFunctionCallVisitor(filename, moduleName, scopeManager);
                    callVisitor.visitPrimary(ctx);
                    functionNode.getCallNodes().addAll(callVisitor.getFunctionCallNodes());
                }

                return super.visitPrimary(ctx);
            }
        };

        // 处理嵌套的 方法字义、方法调用、方法返回内容
        if (ctx.function_def_raw().block() != null) {
            ctx.function_def_raw().block().accept(blockTreeVisitor);
        }
        // 方法定义的圈复杂度处理
        functionNode.setComplexity(blockTreeVisitor.getComplexity());

        // 设置方法定义行数
        functionNode.setStart(LocationInfo.of(ctx.getStart().getLine(), ctx.getStart().getCharPositionInLine()));
        functionNode.setEnd(LocationInfo.of(ctx.getStop().getLine() - 1, ctx.getStop().getCharPositionInLine()));

        // 设置装饰器
        if (ctx.decorators() != null) {
            PythonDecoratorVisitor decoratorVisitor = new PythonDecoratorVisitor(filename);
            decoratorVisitor.visit(ctx.decorators());
            List<PythonDecoratorFunc> decorators = new ArrayList<>(decoratorVisitor.getDecoratorFuncList());
            functionNode.setDecorators(decorators);

            // 存在函数装饰器时，函数的定义需要以装饰器 结束行 + 1
            functionNode.getDecorators().stream().max(
                    Comparator.comparing(d -> d.getStart().getLine())
            ).map(PythonDecoratorFunc::getStart).map(LocationInfo::getLine).ifPresent(l -> functionNode.getStart().setLine(l + 1));
        }

        // 设置可能的返回值【来源于方法定义的 返回值类型提示】
        if (ctx.function_def_raw().RARROW() != null) {
            functionNode.setFunctionReturnType(ctx.function_def_raw().expression().getText());
        }

        functionNode.setFuncBody(ctx.getText());

        // 设置所在模块
        functionNode.setModuleName(moduleName);
        // 设置是否为异步方法
        functionNode.setAsync(ctx.function_def_raw().ASYNC() != null);

        // 检查是否为生成器函数
        functionNode.setIsGenerator(isGenerator(ctx));
        scopeManager.exitScope();

//        return super.visitFunction_def(ctx);
        return null;
    }


    private void processParameterTypeHints(List<PythonParameterNode> parameters) {
        for (PythonParameterNode param : parameters) {
            if (param.getName().equals("self") && param.getParamIndex() == 0) {
                log.info("self作为第1个参数，默认是类中定义的实例，不进行作用域解析和写入");
            } else if (param.getType() != null && !param.getType().isEmpty()) {
                String typeHint = param.getType();
                String resolvedType = scopeManager.resolveSymbol(typeHint, param.getLocation().getLine());
                if (resolvedType != null) {
                    // 如果类型提示可以解析到一个已知的模块或类，使用解析后的路径
                    scopeManager.addToScope(param.getName(), resolvedType, param.getLocation().getLine());
                    param.setTypeModule(resolvedType);
                } else {
                    // 如果无法解析，仍然使用原始的类型提示
                    scopeManager.addToScope(param.getName(), typeHint, param.getLocation().getLine());
                    param.setTypeModule(typeHint);
                }
            } else {
                // 如果没有类型提示，不将参数添加到作用域中，因为我们无法确定其类型
                // 这样在函数调用解析时，这些参数会被正确识别为 "Unknown"
                // scopeManager.addToScope(param.getName(), "Unknown", param.getLocation().getLine());
                param.setTypeModule("Unknown");
            }
        }
    }

    private void processFunctionDocstringAndComments(PythonParser.BlockContext blockCtx) {
        if (blockCtx == null) {
            return;
        }

        if (blockCtx.statements() != null) {
            List<PythonBlockComment> otherBlockComments = new ArrayList<>();
            boolean foundFirstDocstring = false;

            for (PythonParser.StatementContext stmt : blockCtx.statements().statement()) {
                // 检查是否是简单语句且只包含一个表达式（可能是字符串字面量）
                if (stmt.simple_stmts() != null && stmt.simple_stmts().simple_stmt().size() == 1) {
                    PythonParser.Simple_stmtContext simpleStmt = stmt.simple_stmts().simple_stmt(0);

                    // 检查是否是表达式语句
                    if (simpleStmt.star_expressions() != null) {
                        String possibleDocstring = extractStringLiteral(simpleStmt.star_expressions());

                        if (possibleDocstring != null && isMultilineString(possibleDocstring)) {
                            PythonBlockComment blockComment = new PythonBlockComment()
                                    .setStart(LocationInfo.of(stmt.getStart().getLine()))
                                    .setEnd(LocationInfo.of(stmt.getStop().getLine()))
                                    .setComment(cleanDocstring(possibleDocstring))
                                    .setBlockModulePath(functionNode.getModulePath());

                            if (!foundFirstDocstring) {
                                // 第一个三引号字符串作为函数的文档字符串
                                functionNode.setFunctionComment(blockComment);
                                foundFirstDocstring = true;
                            } else {
                                // 其他的作为其他块级注释
                                otherBlockComments.add(blockComment);
                            }
                        }
                    }
                }
            }

            functionNode.setOtherBlockComments(otherBlockComments);
        }
    }

    /**
     * 从表达式中提取字符串字面量
     */
    private String extractStringLiteral(PythonParser.Star_expressionsContext ctx) {
        // 简化的字符串提取逻辑，实际可能需要更复杂的解析
        String text = ctx.getText();
        if (text.startsWith("\"\"\"") || text.startsWith("'''")) {
            return text;
        }
        return null;
    }

    /**
     * 清理文档字符串，移除三引号
     */
    private String cleanDocstring(String docstring) {
        if (docstring.startsWith("'''") && docstring.endsWith("'''")) {
            return docstring.substring(3, docstring.length() - 3).trim();
        } else if (docstring.startsWith("\"\"\"") && docstring.endsWith("\"\"\"")) {
            return docstring.substring(3, docstring.length() - 3).trim();
        }
        return docstring;
    }

    private boolean isMultilineString(String str) {
        return str.startsWith("'''") || str.startsWith("\"\"\"");
    }


    // 辅助方法：检查函数是否为生成器
    private boolean isGenerator(PythonParser.Function_defContext ctx) {
        // 遍历函数体，查找yield语句
        if (ctx.function_def_raw().block() == null) {
            return false;
        }
        return ctx.function_def_raw().block().accept(new PythonParserBaseVisitor<Boolean>() {
            @Override
            public Boolean visitYield_expr(PythonParser.Yield_exprContext ctx) {
                return true;
            }

            @Override
            protected Boolean aggregateResult(Boolean aggregate, Boolean nextResult) {
                return aggregate != null ? aggregate : nextResult;
            }
        }) != null;
    }

    public void processParameters(PythonParser.ParametersContext parametersCtx, List<PythonParameterNode> parameters,
                                  boolean hasArgs, boolean hasKwargs, int slashIndex, int starIndex) {
        int paramIndex = 0;

        // 处理 slash_no_default
        if (parametersCtx.slash_no_default() != null) {
            processParamNoDefault(parametersCtx.slash_no_default().param_no_default(), parameters, paramIndex);
            slashIndex = parameters.size() - 1;
        }

        // 处理 slash_with_default
        if (parametersCtx.slash_with_default() != null) {
            processParamNoDefault(parametersCtx.slash_with_default().param_no_default(), parameters, paramIndex);
            processParamWithDefault(parametersCtx.slash_with_default().param_with_default(), parameters, paramIndex);
            slashIndex = parameters.size() - 1;
        }

        // 处理 param_no_default
        if (parametersCtx.param_no_default() != null) {
            processParamNoDefault(parametersCtx.param_no_default(), parameters, paramIndex);
        }

        // 处理 param_with_default
        if (parametersCtx.param_with_default() != null) {
            processParamWithDefault(parametersCtx.param_with_default(), parameters, paramIndex);
        }

        // 处理 star_etc
        if (parametersCtx.star_etc() != null) {
            PythonParser.Star_etcContext starEtcCtx = parametersCtx.star_etc();
            if (starEtcCtx.param_no_default() != null) {
                hasArgs = true;
                starIndex = parameters.size();
                PythonParameterNode param = processParam(starEtcCtx.param_no_default().param(), paramIndex);
                param.setName("*" + param.getName());
                parameters.add(param);
            }
            if (starEtcCtx.kwds() != null) {
                hasKwargs = true;
                PythonParameterNode param = processParam(starEtcCtx.kwds().param_no_default().param(), paramIndex);
                param.setName("**" + param.getName());
                parameters.add(param);
            }
        }
        functionNode.setHasArgs(hasArgs);
        functionNode.setHasKwargs(hasKwargs);
        functionNode.setSlashIndex(slashIndex);
        functionNode.setStarIndex(starIndex);
    }

    private void processParamNoDefault(List<PythonParser.Param_no_defaultContext> paramCtxList, List<PythonParameterNode> parameters, int startIndex) {
        for (int i = 0; i < paramCtxList.size(); i++) {
            parameters.add(processParam(paramCtxList.get(i).param(), startIndex + i));
        }
    }

    private void processParamWithDefault(List<PythonParser.Param_with_defaultContext> paramCtxList, List<PythonParameterNode> parameters, int startIndex) {
        for (int i = 0; i < paramCtxList.size(); i++) {
            PythonParser.Param_with_defaultContext paramCtx = paramCtxList.get(i);
            PythonParameterNode param = processParam(paramCtx.param(), startIndex + i);
            param.setDefaultValue(paramCtx.default_assignment().expression().getText());
            param.setLocation(LocationInfo.of(paramCtx.getStart().getLine(), paramCtx.getStart().getCharPositionInLine()));
            parameters.add(param);
        }
    }

    private PythonParameterNode processParam(PythonParser.ParamContext paramCtx, int index) {
        PythonParameterNode param = new PythonParameterNode();
        param.setName(paramCtx.NAME().getText());
        param.setParamIndex(index);
        param.setLocation(LocationInfo.of(paramCtx.getStart().getLine(), paramCtx.getStart().getCharPositionInLine()));
        if (paramCtx.annotation() != null) {
            param.setType(paramCtx.annotation().expression().getText());
        }
        return param;
    }
}
