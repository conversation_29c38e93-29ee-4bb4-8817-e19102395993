package com.sankuai.deepcode.ast.typescript.visitor;

import com.sankuai.deepcode.ast.model.typescript.*;
import com.sankuai.deepcode.ast.typescript.gen.TypeScriptParser;
import com.sankuai.deepcode.ast.typescript.gen.TypeScriptParserBaseVisitor;
import lombok.Getter;

import java.util.ArrayList;
import java.util.List;

public class MyInterfaceDeclarationVisitor extends TypeScriptParserBaseVisitor<Object> {

    @Getter
    private ScriptClass scriptClass;

    private ScriptNode scriptNode;

    MyInterfaceDeclarationVisitor(ScriptNode myScriptNode) {
        scriptClass = new ScriptClass();
        scriptNode = myScriptNode;
        scriptNode.getClasses().add(scriptClass);
    }

    @Override
    public Object visitInterfaceDeclaration(TypeScriptParser.InterfaceDeclarationContext ctx) {
        if (ctx.Export() != null) {
            scriptClass.setExport(true);
        }
        if (ctx.Declare() != null) {
            scriptClass.setDeclare(true);
        }
        scriptClass.setStartLine(ctx.start.getLine());
        scriptClass.setEndLine(ctx.stop.getLine());
        scriptClass.setClassName(ctx.identifier().getText());
        scriptClass.setType("interface");


        if (ctx.typeParameters() != null && ctx.typeParameters().typeParameterList() != null) {
            ScriptGenerics scriptGenerics = new ScriptGenerics();
            scriptGenerics.setName(ctx.typeParameters().typeParameterList().getText());
            scriptClass.setGenerics(scriptGenerics);
        }
        if (ctx.interfaceExtendsClause() != null) {
            List<TypeScriptParser.TypeReferenceContext> typeNameContexts = ctx.interfaceExtendsClause().classOrInterfaceTypeList().typeReference();
            for (TypeScriptParser.TypeReferenceContext typeNameContext : typeNameContexts) {
                ScriptExtends superClass = new ScriptExtends();
                if (typeNameContext.typeName().identifier() != null) {
                    superClass.setName(typeNameContext.typeName().identifier().getText());
                } else if (typeNameContext.typeName().namespaceName() != null) {
                    superClass.setNameSpace(typeNameContext.typeName().namespaceName().getText());
                }
                TypeScriptParser.TypeGenericContext typeGenericContext = typeNameContext.typeGeneric();
                if (typeGenericContext != null) {
                    List<ScriptGenerics> generics = new ArrayList<>();
                    for (TypeScriptParser.TypeArgumentContext typeArgumentContext : typeGenericContext.typeArgumentList().typeArgument()) {
                        ScriptGenerics scriptGenerics = new ScriptGenerics();
                        scriptGenerics.setName(typeArgumentContext.getText());
                        generics.add(scriptGenerics);
                    }
                    if (typeGenericContext.typeGeneric() != null) {
                        ScriptGenerics scriptGenerics = new ScriptGenerics();
                        scriptGenerics.setName(typeGenericContext.typeGeneric().getText());
                        generics.add(scriptGenerics);
                    }
                    superClass.setGenerics(generics);
                }
                scriptClass.setSuperClass(superClass);
            }
        }
        visitObjectType(ctx.objectType());
        return null;
    }

    @Override
    public Object visitObjectType(TypeScriptParser.ObjectTypeContext ctx) {
        if (ctx.typeBody() != null) {
            visitTypeBody(ctx.typeBody());
        }
        return null;
    }


    @Override
    public Object visitTypeBody(TypeScriptParser.TypeBodyContext ctx) {
        for (TypeScriptParser.TypeMemberContext typeMemberContext : ctx.typeMemberList().typeMember()) {
            if (typeMemberContext.propertySignatur() != null) {
                ScriptVariable scriptVariable = new ScriptVariable();
                scriptVariable.setStartLine(typeMemberContext.start.getLine());
                scriptVariable.setEndLine(typeMemberContext.stop.getLine());
                scriptVariable.setBody(typeMemberContext.getText());
                if (typeMemberContext.propertySignatur().ReadOnly() != null) {
                    scriptVariable.setReadOnly(true);
                }
                scriptVariable.setName(typeMemberContext.propertySignatur().propertyName().getText());
                if (null != typeMemberContext.propertySignatur().typeAnnotation()) {
                    scriptVariable.setType(typeMemberContext.propertySignatur().typeAnnotation().type_().getText());
                }
                scriptClass.getVariables().add(scriptVariable);
            }

            if (typeMemberContext.callSignature() != null) {
                ScriptMethod scriptMethod = new ScriptMethod();
                scriptMethod.setStartLine(typeMemberContext.start.getLine());
                scriptMethod.setEndLine(typeMemberContext.stop.getLine());
                scriptMethod.setBody(typeMemberContext.getText());
                scriptMethod.setCallSignature(true);
                visitCallSignature(typeMemberContext.callSignature(), scriptMethod);
                scriptClass.getMethods().add(scriptMethod);
            }
            if (typeMemberContext.constructSignature() != null) {
                visitConstructSignature(typeMemberContext.constructSignature());
            }
            if (typeMemberContext.indexSignature() != null) {
                //todo 索引成员声明 暂不处理
            }
            if (typeMemberContext.methodSignature() != null) {
                visitMethodSignature(typeMemberContext.methodSignature());
                if (typeMemberContext.type_() != null) {

                }
            }
        }
        return null;
    }

    @Override
    public Object visitConstructSignature(TypeScriptParser.ConstructSignatureContext ctx) {
        ScriptMethod method = new ScriptMethod();
        method.setStartLine(ctx.start.getLine());
        method.setEndLine(ctx.stop.getLine());
        method.setBody(ctx.getText());
        if (ctx.typeParameters() != null && ctx.typeParameters().typeParameterList() != null) {
            for (TypeScriptParser.TypeParameterContext typeParameterContext : ctx.typeParameters().typeParameterList().typeParameter()) {
                ScriptGenerics scriptGenerics = new ScriptGenerics();
                scriptGenerics.setName(typeParameterContext.getText());
                method.getGenerics().add(scriptGenerics);
            }
        }

        if (ctx.parameterList() != null) {
            if (ctx.parameterList().restParameter() != null) {
                ScriptMethodParam scriptMethodParam1 = new ScriptMethodParam();
                scriptMethodParam1.setName("...");
                method.getParams().add(scriptMethodParam1);
                ScriptMethodParam scriptMethodParam2 = new ScriptMethodParam();
                scriptMethodParam2.setName("...");
                if (ctx.parameterList().restParameter().typeAnnotation() != null) {
                    scriptMethodParam2.setType(ctx.parameterList().restParameter().typeAnnotation().type_().getText());
                }
                method.getParams().add(scriptMethodParam2);
            } else if (ctx.parameterList().parameter() != null) {
                for (TypeScriptParser.ParameterContext parameterContext : ctx.parameterList().parameter()) {
                    ScriptMethodParam scriptMethodParam = new ScriptMethodParam();
                    if (parameterContext.requiredParameter() != null) {
                        scriptMethodParam.setRequired(true);
                        if (parameterContext.requiredParameter().decoratorList() != null) {
                            List<String> decorators = new ArrayList<>();
                            for (TypeScriptParser.DecoratorContext decoratorContext : parameterContext.requiredParameter().decoratorList().decorator()) {
                                decorators.add(decoratorContext.getText());
                            }
                            scriptMethodParam.setDecorators(decorators);
                        }
                        if (parameterContext.requiredParameter().accessibilityModifier() != null) {
                            scriptMethodParam.setModifier(parameterContext.requiredParameter().accessibilityModifier().getText());
                        }
                        scriptMethodParam.setName(parameterContext.requiredParameter().identifierOrPattern().getText());
                        if (parameterContext.requiredParameter().typeAnnotation() != null) {
                            scriptMethodParam.setType(parameterContext.requiredParameter().typeAnnotation().type_().getText());
                        }
                    } else {
                        if (parameterContext.optionalParameter().decoratorList() != null) {
                            List<String> decorators = new ArrayList<>();
                            for (TypeScriptParser.DecoratorContext decoratorContext : parameterContext.optionalParameter().decoratorList().decorator()) {
                                decorators.add(decoratorContext.getText());
                            }
                            scriptMethodParam.setDecorators(decorators);
                        }
                        if (parameterContext.optionalParameter().accessibilityModifier() != null) {
                            scriptMethodParam.setModifier(parameterContext.optionalParameter().accessibilityModifier().getText());
                        }
                        scriptMethodParam.setName(parameterContext.optionalParameter().identifierOrPattern().getText());
                        if (parameterContext.optionalParameter().initializer() != null) {
                            scriptMethodParam.setDefaultValue(parameterContext.optionalParameter().initializer().getText());
                        }
                        if (parameterContext.optionalParameter().typeAnnotation() != null) {
                            scriptMethodParam.setType(parameterContext.optionalParameter().typeAnnotation().type_().getText());
                        }
                    }
                    method.getParams().add(scriptMethodParam);
                }
            }
        }

        if (ctx.typeAnnotation() != null) {
            ScriptMethodReturn scriptMethodReturn = new ScriptMethodReturn();
            scriptMethodReturn.setType(ctx.typeAnnotation().type_().getText());
            method.setMethodReturn(scriptMethodReturn);
        } else {
            ScriptMethodReturn scriptMethodReturn = new ScriptMethodReturn();
            scriptMethodReturn.setType("void");
            method.setMethodReturn(scriptMethodReturn);
        }
        scriptClass.setConstructor(method);
        return null;
    }


    @Override
    public Object visitMethodSignature(TypeScriptParser.MethodSignatureContext ctx) {
        ScriptMethod method = new ScriptMethod();
        method.setStartLine(ctx.start.getLine());
        method.setEndLine(ctx.stop.getLine());
        method.setBody(ctx.getText());
        method.setMethodName(ctx.propertyName().getText());

        scriptClass.getMethods().add(method);
        visitCallSignature(ctx.callSignature(), method);

        return null;
    }


    public Object visitCallSignature(TypeScriptParser.CallSignatureContext ctx, ScriptMethod method) {
        method.setStartLine(ctx.start.getLine());
        method.setEndLine(ctx.stop.getLine());
        //todo   callSignature 简单处理
        if (ctx.typeParameters() != null && ctx.typeParameters().typeParameterList() != null) {
            for (TypeScriptParser.TypeParameterContext typeParameterContext : ctx.typeParameters().typeParameterList().typeParameter()) {
                ScriptGenerics scriptGenerics = new ScriptGenerics();
                scriptGenerics.setName(typeParameterContext.getText());
                method.getGenerics().add(scriptGenerics);
            }
        }

        if (ctx.parameterList() != null) {
            if (ctx.parameterList().restParameter() != null) {
                ScriptMethodParam scriptMethodParam1 = new ScriptMethodParam();
                scriptMethodParam1.setName("...");
                method.getParams().add(scriptMethodParam1);
                ScriptMethodParam scriptMethodParam2 = new ScriptMethodParam();
                scriptMethodParam2.setName("...");
                scriptMethodParam2.setType(ctx.parameterList().restParameter().typeAnnotation().type_().getText());
                method.getParams().add(scriptMethodParam2);
            } else if (ctx.parameterList().parameter() != null) {
                for (TypeScriptParser.ParameterContext parameterContext : ctx.parameterList().parameter()) {
                    ScriptMethodParam scriptMethodParam = new ScriptMethodParam();
                    if (parameterContext.requiredParameter() != null) {
                        scriptMethodParam.setRequired(true);
                        if (parameterContext.requiredParameter().decoratorList() != null) {
                            List<String> decorators = new ArrayList<>();
                            for (TypeScriptParser.DecoratorContext decoratorContext : parameterContext.requiredParameter().decoratorList().decorator()) {
                                decorators.add(decoratorContext.getText());
                            }
                            scriptMethodParam.setDecorators(decorators);
                        }
                        if (parameterContext.requiredParameter().accessibilityModifier() != null) {
                            scriptMethodParam.setModifier(parameterContext.requiredParameter().accessibilityModifier().getText());
                        }
                        scriptMethodParam.setName(parameterContext.requiredParameter().identifierOrPattern().getText());
                        if (parameterContext.requiredParameter().typeAnnotation() != null) {
                            scriptMethodParam.setType(parameterContext.requiredParameter().typeAnnotation().type_().getText());
                        }
                    } else {
                        if (parameterContext.optionalParameter().decoratorList() != null) {
                            List<String> decorators = new ArrayList<>();
                            for (TypeScriptParser.DecoratorContext decoratorContext : parameterContext.optionalParameter().decoratorList().decorator()) {
                                decorators.add(decoratorContext.getText());
                            }
                            scriptMethodParam.setDecorators(decorators);
                        }
                        if (parameterContext.optionalParameter().accessibilityModifier() != null) {
                            scriptMethodParam.setModifier(parameterContext.optionalParameter().accessibilityModifier().getText());
                        }
                        scriptMethodParam.setName(parameterContext.optionalParameter().identifierOrPattern().getText());
                        if (parameterContext.optionalParameter().initializer() != null) {
                            scriptMethodParam.setDefaultValue(parameterContext.optionalParameter().initializer().getText());
                        }
                        if (parameterContext.optionalParameter().typeAnnotation() != null) {
                            scriptMethodParam.setType(parameterContext.optionalParameter().typeAnnotation().type_().getText());
                        }
                    }
                    method.getParams().add(scriptMethodParam);
                }
            }
        }

        if (ctx.typeAnnotation() != null) {
            ScriptMethodReturn scriptMethodReturn = new ScriptMethodReturn();
            scriptMethodReturn.setType(ctx.typeAnnotation().type_().getText());
            method.setMethodReturn(scriptMethodReturn);
        } else {
            ScriptMethodReturn scriptMethodReturn = new ScriptMethodReturn();
            scriptMethodReturn.setType("void");
            method.setMethodReturn(scriptMethodReturn);
        }
        return null;
    }


}
