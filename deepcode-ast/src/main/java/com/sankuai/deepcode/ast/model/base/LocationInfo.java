package com.sankuai.deepcode.ast.model.base;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 * Package: com.sankuai.deepcode.ast.model.base
 * Description:
 *
 * <AUTHOR>
 * @since 2024/12/5 10:30
 */
@Setter
@Getter
@ToString(exclude = {"column"}, includeFieldNames = false)
@NoArgsConstructor
@Accessors(chain = true)
public class LocationInfo {
    private int line;
    private int column;

    public LocationInfo(int line) {
        this(line, -1);
    }

    public LocationInfo(int line, int column) {
        this.line = line;
        this.column = column;
    }

    public static LocationInfo of(int line, int column) {
        return new LocationInfo(line, column);
    }

    public static LocationInfo of(int line) {
        return of(line, -1);
    }
}
