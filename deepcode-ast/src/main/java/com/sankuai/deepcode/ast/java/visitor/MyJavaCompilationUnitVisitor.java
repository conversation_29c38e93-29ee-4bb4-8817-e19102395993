package com.sankuai.deepcode.ast.java.visitor;

import com.sankuai.deepcode.ast.enums.java.ClassTypeEnum;
import com.sankuai.deepcode.ast.java.gen.JavaParser;
import com.sankuai.deepcode.ast.java.gen.JavaParserBaseVisitor;
import com.sankuai.deepcode.ast.java.visitor.MyJavaAnnotationVisitor;
import com.sankuai.deepcode.ast.model.java.ClassNode;
import com.sankuai.deepcode.ast.model.java.JavaAnnotation;
import com.sankuai.deepcode.ast.util.Md5Util;
import lombok.Getter;

import java.util.ArrayList;
import java.util.List;

public class MyJavaCompilationUnitVisitor extends JavaParserBaseVisitor<Object> {

    private String classPath;

    public MyJavaCompilationUnitVisitor(String path) {
        classPath = path;
    }

    private String packageName = "";
    private List<String> imports = new ArrayList<>();
    @Getter
    private List<ClassNode> classNodes = new ArrayList<>();

    @Override
    public Object visitPackageDeclaration(JavaParser.PackageDeclarationContext ctx) {
        packageName = ctx.qualifiedName().getText();
        return null;
    }

    @Override
    public Object visitImportDeclaration(JavaParser.ImportDeclarationContext ctx) {
        if (!imports.contains(ctx.qualifiedName().getText())) {
            imports.add(ctx.qualifiedName().getText());
        }
        return null;
    }


    @Override
    public Object visitTypeDeclaration(JavaParser.TypeDeclarationContext ctx) {
        List<JavaParser.ClassOrInterfaceModifierContext> classOrInterfaceModifierContexts = ctx.classOrInterfaceModifier();
        if (classOrInterfaceModifierContexts != null) {
            List<JavaAnnotation> annotations = new ArrayList<>();
            String access = "";
            for (JavaParser.ClassOrInterfaceModifierContext classOrInterfaceModifierContext : classOrInterfaceModifierContexts) {
                if (null != classOrInterfaceModifierContext.annotation()) {
                    com.sankuai.deepcode.ast.java.visitor.MyJavaAnnotationVisitor visitor = new MyJavaAnnotationVisitor();
                    visitor.visit(classOrInterfaceModifierContext.annotation());
                    annotations.add(visitor.getAnnotation());
                } else {
                    access += " " + classOrInterfaceModifierContext.getText();
                }
            }
            access = access.trim();
            ClassNode classNode = new ClassNode();
            classNode.setImports(imports);
            classNode.setClassPath(classPath);
            classNode.setFileVid(Md5Util.filePathToMd5(classNode.getClassPath()));
            classNode.setAnnotations(annotations);
            classNode.setAccess(access);
            if (null != ctx.classDeclaration()) {
                classNode.setClassType(ClassTypeEnum.CLASS.getCode());
                MyJavaClassDeclarationVisitor visitor = new MyJavaClassDeclarationVisitor(null, classNode, packageName);
                visitor.visit(ctx.classDeclaration());
            } else if (null != ctx.enumDeclaration()) {
                classNode.setClassType(ClassTypeEnum.ENUM.getCode());
                MyJavaEnumDeclarationVisitor visitor = new MyJavaEnumDeclarationVisitor(null, classNode, packageName);
                visitor.visit(ctx.enumDeclaration());
            } else if (null != ctx.interfaceDeclaration()) {
                classNode.setClassType(ClassTypeEnum.INTERFACE.getCode());
                MyJavaInterfaceDeclarationVisitor visitor = new MyJavaInterfaceDeclarationVisitor(null, classNode, packageName);
                visitor.visit(ctx.interfaceDeclaration());
            } else if (null != ctx.annotationTypeDeclaration()) {
                classNode.setClassType(ClassTypeEnum.ANNOTATION.getCode());
                MyAnnotationTypeDeclarationVisitor visitor = new MyAnnotationTypeDeclarationVisitor(null, classNode, packageName);
                visitor.visit(ctx.annotationTypeDeclaration());
            } else {
                //todo recordDeclaration暂不处理
            }
            classNodes.add(classNode);
        }
        return null;
    }


}
