package com.sankuai.deepcode.ast.typescript.visitor;

import com.sankuai.deepcode.ast.model.typescript.*;
import com.sankuai.deepcode.ast.typescript.gen.TypeScriptParser;
import com.sankuai.deepcode.ast.typescript.gen.TypeScriptParserBaseVisitor;
import lombok.Getter;

import java.util.ArrayList;
import java.util.List;

public class MyImportStatementVisitor extends TypeScriptParserBaseVisitor<Object> {

    @Getter
    private ScriptImport scriptImport;

    private ScriptNode scriptNode;

    MyImportStatementVisitor(ScriptNode myScriptNode) {
        scriptNode = myScriptNode;
        scriptImport = new ScriptImport();
        scriptNode.getImports().add(scriptImport);
    }


    @Override
    public Object visitImportStatement(TypeScriptParser.ImportStatementContext ctx) {
        scriptImport.setStartLine(ctx.start.getLine());
        scriptImport.setEndLine(ctx.stop.getLine());
        if (ctx.TypeAlias() != null) {
            scriptImport.setType(true);
        }
        visitImportFromBlock(ctx.importFromBlock());
        return null;
    }

    @Override
    public Object visitImportFromBlock(TypeScriptParser.ImportFromBlockContext ctx) {
        if (ctx.StringLiteral() != null) {
            scriptImport.setFromName(ctx.StringLiteral().getText());
        } else {
            if (ctx.importDefault() != null) {
                TypeScriptParser.AliasNameContext aliasNameContext = ctx.importDefault().aliasName();
                ScriptImportAsName scriptImportAsName = new ScriptImportAsName();
                scriptImportAsName.setName(aliasNameContext.identifierName().get(0).getText());
                scriptImport.setDefaultName(scriptImportAsName);
                if (aliasNameContext.As() != null) {
                    scriptImportAsName.setAsName(aliasNameContext.identifierName().get(1).getText());
                }
            }
            if (ctx.importNamespace() != null) {
                ScriptImportNameSpace scriptImportNameSpace = new ScriptImportNameSpace();
                if (ctx.importNamespace().Multiply() != null) {
                    scriptImportNameSpace.setName("*");
                } else {
                    scriptImportNameSpace.setName(ctx.importNamespace().identifierName().get(0).getText());
                }

                if (ctx.importNamespace().As() != null) {
                    if (ctx.importNamespace().identifierName().size() == 1) {
                        scriptImportNameSpace.setAsName(ctx.importNamespace().identifierName().get(0).getText());
                    } else {
                        scriptImportNameSpace.setAsName(ctx.importNamespace().identifierName().get(1).getText());
                    }
                }
                scriptImport.setNameSpace(scriptImportNameSpace);
            }

            if (ctx.importModuleItems() != null) {
                List<ScriptImportModule> moduleNames = new ArrayList<>();
                for (TypeScriptParser.ImportAliasNameContext importAliasNameContext : ctx.importModuleItems().importAliasName()) {
                    ScriptImportModule scriptImportModule = new ScriptImportModule();
                    scriptImportModule.setExportName(importAliasNameContext.moduleExportName().getText());
                    if (importAliasNameContext.As() != null) {
                        scriptImportModule.setAsName(importAliasNameContext.importedBinding().getText());
                    }
                    moduleNames.add(scriptImportModule);
                }
                scriptImport.setModuleNames(moduleNames);
            }
            if (ctx.importFrom() != null) {
                scriptImport.setFromName(ctx.importFrom().StringLiteral().getText());
            }
            scriptImport.setFromName(ctx.importFrom().StringLiteral().getText());
        }
        return null;
    }


}
