package com.sankuai.deepcode.ast.model.java;

import com.sankuai.deepcode.ast.model.java.JavaAnnotation;
import com.sankuai.deepcode.ast.model.java.JavaGenerics;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
public class JavaParam {
    private String access;
    private List<JavaAnnotation> annotations;
    private String type;
    private String name;
    private List<JavaGenerics> generics = new ArrayList<>();
    private Object value;
}
