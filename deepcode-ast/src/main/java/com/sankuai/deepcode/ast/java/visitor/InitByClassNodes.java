package com.sankuai.deepcode.ast.java.visitor;

import com.sankuai.deepcode.ast.model.java.ClassNode;
import com.sankuai.deepcode.ast.model.java.FieldNode;
import com.sankuai.deepcode.ast.model.java.MethodNode;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;

public class InitByClassNodes {

    public static void init(ClassNode classNode, Map<String, ClassNode> classMap, Map<String, List<MethodNode>> classAndMethodMap, Set<String> classList, Map<String, List<FieldNode>> nameFieldMap) {
        classMap.put(classNode.getClassName() + "$" + classNode.getInClassName(), classNode);
        classList.add(classNode.getClassName());
        List<MethodNode> methodNodes = new ArrayList<>();
        for (MethodNode methodNode : classNode.getMethodNodes()) {
            methodNodes.add(methodNode);
        }
        if (null == classAndMethodMap.get(classNode.getClassName() + "$" + classNode.getInClassName())) {
            classAndMethodMap.put(classNode.getClassName() + "$" + classNode.getInClassName(), methodNodes);
        } else {
            classAndMethodMap.get(classNode.getClassName() + "$" + classNode.getInClassName()).addAll(methodNodes);
        }
        for (FieldNode fieldNode : classNode.getFieldNodes()) {
            //todo 先不处理枚举
            boolean addField = false;
            if (StringUtils.isNotEmpty(fieldNode.getAccess())) {
                if (fieldNode.getAccess().contains("public") && fieldNode.getAccess().contains("static")) {
                    addField = true;
                }
            }
            if (addField) {
                if (null == nameFieldMap.get(fieldNode.getFieldName())) {
                    List<FieldNode> fieldNodes = new ArrayList<>();
                    fieldNodes.add(fieldNode);
                    nameFieldMap.put(fieldNode.getFieldName(), fieldNodes);
                } else {
                    nameFieldMap.get(fieldNode.getFieldName()).add(fieldNode);
                }
            }
        }
        if (CollectionUtils.isNotEmpty(classNode.getInClassNodes())) {
            for (ClassNode inClassNode : classNode.getInClassNodes()) {
                init(inClassNode, classMap, classAndMethodMap, classList, nameFieldMap);
            }
        }
    }
}
