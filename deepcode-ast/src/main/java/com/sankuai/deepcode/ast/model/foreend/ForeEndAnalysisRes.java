package com.sankuai.deepcode.ast.model.foreend;

import com.sankuai.deepcode.ast.model.html.HtmlNode;
import com.sankuai.deepcode.ast.model.java.FileNode;
import com.sankuai.deepcode.ast.model.java.MethodInvokeMethodEdge;
import com.sankuai.deepcode.ast.model.java.MethodQuoteFieldEdge;
import com.sankuai.deepcode.ast.model.scss.ScssRule;
import com.sankuai.deepcode.ast.model.typescript.ScriptMethod;
import com.sankuai.deepcode.ast.model.typescript.ScriptVariable;
import com.sankuai.deepcode.ast.util.Md5Util;
import lombok.Data;

import java.util.*;

@Data
public class ForeEndAnalysisRes {
    private String fromCommit;
    private String toCommit;
    private String buildCommit;
    private Map<String, Map<String, HtmlNode>> htmlNodeMap = new HashMap<>();
    private Map<String, Map<String, ScssRule>> scssRuleMap = new HashMap<>();
    private Map<String, Map<String, ScriptMethod>> scriptMethodMap = new HashMap<>();
    private Map<String, Map<String, ScriptVariable>> scriptVariableMap = new HashMap<>();
    private Map<String, List<Integer>> commentList = new HashMap<>();

    private Map<String, FileNode> fileNodeMap = new HashMap<>();

    private Map<String, Set<String>> fileImportFile = new HashMap<>();
    private Map<String, Set<String>> fileDefineMethod = new HashMap<>();
    private Map<String, Set<String>> fileDefineField = new HashMap<>();
    private Map<String, List<ForeEndBaseEdge>> htmlInvokeMethod = new HashMap<>();
    //    private Map<String, List<ForeEndBaseEdge>> htmlQuoteField = new HashMap<>();
    private Map<String, List<ForeEndBaseEdge>> methodInvokeMethod = new HashMap<>();
    private Map<String, List<ForeEndBaseEdge>> htmlQuoteScss = new HashMap<>();
//    private Map<String, List<ForeEndBaseEdge>> htmlQuoteField = new HashMap<>();



    public void addFileImportFile(String sourcePath, String targetPath) {
        String source = Md5Util.filePathToMd5(sourcePath);
        String target = Md5Util.filePathToMd5(targetPath);
        if (fileImportFile.containsKey(source)) {
            fileImportFile.get(source).add(target);
        } else {
            Set<String> set = new HashSet<>();
            set.add(target);
            fileImportFile.put(source, set);
        }
    }

    public void addFileDefineMethod(String filePath, String methodVid) {
        String file = Md5Util.filePathToMd5(filePath);
        if (fileDefineMethod.containsKey(file)) {
            fileDefineMethod.get(file).add(methodVid);
        } else {
            Set<String> set = new HashSet<>();
            set.add(methodVid);
            fileDefineMethod.put(file, set);
        }
    }

    public void addFileDefineField(String filePath, String fieldVid) {
        String file = Md5Util.filePathToMd5(filePath);
        if (fileDefineField.containsKey(file)) {
            fileDefineField.get(file).add(fieldVid);
        } else {
            Set<String> set = new HashSet<>();
            set.add(fieldVid);
            fileDefineField.put(file, set);
        }
    }

}
