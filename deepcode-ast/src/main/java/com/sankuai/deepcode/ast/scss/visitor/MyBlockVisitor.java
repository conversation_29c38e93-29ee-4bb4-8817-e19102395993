package com.sankuai.deepcode.ast.scss.visitor;

import com.sankuai.deepcode.ast.model.scss.ScssRule;
import com.sankuai.deepcode.ast.scss.gen.ScssParser;
import com.sankuai.deepcode.ast.scss.gen.ScssParserBaseVisitor;
import com.sankuai.deepcode.ast.scss.visitor.MyRulesetVisitor;
import lombok.Getter;
import org.apache.commons.collections4.CollectionUtils;

public class MyBlockVisitor extends ScssParserBaseVisitor<Object> {

    @Getter
    private ScssRule scssRule;

    public MyBlockVisitor(ScssRule myScssRule) {
        scssRule = myScssRule;
    }


    @Override
    public Object visitBlock(ScssParser.BlockContext ctx) {
        scssRule.setStartLine(ctx.start.getLine());
        scssRule.setEndLine(ctx.stop.getLine());

        if (CollectionUtils.isNotEmpty(ctx.statement())) {
            for (ScssParser.StatementContext statementContext : ctx.statement()) {
                if (statementContext.ruleset() != null) {
                    MyRulesetVisitor visitor = new MyRulesetVisitor(null);
                    visitor.visitRuleset(statementContext.ruleset());
                    scssRule.getChildren().add(visitor.getScssRule());
                }
                if (statementContext.variableDeclaration() != null) {
                    MyVariableDeclarationVisitor visitor = new MyVariableDeclarationVisitor();
                    visitor.visitVariableDeclaration(statementContext.variableDeclaration());
                    scssRule.getVariables().add(visitor.getScssVariable());
                }
                if (statementContext.propertyDeclaration() != null) {
                    MyPropertyDeclarationVisitor visitor = new MyPropertyDeclarationVisitor();
                    visitor.visitPropertyDeclaration(statementContext.propertyDeclaration());
                    scssRule.getVariables().add(visitor.getScssVariable());
                }
                if (statementContext.errorDeclaration() != null) {
                    MyErrorDeclarationVisitor visitor = new MyErrorDeclarationVisitor();
                    visitor.visitErrorDeclaration(statementContext.errorDeclaration());
                    scssRule.getVariables().add(visitor.getScssVariable());
                }
                if (statementContext.warndingDeclaration() != null) {
                    MyWarndingDeclarationVisitor visitor = new MyWarndingDeclarationVisitor();
                    visitor.visitWarndingDeclaration(statementContext.warndingDeclaration());
                    scssRule.getVariables().add(visitor.getScssVariable());
                }
            }
        }
        return null;
    }

}
