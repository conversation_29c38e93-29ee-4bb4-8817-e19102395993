package com.sankuai.deepcode.ast.scss.visitor;

import com.sankuai.deepcode.ast.model.scss.ScssVariable;
import com.sankuai.deepcode.ast.scss.gen.ScssParser;
import com.sankuai.deepcode.ast.scss.gen.ScssParserBaseVisitor;
import lombok.Getter;
import org.apache.commons.collections4.CollectionUtils;

public class MyVariableDeclarationVisitor extends ScssParserBaseVisitor<Object> {

    @Getter
    private ScssVariable scssVariable;


    @Override
    public Object visitVariableDeclaration(ScssParser.VariableDeclarationContext ctx) {
        scssVariable = new ScssVariable();
        scssVariable.setStartLine(ctx.getStart().getLine());
        scssVariable.setEndLine(ctx.getStop().getLine());
        visitVariableName(ctx.variableName());
        if (ctx.prio() != null) {
            scssVariable.setPrio(ctx.prio().getText());
        }
        visitVariableValue(ctx.variableValue());
        return null;
    }

    @Override
    public Object visitVariableName(ScssParser.VariableNameContext ctx) {
        if (ctx.identifier() != null) {
            scssVariable.setName(ctx.identifier().getText());
        } else if (ctx.Variable() != null) {
            scssVariable.setName(ctx.Variable().getText());
        }
        if (ctx.plusMinus() != null) {
            scssVariable.setPlusMinus(ctx.plusMinus().getText());
        }
        if (CollectionUtils.isNotEmpty(ctx.Minus())) {
            for (int i = 0; i < ctx.Minus().size(); i++) {
                scssVariable.setPlusMinus(scssVariable.getPlusMinus() + "-");
            }
            scssVariable.setName(ctx.identifier().getText());
        }
        if (ctx.namespace_() != null) {
            //todo 空间内嵌套$暂不解析
            scssVariable.setNameSpace(ctx.namespace_().getText());
        }
        return null;
    }

    @Override
    public Object visitVariableValue(ScssParser.VariableValueContext ctx) {
        //todo 先简单处理 成string 不解析
//        variableValue
//        : value
//                | functionDeclaration
//                | functionCall
//                | mapDeclaration+
//                | listDeclaration+
//                | variableName
//                | identifier
//        ;
        scssVariable.setValue(ctx.getText());
        return null;
    }


}
