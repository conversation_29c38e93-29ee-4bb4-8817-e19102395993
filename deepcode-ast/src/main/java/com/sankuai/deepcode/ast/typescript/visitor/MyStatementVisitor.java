package com.sankuai.deepcode.ast.typescript.visitor;

import com.sankuai.deepcode.ast.model.typescript.ScriptNode;
import com.sankuai.deepcode.ast.model.typescript.ScriptNameSpace;
import com.sankuai.deepcode.ast.typescript.gen.TypeScriptParser;
import com.sankuai.deepcode.ast.typescript.gen.TypeScriptParserBaseVisitor;
import lombok.Getter;
import org.antlr.v4.runtime.tree.ParseTree;
import org.apache.commons.collections4.CollectionUtils;

public class MyStatementVisitor extends TypeScriptParserBaseVisitor<Object> {

    @Getter
    private ScriptNode scriptNode;

    MyStatementVisitor(ScriptNode myScriptNode) {
        if (myScriptNode == null) {
            scriptNode = new ScriptNode();
        } else {
            scriptNode = myScriptNode;
        }
    }

    @Override
    public Object visitStatement(TypeScriptParser.StatementContext ctx) {
        if (ctx == null) {
            return null;
        }
        scriptNode.setStartLine(ctx.getStart().getLine());
        if (ctx.getStop() == null) {
            return null;
        }
        scriptNode.setEndLine(ctx.getStop().getLine());
        if (ctx.block() != null && ctx.block().statementList() != null) {
            for (TypeScriptParser.StatementContext statementContext : ctx.block().statementList().statement()) {
                MyStatementVisitor myStatementVisitor = new MyStatementVisitor(scriptNode);
                myStatementVisitor.visit(statementContext);
            }
        } else if (ctx.variableStatement() != null) {
            MyVariableStatementVisitor myVariableStatementVisitor = new MyVariableStatementVisitor(scriptNode);
            myVariableStatementVisitor.visit(ctx.variableStatement());
        } else if (ctx.moduleStatement() != null) {
            MyModuleStatementVisitor myModuleStatementVisitor = new MyModuleStatementVisitor(scriptNode);
            myModuleStatementVisitor.visit(ctx.moduleStatement());
        } else if (ctx.importStatement() != null) {
            MyImportStatementVisitor myImportStatementVisitor = new MyImportStatementVisitor(scriptNode);
            myImportStatementVisitor.visit(ctx.importStatement());
        } else if (ctx.exportStatement() != null) {
            MyExportStatementVisitor myExportStatementVisitor = new MyExportStatementVisitor(scriptNode);
            myExportStatementVisitor.visit(ctx.exportStatement());
        } else if (ctx.abstractDeclaration() != null) {
            MyAbstractDeclarationVisitor myAbstractDeclarationVisitor = new MyAbstractDeclarationVisitor(scriptNode);
            myAbstractDeclarationVisitor.visit(ctx.abstractDeclaration());
        } else if (ctx.classDeclaration() != null) {
            MyClassDeclarationVisitor myClassDeclarationVisitor = new MyClassDeclarationVisitor(scriptNode);
            myClassDeclarationVisitor.visit(ctx.classDeclaration());
        } else if (ctx.functionDeclaration() != null) {
            MyFunctionDeclarationVisitor myFunctionDeclarationVisitor = new MyFunctionDeclarationVisitor(scriptNode);
            myFunctionDeclarationVisitor.visit(ctx.functionDeclaration());
        } else if (ctx.expressionStatement() != null) {
            if (ctx.expressionStatement().expressionSequence() != null) {
                for (TypeScriptParser.SingleExpressionContext singleExpressionContext : ctx.expressionStatement().expressionSequence().singleExpression()) {
                    MySingleExpressionVisitor mySingleExpressionVisitor = new MySingleExpressionVisitor(scriptNode);
                    mySingleExpressionVisitor.visit(singleExpressionContext);
                }
            }
        } else if (ctx.interfaceDeclaration() != null) {
            MyInterfaceDeclarationVisitor myInterfaceDeclarationVisitor = new MyInterfaceDeclarationVisitor(scriptNode);
            myInterfaceDeclarationVisitor.visit(ctx.interfaceDeclaration());
        } else if (ctx.namespaceDeclaration() != null) {
            ScriptNameSpace scriptNameSpace = new ScriptNameSpace();
            if (scriptNameSpace.isDecare()) {
                scriptNameSpace.setDecare(true);
            }
            scriptNameSpace.setName(ctx.namespaceDeclaration().namespaceName().getText());
            if (ctx.namespaceDeclaration().statementList() != null && CollectionUtils.isNotEmpty(ctx.namespaceDeclaration().statementList().statement())) {
                for (TypeScriptParser.StatementContext statementContext : ctx.namespaceDeclaration().statementList().statement()) {
                    MyStatementVisitor myStatementVisitor = new MyStatementVisitor(null);
                    myStatementVisitor.visit(statementContext);
                    if (myStatementVisitor.getScriptNode().getMethods() != null) {
                        scriptNameSpace.getMethods().addAll(myStatementVisitor.getScriptNode().getMethods());
                    }
                    if (myStatementVisitor.getScriptNode().getClasses() != null) {
                        scriptNameSpace.getClasses().addAll(myStatementVisitor.getScriptNode().getClasses());
                    }
                    if (myStatementVisitor.getScriptNode().getVariables() != null) {
                        scriptNameSpace.getVariables().addAll(myStatementVisitor.getScriptNode().getVariables());
                    }
                    if (myStatementVisitor.getScriptNode().getNamespaces() != null) {
                        scriptNameSpace.getNamespaces().addAll(myStatementVisitor.getScriptNode().getNamespaces());
                    }
                    if (myStatementVisitor.getScriptNode().getTypeAliases() != null) {
                        scriptNameSpace.getTypeAliases().addAll(myStatementVisitor.getScriptNode().getTypeAliases());
                    }
                    if (myStatementVisitor.getScriptNode().getInvokeMethods() != null) {
                        scriptNameSpace.getInvokeMethods().addAll(myStatementVisitor.getScriptNode().getInvokeMethods());
                    }
                }
            }
            scriptNode.getNamespaces().add(scriptNameSpace);
        } else if (ctx.ifStatement() != null) {
            for (TypeScriptParser.StatementContext statementContext : ctx.ifStatement().statement()) {
                MyStatementVisitor statementVisitor = new MyStatementVisitor(scriptNode);
                statementVisitor.visit(statementContext);
            }
            for (TypeScriptParser.SingleExpressionContext singleExpressionContext : ctx.ifStatement().expressionSequence().singleExpression()) {
                MySingleExpressionVisitor singleExpressionVisitor = new MySingleExpressionVisitor(scriptNode);
                singleExpressionVisitor.visit(singleExpressionContext);
            }
        } else if (ctx.iterationStatement() != null) {
            if (ctx.iterationStatement().children != null) {
                for (ParseTree parseTree : ctx.iterationStatement().children) {
                    if (parseTree instanceof TypeScriptParser.StatementContext) {
                        MyStatementVisitor statementVisitor = new MyStatementVisitor(scriptNode);
                        statementVisitor.visit(parseTree);
                    }
                    if (parseTree instanceof TypeScriptParser.SingleExpressionContext) {
                        MySingleExpressionVisitor singleExpressionVisitor = new MySingleExpressionVisitor(scriptNode);
                        singleExpressionVisitor.visit(parseTree);
                    }
                    if (parseTree instanceof TypeScriptParser.VariableStatementContext) {
                        MyVariableStatementVisitor variableStatementVisitor = new MyVariableStatementVisitor(scriptNode);
                        variableStatementVisitor.visit(parseTree);
                    }
                    if (parseTree instanceof TypeScriptParser.VariableDeclarationListContext) {
                        TypeScriptParser.VariableDeclarationListContext variableDeclarationListContext = (TypeScriptParser.VariableDeclarationListContext) parseTree;
                        for (TypeScriptParser.VariableDeclarationContext variableDeclarationContext : variableDeclarationListContext.variableDeclaration()) {
                            MyVariableStatementVisitor variableStatementVisitor = new MyVariableStatementVisitor(scriptNode);
                            variableStatementVisitor.visit(variableDeclarationContext);
                        }
                    }
                }
            }
        } else if (ctx.continueStatement() != null) {
            //不需要处理
        } else if (ctx.breakStatement() != null) {
            //不需要处理
        } else if (ctx.returnStatement() != null) {
            if (ctx.returnStatement().expressionSequence() != null) {
                for (TypeScriptParser.SingleExpressionContext singleExpressionContext : ctx.returnStatement().expressionSequence().singleExpression()) {
                    MySingleExpressionVisitor singleExpressionVisitor = new MySingleExpressionVisitor(scriptNode);
                    singleExpressionVisitor.visit(singleExpressionContext);
                }
            } else if (ctx.returnStatement().jsxElements() != null) {
                MyJsxElementsVisitor myJsxElementsVisitor = new MyJsxElementsVisitor();
                myJsxElementsVisitor.visitJsxElement(ctx.returnStatement().jsxElements(), scriptNode);
            }
        } else if (ctx.yieldStatement() != null) {
            for (TypeScriptParser.SingleExpressionContext singleExpressionContext : ctx.yieldStatement().expressionSequence().singleExpression()) {
                MySingleExpressionVisitor singleExpressionVisitor = new MySingleExpressionVisitor(scriptNode);
                singleExpressionVisitor.visit(singleExpressionContext);
            }
        } else if (ctx.withStatement() != null) {
            for (TypeScriptParser.SingleExpressionContext singleExpressionContext : ctx.withStatement().expressionSequence().singleExpression()) {
                MySingleExpressionVisitor singleExpressionVisitor = new MySingleExpressionVisitor(scriptNode);
                singleExpressionVisitor.visit(singleExpressionContext);
            }
        } else if (ctx.labelledStatement() != null) {
            //todo label简单处理  目前可能有重复
            MyStatementVisitor statementVisitor = new MyStatementVisitor(scriptNode);
            statementVisitor.visit(ctx.labelledStatement().statement());
        } else if (ctx.switchStatement() != null) {
            for (TypeScriptParser.SingleExpressionContext singleExpressionContext : ctx.switchStatement().expressionSequence().singleExpression()) {
                MySingleExpressionVisitor singleExpressionVisitor = new MySingleExpressionVisitor(scriptNode);
                singleExpressionVisitor.visit(singleExpressionContext);
            }
        } else if (ctx.throwStatement() != null) {
            for (TypeScriptParser.SingleExpressionContext singleExpressionContext : ctx.throwStatement().expressionSequence().singleExpression()) {
                MySingleExpressionVisitor mySingleExpressionVisitor = new MySingleExpressionVisitor(scriptNode);
                mySingleExpressionVisitor.visit(singleExpressionContext);
            }
        } else if (ctx.tryStatement() != null) {
            for (TypeScriptParser.StatementContext statementContext : ctx.tryStatement().block().statementList().statement()) {
                MyStatementVisitor myStatementVisitor = new MyStatementVisitor(scriptNode);
                myStatementVisitor.visit(statementContext);
            }
            if (ctx.tryStatement().catchProduction() != null
                    && ctx.tryStatement().catchProduction().block() != null
                    && ctx.tryStatement().catchProduction().block().statementList() != null) {
                for (TypeScriptParser.StatementContext statementContext : ctx.tryStatement().catchProduction().block().statementList().statement()) {
                    MyStatementVisitor myStatementVisitor = new MyStatementVisitor(scriptNode);
                    myStatementVisitor.visit(statementContext);
                }
            }
            if (ctx.tryStatement().finallyProduction() != null
                    && ctx.tryStatement().finallyProduction().block() != null
                    && ctx.tryStatement().finallyProduction().block().statementList() != null) {
                for (TypeScriptParser.StatementContext statementContext : ctx.tryStatement().finallyProduction().block().statementList().statement()) {
                    MyStatementVisitor myStatementVisitor = new MyStatementVisitor(scriptNode);
                    myStatementVisitor.visit(statementContext);
                }
            }
        } else if (ctx.debuggerStatement() != null) {
            //todo 暂不处理
        } else if (ctx.arrowFunctionDeclaration() != null) {
            MyArrowFunctionDeclarationVisitor myArrowFunctionDeclarationVisitor = new MyArrowFunctionDeclarationVisitor(scriptNode);
            myArrowFunctionDeclarationVisitor.visit(ctx.arrowFunctionDeclaration());
        } else if (ctx.generatorFunctionDeclaration() != null) {
            MyGeneratorFunctionDeclarationVisitor myGeneratorFunctionDeclarationVisitor = new MyGeneratorFunctionDeclarationVisitor(scriptNode);
            myGeneratorFunctionDeclarationVisitor.visit(ctx.generatorFunctionDeclaration());
        } else if (ctx.typeAliasDeclaration() != null) {
            MyTypeAliasDeclarationVisitor myTypeAliasDeclarationVisitor = new MyTypeAliasDeclarationVisitor(scriptNode);
            myTypeAliasDeclarationVisitor.visit(ctx.typeAliasDeclaration());
        } else if (ctx.enumDeclaration() != null) {
            MyEnumDeclarationVisitor myEnumDeclarationVisitor = new MyEnumDeclarationVisitor(scriptNode);
            myEnumDeclarationVisitor.visit(ctx.enumDeclaration());
        } else if (ctx.statement() != null) {
            MyStatementVisitor myStatementVisitor = new MyStatementVisitor(scriptNode);
            myStatementVisitor.visit(ctx.statement());
        }
        return null;
    }

}
