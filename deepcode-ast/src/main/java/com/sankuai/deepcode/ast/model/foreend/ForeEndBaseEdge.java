package com.sankuai.deepcode.ast.model.foreend;

import com.sankuai.deepcode.ast.model.base.BaseEdge;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
public class ForeEndBaseEdge extends BaseEdge {
    private int type;
    private List<Integer> lines = new ArrayList<>();

    public void addLine(int line) {
        if (!lines.contains(line)) {
            lines.add(line);
        }
    }
}
