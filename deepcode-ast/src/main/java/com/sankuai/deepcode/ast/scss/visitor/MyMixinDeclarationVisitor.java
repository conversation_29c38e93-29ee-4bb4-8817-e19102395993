package com.sankuai.deepcode.ast.scss.visitor;

import com.sankuai.deepcode.ast.model.scss.ScssRule;
import com.sankuai.deepcode.ast.model.scss.ScssSelector;
import com.sankuai.deepcode.ast.model.scss.ScssVariable;
import com.sankuai.deepcode.ast.scss.gen.ScssParser;
import com.sankuai.deepcode.ast.scss.gen.ScssParserBaseVisitor;
import com.sankuai.deepcode.ast.scss.visitor.MyBlockVisitor;
import com.sankuai.deepcode.ast.scss.visitor.MyParametersVisitor;
import lombok.Getter;

import java.util.ArrayList;
import java.util.List;

public class MyMixinDeclarationVisitor extends ScssParserBaseVisitor<Object> {

    @Getter
    private ScssRule scssRule;


    @Override
    public Object visitMixinDeclaration(ScssParser.MixinDeclarationContext ctx) {
        scssRule = new ScssRule();
        scssRule.setStartLine(ctx.getStart().getLine());
        scssRule.setEndLine(ctx.getStop().getLine());
        scssRule.setBody(ctx.getText());
        List<ScssSelector> selectors = new ArrayList<>();
        ScssSelector scssSelector = new ScssSelector();
        scssSelector.setName(ctx.identifier().getText());
        if(ctx.parameters()!=null){
            com.sankuai.deepcode.ast.scss.visitor.MyParametersVisitor parametersVisitor = new MyParametersVisitor();
            parametersVisitor.visit(ctx.parameters());
            scssSelector.setParameters(parametersVisitor.getParameters());
        }
        selectors.add(scssSelector);
        scssRule.setSelectors(selectors);
        com.sankuai.deepcode.ast.scss.visitor.MyBlockVisitor blockVisitor = new MyBlockVisitor(scssRule);
        blockVisitor.visit(ctx.block());
        return null;
    }


}
