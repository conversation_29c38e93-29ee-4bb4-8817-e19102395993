package com.sankuai.deepcode.ast.model.typescript;

import com.sankuai.deepcode.ast.model.java.JavaExtends;
import com.sankuai.deepcode.ast.model.java.JavaImplements;
import com.sankuai.deepcode.ast.model.typescript.ScriptExtends;
import com.sankuai.deepcode.ast.model.typescript.ScriptMethod;
import jdk.nashorn.internal.runtime.ScriptFunction;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
public class ScriptClass {
    private boolean isExport;
    private boolean isConst;
    private boolean isDeclare;
    private int startLine;
    private int endLine;
    private String type;
    private String className;
    private List<String> decorators = new ArrayList<>();
    private ScriptExtends superClass = new ScriptExtends();
    private List<ScriptImplements> interfaces = new ArrayList<>();
    private ScriptGenerics generics;
    private String filePath;
    private List<ScriptMethod> methods = new ArrayList<>();  //array object
    private List<ScriptVariable> variables = new ArrayList<>();
    private ScriptMethod constructor;
}
