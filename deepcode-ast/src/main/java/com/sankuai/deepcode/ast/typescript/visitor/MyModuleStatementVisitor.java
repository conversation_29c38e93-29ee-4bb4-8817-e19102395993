package com.sankuai.deepcode.ast.typescript.visitor;

import com.sankuai.deepcode.ast.model.typescript.*;
import com.sankuai.deepcode.ast.typescript.gen.TypeScriptParser;
import com.sankuai.deepcode.ast.typescript.gen.TypeScriptParserBaseVisitor;
import lombok.Getter;

public class MyModuleStatementVisitor extends TypeScriptParserBaseVisitor<Object> {

    @Getter
    private ScriptDeclareModule scriptDeclareModule;

    private ScriptNode scriptNode;

    MyModuleStatementVisitor(ScriptNode myScriptNode) {
        scriptNode = myScriptNode;
        scriptDeclareModule = new ScriptDeclareModule();
        scriptNode.getDeclareModules().add(scriptDeclareModule);
    }


    @Override
    public Object visitModuleStatement(TypeScriptParser.ModuleStatementContext ctx) {
        scriptDeclareModule.setStartLine(ctx.getStart().getLine());
        scriptDeclareModule.setEndLine(ctx.getStop().getLine());
        if (ctx.moduleExportName() != null) {
            scriptDeclareModule.setName(ctx.moduleExportName().getText());
        }
        if (ctx.block() != null) {
            for (TypeScriptParser.StatementContext statementContext : ctx.block().statementList().statement()) {
                com.sankuai.deepcode.ast.typescript.visitor.MyStatementVisitor myStatementVisitor = new MyStatementVisitor(null);
                myStatementVisitor.visit(statementContext);
                ScriptNode scriptNode = myStatementVisitor.getScriptNode();
                scriptDeclareModule.getVariables().addAll(scriptNode.getVariables());
                scriptDeclareModule.getDeclareModules().addAll(scriptNode.getDeclareModules());
                scriptDeclareModule.getMethods().addAll(scriptNode.getMethods());
                scriptDeclareModule.getClasses().addAll(scriptNode.getClasses());
                scriptDeclareModule.getNamespaces().addAll(scriptNode.getNamespaces());
                scriptDeclareModule.getTypeAliases().addAll(scriptNode.getTypeAliases());
            }
        }

        return null;
    }





}
