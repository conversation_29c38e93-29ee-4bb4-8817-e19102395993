package com.sankuai.deepcode.ast.util;

import lombok.extern.slf4j.Slf4j;

import java.io.BufferedReader;
import java.io.File;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadFactory;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;

@Slf4j
public class LinuxCmdUtil {

    private static final ThreadPoolExecutor EXECUTOR;

    static {
        EXECUTOR = new ThreadPoolExecutor(
                100, // 核心线程数
                200, // 最大线程数
                60L, // 线程空闲时间（秒）
                TimeUnit.SECONDS,
                new LinkedBlockingQueue<>(100), // 队列容量
                new ThreadFactory() {
                    private final AtomicInteger threadNumber = new AtomicInteger(1);

                    @Override
                    public Thread newThread(Runnable r) {
                        return new Thread(r, "async-cmd-" + threadNumber.getAndIncrement());
                    }
                },
                new ThreadPoolExecutor.CallerRunsPolicy() // 拒绝策略
        );
    }


    public static void newTheadGetBuffer(StringBuffer result, InputStream inputStream) {
        Runnable task = () -> {
            BufferedReader bufferedReader = new BufferedReader(new InputStreamReader(inputStream));
            try {
                String tmp;
                while ((tmp = bufferedReader.readLine()) != null) {
                    result.append(tmp).append('\n');
                }
                bufferedReader.close();
            } catch (Exception e) {
                // e.printStackTrace();
            }
        };
        EXECUTOR.execute(task);
    }

    public static String execCmd(List<String> commands, String path) {
        System.out.println("执行: " + commands);
        StringBuffer result = new StringBuffer();
        Process process = null;
        try {
            // 执行命令, 返回一个子进程对象（命令在子进程中执行）
            String[] command = new String[commands.size()];
            commands.toArray(command);
            process = Runtime.getRuntime().exec(command, null, new File(path));

            //启动两个线程，一个线程负责读标准输出流，另一个负责读标准错误流
            newTheadGetBuffer(result, process.getInputStream());
            newTheadGetBuffer(result, process.getErrorStream());
            process.waitFor();
        } catch (Exception e) {
            System.out.println("execCmd 异常" + e);
        } finally {
            if (null != process) {
                process.destroy();
            }
        }
        return result.toString();
    }


    public static String execCmdShell(String command, String path) {
        System.out.println("执行: " + command);
        StringBuffer result = new StringBuffer();
        Process process = null;
        try {
            ProcessBuilder processBuilder = new ProcessBuilder();
            processBuilder.directory(new File(path));
            processBuilder.command("bash", "-c", command);
            process = processBuilder.start();

            //启动两个线程，一个线程负责读标准输出流，另一个负责读标准错误流
            newTheadGetBuffer(result, process.getInputStream());
            newTheadGetBuffer(result, process.getErrorStream());
            process.waitFor();
        } catch (Exception e) {
            System.out.println("execCmd 异常" + e);
        } finally {
            if (null != process) {
                process.destroy();
            }
        }
        return result.toString();
    }


    public static String execCmdTimeout(List<String> commands, String path, int timeout) throws Exception {
        StringBuffer result = new StringBuffer();

        Process process = null;
        try {
            // 执行命令, 返回一个子进程对象（命令在子进程中执行）
            String[] command = new String[commands.size()];
            commands.toArray(command);
            process = Runtime.getRuntime().exec(command, null, new File(path));

            //启动两个线程，一个线程负责读标准输出流，另一个负责读标准错误流
            newTheadGetBuffer(result, process.getInputStream());
            newTheadGetBuffer(result, process.getErrorStream());
            process.waitFor(timeout, TimeUnit.SECONDS);
        } catch (Exception e) {
            System.out.println("execCmd 异常" + e);
            throw new Exception("execCmdTimeout 异常");
        } finally {
            if (null != process) {
                process.destroy();
            }
        }
        log.info("[LinuxCmd] cmd: [{}], result: {}", commands, result);
        return result.toString();
    }
}
