package com.sankuai.deepcode.ast.java.visitor;//package com.sankuai.deepcode.ast.java.visitor;
//
//import com.sankuai.deepcode.ast.java.gen.JavaParser;
//import com.sankuai.deepcode.ast.java.gen.JavaParserBaseVisitor;
//import com.sankuai.deepcode.ast.model.java.FieldNode;
//import lombok.Getter;
//
//import java.util.ArrayList;
//import java.util.List;
//
//public class MyJavaEnumVisitor extends JavaParserBaseVisitor<Object> {
//
//    @Getter
//    private List<FieldNode> fieldNodes = new ArrayList<>();
//
//    @Override
//    public Object visitEnumConstant(JavaParser.EnumConstantContext ctx) {
//        FieldNode fieldNode = new FieldNode();
//        fieldNode.setStartLine(ctx.getStart().getLine());
//        fieldNode.setEndLine(ctx.getStop().getLine());
//        fieldNode.setFieldName(ctx.getChild(0).getText());
//        //todo value 定义未处理
//        if (ctx.getChild(1) != null) {
//            fieldNode.setValue(ctx.getChild(1).getText());
//        }
//        fieldNodes.add(fieldNode);
//        return visitChildren(ctx);
//    }
//
//
//}
