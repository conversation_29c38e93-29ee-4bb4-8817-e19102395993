package com.sankuai.deepcode.ast.model.base;

import com.sankuai.deepcode.ast.enums.DiffTypeEnum;
import lombok.Data;

import java.util.List;

@Data
public class LcsNode {
    int start;
    List<CodeView> value;
    int length;
    DiffTypeEnum type;

    public LcsNode(int start, int length, DiffTypeEnum type) {
        this.start = start;
        this.length = length;
        this.type = type;
    }

    public LcsNode(int start, int length, DiffTypeEnum type, List<CodeView> value) {
        this.start = start;
        this.length = length;
        this.type = type;
        this.value = value;
    }
}
