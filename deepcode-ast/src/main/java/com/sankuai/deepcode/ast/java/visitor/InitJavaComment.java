package com.sankuai.deepcode.ast.java.visitor;

import com.sankuai.deepcode.ast.model.java.*;
import org.antlr.v4.runtime.CommonTokenStream;
import org.antlr.v4.runtime.Token;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Iterator;
import java.util.List;

public class InitJavaComment {

    public static void initCommon(MyJavaCompilationUnitVisitor visitor, CommonTokenStream tokens) {
        List<JavaComment> javaComments = getHiddenTokens(tokens);
        List<CountComment> countComments = new ArrayList<>();
        //class method field inClass inClassMethod inClassField
        initClassNode(visitor.getClassNodes(), countComments);
        Collections.sort(countComments);
        Iterator<JavaComment> iterator = javaComments.iterator();
        while (iterator.hasNext()) {
            JavaComment javaComment = iterator.next();
            List<Integer> lines = new ArrayList<>();
            for (int i = javaComment.getStartLine(); i <= javaComment.getEndLine(); i++) {
                lines.add(i);
            }

            int commonIndex = 0;
            List<Integer> removeIndex = new ArrayList<>();
            for (CountComment countComment : countComments) {
                if (javaComment.getEndLine() <= countComment.getStartLine()) {
                    addCommon(countComment, lines);
                    break;
                }

                if (countComment.getType().equals("method")
                        || countComment.getType().equals("inMethod")) {
                    if (javaComment.getStartLine() > countComment.getStartLine() && javaComment.getEndLine() < countComment.getEndLine()) {
                        addCommon(countComment, lines);
                        break;
                    }
                }
                if (javaComment.getEndLine() > countComment.getEndLine()) {
                    removeIndex.add(commonIndex);
                }
                commonIndex++;
            }
            Collections.sort(removeIndex, Collections.reverseOrder());
            for (Integer remove : removeIndex) {
                countComments.remove(remove.intValue());
            }
        }
    }

    public static void addCommon(CountComment countComment, List<Integer> lines) {
        if (countComment.getType().equals("class")) {
            countComment.getClassNode().getCommentLines().addAll(lines);
        } else if (countComment.getType().equals("method")) {
            countComment.getMethodNode().getCommentLines().addAll(lines);
        } else if (countComment.getType().equals("field")) {
            countComment.getFieldNode().getCommentLines().addAll(lines);
        } else if (countComment.getType().equals("inClass")) {
            countComment.getClassNode().getCommentLines().addAll(lines);
        } else if (countComment.getType().equals("inClassMethod")) {
            countComment.getMethodNode().getCommentLines().addAll(lines);
        } else if (countComment.getType().equals("inClassField")) {
            countComment.getFieldNode().getCommentLines().addAll(lines);
        }
    }

    public static void initClassNode(List<ClassNode> classNodes, List<CountComment> countComments) {
        for (ClassNode classNode : classNodes) {
            CountComment common = new CountComment();
            if (StringUtils.isEmpty(classNode.getInClassName())) {
                common.setStartLine(classNode.getStartLine());
                common.setEndLine(classNode.getEndLine());
                common.setType("class");
                common.setClassNode(classNode);
                countComments.add(common);
                for (MethodNode methodNode : classNode.getMethodNodes()) {
                    CountComment inCountComment = new CountComment();
                    inCountComment.setStartLine(methodNode.getStartLine());
                    inCountComment.setEndLine(methodNode.getEndLine());
                    inCountComment.setType("classMethod");
                    inCountComment.setMethodNode(methodNode);
                    countComments.add(inCountComment);
                }
                for (FieldNode fieldNode : classNode.getFieldNodes()) {
                    CountComment inCountComment = new CountComment();
                    inCountComment.setStartLine(fieldNode.getStartLine());
                    inCountComment.setEndLine(fieldNode.getEndLine());
                    inCountComment.setType("classField");
                    inCountComment.setFieldNode(fieldNode);
                    countComments.add(inCountComment);
                }
            } else {
                common.setStartLine(classNode.getStartLine());
                common.setEndLine(classNode.getEndLine());
                common.setType("inClass");
                common.setClassNode(classNode);
                countComments.add(common);
                for (MethodNode methodNode : classNode.getMethodNodes()) {
                    CountComment inCountComment = new CountComment();
                    inCountComment.setStartLine(methodNode.getStartLine());
                    inCountComment.setEndLine(methodNode.getEndLine());
                    inCountComment.setType("inClassMethod");
                    inCountComment.setMethodNode(methodNode);
                    countComments.add(inCountComment);
                }
                for (FieldNode fieldNode : classNode.getFieldNodes()) {
                    CountComment inCountComment = new CountComment();
                    inCountComment.setStartLine(fieldNode.getStartLine());
                    inCountComment.setEndLine(fieldNode.getEndLine());
                    inCountComment.setType("inClassField");
                    inCountComment.setFieldNode(fieldNode);
                    countComments.add(inCountComment);
                }
            }
            if (CollectionUtils.isNotEmpty(classNode.getInClassNodes())) {
                initClassNode(classNode.getInClassNodes(), countComments);
            }
        }
    }

//    public static void initCommon(MyJavaClassVisitor visitor, CommonTokenStream tokens) {
//        List<JavaCommon> javaCommons = getHiddenTokens(tokens);
//        List<CountCommon> countCommons = new ArrayList<>();
//        //class method field inClass inClassMethod inClassField
//        CountCommon common = new CountCommon();
//        common.setStartLine(visitor.getClassNode().getStartLine());
//        common.setEndLine(visitor.getClassNode().getEndLine());
//        common.setType("class");
//        countCommons.add(common);
//
//        int index = 0;
//        for (ClassNode classNode : visitor.getInClassNode()) {
//            CountCommon countCommon = new CountCommon();
//            countCommon.setIndex(index);
//            countCommon.setStartLine(classNode.getStartLine());
//            countCommon.setEndLine(classNode.getEndLine());
//            countCommon.setType("inClass");
//            countCommons.add(countCommon);
//            int inIndex = 0;
//            for (MethodNode methodNode : classNode.getMethodNodes()) {
//                CountCommon inCountCommon = new CountCommon();
//                inCountCommon.setIndex(index);
//                inCountCommon.setInIndex(inIndex);
//                inCountCommon.setStartLine(methodNode.getStartLine());
//                inCountCommon.setEndLine(methodNode.getEndLine());
//                inCountCommon.setType("inClassMethod");
//                countCommons.add(inCountCommon);
//                inIndex++;
//            }
//            inIndex = 0;
//            for (FieldNode fieldNode : classNode.getFieldNodes()) {
//                CountCommon inCountCommon = new CountCommon();
//                inCountCommon.setIndex(index);
//                inCountCommon.setInIndex(inIndex);
//                inCountCommon.setStartLine(fieldNode.getStartLine());
//                inCountCommon.setEndLine(fieldNode.getEndLine());
//                inCountCommon.setType("inClassField");
//                countCommons.add(inCountCommon);
//                inIndex++;
//            }
//            index++;
//        }
//
//        index = 0;
//        for (MethodNode methodNode : visitor.getClassNode().getMethodNodes()) {
//            CountCommon countCommon = new CountCommon();
//            countCommon.setIndex(index);
//            countCommon.setStartLine(methodNode.getStartLine());
//            countCommon.setEndLine(methodNode.getEndLine());
//            countCommon.setType("method");
//            countCommons.add(countCommon);
//            index++;
//        }
//
//        index = 0;
//        for (FieldNode fieldNode : visitor.getClassNode().getFieldNodes()) {
//            CountCommon countCommon = new CountCommon();
//            countCommon.setIndex(index);
//            countCommon.setStartLine(fieldNode.getStartLine());
//            countCommon.setEndLine(fieldNode.getEndLine());
//            countCommon.setType("field");
//            countCommons.add(countCommon);
//            index++;
//        }
//
//        Collections.sort(countCommons);
//
//        Iterator<JavaCommon> iterator = javaCommons.iterator();
//        while (iterator.hasNext()) {
//            JavaCommon javaCommon = iterator.next();
//            List<Integer> lines = new ArrayList<>();
//            for (int i = javaCommon.getStartLine(); i <= javaCommon.getEndLine(); i++) {
//                lines.add(i);
//            }
//
//            int commonIndex = 0;
//            List<Integer> removeIndex = new ArrayList<>();
//            for (CountCommon countCommon : countCommons) {
//                if (javaCommon.getEndLine() <= countCommon.getStartLine()) {
//                    addCommon(visitor, countCommon, lines);
//                    break;
//                }
//
//                if (countCommon.getType().equals("method")
//                        || countCommon.getType().equals("inMethod")) {
//                    if (javaCommon.getStartLine() > countCommon.getStartLine() && javaCommon.getEndLine() < countCommon.getEndLine()) {
//                        addCommon(visitor, countCommon, lines);
//                        break;
//                    }
//                }
//                if (javaCommon.getEndLine() > countCommon.getEndLine()) {
//                    removeIndex.add(commonIndex);
//                }
//                commonIndex++;
//            }
//            Collections.sort(removeIndex, Collections.reverseOrder());
//            for (Integer remove : removeIndex) {
//                countCommons.remove(remove.intValue());
//            }
//        }
//    }
//
//    public static void addCommon(MyJavaClassVisitor visitor, CountCommon countCommon, List<Integer> lines) {
//        if (countCommon.getType().equals("class")) {
//            visitor.getClassNode().getCommentLines().addAll(lines);
//        } else if (countCommon.getType().equals("method")) {
//            visitor.getClassNode().getMethodNodes().get(countCommon.getIndex()).getCommentLines().addAll(lines);
//        } else if (countCommon.getType().equals("field")) {
//            visitor.getClassNode().getFieldNodes().get(countCommon.getIndex()).getCommentLines().addAll(lines);
//        } else if (countCommon.getType().equals("inClass")) {
//            visitor.getInClassNode().get(countCommon.getIndex()).getCommentLines().addAll(lines);
//        } else if (countCommon.getType().equals("inClassMethod")) {
//            visitor.getInClassNode().get(countCommon.getIndex()).getMethodNodes().get(countCommon.getInIndex()).getCommentLines().addAll(lines);
//        } else if (countCommon.getType().equals("inClassField")) {
//            visitor.getInClassNode().get(countCommon.getIndex()).getFieldNodes().get(countCommon.getInIndex()).getCommentLines().addAll(lines);
//        }
//    }


    public static List<JavaComment> getHiddenTokens(CommonTokenStream tokens) {
        List<JavaComment> hiddenTokens = new ArrayList<>();
        for (Token token : tokens.getTokens()) {
            if (token.getChannel() == Token.HIDDEN_CHANNEL) {
                if (StringUtils.isNotEmpty(token.getText().trim())) {
                    JavaComment javaComment = new JavaComment();
                    javaComment.setStartLine(token.getLine());
                    Token nextToken = tokens.get(token.getTokenIndex() + 1);
                    if (null != nextToken) {
                        javaComment.setEndLine(nextToken.getLine());
                    } else {
                        javaComment.setStartLine(token.getLine());
                    }
                    hiddenTokens.add(javaComment);
                }
            }
        }
        return hiddenTokens;
    }
}
