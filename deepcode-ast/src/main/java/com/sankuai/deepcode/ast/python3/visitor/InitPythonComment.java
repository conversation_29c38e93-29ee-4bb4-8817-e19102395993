package com.sankuai.deepcode.ast.python3.visitor;

import com.sankuai.deepcode.ast.model.python3.*;
import org.antlr.v4.runtime.CommonTokenStream;
import org.antlr.v4.runtime.Token;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Iterator;
import java.util.List;

/**
 * Package: com.sankuai.deepcode.ast.python3.visitor
 * Description: Python注释初始化和分配逻辑
 *
 * <AUTHOR>
 * @since 2024/12/9 15:10
 */
public class InitPythonComment {

    /**
     * 初始化并分配注释到各个节点
     *
     * @param moduleNode 模块节点
     * @param tokens     token流
     */
    public static void initComment(PythonModuleNode moduleNode, CommonTokenStream tokens) {
        List<PythonComment> pythonComments = getHiddenTokens(tokens);
        List<PythonCountComment> countComments = new ArrayList<>();
        
        // 初始化模块、类、方法等节点的范围信息
        initModuleNode(moduleNode, countComments);
        
        // 按行号排序
        Collections.sort(countComments);
        
        // 分配注释到各个节点
        Iterator<PythonComment> iterator = pythonComments.iterator();
        while (iterator.hasNext()) {
            PythonComment pythonComment = iterator.next();
            int commentLine = pythonComment.commentLine();
            
            if (commentLine == -1) {
                continue;
            }
            
            int commentIndex = 0;
            List<Integer> removeIndex = new ArrayList<>();
            boolean assigned = false;
            
            for (PythonCountComment countComment : countComments) {
                // 如果注释在当前节点之前，分配给当前节点
                if (commentLine <= countComment.getStartLine()) {
                    addComment(countComment, pythonComment);
                    assigned = true;
                    break;
                }
                
                // 如果注释在方法内部，分配给方法
                if (countComment.getType().equals("method") || 
                    countComment.getType().equals("classMethod")) {
                    if (commentLine > countComment.getStartLine() && 
                        commentLine < countComment.getEndLine()) {
                        addComment(countComment, pythonComment);
                        assigned = true;
                        break;
                    }
                }
                
                // 如果注释在类内部但不在方法内，分配给类
                if (countComment.getType().equals("class")) {
                    if (commentLine > countComment.getStartLine() && 
                        commentLine < countComment.getEndLine()) {
                        // 检查是否在类的方法内部
                        boolean inMethod = false;
                        for (PythonCountComment methodComment : countComments) {
                            if (methodComment.getType().equals("classMethod") &&
                                commentLine > methodComment.getStartLine() &&
                                commentLine < methodComment.getEndLine()) {
                                inMethod = true;
                                break;
                            }
                        }
                        if (!inMethod) {
                            addComment(countComment, pythonComment);
                            assigned = true;
                            break;
                        }
                    }
                }
                
                // 标记已处理完的节点
                if (commentLine > countComment.getEndLine()) {
                    removeIndex.add(commentIndex);
                }
                commentIndex++;
            }
            
            // 如果没有分配给任何节点，分配给模块
            if (!assigned) {
                moduleNode.getComments().add(pythonComment);
            }
            
            // 移除已处理完的节点
            Collections.sort(removeIndex, Collections.reverseOrder());
            for (Integer remove : removeIndex) {
                countComments.remove(remove.intValue());
            }
        }
    }

    /**
     * 将注释添加到对应的节点
     */
    private static void addComment(PythonCountComment countComment, PythonComment pythonComment) {
        switch (countComment.getType()) {
            case "module":
                if (countComment.getModuleNode() != null) {
                    countComment.getModuleNode().getComments().add(pythonComment);
                }
                break;
            case "class":
                if (countComment.getClassNode() != null) {
                    countComment.getClassNode().getComments().add(pythonComment);
                }
                break;
            case "method":
                if (countComment.getFunctionNode() != null) {
                    countComment.getFunctionNode().getComments().add(pythonComment);
                }
                break;
            case "classMethod":
                if (countComment.getMethodNode() != null) {
                    countComment.getMethodNode().getComments().add(pythonComment);
                }
                break;
            case "classField":
                if (countComment.getParameterNode() != null) {
                    // 参数节点暂时没有注释字段，可以考虑添加
                }
                break;
            case "moduleField":
                if (countComment.getParameterNode() != null) {
                    // 参数节点暂时没有注释字段，可以考虑添加
                }
                break;
        }
    }

    /**
     * 初始化模块节点及其子节点的范围信息
     */
    private static void initModuleNode(PythonModuleNode moduleNode, List<PythonCountComment> countComments) {
        // 添加模块级别的注释范围（整个文件）
        PythonCountComment moduleComment = new PythonCountComment();
        moduleComment.setStartLine(1);
        moduleComment.setEndLine(Integer.MAX_VALUE); // 模块覆盖整个文件
        moduleComment.setType("module");
        moduleComment.setModuleNode(moduleNode);
        countComments.add(moduleComment);
        
        // 处理模块级函数
        for (PythonFunctionNode functionNode : moduleNode.getFunctionNodes()) {
            if (functionNode.getStart() != null && functionNode.getEnd() != null) {
                PythonCountComment functionComment = new PythonCountComment();
                functionComment.setStartLine(functionNode.getStart().getLine());
                functionComment.setEndLine(functionNode.getEnd().getLine());
                functionComment.setType("method");
                functionComment.setFunctionNode(functionNode);
                countComments.add(functionComment);
            }
        }
        
        // 处理模块级参数/变量
        for (PythonParameterNode paramNode : moduleNode.getModuleParamNodes()) {
            if (paramNode.getLocation() != null) {
                PythonCountComment paramComment = new PythonCountComment();
                paramComment.setStartLine(paramNode.getLocation().getLine());
                paramComment.setEndLine(paramNode.getLocation().getLine());
                paramComment.setType("moduleField");
                paramComment.setParameterNode(paramNode);
                countComments.add(paramComment);
            }
        }
        
        // 处理类节点
        for (PythonClassNode classNode : moduleNode.getClassNodes()) {
            initClassNode(classNode, countComments);
        }
    }

    /**
     * 初始化类节点及其子节点的范围信息
     */
    private static void initClassNode(PythonClassNode classNode, List<PythonCountComment> countComments) {
        if (classNode.getStart() != null && classNode.getEnd() != null) {
            PythonCountComment classComment = new PythonCountComment();
            classComment.setStartLine(classNode.getStart().getLine());
            classComment.setEndLine(classNode.getEnd().getLine());
            classComment.setType("class");
            classComment.setClassNode(classNode);
            countComments.add(classComment);
            
            // 处理类方法
            for (PythonMethodNode methodNode : classNode.getMethods()) {
                if (methodNode.getStart() != null && methodNode.getEnd() != null) {
                    PythonCountComment methodComment = new PythonCountComment();
                    methodComment.setStartLine(methodNode.getStart().getLine());
                    methodComment.setEndLine(methodNode.getEnd().getLine());
                    methodComment.setType("classMethod");
                    methodComment.setMethodNode(methodNode);
                    countComments.add(methodComment);
                }
            }
            
            // 处理类属性/参数
            for (PythonParameterNode paramNode : classNode.getParameters()) {
                if (paramNode.getLocation() != null) {
                    PythonCountComment paramComment = new PythonCountComment();
                    paramComment.setStartLine(paramNode.getLocation().getLine());
                    paramComment.setEndLine(paramNode.getLocation().getLine());
                    paramComment.setType("classField");
                    paramComment.setParameterNode(paramNode);
                    countComments.add(paramComment);
                }
            }
            
            // 处理内部类
            if (CollectionUtils.isNotEmpty(classNode.getInnerClasses())) {
                for (PythonClassNode innerClass : classNode.getInnerClasses()) {
                    initClassNode(innerClass, countComments);
                }
            }
        }
    }

    /**
     * 从token流中提取隐藏通道的注释
     */
    public static List<PythonComment> getHiddenTokens(CommonTokenStream tokens) {
        List<PythonComment> hiddenTokens = new ArrayList<>();
        for (Token token : tokens.getTokens()) {
            if (token.getChannel() == Token.HIDDEN_CHANNEL) {
                if (StringUtils.isNotEmpty(token.getText().trim())) {
                    PythonComment pythonComment = new PythonComment();
                    pythonComment.setStart(new com.sankuai.deepcode.ast.model.base.LocationInfo()
                            .setLine(token.getLine())
                            .setColumn(token.getCharPositionInLine()));
                    pythonComment.setComment(token.getText().trim());
                    hiddenTokens.add(pythonComment);
                }
            }
        }
        return hiddenTokens;
    }
}
