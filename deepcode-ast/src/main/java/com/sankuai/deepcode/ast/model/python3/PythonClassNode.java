package com.sankuai.deepcode.ast.model.python3;

import com.sankuai.deepcode.ast.enums.ChangeTypeEnum;
import com.sankuai.deepcode.ast.model.base.BaseNode;
import com.sankuai.deepcode.ast.model.base.LocationInfo;
import com.sankuai.deepcode.ast.util.Md5Util;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * Package: com.sankuai.deepcode.ast.model.python3
 * Description:
 *
 * <AUTHOR>
 * @since 2024/12/4 15:16
 */
@Setter
@Getter
@ToString
@Accessors(chain = true)
public class PythonClassNode extends BaseNode {
    /**
     * 文件名称
     */
    private String fileName;
    /**
     * 文件vid
     */
    private String fileVid;
    /**
     * 类的名称
     */
    private String name;
    /**
     * 所属模块名称
     */
    private String moduleName;
    /**
     * 所属模块路径【会包含当前类名称】
     */
    private String modulePath;
    /**
     * 类的文档字符串
     */
    private String docString;

    /**
     * 类定义的起始行号
     */
    private LocationInfo start;

    /**
     * 类定义的结束行号
     */
    private LocationInfo end;

    /**
     * 类体内容
     */
    String classBody;

    /**
     * 类的中定义的变量列表【非类实例属性】
     */
    private List<PythonParameterNode> parameters = new ArrayList<>();

    /**
     * 类的装饰器列表
     */
    private List<PythonDecoratorClass> decorators = new ArrayList<>();

    /**
     * 类的父类列表
     */
    private List<PythonSuperClass> superClasses = new ArrayList<>();

    /**
     * 类的方法列表
     */
    private List<PythonMethodNode> methods = new ArrayList<>();
    /**
     * 类的内部类列表
     */
    private List<PythonClassNode> innerClasses = new ArrayList<>();
    /**
     * 类的内部导入列表
     */
    private List<PythonImportNode> innerImports = new ArrayList<>();

    /**
     * 类的块级注释（类定义后的第一个三引号字符串）
     */
    private PythonBlockComment blockComment;

    /**
     * 类中的其他块级注释
     */
    private List<PythonBlockComment> otherBlockComments = new ArrayList<>();

    private PythonBlockComment moduleComment;

    List<PythonComment> comments = new ArrayList<>();


    /**
     * FIXME: 暂时参照 Java 变更内容进行定义，后期需要依据实际情况进行调整
     */
    private ChangeTypeEnum changeType = ChangeTypeEnum.DEFAULT;
    private List<Integer> changeLines;


    @Setter
    @Getter
    @ToString
    public static class PythonSuperClass {
        private Integer index;
        private String name;
        private String sourceModulePath;
    }

    @Override
    public String getVid() {
        if (StringUtils.isNotEmpty(getModulePath())) {
            return Md5Util.stringToMd5(getModulePath());
        }
        return StringUtils.EMPTY;
    }

    public String getFileVid() {
        if (StringUtils.isNotEmpty(getFileName())) {
            return Md5Util.stringToMd5(getFileName());
        }
        return StringUtils.EMPTY;
    }
}


