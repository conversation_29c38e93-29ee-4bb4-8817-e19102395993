package com.sankuai.deepcode.ast.model.java;

import com.sankuai.deepcode.ast.model.base.BaseEdge;
import com.sankuai.deepcode.ast.model.base.BaseNode;
import com.sankuai.deepcode.ast.model.base.CodeView;
import com.sankuai.deepcode.ast.model.java.InvokeParam;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
public class MethodInvokeMethodEdge extends BaseEdge {
    private List<InvokeParam> invokeParams = new ArrayList<>();

}
