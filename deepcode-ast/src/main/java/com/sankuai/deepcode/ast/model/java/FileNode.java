package com.sankuai.deepcode.ast.model.java;

import com.sankuai.deepcode.ast.model.base.BaseNode;
import com.sankuai.deepcode.ast.model.base.CodeView;
import com.sankuai.deepcode.ast.model.base.GitDiffInfo;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class FileNode extends BaseNode {
    private String moduleName = "";
    private String fileName;
    private String path;
    private String fileType;
    private int startLine = -1;
    private int endLine = -1;
    private int changeType = 0;
    private List<Integer> changeLines = new ArrayList<>();
    private int checkType = 0;
    private List<Integer> checkLines = new ArrayList<>();
    private List<CodeView> codeViews = new ArrayList<>();
    private List<Integer> commentLines = new ArrayList<>();
    private int commitCount = 0;

    public static FileNode of(GitDiffInfo gitDiffInfo) {
        FileNode fileNode = new FileNode();

        fileNode.setModuleName(gitDiffInfo.getModuleName());
        fileNode.setFileName(gitDiffInfo.getFileName());
        fileNode.setPath(gitDiffInfo.getPath());
        fileNode.setFileType(gitDiffInfo.getFileType());
        fileNode.setStartLine(1);
        fileNode.setEndLine(gitDiffInfo.getCodeViews() != null ? gitDiffInfo.getCodeViews().size() - 1 : -1);
        fileNode.setChangeType(gitDiffInfo.getChangeType());
        fileNode.setChangeLines(gitDiffInfo.getChangeLines());

        fileNode.setCodeViews(gitDiffInfo.getCodeViews());

        fileNode.setCommitCount(gitDiffInfo.getCommitCount());

        return fileNode;
    }
}
