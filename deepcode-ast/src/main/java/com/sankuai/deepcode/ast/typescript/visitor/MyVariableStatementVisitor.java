package com.sankuai.deepcode.ast.typescript.visitor;

import com.sankuai.deepcode.ast.model.typescript.ScriptNode;
import com.sankuai.deepcode.ast.model.typescript.ScriptLiteral;
import com.sankuai.deepcode.ast.model.typescript.ScriptVariable;
import com.sankuai.deepcode.ast.typescript.gen.TypeScriptParser;
import com.sankuai.deepcode.ast.typescript.gen.TypeScriptParserBaseVisitor;
import lombok.Getter;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;

public class MyVariableStatementVisitor extends TypeScriptParserBaseVisitor<Object> {

    @Getter
    private ScriptVariable scriptVariable;

    private ScriptNode scriptNode;

    MyVariableStatementVisitor(ScriptNode myScriptNode) {
        scriptVariable = new ScriptVariable();
        scriptNode = myScriptNode;
        if (scriptNode.isExport()) {
            scriptVariable.setExport(true);
        }
        scriptNode.getVariables().add(scriptVariable);
    }

    @Override
    public Object visitVariableStatement(TypeScriptParser.VariableStatementContext ctx) {
        scriptVariable.setStartLine(ctx.getStart().getLine());
        scriptVariable.setEndLine(ctx.getStop().getLine());
        scriptVariable.setBody(ctx.getText());
        if (ctx.bindingPattern() != null) {
            if (ctx.bindingPattern().arrayLiteral() != null) {
                scriptVariable.setType("array");
                visitElementList(ctx.bindingPattern().arrayLiteral().elementList());

            } else if (ctx.bindingPattern().objectLiteral() != null) {
                scriptVariable.setType("object");
            }
            if (ctx.typeAnnotation() != null) {
                visitTypeAnnotation(ctx.typeAnnotation());
            }
            visitInitializer(ctx.initializer());

        }
        if (ctx.Declare() != null) {
            scriptVariable.setDecare(true);
            if (ctx.varModifier() != null) {
                scriptVariable.setModifier(ctx.varModifier().getText());
            }

            List<ScriptLiteral> literal = new ArrayList<>();
            for (TypeScriptParser.VariableDeclarationContext variableDeclarationContext : ctx.variableDeclarationList().variableDeclaration()) {
                ScriptLiteral scriptLiteral = new ScriptLiteral();
                String name = "";
                if (variableDeclarationContext.identifierOrKeyWord() != null) {
                    name = variableDeclarationContext.identifierOrKeyWord().getText();
                }
                if (variableDeclarationContext.arrayLiteral() != null) {
                    if (StringUtils.isEmpty(name)) {
                        name = variableDeclarationContext.arrayLiteral().getText();
                    } else {
                        name += " " + variableDeclarationContext.arrayLiteral();
                    }
                }
                if (variableDeclarationContext.objectLiteral() != null) {
                    if (StringUtils.isEmpty(name)) {
                        name = variableDeclarationContext.objectLiteral().getText();
                    } else {
                        name += " " + variableDeclarationContext.objectLiteral().getText();
                    }
                }
                if (variableDeclarationContext.typeAnnotation() != null) {
                    if (StringUtils.isEmpty(name)) {
                        name = variableDeclarationContext.typeAnnotation().getText();
                    } else {
                        name += " " + variableDeclarationContext.typeAnnotation().getText();
                    }
                }


                String type = "";
                if (variableDeclarationContext.typeParameters() != null) {
                    type = variableDeclarationContext.typeParameters().getText();
                }

                if (variableDeclarationContext.singleExpression() != null) {
                    //todo 变量singleExpression暂时仅处理方法调用
                    for (TypeScriptParser.SingleExpressionContext singleExpressionContext : variableDeclarationContext.singleExpression()) {
                        com.sankuai.deepcode.ast.typescript.visitor.MySingleExpressionVisitor mySingleExpressionVisitor = new com.sankuai.deepcode.ast.typescript.visitor.MySingleExpressionVisitor(null);
                        mySingleExpressionVisitor.visit(singleExpressionContext);
                        if (mySingleExpressionVisitor.getScriptNode() != null) {
                            if (CollectionUtils.isNotEmpty(mySingleExpressionVisitor.getScriptNode().getMethods())) {
                                scriptVariable.getMethods().addAll(mySingleExpressionVisitor.getScriptNode().getMethods());
                            }
                            if (CollectionUtils.isNotEmpty(mySingleExpressionVisitor.getScriptNode().getClasses())) {
                                scriptVariable.getClasses().addAll(mySingleExpressionVisitor.getScriptNode().getClasses());
                            }
                            if (CollectionUtils.isNotEmpty(mySingleExpressionVisitor.getScriptNode().getHtmlNodes())) {
                                scriptVariable.getHtmlNodes().addAll(mySingleExpressionVisitor.getScriptNode().getHtmlNodes());
                            }
                        }
                    }
                }
                scriptLiteral.setName(name);
                scriptLiteral.setType(type);
                literal.add(scriptLiteral);
            }
            scriptVariable.setLiteral(literal);
        }
        if (ctx.variableDeclarationList() != null) {
            String modifier = "";
            if (ctx.accessibilityModifier() != null) {
                modifier = ctx.accessibilityModifier().getText();
            }
            if (ctx.varModifier() != null) {
                if (StringUtils.isEmpty(modifier)) {
                    modifier = ctx.varModifier().getText();
                } else {
                    modifier += " " + ctx.varModifier().getText();
                }
            }
            if (ctx.ReadOnly() != null) {
                if (StringUtils.isEmpty(modifier)) {
                    modifier = ctx.ReadOnly().getText();
                } else {
                    modifier += " " + ctx.ReadOnly().getText();
                }
            }
            scriptVariable.setModifier(modifier);

            List<ScriptLiteral> literal = new ArrayList<>();
            for (TypeScriptParser.VariableDeclarationContext variableDeclarationContext : ctx.variableDeclarationList().variableDeclaration()) {
                ScriptLiteral scriptLiteral = new ScriptLiteral();
                String name = "";
                if (variableDeclarationContext.identifierOrKeyWord() != null) {
                    name = variableDeclarationContext.identifierOrKeyWord().getText();
                }
                if (variableDeclarationContext.arrayLiteral() != null) {
                    if (StringUtils.isEmpty(name)) {
                        name = variableDeclarationContext.arrayLiteral().getText();
                    } else {
                        name += " " + variableDeclarationContext.arrayLiteral();
                    }
                }
                if (variableDeclarationContext.objectLiteral() != null) {
                    if (StringUtils.isEmpty(name)) {
                        name = variableDeclarationContext.objectLiteral().getText();
                    } else {
                        name += " " + variableDeclarationContext.objectLiteral().getText();
                    }
                }
                if (variableDeclarationContext.typeAnnotation() != null) {
                    if (StringUtils.isEmpty(name)) {
                        name = variableDeclarationContext.typeAnnotation().getText();
                    } else {
                        name += " " + variableDeclarationContext.typeAnnotation().getText();
                    }
                }


                String type = "";
                if (variableDeclarationContext.typeParameters() != null) {
                    type = variableDeclarationContext.typeParameters().getText();
                }

                if (variableDeclarationContext.singleExpression() != null) {
                    //todo 变量singleExpression暂进处理方法调用
                    for (TypeScriptParser.SingleExpressionContext singleExpressionContext : variableDeclarationContext.singleExpression()) {
                        com.sankuai.deepcode.ast.typescript.visitor.MySingleExpressionVisitor mySingleExpressionVisitor = new MySingleExpressionVisitor(null);
                        mySingleExpressionVisitor.visit(singleExpressionContext);
                        if (mySingleExpressionVisitor.getScriptNode() != null) {
                            if (CollectionUtils.isNotEmpty(mySingleExpressionVisitor.getScriptNode().getMethods())) {
                                scriptVariable.getMethods().addAll(mySingleExpressionVisitor.getScriptNode().getMethods());
                            }
                            if (CollectionUtils.isNotEmpty(mySingleExpressionVisitor.getScriptNode().getClasses())) {
                                scriptVariable.getClasses().addAll(mySingleExpressionVisitor.getScriptNode().getClasses());
                            }
                            if (CollectionUtils.isNotEmpty(mySingleExpressionVisitor.getScriptNode().getHtmlNodes())) {
                                scriptVariable.getHtmlNodes().addAll(mySingleExpressionVisitor.getScriptNode().getHtmlNodes());
                            }
                        }
                    }
                }
                scriptLiteral.setName(name);
                scriptLiteral.setType(type);
                literal.add(scriptLiteral);
            }
            scriptVariable.setLiteral(literal);

        }
        return null;
    }


    @Override
    public Object visitElementList(TypeScriptParser.ElementListContext ctx) {
        for (TypeScriptParser.ArrayElementContext arrayElementContext : ctx.arrayElement()) {
            visitArrayElement(arrayElementContext);
        }
        return null;
    }

    @Override
    public Object visitArrayElement(TypeScriptParser.ArrayElementContext ctx) {
        ScriptLiteral scriptLiteral = new ScriptLiteral();
        if (ctx.Ellipsis() != null) {
            scriptLiteral.setEllipsis(true);
        }

        if (ctx.identifier() != null) {
            scriptLiteral.setName(ctx.identifier().getText());
        } else if (ctx.singleExpression() != null) {
            //todo singleExpression 先简单处理
            scriptLiteral.setName(ctx.singleExpression().getText());
        }
        scriptVariable.getLiteral().add(scriptLiteral);
        return null;
    }


    @Override
    public Object visitTypeAnnotation(TypeScriptParser.TypeAnnotationContext ctx) {
        visitType_(ctx.type_());
        return null;
    }

    @Override
    public Object visitType_(TypeScriptParser.Type_Context ctx) {
        //todo visitType_ 先不处理

        return null;
    }


    @Override
    public Object visitInitializer(TypeScriptParser.InitializerContext ctx) {
        //todo initializer 先不处理
//        MySingleExpressionVisitor mySingleExpressionVisitor = new MySingleExpressionVisitor();
//        mySingleExpressionVisitor.visit(ctx.singleExpression());
        return null;
    }


}
