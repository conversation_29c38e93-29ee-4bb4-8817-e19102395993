"""
我是一个模块级别的块级注释
"""

'''这个已经不是注释了，是模块级别的字符串表达式，目前没有意义
'''

x = '''我已经不是一个注释了
我是模块下的一个变量定义的常量字符串的值
'''

class RerankResponse(BaseModel):
    '''
    我是类的 docstring
    应该要被解析出来 的
    '''

    """
    我是类中的常量字符串，但没有变量承接，所以算是被注释掉了，不会生效
    """
    # 我是一个类的行级注释
    results: List[RankResult] # 类中的变量注释


@rerank_router.post("/normal")
async def batch_rerank(req: RerankRequest) -> Resp[List[RankResult]]:
    pass


class CustomJSONEncoder(JSONEncoder):
    @singledispatchmethod
    def default(self, o):
        if dataclasses.is_dataclass(o):
            return dataclasses.asdict(o)
        try:
            return super().default(o)
        except TypeError as e:
            logging.error(f"响应数据时，{o} 序列化 Json 异常：{e}")
            return {"msg": f"{type(o)} 序列化异常"}

    @default.register(set)
    def _(self, o):
        return list(o)

    @default.register(decimal.Decimal)
    def _(self, o: decimal.Decimal):
        return float(o)

    @default.register(enum.Enum)
    def _(self, o: enum.Enum):
        return o.name

    @default.register(datetime.datetime)
    def _(self, o: datetime.datetime):
        representation = o.isoformat()
        if representation.endswith('+00:00'):
            representation = representation[:-6] + 'Z'
        return representation

    @default.register(datetime.date)
    def _(self, o: datetime.date):
        return o.isoformat()

    @default.register(datetime.timedelta)
    def _(self, o: datetime.timedelta):
        return o.total_seconds()

    @default.register(uuid.UUID)
    def _(self, o: uuid.UUID):
        return str(o)

    @default.register(BaseModel)
    def _(self, o: BaseModel):
        return o.json()
