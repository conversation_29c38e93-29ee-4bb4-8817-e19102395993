# 测试相对导入
# 假设当前模块路径为: package.subpackage.relative_import_test

# 绝对导入
import os
from collections import defaultdict

# 相对导入
from . import sibling_module          # 同级模块: package.subpackage.sibling_module
from .sibling_module import some_func # 同级模块的函数: package.subpackage.sibling_module.some_func
from .. import parent_module          # 父级模块: package.parent_module
from ..parent_module import some_class # 父级模块的类: package.parent_module.some_class
from ...grandparent import utils      # 祖父级模块: grandparent.utils
