import re
from collections import defaultdict
from typing import List, Dict, Optional, Iterable

from pydantic import BaseModel


class StringMatcher(BaseModel):
    raw_pattern: str
    case_sensitive: bool
    exact_match: bool = False
    variable_names: List[str] = []
    pattern: Optional[re.Pattern]

    _GLOB_PATTERN = re.compile(r"\?|\*|\{((?:\{[^/]+?\}|[^/{}]|\\[{}])+?)\}")
    _DEFAULT_VARIABLE_PATTERN = "((?s).*)"

    def __init__(self, pattern: str, case_sensitive: bool, **kwargs):
        super().__init__(raw_pattern=pattern, case_sensitive=case_sensitive, **kwargs)
        pattern_builder = []
        end = 0
        for match in re.finditer(self._GLOB_PATTERN, pattern):
            pattern_builder.append(re.escape(pattern[end:match.start()]))
            if match.group() == "?":
                pattern_builder.append(".")
            elif match.group() == "*":
                pattern_builder.append(".*")
            elif match.group().startswith("{") and match.group().endswith("}"):
                colon_idx = match.group().find(":")
                if colon_idx == -1:
                    pattern_builder.append(self._DEFAULT_VARIABLE_PATTERN)
                    self.variable_names.append(match.group(1))
                else:
                    variable_pattern = match.group()[colon_idx + 1: -1]
                    pattern_builder.append(F"({variable_pattern})")
                    variable_name = match.group()[1:colon_idx]
                    self.variable_names.append(variable_name)
            end = match.end()
        if end == 0:
            self.exact_match = True
            self.pattern = None
        else:
            self.exact_match = False
            pattern_builder.append(re.escape(pattern[end:]))
            self.pattern = re.compile(
                "".join(pattern_builder),
                re.DOTALL | (re.NOFLAG if self.case_sensitive else re.IGNORECASE)
            )

    def match_strings(
            self,
            raw_str: str,
            uri_template_variables: Optional[Dict[str, str]]
    ) -> bool:
        if self.exact_match:
            return self.raw_pattern == raw_str if self.case_sensitive else self.raw_pattern.upper() == raw_str.upper()
        elif self.pattern:
            m = self.pattern.match(raw_str)
            if m:
                if uri_template_variables:
                    if len(self.variable_names) != len(m.groups()):
                        raise ValueError(
                            F"The number of capturing groups in the pattern segment {self.pattern.pattern}"
                            " does not match the number of URI template variables it defines, "
                            "which can occur if capturing groups are used in a URI template regex. "
                            "Use non-capturing groups instead."
                        )
                    for i, name in enumerate(self.variable_names, 1):
                        if name.startswith("*"):
                            raise ValueError(
                                F"Capturing patterns ({name}) are not "
                                "supported by the AntPathMatcher. Use the PathPatternParser instead."
                            )
                        uri_template_variables[name] = m.group(i)
                return True
        return False


class AntPathMatcher(BaseModel):
    path_separator: str = "/"
    case_sensitive: bool = False
    trim_tokens: bool = False

    cache_patterns: bool = True
    tokenized_pattern_cache: Dict[str, List[str]] = defaultdict(list)
    string_matcher_cache: Dict[str, StringMatcher] = dict()

    _WILDCARD_CHARS = {'*', '?', '{'}

    def do_match(self, pattern, path=None, full_match=False, uri_template_variables=None):
        if not path or path.startswith(self.path_separator) != pattern.startswith(self.path_separator):
            return False

        patt_dirs = self.tokenize_pattern(pattern)
        if full_match and self.case_sensitive and not self.is_potential_match(path, patt_dirs):
            return False

        path_dirs = self.tokenize_path(path)
        patt_idx_start = 0
        patt_idx_end = len(patt_dirs) - 1
        path_idx_start = 0
        path_idx_end = len(path_dirs) - 1

        while patt_idx_start <= patt_idx_end and path_idx_start <= path_idx_end:
            patt_dir = patt_dirs[patt_idx_start]
            if patt_dir == "**":
                break
            if not self.match_strings(patt_dir, path_dirs[path_idx_start], uri_template_variables):
                return False
            patt_idx_start += 1
            path_idx_start += 1

        if path_idx_start > path_idx_end:
            if patt_idx_start > patt_idx_end:
                return pattern.endswith(self.path_separator) == path.endswith(self.path_separator)
            elif not full_match:
                return True
            elif patt_idx_start == patt_idx_end and patt_dirs[patt_idx_start] == "*" and path.endswith(
                    self.path_separator):
                return True
            else:
                for pat_idx_tmp in range(patt_idx_start, patt_idx_end + 1):
                    if patt_dirs[pat_idx_tmp] != "**":
                        return False
                return True
        elif patt_idx_start > patt_idx_end:
            return False
        elif not full_match and patt_dirs[patt_idx_start] == "**":
            return True

        while patt_idx_start <= patt_idx_end and path_idx_start <= path_idx_end:
            patt_dir = patt_dirs[patt_idx_end]
            if patt_dir == "**":
                break
            if not self.match_strings(patt_dir, path_dirs[path_idx_end], uri_template_variables):
                return False
            patt_idx_end -= 1
            path_idx_end -= 1

        if path_idx_start > path_idx_end:
            for pat_idx_tmp in range(patt_idx_start, patt_idx_end + 1):
                if patt_dirs[pat_idx_tmp] != "**":
                    return False
            return True

        while patt_idx_start != patt_idx_end and path_idx_start <= path_idx_end:
            pat_idx_tmp = -1
            for pat_length in range(patt_idx_start + 1, patt_idx_end + 1):
                if patt_dirs[pat_length] == "**":
                    pat_idx_tmp = pat_length
                    break
            if pat_idx_tmp == patt_idx_start + 1:
                patt_idx_start += 1
            else:
                pat_length = pat_idx_tmp - patt_idx_start - 1
                str_length = path_idx_end - path_idx_start + 1
                found_idx = -1
                for i in range(0, str_length - pat_length + 1):
                    for j in range(pat_length):
                        sub_pat = patt_dirs[patt_idx_start + j + 1]
                        sub_str = path_dirs[path_idx_start + i + j]
                        if not self.match_strings(sub_pat, sub_str, uri_template_variables):
                            break
                    else:
                        found_idx = path_idx_start + i
                        break
                if found_idx == -1:
                    return False
                patt_idx_start = pat_idx_tmp
                path_idx_start = found_idx + pat_length

        for pat_idx_tmp in range(patt_idx_start, patt_idx_end + 1):
            if patt_dirs[pat_idx_tmp] != "**":
                return False
        return True

    def tokenize_pattern(self, pattern: str) -> List[str]:
        if self.cache_patterns and \
                pattern in self.tokenized_pattern_cache and (r := self.tokenized_pattern_cache.get(pattern)):
            return r

        result = self.tokenize_path(pattern)
        self.tokenized_pattern_cache[pattern] = result
        return result

    def match(self, pattern: str, path: str) -> bool:
        return self.do_match(pattern, path, True)

    def match_start(self, pattern: str, path: str) -> bool:
        return self.do_match(pattern, path, False)

    def is_potential_match(self, path, patt_dirs):
        if not self.trim_tokens:
            pos = 0
            for patt_dir in patt_dirs:
                skipped = self._skip_separator(path, pos, self.path_separator)
                pos += skipped
                skipped = self._skip_segment(path, pos, patt_dir)
                if skipped < len(patt_dir):
                    return skipped > 0 or (len(patt_dir) > 0 and patt_dir[0] in AntPathMatcher._WILDCARD_CHARS)
                pos += skipped
        return True

    def tokenize_path(self, path):
        return [
            self.trim_tokens and _.strip() or _
            for _ in path.split(self.path_separator) if _
        ]

    def match_strings(self, pattern, raw_str, uri_template_variables):
        return self.get_string_matcher(pattern).match_strings(raw_str, uri_template_variables)

    @staticmethod
    def _skip_separator(path, pos, path_separator):
        skipped = 0
        while path.startswith(path_separator, pos + skipped):
            skipped += len(path_separator)
        return skipped

    @staticmethod
    def _skip_segment(path, pos, patt_dir):
        skipped = 0
        for i in range(len(patt_dir)):
            c = patt_dir[i]
            if c in AntPathMatcher._WILDCARD_CHARS:
                return skipped
            curr_pos = pos + skipped
            if curr_pos >= len(path):
                return 0
            if c == path[curr_pos]:
                skipped += 1
        return skipped

    def get_string_matcher(self, pattern) -> StringMatcher:
        if self.cache_patterns \
                and pattern in self.string_matcher_cache and (r := self.string_matcher_cache.get(pattern)):
            return r
        str_matcher = StringMatcher(
            pattern=pattern,
            case_sensitive=self.case_sensitive
        )
        self.string_matcher_cache[pattern] = str_matcher
        return str_matcher


URL_MATCHER = AntPathMatcher()


def batch_pattern_match(patterns: Iterable[str], url):
    if not (patterns or url):
        return False
    return any((URL_MATCHER.match(p, url) for p in patterns))