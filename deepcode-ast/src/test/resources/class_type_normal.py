"""
:module_name: base.py
:author: "zhouyong15<<EMAIL>>"
:copyright: @2024 by zhouyong15
:date 2024/7/10 15:40
"""
__author__ = 'zhouyong15'

import dataclasses
import datetime
import decimal
import enum
import logging
import typing
import uuid
from functools import singledispatchmethod
from json import JSONEncoder

from pydantic import BaseModel


class PlatformError(Exception):
    """
    Parent exception class for all expected backend exceptions
    """

    detail: str
    code: int
    should_log: bool = True

    def __init__(
            self,
            base_exception: Exception,
            detail: str = "",
            code: int = 409,
            should_log: bool = True,
    ):
        super().__init__(base_exception)
        self.detail = detail
        self.code = code
        self.should_log = should_log


class RespCode(enum.Enum):
    UNKNOWN = ("1000", "服务端未知信息")
    # 遵照 HTTP 返回码的相关规则， 进行返回码范围组织
    # 2 代表成功
    # 000~999 是成功之间的范围
    # 成功的提醒
    SUCCESS = ('2000', '成功')

    # 5 代表异常
    # 000~999 划分各个异常码
    # 异常的处理，比如参数验证失败、内部错误等
    SYSTEM_ERROR = ('-1', '系统未知错误，请联系管理员')

    # 4 代表失败
    # 000~999 划分各个失败的范围
    # 不允许的操作、鉴权、登录等操作
    FAILED = ('4000', '失败')
    DUPLICATED = ('4001', '数据已存在，不允许重复')
    PERMISSION_DENIED = ("4003", "您没有此操作的权限, 请联系 zhouyong15 开通权限")
    PARAMS_ERROR = ("4004", "传入参数内容错误")
    DATA_NOT_EXISTS = ("4004", "数据不存在")

    def __init__(self, code, msg):
        self.code = code
        self.msg = msg

    def to_dict(self):
        return {'code': self.code, 'msg': self.msg}

    def __str__(self):
        return str(self.to_dict())

    def __repr__(self):
        return self.__str__()


class ServiceException(Exception):
    def __init__(
            self,
            resp_code: typing.Optional[RespCode] = RespCode.SYSTEM_ERROR,
            code: int = -1,
            msg: str = '系统异常',
            *args: object
    ) -> None:
        """
        自定义的服务端异常
        :param resp_code: 异常发生的原因，包含错误码、错误提示信息等
        :param args:  异常父类需要的一些参数
        """
        super().__init__(*args)
        self.resp_code = resp_code
        self.code = resp_code and resp_code.code or code
        self.msg = resp_code and resp_code.msg or msg

    def update_msg(self, msg):
        self.msg = msg
        return self

    def __str__(self):
        return F"\\{'code': {self.code}, 'msg':{self.msg}}"

    def __repr__(self):
        return self.__str__()


T = typing.TypeVar("T")


class Resp(BaseModel, typing.Generic[T]):
    data: typing.Union[T, typing.Any] | None = None
    msg: str | None = "成功"
    code: str | None = '200'
    success: bool = True

    def __init__(
            self,
            data: typing.Union[T, typing.Any] | None,
            *,
            msg: str | None = None,
            code: str | None = None,
            success=True,
            exception: Exception | None = None,
            resp_code: RespCode = RespCode.SUCCESS,
            **kwargs) -> None:
        """
        自定义响应内容
        """
        super().__init__(**kwargs)
        self.data = data

        if exception:
            if isinstance(exception, ServiceException):
                self.msg = exception.msg
            else:
                self.msg = repr(exception)
            self.success = False
            self.code = RespCode.SYSTEM_ERROR.code
        self.success = success

        self.msg = resp_code.msg or msg
        self.code = resp_code.code or code

    def add_data(self, **kwargs) -> "Resp[T]":
        if not self.data:
            self.data = dict()
        for k, v in kwargs.items():
            if not k:
                continue
            if isinstance(self.data, dict):
                self.data[k] = v
            elif hasattr(self.data, k):
                setattr(self.data, k, v)

        return self

    def set_msg(self, msg) -> "Resp[T]":
        self.msg = msg
        return self

    @staticmethod
    def ok(data: typing.Optional[T], **kwargs) -> "Resp[T]":
        return Resp(data, resp_code=RespCode.SUCCESS, **kwargs)

    @staticmethod
    def fail(resp_code: RespCode = RespCode.FAILED, **kwargs) -> "Resp[T]":
        return Resp(resp_code=resp_code, success=False, **kwargs)

    @staticmethod
    def exception(
            exc: ServiceException = None,
            resp_code: RespCode = RespCode.SYSTEM_ERROR,
            **kwargs
    ) -> "Resp[T]":
        _code = resp_code or RespCode.SYSTEM_ERROR
        if exc is not None:
            _code = exc.resp_code
        return Resp(None, resp_code=_code, success=False, **kwargs)
