import os
import sys.path
from fastapi import (FastAPI, Response, Depends as _Depends)
from fastapi.security import (
    OAuth2PasswordRequestForm, OAuth2PasswordBearer,
    HTTPBasic as _HTTPBasic
)
from fastapi.security import OAuth2PasswordBearer
from fastapi.response import Response as _Response
import os.path as _path
from .demo import abc
from ..double import handle
from ...triple import handle as _handle
from .. import *


"""
this is module description
module multi line
"""


def foo():
    """
    this is a foo function
    foo multi line
    :return:
    """
    '''
    this is a foo function
    '''
    print('before block comment')
    """
    this is a foo function, inline block comment
    foo multi line
    :return:
    """
    y = """
    333
    """
    x = f"""222 {print()} """
    a = 2
    from rich import print # end line comment
    print('foo')
    bar()

    if True:
        a = 3
    else:
        b = 3

    def foo1():
        '''
        this is inner foo1 function
        foo1 multi line
        :return:
        '''
        a = 2
        print('foo')
        bar(foo())
        return 'a', 'b'

    foo1(bar())
    # this is comment line
    # this is comment line2
    return 'a', 'b'


def bar(*args, **kwargs) -> None:
    inline_block_lines = """
    blockLines, not comment
    """
    print('bar')


async def async_func():
    pass


#  this is module line comment
foo()[1]

if True:
    foo(bar())


class A(ABC, FastAPI):
    """
    this is class A comment
    """

    def __init__(self):
        super(A, self).__init__()
        self.a = 1

    class B:
        """
        this is inner class B comment
        """

        def __init__(self):
            import os
            self.b = 2

    @property
    @wrapper.register(3, x=1)
    def b(self) -> int:
        foo()
        inner_b = B()
        if True:
            super().x()
        else:
            super().y(bar(foo()))
        self.method1()
        return self.a

    def wrapper(func) -> int | Tuple[int, int]:
        def inner():
            r = func()
            return r

        return inner

    def method1(self, a: int):
        ...

    @wrapper(3, x=1)
    def method1(self, a: str) -> Union[int, Tuple[int, int]]:
        other_b = B()
        if True and other_b:
            return 1
        return 2, 2


class B:
    '''
    this is outer class B comment
    '''

    def __init__(self, *args, **kwargs):
        self.b = 2

x = A()
x, y = A(), B()
x = y = z = A()

x = B()

x.method1(1)
app = FastAPI()
app.x.y().get()

z = 3
m = True

