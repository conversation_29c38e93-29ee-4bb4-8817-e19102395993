"""
:module_name: DJ<PERSON>eranker.py
:author: "zhouyong15<<EMAIL>>"
:copyright: @2024 by zhouyong15
:date 2024/7/17 17:58
"""
__author__ = 'zhouyong15'

import logging
from copy import deepcopy
from typing import Sequence, Optional, Dict, Union, Any, List

import requests
from langchain_core.callbacks.base import Callbacks
from langchain_core.documents import BaseDocumentCompressor, Document
from langchain_core.pydantic_v1 import root_validator
from langchain_core.utils import get_from_dict_or_env

from . import RerankModel


class DjDeliveryReranker(BaseDocumentCompressor):
    top_n: Optional[int] = 3
    """重排后，需要返回的最多文档数量"""

    relevance_score: Optional[float] = .7
    """归一化处理后，需要过滤的相关性得分情况, 不做归一化处理时，过滤得分时，小于 0 的将被过滤掉"""
    filter_uncorrelated: Optional[bool] = None
    normalized: Optional[bool] = True
    """相似性得分是否需要做归一化处理，归一化函数为 torch.sigmoid"""
    model: Optional[RerankModel] = None
    token: Optional[str] = None
    """调用到家履约平台Rerank需要的token"""
    base_url: Optional[str] = None
    """调用服务的域名"""
    session: Any = None

    @root_validator()
    def validate_environment(cls, values: Dict) -> Dict:
        values['token'] = get_from_dict_or_env(values, 'token', "DJ_RERANK_API_TOKEN")
        values['base_url'] = get_from_dict_or_env(
            values, 'base_url', "DJ_RERANK_BASE_URL",
            "https://copilot.banma.test.sankuai.com/api"
        )
        session = requests.Session()
        session.headers.update({
            "Authorization": f"Bearer {values['token']}",
        })
        values['session'] = session

        return values

    def compress_documents(
            self,
            documents: Sequence[Document],
            query: str,
            callbacks: Optional[Callbacks] = None
    ) -> Sequence[Document]:
        compressed = []
        for res in self.rerank(documents, query):
            doc = documents[res["index"]]
            if self.filter_uncorrelated:
                if self.normalized and res['relevance_score'] < self.relevance_score:
                    continue
                elif not self.normalized and res['relevance_score'] < 0:
                    continue
            doc_copy = Document(doc.page_content, metadata=deepcopy(doc.metadata))
            doc_copy.metadata["relevance_score"] = res["relevance_score"]
            compressed.append(doc_copy)
        logging.info(F"查询语句：{query} 重排前文档数：{len(documents)}, 重排后文档数量：{len(compressed)}")
        return compressed

    def rerank(
            self,
            documents: Sequence[Union[str, Document, dict]],
            query: str,
            *,
            top_n: Optional[int] = -1,
    ) -> List[Dict[str, Any]]:
        if len(documents) == 0:  # to avoid empty api call
            return []

        docs = [
            {
                "query": query,
                "content": doc.page_content if isinstance(doc, Document) else doc,
            }
            for doc in documents
        ]
        top_n = top_n if (top_n is None or top_n > 0) else self.top_n
        data = {
            "datas": docs,
            "normalize": self.normalized,
            "top_n": top_n,
            "model_name": self.model
        }
        res = self.session.post(
            F"{self.base_url}/v1/rerank/normal",
            json=data
        )
        score_results = self._handle_response(res)['results']
        result_dicts = []
        for s in score_results:
            result_dicts.append({
                "index": s['index'],
                'relevance_score': s['relevance_score']
            })
        return result_dicts

    @staticmethod
    def _handle_response(res: requests.Response) -> Dict:
        if not res or res.status_code != 200:
            raise RuntimeError(F"rerank response error: {res=}")
        json_result = res.json()
        if not json_result['success']:
            raise RuntimeError(F"{json_result['msg']}")
        return json_result['data']
