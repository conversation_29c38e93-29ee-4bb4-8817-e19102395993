# 模块开头注释
"""
模块文档字符串
"""

# 导入前注释
import sys

# 全局变量注释
GLOBAL_VAR = 42

class OuterClass:
    # 外部类内部注释
    class_var = "outer"
    
    def __init__(self):
        # 外部类构造函数注释
        self.value = 1
    
    class InnerClass:
        # 内部类注释
        inner_var = "inner"
        
        def inner_method(self):
            # 内部类方法注释
            return "inner"
    
    def outer_method(self):
        # 外部类方法注释
        def nested_function():
            # 嵌套函数注释
            return "nested"
        
        # 方法中的注释
        result = nested_function()
        return result

# 类之间的注释
def standalone_function():
    # 独立函数注释
    class LocalClass:
        # 局部类注释
        def local_method(self):
            # 局部类方法注释
            pass
    
    # 函数末尾注释
    return LocalClass()

# 文件末尾注释
