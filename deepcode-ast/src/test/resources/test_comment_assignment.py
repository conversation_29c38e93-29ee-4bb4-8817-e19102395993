# 这是模块级注释1
# 这是模块级注释2

import os  # 导入注释

# 模块级变量注释
MODULE_VAR = "test"

# 这是模块级函数的注释
def module_function():
    # 函数内部注释1
    x = 1  # 行尾注释
    # 函数内部注释2
    return x

# 类定义前的注释
class TestClass:
    # 类内部注释1
    class_var = "test"  # 类变量注释
    
    # 构造函数注释
    def __init__(self):
        # 构造函数内部注释
        self.instance_var = 1  # 实例变量注释
    
    # 方法注释
    def method1(self):
        # 方法内部注释1
        result = 2
        # 方法内部注释2
        return result
    
    # 另一个方法注释
    def method2(self, param):
        # 方法内注释
        return param * 2

# 另一个类的注释
class AnotherClass:
    # 类内注释
    pass

# 模块末尾的注释1
# 模块末尾的注释2
