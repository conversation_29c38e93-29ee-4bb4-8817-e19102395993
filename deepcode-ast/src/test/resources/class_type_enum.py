"""
:module_name: custome_types.py
:author: "zhouyong15<<EMAIL>>"
:copyright: @2024 by zhouyong15
:date 2024/7/22 13:56
"""
__author__ = 'zhouyong15'

import enum
import warnings


class DeprecatedEnumMeta(enum.EnumMeta):
    """自定义元类以处理废弃的枚举成员"""

    def __getattribute__(self, name):
        value = super().__getattribute__(name)
        if hasattr(value, '_deprecated') and getattr(value, '_deprecated', False):
            prefix_msg = f"{value.name} is deprecated."
            warning_msg = getattr(value, '_warning_msg', prefix_msg)
            warnings.warn(F"{prefix_msg} {warning_msg or ''}", DeprecationWarning, stacklevel=2)
        return value


class CustomStrEnum(str, enum.Enum, metaclass=DeprecatedEnumMeta):
    """自定义枚举类，继承自str和enum.Enum，使用自定义元类处理废弃状态"""

    def __new__(cls, value, deprecated=False, warning_msg=None):
        # 使用super()来正确调用Enum的__new__，并传递cls和value
        obj = super().__new__(cls, value)
        obj._value_ = value
        obj._deprecated = deprecated
        obj._warning_msg = warning_msg  # 动态传入的警告消息
        return obj
