package com.sankuai.deepcode.ast.java.test;

@Deprecated
public class MultipleClass {
    private int[] ints;
    private int[][] ints2;

    public <T> void printArray(T[] array) {

    }

    private void test1(int i[][]) {
        System.out.println();
    }

    private void test2(int... i) {
        System.out.println();
    }

    @Deprecated
    int a = 1, b = 2, c = 3;

    @Deprecated
    class InnerClass {

        @Deprecated
        int a = 1, b = 2, c = 3;

    }

    enum InnerEnum {}
}

interface I {
}

enum E {}
