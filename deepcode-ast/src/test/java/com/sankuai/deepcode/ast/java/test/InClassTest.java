package com.sankuai.deepcode.ast.java.test;


import lombok.Getter;
import lombok.Setter;

public final class InClassTest {

    public void test0() {

    }

    @Deprecated
    public final class i {
        public void test1() {

        }

        @Getter
        private class j {
            public void test2() {

            }

            @Setter
            protected class k {
                public void test3() {

                }
            }
        }
    }

    static class a {

        static class b {
            enum c {
                ee,
                rr;

                public void test4() {

                }
            }
        }
    }

    interface d {
        class e {
            @interface f {
                String method();

                int asd = 0;
            }
        }
    }

    @interface g {
        @interface h {
            class o {
            }
        }
    }
}
