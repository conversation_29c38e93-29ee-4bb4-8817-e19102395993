package com.sankuai.deepcode.ast.java.test;


import com.sankuai.deepcode.ast.model.java.MethodNode;

public final class InvokeTest {

    public static class Criteria {

        protected Criteria() {
            super();
        }
    }

    public Criteria createCriteria() {
        MethodNode methodNode = new MethodNode();
        methodNode.setBody("");
        Criteria criteria = createCriteriaInternal();
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public int hashCode() {
        return 0;
    }


    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("Hash = ").append(hashCode());
        return sb.toString();
    }
}
