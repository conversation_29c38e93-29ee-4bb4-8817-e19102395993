package com.sankuai.deepcode.ast.html;

import com.sankuai.deepcode.ast.html.gen.HTMLLexer;
import com.sankuai.deepcode.ast.html.gen.HTMLParser;
import com.sankuai.deepcode.ast.html.visitor.MyHtmlVisitor;
import com.sankuai.deepcode.ast.util.FileUtil;
import org.antlr.v4.runtime.CharStreams;
import org.antlr.v4.runtime.CommonTokenStream;
import org.antlr.v4.runtime.tree.ParseTree;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.Map;

public class TestVisitors {

    public static void main(String[] args) throws IOException {
        String reposPath = "/Users/<USER>/idea/webstorm/houyi_edc_admin";
        Map<String, File> fileMap = FileUtil.getAllFiles(reposPath);
        long start = System.currentTimeMillis();
        int count = 0;
        for (Map.Entry<String, File> entry : fileMap.entrySet()) {
            if (entry.getKey().endsWith(".vue")) {

                String cssFilePath = entry.getValue().getPath();
                if (entry.getValue().getPath().contains("node_modules")) {
                    continue;
                }
                if (entry.getValue().getPath().contains("/.")) {
                    continue;
                }
                count++;
                long start1 = System.currentTimeMillis();


                String code = new String(Files.readAllBytes(Paths.get(cssFilePath)));
                HTMLLexer lexer = new HTMLLexer(CharStreams.fromString(code));
                CommonTokenStream tokens = new CommonTokenStream(lexer);
                HTMLParser parser = new HTMLParser(tokens);
                ParseTree tree = parser.htmlDocument();


                System.out.println(entry.getValue().getPath() + " 1耗时:" + (System.currentTimeMillis() - start1));


                long start2 = System.currentTimeMillis();
                MyHtmlVisitor visitor = new MyHtmlVisitor();
                visitor.visit(tree);

                System.out.println(entry.getValue().getPath() + " 2耗时:" + (System.currentTimeMillis() - start2));


                System.out.println();
            }
        }
        System.out.println("耗时:" + (System.currentTimeMillis() - start));
        System.out.println();

    }
}
