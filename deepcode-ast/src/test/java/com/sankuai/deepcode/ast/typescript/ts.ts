/*
 * Comprehensive TypeScript Syntax Example
 * This file demonstrates various TypeScript syntax features.
 */

/* =======================
   Import Statements
   ======================= */

import {join} from 'path';
import * as fs from 'fs';
import defaultExport, {namedExport} from './module';
import type {InterfaceExample} from './types';
import '@/apis/request'


/* =======================
   Decorators
   ======================= */

// A simple class decorator
function sealed(constructor: Function): void {
    Object.seal(constructor);
    Object.seal(constructor.prototype);
}

@sealed
class DecoratedClass {
    constructor(public name: string) {
    }
}

/* =======================
   Enums
   ======================= */

enum Color {
    Red,
    Green,
    Blue,
}

enum Direction {
    Up = 1,
    Down,
    Left = "LEFT",
    Right = "RIGHT",
}

/* =======================
   Interfaces
   ======================= */

interface IUser {
    readonly id: number;
    name: string;
    age?: number;

    getAddress(): string;
}

interface IEmployee extends IUser {
    readonly employeeId: string;
    role: string;
}

/* =======================
   Type Aliases
   ======================= */

type StringOrNumber = string | number;

type Callback = (data: StringOrNumber) => void;

type GenericType<T> = {
    data: T;
    count: number;
};

/* =======================
   Classes
   ======================= */

abstract class Animal {
    constructor(public name: string) {
    }

    abstract makeSound(): void;

    move(distance: number): void {
        console.log(`${this.name} moved ${distance} meters.`);
    }
}

class Dog extends Animal implements IEmployee {
    public readonly employeeId: string;
    private breed: string;

    constructor(name: string, breed: string) {
        super(name);
        this.breed = breed;
        this.employeeId = 'EMP123';
    }

    makeSound(): void {
        console.log('Woof! Woof!');
    }

    getBreed(): string {
        return this.breed;
    }

    getAddress(): string {
        return '123 Dog Street';
    }
}

/* =======================
   Functions
   ======================= */

// Regular function
function add(a: number, b: number): number {
    return a + b;
}

// Arrow function
const subtract = (a: number, b: number): number => a - b;

// Anonymous function
const multiply = function (a: number, b: number): number {
    return a * b;
};

// Async function
async function fetchData(url: string): Promise<string> {
    // Simulate fetching data
    return `Data from ${url}`;
}

// Generator function
function* idGenerator(): Generator<number> {
    let id = 1;
    while (true) {
        yield id++;
    }
}

/* =======================
   Variables
   ======================= */

var globalVar: string = "I am a var";
let globalLet: number = 42;
const globalConst: boolean = true;

/* =======================
   Control Flow Statements
   ======================= */

// If-Else
function checkValue(x: number): string {
    if (x > 10) {
        return "Greater than 10";
    } else if (x === 10) {
        return "Equal to 10";
    } else {
        return "Less than 10";
    }
}

// Switch
function getColorName(color: Color): string {
    switch (color) {
        case Color.Red:
            return "Red";
        case Color.Green:
            return "Green";
        case Color.Blue:
            return "Blue";
        default:
            return "Unknown";
    }
}

// For Loop
for (let i = 0; i < 5; i++) {
    console.log(`For Loop Iteration: ${i}`);
}

// While Loop
let count = 0;
while (count < 3) {
    console.log(`While Loop Count: ${count}`);
    count++;
}

// Do-While Loop
let doCount = 0;
do {
    console.log(`Do-While Loop Count: ${doCount}`);
    doCount++;
} while (doCount < 2);

// Try-Catch-Finally
function riskyOperation() {
    try {
        throw new Error("Something went wrong!");
    } catch (error) {
        console.error(error);
    } finally {
        console.log("Cleanup completed.");
    }
}

/* =======================
   Namespaces
   ======================= */

namespace Utilities {
    export function greet(name: string): string {
        return `Hello, ${name}!`;
    }

    export namespace MathUtils {
        export function square(n: number): number {
            return n * n;
        }
    }
}

/* =======================
   Generics
   ======================= */

function identity<T>(arg: T): T {
    return arg;
}

const stringIdentity = identity<string>("TypeScript");
const numberIdentity = identity<number>(100);

/* =======================
   Tuples
   ======================= */

let user: [number, string];
user = [1, "Alice"];

/* =======================
   Advanced Types
   ======================= */

type PartialUser = Partial<IUser>;
type ReadonlyEmployee = Readonly<IEmployee>;
type NullableString = string | null;

/* =======================
   Using Classes and Functions
   ======================= */

const myDog = new Dog("Buddy", "Golden Retriever");
myDog.makeSound();
myDog.move(10);
console.log(`Breed: ${myDog.getBreed()}`);
console.log(`Employee ID: ${myDog.employeeId}`);

console.log(add(5, 3));
console.log(subtract(10, 4));
console.log(multiply(6, 7));

const generator = idGenerator();
console.log(generator.next().value);
console.log(generator.next().value);

fetchData("https://example.com").then(data => console.log(data));

console.log(Utilities.greet("Bob"));
console.log(Utilities.MathUtils.square(5));

console.log(`Color Name: ${getColorName(Color.Green)}`);
console.log(checkValue(15));

riskyOperation();

/* =======================
   Export Statements
   ======================= */

export {Dog, add, fetchData};
export default class DefaultExportClass {
    sayHello() {
        console.log("Hello from the default export class!");
    }
}

class MyClass {
    private _value: number = 0;

    // Getter
    get value(): number {
        return this._value;
    }

    // Setter
    set value(newValue: number) {
        if (newValue >= 0) {
            this._value = newValue;
        } else {
            console.log("Value must be non-negative");
        }
    }
}

const instance = new MyClass();
instance.value = 10; // 使用 setter
console.log(instance.value); // 使用 getter


interface StringFormatter {
    (text: string, maxLength: number): string;
}

const format: StringFormatter = (text, maxLength) => {
    return text.length > maxLength ? text.slice(0, maxLength) + "..." : text;
};


// 简写参数
const square = x => x * x;

// 带类型注解
const add = (a: number, b: number): number => a + b;

// 代码块形式
const greet = (name: string) => {
    console.log(`Hello ${name}`);
};


function* numberGenerator() {
    yield 1;
    yield 2;
    yield 3;
}

// 使用
const gen = numberGenerator();
console.log(gen.next().value); // 1


const myIterable = {
    *[Symbol.iterator]() {
        yield 1;
        yield 2;
        yield 3;
    }
};

// 使用
for (const value of myIterable) {
    console.log(value);
}


