package com.sankuai.deepcode.ast.analysis;

import com.sankuai.deepcode.ast.model.python3.PythonAnalysesResult;
import com.sankuai.deepcode.ast.model.python3.PythonImportNode;
import com.sankuai.deepcode.ast.python3.analysis.PythonAnalyzer;
import com.sankuai.deepcode.ast.python3.visitor.PythonImportVisitor;
import lombok.SneakyThrows;
import org.testng.annotations.Test;

import java.nio.file.Path;
import java.nio.file.Paths;

public class PythonRelativeImportTest extends BaseTest {

    @SneakyThrows
    @Test
    public void testRelativeImport() {
        // 测试简单模块的相对导入
        testRelativeImportForModule("relative_import_test", "relative_import_test");

        // 测试包结构中的相对导入
        testRelativeImportForModule("package.subpackage.relative_import_test", "package.subpackage.relative_import_test");
    }

    @SneakyThrows
    private void testRelativeImportForModule(String modulePath, String expectedModuleName) {
        System.out.println("=== 测试模块: " + modulePath + " ===");

        // 手动创建模块节点和访问者来测试相对导入
        com.sankuai.deepcode.ast.model.python3.ScopeManager scopeManager =
            new com.sankuai.deepcode.ast.model.python3.ScopeManager(expectedModuleName);

        PythonImportVisitor importVisitor = new PythonImportVisitor(
            "relative_import_test.py",
            expectedModuleName,
            modulePath,
            scopeManager
        );

        // 测试不同的相对导入场景
        testRelativeImportScenario(importVisitor, ".", "", 1, modulePath); // from . import
        testRelativeImportScenario(importVisitor, ".", "sibling_module", 1, modulePath); // from .sibling_module import
        testRelativeImportScenario(importVisitor, "..", "", 2, modulePath); // from .. import
        testRelativeImportScenario(importVisitor, "..", "parent_module", 2, modulePath); // from ..parent_module import
        testRelativeImportScenario(importVisitor, "...", "grandparent", 3, modulePath); // from ...grandparent import

        System.out.println();
    }

    private void testRelativeImportScenario(PythonImportVisitor visitor, String dots, String moduleName, int expectedDotCount, String currentModulePath) {
        // 使用反射调用私有方法进行测试
        try {
            java.lang.reflect.Method method = PythonImportVisitor.class.getDeclaredMethod("getFullModulePath", String.class, int.class);
            method.setAccessible(true);
            String result = (String) method.invoke(visitor, moduleName, expectedDotCount);

            System.out.println(String.format("当前模块: %s, 导入: %s%s -> 解析为: %s",
                currentModulePath, dots, moduleName, result));
        } catch (Exception e) {
            System.err.println("测试失败: " + e.getMessage());
        }
    }
}
