package com.sankuai.deepcode.ast.test;

import com.sankuai.deepcode.ast.xml.gen.XMLLexer;
import com.sankuai.deepcode.ast.xml.gen.XMLParser;
import com.sankuai.deepcode.ast.xml.visitor.MyXmlVisitor;
import org.antlr.v4.runtime.CharStreams;
import org.antlr.v4.runtime.CommonTokenStream;
import org.antlr.v4.runtime.tree.ParseTree;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Paths;

public class TestVisitor {
    public static void main(String[] args) throws IOException {
        String xmlFilePath = "/Users/<USER>/idea/tools/houyi_code_analysis_server/houyi_code_ast_common/pom.xml";

        String code = new String(Files.readAllBytes(Paths.get(xmlFilePath)));
        XMLLexer lexer = new XMLLexer(CharStreams.fromString(code));
        CommonTokenStream tokens = new CommonTokenStream(lexer);
        XMLParser parser = new XMLParser(tokens);
        ParseTree tree = parser.document();


        MyXmlVisitor visitor = new MyXmlVisitor();
        visitor.visit(tree);


        System.out.println();
    }
}
