package com.sankuai.deepcode.ast.analysis;

import com.sankuai.deepcode.ast.model.python3.PythonAnalysesResult;
import com.sankuai.deepcode.ast.python3.analysis.PythonAnalyzer;
import org.testng.annotations.Test;

import java.nio.file.Path;

/**
 * Package: com.sankuai.deepcode.ast.python3.analysis
 * Description:
 *
 * <AUTHOR>
 * @since 2025/1/13 14:49
 */
public class PythonAssigmentVisitorTest extends BaseTest {
    @Test
    public void visitAssign() {
        PythonAnalysesResult result = new PythonAnalysesResult();

        Path pythonFilePath = getResourcePath().resolve("test_api_common_handler.py");
        PythonAnalyzer.analyzePythonFile(getResourcePath(), pythonFilePath, result);

        prettyPrint(result);
    }
}
