package com.sankuai.deepcode.ast.test;

import com.sankuai.deepcode.ast.model.python3.ScopeManager;
import com.sankuai.deepcode.ast.python3.gen.PythonLexer;
import com.sankuai.deepcode.ast.python3.gen.PythonParser;
import com.sankuai.deepcode.ast.python3.visitor.PythonFunctionCallVisitor;
import lombok.SneakyThrows;
import org.antlr.v4.runtime.CharStreams;
import org.antlr.v4.runtime.CommonTokenStream;
import org.antlr.v4.runtime.tree.ParseTree;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;

public class TestPython3Visitor {


    @SneakyThrows
    public static void main(String[] args) throws IOException {
        Path currentDirectory = Paths.get(TestPython3Visitor.class.getProtectionDomain().getCodeSource().getLocation().toURI()).toAbsolutePath();
        String pythonFilePath = currentDirectory.resolve("example.py").toString();

        String code = new String(Files.readAllBytes(Paths.get(pythonFilePath)));
//        String code = "a = 1";

        PythonLexer lexer = new PythonLexer(CharStreams.fromString(code));

        CommonTokenStream tokens = new CommonTokenStream(lexer);

        PythonParser parser = new PythonParser(tokens);

        ParseTree tree = parser.file_input();

        PythonFunctionCallVisitor visitor = new PythonFunctionCallVisitor(pythonFilePath, "root",new ScopeManager());
        visitor.visit(tree);

        System.out.println();
    }
}
