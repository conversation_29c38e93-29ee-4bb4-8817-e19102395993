package com.sankuai.deepcode.ast.html;

import com.sankuai.deepcode.ast.html.gen.HTMLLexer;
import com.sankuai.deepcode.ast.html.gen.HTMLParser;
import com.sankuai.deepcode.ast.html.visitor.MyHtmlVisitor;
import org.antlr.v4.runtime.CharStreams;
import org.antlr.v4.runtime.CommonTokenStream;
import org.antlr.v4.runtime.tree.ParseTree;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Paths;

public class TestVisitor {

    public static void main(String[] args) throws IOException {
        String xmlFilePath = "/Users/<USER>/idea/webstorm/houyi_edc_admin/edc/houyi-conf-card/src/configureList.vue";

        String code = new String(Files.readAllBytes(Paths.get(xmlFilePath)));
        HTMLLexer lexer = new HTMLLexer(CharStreams.fromString(code));
        CommonTokenStream tokens = new CommonTokenStream(lexer);
        HTMLParser parser = new HTMLParser(tokens);
        ParseTree tree = parser.htmlDocument();


        MyHtmlVisitor visitor = new MyHtmlVisitor();
        visitor.visit(tree);





        System.out.println();
    }
}
