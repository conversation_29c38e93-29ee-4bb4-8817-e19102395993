package com.sankuai.deepcode.ast.python3;

import com.sankuai.deepcode.ast.analysis.BaseTest;
import com.sankuai.deepcode.ast.model.python3.PythonAnalysesResult;
import com.sankuai.deepcode.ast.model.python3.PythonClassNode;
import com.sankuai.deepcode.ast.model.python3.PythonComment;
import com.sankuai.deepcode.ast.model.python3.PythonFunctionNode;
import com.sankuai.deepcode.ast.model.python3.PythonMethodNode;
import com.sankuai.deepcode.ast.model.python3.PythonModuleNode;
import com.sankuai.deepcode.ast.python3.analysis.PythonAnalyzer;
import org.testng.annotations.Test;

import java.nio.file.Path;
import java.util.List;

import static org.testng.Assert.*;

/**
 * 测试Python注释分配功能
 */
public class TestCommentAssignment extends BaseTest {

    @Test
    public void testCommentAssignment() {
        // 获取测试文件路径
        Path testFile = getResourcePath().resolve("test_comment_assignment.py");
        Path rootPath = getResourcePath();
        
        // 分析Python文件
        PythonAnalysesResult result = new PythonAnalysesResult();
        PythonAnalyzer.analyzePythonFile(rootPath, testFile, result);
        
        // 获取模块节点
        PythonModuleNode moduleNode = result.getModuleMap().get("test_comment_assignment");
        assertNotNull(moduleNode, "模块节点不应为空");
        
        // 验证模块级注释
        List<PythonComment> moduleComments = moduleNode.getComments();
        assertNotNull(moduleComments, "模块注释列表不应为空");
        assertTrue(moduleComments.size() > 0, "模块应该有注释");
        
        // 打印模块注释用于调试
        System.out.println("=== 模块注释 ===");
        for (PythonComment comment : moduleComments) {
            System.out.println("行 " + comment.commentLine() + ": " + comment.getComment());
        }
        
        // 验证模块级函数注释
        List<PythonFunctionNode> functions = moduleNode.getFunctionNodes();
        assertNotNull(functions, "函数列表不应为空");
        assertTrue(functions.size() > 0, "模块应该有函数");
        
        PythonFunctionNode moduleFunction = functions.stream()
                .filter(f -> "module_function".equals(f.getName()))
                .findFirst()
                .orElse(null);
        assertNotNull(moduleFunction, "应该找到module_function函数");
        
        List<PythonComment> functionComments = moduleFunction.getComments();
        assertNotNull(functionComments, "函数注释列表不应为空");
        
        System.out.println("=== 模块函数注释 ===");
        for (PythonComment comment : functionComments) {
            System.out.println("行 " + comment.commentLine() + ": " + comment.getComment());
        }
        
        // 验证类注释
        List<PythonClassNode> classes = moduleNode.getClassNodes();
        assertNotNull(classes, "类列表不应为空");
        assertTrue(classes.size() > 0, "模块应该有类");
        
        PythonClassNode testClass = classes.stream()
                .filter(c -> "TestClass".equals(c.getName()))
                .findFirst()
                .orElse(null);
        assertNotNull(testClass, "应该找到TestClass类");
        
        List<PythonComment> classComments = testClass.getComments();
        assertNotNull(classComments, "类注释列表不应为空");
        
        System.out.println("=== 类注释 ===");
        for (PythonComment comment : classComments) {
            System.out.println("行 " + comment.commentLine() + ": " + comment.getComment());
        }
        
        // 验证类方法注释
        List<PythonMethodNode> methods = testClass.getMethods();
        assertNotNull(methods, "方法列表不应为空");
        assertTrue(methods.size() > 0, "类应该有方法");
        
        PythonMethodNode method1 = methods.stream()
                .filter(m -> "method1".equals(m.getName()))
                .findFirst()
                .orElse(null);
        assertNotNull(method1, "应该找到method1方法");
        
        List<PythonComment> methodComments = method1.getComments();
        assertNotNull(methodComments, "方法注释列表不应为空");
        
        System.out.println("=== 方法注释 ===");
        for (PythonComment comment : methodComments) {
            System.out.println("行 " + comment.commentLine() + ": " + comment.getComment());
        }
        
        // 验证注释分配的正确性
        // 检查是否有注释被正确分配到各个节点
        int totalComments = moduleComments.size();
        for (PythonFunctionNode func : functions) {
            totalComments += func.getComments().size();
        }
        for (PythonClassNode cls : classes) {
            totalComments += cls.getComments().size();
            for (PythonMethodNode method : cls.getMethods()) {
                totalComments += method.getComments().size();
            }
        }
        
        System.out.println("总注释数量: " + totalComments);
        assertTrue(totalComments > 0, "应该有注释被分配到各个节点");

        // 验证特定注释的分配
        // 检查模块级注释是否包含预期的内容
        boolean hasModuleComment1 = moduleComments.stream()
                .anyMatch(c -> c.getComment().contains("这是模块级注释1"));
        assertTrue(hasModuleComment1, "模块应该包含'这是模块级注释1'");

        // 检查函数注释是否包含预期的内容
        boolean hasFunctionComment = functionComments.stream()
                .anyMatch(c -> c.getComment().contains("这是模块级函数的注释"));
        assertTrue(hasFunctionComment, "函数应该包含'这是模块级函数的注释'");

        // 检查类注释是否包含预期的内容
        boolean hasClassComment = classComments.stream()
                .anyMatch(c -> c.getComment().contains("类定义前的注释"));
        assertTrue(hasClassComment, "类应该包含'类定义前的注释'");

        // 检查方法注释是否包含预期的内容
        boolean hasMethodComment = methodComments.stream()
                .anyMatch(c -> c.getComment().contains("方法内部注释"));
        assertTrue(hasMethodComment, "方法应该包含'方法内部注释'");
    }


}
