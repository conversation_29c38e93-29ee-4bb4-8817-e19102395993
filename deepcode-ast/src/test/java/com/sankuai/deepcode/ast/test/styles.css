/* CSS 变量 */
:root {
    --primary-color: #3498db;
    --secondary-color: #2ecc71;
    --font-stack: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* 基本选择器 */
body {
    font-family: var(--font-stack);
    background-color: #f5f5f5;
    margin: 0;
    padding: 0;
}

/* 类选择器 */
.header {
    background-color: var(--primary-color);
    padding: 20px;
    color: white;
}

.header .nav ul {
    list-style: none;
    display: flex;
}

.header .nav ul li {
    margin-right: 15px;
}

.header .nav ul li a {
    color: white;
    text-decoration: none;
    padding: 5px 10px;
    transition: background-color 0.3s;
}

.header .nav ul li a:hover,
.header .nav ul li a.active {
    background-color: var(--secondary-color);
}

/* ID 选择器 */
#main-title {
    font-size: 2em;
    margin: 0;
}

/* 伪类选择器 */
.post-title:hover {
    color: var(--secondary-color);
}

/* 伪元素选择器 */
.post::before {
    content: "📄 ";
}

/* 属性选择器 */
a[href^="https"] {
    color: darkblue;
}

/* 媒体查询 */
@media (max-width: 600px) {
    .header .nav ul {
        flex-direction: column;
    }
    .header .nav ul li {
        margin-right: 0;
        margin-bottom: 10px;
    }
}
