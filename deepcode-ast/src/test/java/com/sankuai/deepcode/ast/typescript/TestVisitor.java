package com.sankuai.deepcode.ast.typescript;

import com.sankuai.deepcode.ast.typescript.gen.TypeScriptLexer;
import com.sankuai.deepcode.ast.typescript.gen.TypeScriptParser;
import com.sankuai.deepcode.ast.typescript.visitor.MyTypeScriptVisitor;
import org.antlr.v4.runtime.CharStreams;
import org.antlr.v4.runtime.CommonTokenStream;
import org.antlr.v4.runtime.tree.ParseTree;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Paths;

public class TestVisitor {

    public static void main(String[] args) throws IOException {
        String cssFilePath = "/Users/<USER>/idea/git/yiyao_fe_operation_h5/packages/visitingTool/src/pages/Authorize/index.tsx";

        String code = new String(Files.readAllBytes(Paths.get(cssFilePath)));
        TypeScriptLexer lexer = new TypeScriptLexer(CharStreams.fromString(code));
        CommonTokenStream tokens = new CommonTokenStream(lexer);
        TypeScriptParser parser = new TypeScriptParser(tokens);
        ParseTree tree = parser.program();

        MyTypeScriptVisitor visitor = new MyTypeScriptVisitor();
        visitor.visit(tree);


        System.out.println();
    }
}
