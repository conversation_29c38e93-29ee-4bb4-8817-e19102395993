package com.sankuai.deepcode.ast.analysis;

import com.sankuai.deepcode.ast.model.python3.PythonAnalysesResult;
import com.sankuai.deepcode.ast.python3.analysis.PythonAnalyzer;
import org.testng.annotations.Test;

import java.nio.file.Path;

/**
 * Package: com.sankuai.deepcode.ast.python3.analysis
 * Description:
 *
 * <AUTHOR>
 * @since 2025/1/13 14:48
 */
public class PythonClassDefVisitorTest extends BaseTest{
    @Test
    public void testClassDef() {
        PythonAnalysesResult result = new PythonAnalysesResult();
        Path pythonFilePath = getResourcePath().resolve("class_type_special.py");
        PythonAnalyzer.analyzePythonFile(getResourcePath(), pythonFilePath, result);

        prettyPrint(result);
    }

    @Test
    public void testClassDef2() {
        PythonAnalysesResult result = new PythonAnalysesResult();
        Path pythonFilePath = getResourcePath().resolve("class_type_enum.py");
        PythonAnalyzer.analyzePythonFile(getResourcePath(), pythonFilePath, result);

        prettyPrint(result);
    }

    @Test
    public void testSuperCall() {
        PythonAnalysesResult result = new PythonAnalysesResult();
        Path pythonFilePath = getResourcePath().resolve("test_super_simple.py");
        PythonAnalyzer.analyzePythonFile(getResourcePath(), pythonFilePath, result);

        prettyPrint(result);
    }

    @Test
    public void testSuperMethod() {
        PythonAnalysesResult result = new PythonAnalysesResult();
        Path pythonFilePath = getResourcePath().resolve("test_super_method.py");
        PythonAnalyzer.analyzePythonFile(getResourcePath(), pythonFilePath, result);

        prettyPrint(result);
    }

    @Test
    public void testSimpleAttr() {
        PythonAnalysesResult result = new PythonAnalysesResult();
        Path pythonFilePath = getResourcePath().resolve("test_simple_attr.py");
        PythonAnalyzer.analyzePythonFile(getResourcePath(), pythonFilePath, result);

        prettyPrint(result);
    }
}
