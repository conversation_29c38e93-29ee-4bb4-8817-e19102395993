package com.sankuai.deepcode.ast.html;

import com.sankuai.deepcode.ast.foreend.ForeEndAnalysis;

public class testAll {

    public static void main(String[] args) throws Exception {
        ForeEndAnalysis foreEndAnalysis = new ForeEndAnalysis();
        String gitUrl = "ssh://*******************/wbqa/houyi_admin.git";
        String repos = "houyi_admin";
        String fromBranch = "show";
        String toBranch = "master";
        String buildBranch = "master";
        String buildCommit = "";
        foreEndAnalysis.analysis(0, gitUrl, repos, fromBranch, toBranch, buildBranch, buildCommit);
    }
}
