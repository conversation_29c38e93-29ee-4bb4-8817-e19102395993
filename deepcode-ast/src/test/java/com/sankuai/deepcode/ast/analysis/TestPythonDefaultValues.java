package com.sankuai.deepcode.ast.analysis;

import com.sankuai.deepcode.ast.model.python3.PythonAnalysesResult;
import com.sankuai.deepcode.ast.model.python3.PythonClassNode;
import com.sankuai.deepcode.ast.model.python3.PythonModuleNode;
import com.sankuai.deepcode.ast.model.python3.PythonParameterNode;
import com.sankuai.deepcode.ast.python3.analysis.PythonAnalyzer;

public class TestPythonDefaultValues {
    public static void main(String[] args) {
        try {
            System.out.println("Testing Python default value extraction...");
            
            // 分析当前目录
            String currentDir = System.getProperty("user.dir");
            System.out.println("Analyzing directory: " + currentDir);
            
            PythonAnalysesResult result = PythonAnalyzer.analyzePythonProject(currentDir);
            
            if (result != null && result.getModuleMap() != null && !result.getModuleMap().isEmpty()) {
                for (PythonModuleNode moduleNode : result.getModuleMap().values()) {
                    if (moduleNode.getFileName() != null && moduleNode.getFileName().contains("test_simple_values.py")) {
                        System.out.println("\n=== Found test file: " + moduleNode.getFileName() + " ===");
                        
                        // 测试模块变量
                        System.out.println("\n--- Module Variables ---");
                        for (PythonParameterNode param : moduleNode.getModuleParamNodes()) {
                            System.out.println("Variable: " + param.getName() + 
                                             " | Value: '" + param.getDefaultValue() + "'" +
                                             " | Type: " + param.getType());
                        }
                        
                        // 测试类变量
                        System.out.println("\n--- Class Variables ---");
                        for (PythonClassNode classNode : moduleNode.getClassNodes()) {
                            System.out.println("Class: " + classNode.getName());
                            for (PythonParameterNode param : classNode.getParameters()) {
                                System.out.println("  Variable: " + param.getName() + 
                                                 " | Value: '" + param.getDefaultValue() + "'" +
                                                 " | Type: " + param.getType());
                            }
                        }
                        
                        // 验证结果
                        System.out.println("\n--- Validation ---");
                        boolean success = true;
                        
                        for (PythonParameterNode param : moduleNode.getModuleParamNodes()) {
                            String expectedValue = getExpectedValue(param.getName());
                            if (expectedValue != null && !expectedValue.equals(param.getDefaultValue())) {
                                System.out.println("FAIL: " + param.getName() + " expected '" + expectedValue + "' but got '" + param.getDefaultValue() + "'");
                                success = false;
                            }
                        }
                        
                        if (success) {
                            System.out.println("SUCCESS: All default values extracted correctly!");
                        }
                        
                        return;
                    }
                }
                System.out.println("Test file not found in analysis results");
            } else {
                System.out.println("No analysis results found");
            }
        } catch (Exception e) {
            System.out.println("Error: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    private static String getExpectedValue(String varName) {
        switch (varName) {
            case "x": return "1";
            case "y": return "3.14";
            case "name": return "hello";
            case "description": return "world";
            case "flag": return "True";
            case "empty": return "None";
            default: return null;
        }
    }
}
