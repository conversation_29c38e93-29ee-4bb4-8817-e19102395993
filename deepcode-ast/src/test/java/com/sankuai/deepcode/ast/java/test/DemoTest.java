//12311

package com.sankuai.deepcode.ast.java.test;

import javax.xml.ws.ServiceMode;
import java.util.Map;


/**
 *
 */
//12333
@ServiceMode
@AnnotationTest(name = "", value = 0, array = {}, array2 = {}, optionsClass2 = Class.class)
public final class DemoTest {

    static {
        System.out.println("hello static");
    }

    /**
     * 123
     */
    //333
//    @Getter
//    private int[] a;
//
//    @Getter
//    private int b[];
//
//    @Getter
//    private int[] c, d;

//    private int e = 3;
    private int e1 = 3;


//    //注释333
//    @Getter
//    public static void main(String[] args) {
//        //注释333
//        MethodNode methodNode = new MethodNode();
//        methodNode.setAccess("public");
//        System.out.println("hello world");
//    }
//
//    public List<String> method33(String[] args) {
//        //注释333
//        MethodNode methodNode = new MethodNode();
//        methodNode.setAccess("public");
//        System.out.println("hello world");
//        return null;
//    }


//    public Map<String, String> method44(com.sankuai.deepcode.ast.model.java.MethodNode methodNode) {
//        System.out.println("hello world");
//        com.sankuai.deepcode.ast.java.test.DemoTest.method55(new int[]{1, 2, 3});
//        return null;
//    }

    private static int e2 = 3;

    public static int[] method55(int[] a) {
        System.out.println(e2);
        return null;
    }

    public static int[] method55(int[] a, int b) {
        System.out.println(e2);
        return null;
    }


}
