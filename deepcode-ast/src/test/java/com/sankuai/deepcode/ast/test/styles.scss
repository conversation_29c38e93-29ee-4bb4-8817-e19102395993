// SCSS 变量
$primary-color: #e74c3c;
$secondary-color: #8e44ad;
$font-stack: 'Roboto', sans-serif;

// 混入 (Mixin)
@mixin flex-center {
    display: flex;
    justify-content: center;
    align-items: center;
}

// 嵌套选择器
body {
    font-family: $font-stack;
    background-color: #ecf0f1;
    margin: 0;
    padding: 0;

    .header {
        background-color: $primary-color;
        padding: 20px;
        color: white;

        nav {
            ul {
                list-style: none;
                @include flex-center;
                padding: 0;

                li {
                    margin-right: 20px;

                    a {
                        color: white;
                        text-decoration: none;
                        padding: 5px 15px;
                        border-radius: 5px;
                        transition: background-color 0.3s;

                        &:hover,
                        &.active {
                            background-color: $secondary-color;
                        }
                    }
                }
            }
        }
    }

    .content {
        padding: 20px;

        .post {
            background-color: white;
            margin-bottom: 20px;
            padding: 15px;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);

            .post-title {
                color: $primary-color;
                cursor: pointer;

                &:hover {
                    text-decoration: underline;
                }
            }

            p {
                line-height: 1.6;
            }

            a {
                color: $secondary-color;

                &:hover {
                    text-decoration: none;
                    color: darken($secondary-color, 10%);
                }
            }

            img {
                max-width: 100%;
                height: auto;
                border-radius: 5px;
                margin-top: 10px;
            }
        }
    }

    .footer {
        background-color: darken($primary-color, 10%);
        padding: 10px;
        text-align: center;
        color: rgba(255, 255, 255, 0.8);
    }
}

// 嵌套媒体查询
@media (max-width: 800px) {
    .header {
        padding: 15px;

        nav {
            ul {
                flex-direction: column;

                li {
                    margin-right: 0;
                    margin-bottom: 10px;
                }
            }
        }
    }

    .content {
        padding: 10px;
    }
}

// 继承 (Extend)
.button {
    padding: 10px 20px;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    text-transform: uppercase;
    font-weight: bold;
    transition: background-color 0.3s;

    &:hover {
        background-color: lighten($primary-color, 10%);
    }
}

.primary-button {
    @extend .button;
    background-color: $primary-color;
    color: white;

    &:hover {
        background-color: darken($primary-color, 10%);
    }
}

.secondary-button {
    @extend .button;
    background-color: $secondary-color;
    color: white;

    &:hover {
        background-color: darken($secondary-color, 10%);
    }
}

// 函数 (Function)
@function calculate-rem($size) {
    @return $size / 16rem;
}

body {
    font-size: calculate-rem(16);
}

