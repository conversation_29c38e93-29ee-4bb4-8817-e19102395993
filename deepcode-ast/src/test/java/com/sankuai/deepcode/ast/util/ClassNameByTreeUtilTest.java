package com.sankuai.deepcode.ast.util;


import java.util.List;

public class ClassNameByTreeUtilTest {

    public static void main(String[] args) {
        ClassNameByTreeUtil classNameByTreeUtil = new ClassNameByTreeUtil();
//        classNameByTreeUtil.insert("com.sankuai.meituan.banma.pricing.quote.dataresource.handler.ParamResource");
//        classNameByTreeUtil.insert("com.sankuai.meituan.banma.pricing.quote.handler.cache.CrowdCalResultCacheLoader");
//        classNameByTreeUtil.insert("com.sankuai.meituan.banma.pricing.quote.handler.result.CacheResultHandler");
//        classNameByTreeUtil.insert("com.sankuai.meituan.banma.pricing.quote.dto.BudgetKey");
//        classNameByTreeUtil.insert("com.sankuai.meituan.banma.pricing.quote.dataresource.handler.PreferManagerVersionResource");
//
//
//        List<String>  res = classNameByTreeUtil.searchClassName("ParamResource");
//        res = classNameByTreeUtil.searchClassName("CrowdCalResultCacheLoader");
//        res = classNameByTreeUtil.searchClassName("CacheResultHandler");
//        res = classNameByTreeUtil.searchClassName("dataresource.handler.PreferManagerVersionResource");
//        res = classNameByTreeUtil.searchClassName("handler.PreferManagerVersionResource");


        classNameByTreeUtil.insert("com.sankuai.meituan.banma.pricing.quote.dataresource.handler.ParamResource");
        classNameByTreeUtil.insert("com.sankuai.meituan.banma.pricing.quote.handler.cache.ParamResource");
        List<String> res = classNameByTreeUtil.searchClassName("ParamResource");
        System.out.println();
    }
}