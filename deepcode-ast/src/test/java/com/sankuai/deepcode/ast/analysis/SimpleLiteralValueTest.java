package com.sankuai.deepcode.ast.analysis;

import com.sankuai.deepcode.ast.python3.visitor.PythonLiteralValueExtractor;
import org.testng.Assert;
import org.testng.annotations.Test;

/**
 * 测试字面量值提取功能
 */
public class SimpleLiteralValueTest extends BaseTest {

    @Test
    public void testStringValueExtraction() {
        // 测试单引号字符串
        String result1 = PythonLiteralValueExtractor.extractSimpleLiteralValue("'hello'");
        Assert.assertEquals(result1, "hello");
        
        // 测试双引号字符串
        String result2 = PythonLiteralValueExtractor.extractSimpleLiteralValue("\"world\"");
        Assert.assertEquals(result2, "world");
        
        // 测试三引号字符串
        String result3 = PythonLiteralValueExtractor.extractSimpleLiteralValue("'''abc\ndef'''");
        Assert.assertEquals(result3, "abc\ndef");
        
        System.out.println("String extraction tests passed!");
    }

    @Test
    public void testNumberValueExtraction() {
        // 测试整数
        String result1 = PythonLiteralValueExtractor.extractSimpleLiteralValue("123");
        Assert.assertEquals(result1, "123");
        
        // 测试浮点数
        String result2 = PythonLiteralValueExtractor.extractSimpleLiteralValue("3.14");
        Assert.assertEquals(result2, "3.14");
        
        // 测试负数
        String result3 = PythonLiteralValueExtractor.extractSimpleLiteralValue("-42");
        Assert.assertEquals(result3, "-42");
        
        System.out.println("Number extraction tests passed!");
    }

    @Test
    public void testBooleanAndNoneExtraction() {
        // 测试布尔值
        String result1 = PythonLiteralValueExtractor.extractSimpleLiteralValue("True");
        Assert.assertEquals(result1, "True");
        
        String result2 = PythonLiteralValueExtractor.extractSimpleLiteralValue("False");
        Assert.assertEquals(result2, "False");
        
        // 测试None
        String result3 = PythonLiteralValueExtractor.extractSimpleLiteralValue("None");
        Assert.assertEquals(result3, "None");
        
        System.out.println("Boolean and None extraction tests passed!");
    }

    @Test
    public void testComplexValueExtraction() {
        // 测试列表
        String result1 = PythonLiteralValueExtractor.extractSimpleLiteralValue("[1, 2, 3]");
        Assert.assertEquals(result1, "[1, 2, 3]");
        
        // 测试字典
        String result2 = PythonLiteralValueExtractor.extractSimpleLiteralValue("{'key': 'value'}");
        Assert.assertEquals(result2, "{'key': 'value'}");
        
        System.out.println("Complex value extraction tests passed!");
    }
}
