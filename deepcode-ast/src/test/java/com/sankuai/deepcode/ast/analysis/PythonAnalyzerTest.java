package com.sankuai.deepcode.ast.analysis;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.sankuai.deepcode.ast.model.python3.PythonAnalysesResult;
import com.sankuai.deepcode.ast.python.test.TestPython3Visitor;
import com.sankuai.deepcode.ast.python3.analysis.PythonAnalyzer;
import com.sankuai.deepcode.commons.JacksonUtils;
import lombok.SneakyThrows;
import org.testng.annotations.Test;

import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;

public class PythonAnalyzerTest extends BaseTest{

    @SneakyThrows
    @Test
    public void testAnalyzePythonFile() {
        PythonAnalysesResult result = new PythonAnalysesResult();

        Path pythonFilePath = getResourcePath().resolve("example.py");

        PythonAnalyzer.analyzePythonFile(getResourcePath(), pythonFilePath, result);

        System.out.println(JSONObject.toJSONString(result, SerializerFeature.PrettyFormat, SerializerFeature.WriteNonStringKeyAsString));
    }

    @SneakyThrows
    @Test
    public void testAnalyzePythonFile2() {
        String projectPath = "/Users/<USER>/Documents/projects/qa/scenario/banma_fastdata_agent_copilot/";
        String testPath = Paths.get(this.getClass().getResource("/").toURI()).toAbsolutePath().toString();

        PythonAnalysesResult result = PythonAnalyzer.analyzePythonProject(projectPath);

        String jsonResult = JSONObject.toJSONString(
                result,
                SerializerFeature.PrettyFormat,
                SerializerFeature.WriteNonStringKeyAsString,
                SerializerFeature.SortField,
                SerializerFeature.MapSortField
        );

        System.out.println(jsonResult);
    }

    @SneakyThrows
    @Test
    public void testAnalyzePythonFile3() {
//        String projectPath = "/Users/<USER>/Documents/projects/qa/scenario/banma_fastdata_chatgpt/";
        String projectPath = this.getClass().getResource("/").toURI().getPath() + "test_module";
        String testPath = Paths.get(this.getClass().getResource("/").toURI()).toAbsolutePath().toString();

        PythonAnalysesResult result = PythonAnalyzer.analyzePythonProject(projectPath);

        String jsonResult = JSONObject.toJSONString(
                result,
                SerializerFeature.PrettyFormat,
                SerializerFeature.WriteNonStringKeyAsString,
                SerializerFeature.SortField,
                SerializerFeature.MapSortField
        );

        Files.write(Paths.get("result.json"), jsonResult.getBytes());
    }


    @Test
    public void testAnalyzePythonFile4() {
        PythonAnalysesResult result = PythonAnalyzer.analyzePythonProject(1,
                "ssh://*******************/wbqa/banma_fastdata_agent_copilot.git", "banma_fastdata_agent_copilot",
                "master", "main", "master", null);

        System.out.println(JacksonUtils.toJsonString(result, true));
    }

    @Test
    public void testAnalyzePythonFile5() {
        PythonAnalysesResult result = PythonAnalyzer.analyzePythonProject(1,
                "ssh://*******************/wbqa/banma_fastdata_agent_copilot.git", "banma_fastdata_agent_copilot",
                "master", "master", "master", null);

        System.out.println(JacksonUtils.toJsonString(result, true));
    }

    @SneakyThrows
    @Test
    public void testPath() {
        System.out.println(Paths.get(this.getClass().getResource("/").toURI()).toAbsolutePath().toString());
    }
}