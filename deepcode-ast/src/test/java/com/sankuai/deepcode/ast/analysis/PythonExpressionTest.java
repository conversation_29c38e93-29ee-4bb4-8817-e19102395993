package com.sankuai.deepcode.ast.analysis;

import com.sankuai.deepcode.ast.model.python3.PythonAnalysesResult;
import com.sankuai.deepcode.ast.python3.analysis.PythonAnalyzer;
import lombok.SneakyThrows;
import org.testng.annotations.Test;

import java.nio.file.Path;
import java.nio.file.Paths;

public class PythonExpressionTest extends BaseTest {

    @SneakyThrows
    @Test
    public void testExpressionTypes() {
        PythonAnalysesResult result = new PythonAnalysesResult();

        Path classPath = Paths.get(PythonFuncCallVisitorTest.class.getResource("/").toURI().getPath() + "test_module");
        Path pythonFilePath = classPath.resolve("test_expressions.py");

        PythonAnalyzer.analyzePythonFile(classPath, pythonFilePath, result);

        System.out.println("=== 表达式类型测试结果 ===");
        System.out.println("模块级函数调用数量: " + result.getModuleMap().get("test_expressions").getFunctionCallNodes().size());

        // 打印所有函数调用
        result.getModuleMap().get("test_expressions").getFunctionCallNodes().forEach(call -> {
            System.out.println("函数调用: " + call.getRawCode() + " (第" + call.getStart().getLine() + "行)");
        });

        prettyPrint(result);
    }
}
