package com.sankuai.deepcode.ast.java.analysis;

import com.sankuai.deepcode.ast.model.java.JavaAnalysisRes;


class JavaAnalysisTest {

    public static void main(String[] args) throws Exception {
        String gitUrl = "ssh://*******************/bm/waimai_ad_platform_camp.git";
        String repos = "waimai_ad_platform_camp";
        String fromBranch = "master";
        String toBranch = "master";
        String buildBranch = "master";
        String buildCommit = "";
//        String gitUrl = "ssh://*******************/wbqa/houyi_lion_proxy_server.git";
//        String repos = "houyi_lion_proxy_server";
//        String fromBranch = "qa";
//        String toBranch = "master";
//        String buildBranch = "qa";
        JavaAnalysisRes javaAnalysisRes = JavaAnalysis.analysis(2L, gitUrl, repos, fromBranch, toBranch, buildBranch, buildCommit);
        System.out.println();
    }
}