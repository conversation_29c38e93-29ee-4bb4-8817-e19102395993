package com.sankuai.deepcode.ast.analysis;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.sankuai.deepcode.ast.python.test.TestPython3Visitor;
import lombok.SneakyThrows;

import java.nio.file.Path;
import java.nio.file.Paths;

/**
 * Package: com.sankuai.deepcode.ast.python3.analysis
 * Description:
 *
 * <AUTHOR>
 * @since 2025/1/13 16:18
 */
public class BaseTest {
    public void prettyPrint(Object object) {
        System.out.println(
                JSONObject.toJSONString(
                        object,
                        SerializerFeature.PrettyFormat,
                        SerializerFeature.WriteNonStringKeyAsString,
                        SerializerFeature.SortField,
                        SerializerFeature.MapSortField
                )
        );
    }

    @SneakyThrows
    public Path getResourcePath() {
        return Paths.get(TestPython3Visitor.class.getResource("/").toURI());
    }
}
