package com.sankuai.deepcode.ast.analysis;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.sankuai.deepcode.ast.model.python3.PythonAnalysesResult;
import com.sankuai.deepcode.ast.model.python3.PythonModuleNode;
import com.sankuai.deepcode.ast.python3.gen.PythonLexer;
import com.sankuai.deepcode.ast.python3.gen.PythonParser;
import com.sankuai.deepcode.ast.python3.visitor.PythonModuleVisitor;
import org.antlr.v4.runtime.CharStreams;
import org.antlr.v4.runtime.CommonTokenStream;
import org.antlr.v4.runtime.tree.ParseTree;
import org.testng.annotations.DataProvider;
import org.testng.annotations.Test;

/**
 * Package: com.sankuai.deepcode.ast.python3.analysis
 * Description:
 *
 * <AUTHOR>
 * @since 2025/1/13 14:48
 */
public class PythonImportVisitorTest extends BaseTest {
    PythonAnalysesResult pythonAnalysesResult = new PythonAnalysesResult();
    PythonModuleNode pythonModuleNode = new PythonModuleNode();

    {
        pythonModuleNode.setModuleName("test_module")
                .setFileName("test_module.py")
                .setParentModule("root");
    }

    @DataProvider
    public Object[][] dataProvider() {
        return new Object[][]{
                {"import xml.dom.pulldom, xml.dom.domreg"},
        };
    }




    @Test(dataProvider = "dataProvider")
    public void normalImportTest(String inputCode) {
        PythonLexer lexer = new PythonLexer(CharStreams.fromString(inputCode));
        CommonTokenStream tokens = new CommonTokenStream(lexer);
        PythonParser parser = new PythonParser(tokens);
        ParseTree tree = parser.file_input();

        PythonModuleVisitor moduleVisitor = new PythonModuleVisitor(pythonModuleNode, pythonAnalysesResult);
        moduleVisitor.visit(tree);
        pythonAnalysesResult.getModuleMap().put(pythonModuleNode.getModuleName(), pythonModuleNode);
        super.prettyPrint(pythonAnalysesResult);
    }
}
