package com.sankuai.deepcode.ast.typescript;

import com.sankuai.deepcode.ast.typescript.gen.TypeScriptLexer;
import com.sankuai.deepcode.ast.typescript.gen.TypeScriptParser;
import com.sankuai.deepcode.ast.typescript.visitor.MyTypeScriptVisitor;
import com.sankuai.deepcode.ast.util.FileUtil;
import org.antlr.v4.runtime.CharStreams;
import org.antlr.v4.runtime.CommonTokenStream;
import org.antlr.v4.runtime.atn.PredictionMode;
import org.antlr.v4.runtime.tree.ParseTree;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.Map;

public class TestVisitors {

    public static void main(String[] args) throws IOException {
        String reposPath = "/Users/<USER>/idea/git/yiyao_fe_operation_h5";
        Map<String, File> fileMap = FileUtil.getAllFiles(reposPath);
        long start = System.currentTimeMillis();
        int count = 0;
        for (Map.Entry<String, File> entry : fileMap.entrySet()) {
            if (entry.getKey().endsWith(".ts")
                    || entry.getKey().endsWith(".js")
                    || entry.getKey().endsWith(".jsx")
                    || entry.getKey().endsWith(".tsx")) {

//                if (entry.getKey().endsWith(".jsx")
//                        || entry.getKey().endsWith(".tsx")) {
//                    System.out.println();
//                }

                String cssFilePath = entry.getValue().getPath();
                if (entry.getValue().getPath().contains("node_modules")) {
                    continue;
                }
                if (entry.getValue().getPath().contains("/.")) {
                    continue;
                }
                count++;
                long start1 = System.currentTimeMillis();


                String code = new String(Files.readAllBytes(Paths.get(cssFilePath)));
                TypeScriptLexer lexer = new TypeScriptLexer(CharStreams.fromString(code));
                CommonTokenStream tokens = new CommonTokenStream(lexer);
                TypeScriptParser parser = new TypeScriptParser(tokens);
                parser.getInterpreter().setPredictionMode(PredictionMode.LL_EXACT_AMBIG_DETECTION);
                parser.setProfile(true); // 性能分析
                ParseTree tree = parser.program();

                System.out.println(entry.getValue().getPath() + " 1耗时:" + (System.currentTimeMillis() - start1));


                long start2 = System.currentTimeMillis();
                MyTypeScriptVisitor visitor = new MyTypeScriptVisitor();
                visitor.visit(tree);

                System.out.println(entry.getValue().getPath() + " 2耗时:" + (System.currentTimeMillis() - start2));


                System.out.println();
            }
        }
        System.out.println("耗时:" + (System.currentTimeMillis() - start));
        System.out.println();

    }
}
