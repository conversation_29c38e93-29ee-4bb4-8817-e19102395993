package com.sankuai.deepcode.ast.java.test;

import com.sankuai.deepcode.ast.java.gen.JavaLexer;
import com.sankuai.deepcode.ast.java.gen.JavaParser;
import com.sankuai.deepcode.ast.java.visitor.InitByClassNodes;
import com.sankuai.deepcode.ast.java.visitor.MyJavaCompilationUnitVisitor;
import com.sankuai.deepcode.ast.java.visitor.MyJavaLocalFieldsVisitor;
import com.sankuai.deepcode.ast.java.visitor.MyJavaMethodCallNewVisitor;
import com.sankuai.deepcode.ast.model.java.ClassNode;
import com.sankuai.deepcode.ast.model.java.FieldNode;
import com.sankuai.deepcode.ast.model.java.MethodNode;
import org.antlr.v4.runtime.CharStreams;
import org.antlr.v4.runtime.CommonTokenStream;
import org.antlr.v4.runtime.ParserRuleContext;
import org.antlr.v4.runtime.tree.ParseTree;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.*;

public class TestVisitor {


    public static void main(String[] args) throws IOException {

//        String javaFilePath = "/Users/<USER>/idea/tools/houyi_code_analysis_server/houyi_code_ast_common/src/test/java/com/sankuai/houyi/code/analysis/java/test/InClassTest.java";
//        String javaFilePath = "/Users/<USER>/idea/tools/houyi_code_analysis_server/houyi_code_ast_common/src/test/java/com/sankuai/houyi/code/analysis/java/test/AnnotationTest.java";
//        String javaFilePath = "/Users/<USER>/idea/tools/houyi_code_analysis_server/houyi_code_ast_common/src/test/java/com/sankuai/houyi/code/analysis/java/test/EnumTest.java";

//        String javaFilePath = "/Users/<USER>/idea/tools/houyi_code_analysis_server/houyi_code_ast_common/src/test/java/com/sankuai/houyi/code/analysis/java/test/GenericTest.java";
//        String javaFilePath = "/Users/<USER>/idea/tools/houyi_code_analysis_server/houyi_code_ast_common/src/test/java/com/sankuai/houyi/code/analysis/java/test/GenericInterfaceTest.java";
//        String javaFilePath = "/Users/<USER>/idea/tools/houyi_code_analysis_server/houyi_code_ast_common/src/main/java/com/sankuai/houyi/code/analysis/java/gen/JavaParserBaseVisitor.java";
//        String javaFilePath = "/Users/<USER>/idea/tools/houyi_code_analysis_server/houyi_code_ast_common/src/test/java/com/sankuai/houyi/code/analysis/java/test/InClassTest.java";
//        String javaFilePath = "/Users/<USER>/idea/git/banma_service_waybill_trans_server/src/main/java/com/sankuai/meituan/banma/thrift/waybill/trans/vo/PoiStockSnapshotVo.java";
//        String javaFilePath = "/Users/<USER>/idea/git/banma_service_waybill_trans_server/src/main/java/com/sankuai/meituan/banma/thrift/waybill/trans/vo/cons/GroupRelationChangeType.java";
//        String javaFilePath = "/Users/<USER>/idea/git/banma_service_waybill_trans_server/src/main/java/com/sankuai/meituan/banma/thrift/waybill/trans/shard/strategy/ModShardStrategy.java";
//        String javaFilePath = "/Users/<USER>/idea/git/banma_service_waybill_trans_server/src/main/java/com/sankuai/meituan/banma/thrift/waybill/trans/dao/BmWaybillDeliveredCallDao.java";
//        String javaFilePath = "/Users/<USER>/idea/git/banma_service_waybill_trans_server/src/main/java/com/sankuai/meituan/banma/thrift/waybill/trans/dao/BmPackageShardByIdDao.java";
        String javaFilePath ="/Users/<USER>/idea/git/banma_service_staff_api_server/src/main/java/com/sankuai/meituan/banma/driver/staff/common/DataResponse.java";

        String code = new String(Files.readAllBytes(Paths.get(javaFilePath)));
        JavaLexer lexer = new JavaLexer(CharStreams.fromString(code));
        CommonTokenStream tokens = new CommonTokenStream(lexer);
        JavaParser parser = new JavaParser(tokens);
        ParseTree tree = parser.compilationUnit();


        Map<String, ClassNode> classMap = new HashMap<>();
        Map<String, List<MethodNode>> classAndMethodMap = new HashMap<>();
        Set<String> classList = new HashSet<>();
        Map<String, List<FieldNode>> valueFieldMap = new HashMap<>();
        MyJavaCompilationUnitVisitor visitor = new MyJavaCompilationUnitVisitor(javaFilePath);
        visitor.visit(tree);

        for (ClassNode classNode : visitor.getClassNodes()) {
            InitByClassNodes.init(classNode, classMap, classAndMethodMap, classList, valueFieldMap);
        }

        Map<String, List<FieldNode>> nameFieldMap = new HashMap<>();

        for (ClassNode classNode : visitor.getClassNodes()) {
            Map<String, String> classFieldNameAndType = new HashMap<>();
            for (FieldNode fieldNode : classNode.getFieldNodes()) {
                classFieldNameAndType.put(fieldNode.getFieldName(), fieldNode.getFieldType());
            }
            for (MethodNode methodNode : classNode.getMethodNodes()) {
                MyJavaMethodCallNewVisitor myJavaMethodCallVisitor = new MyJavaMethodCallNewVisitor(classNode, classAndMethodMap, classList);
                myJavaMethodCallVisitor.setClassFieldNameAndType(classFieldNameAndType);
                for (ParserRuleContext parserRuleContext : methodNode.getMethodCtxs()) {
                    MyJavaLocalFieldsVisitor myJavaLocalFieldsVisitor = new MyJavaLocalFieldsVisitor(classNode, methodNode, nameFieldMap);
                    myJavaLocalFieldsVisitor.visit(parserRuleContext);
                    myJavaMethodCallVisitor.setLocalFields(myJavaLocalFieldsVisitor.getLocalFields());
                    myJavaMethodCallVisitor.visit(parserRuleContext);
                    methodNode.setInvokeMethods(myJavaMethodCallVisitor.getInvokeMethodNodes());
                    methodNode.setComplexity(myJavaLocalFieldsVisitor.getComplexity());
                }
                System.out.println();
            }
        }

        System.out.println();

//        classList.add(visitor.getClassNode().getClassName());
//        classAndMethodMap.put(visitor.getClassNode().getClassName(), visitor.getClassNode().getMethodNodes());
//        for (ClassNode inClassNode : visitor.getInClassNode()) {
//            classAndMethodMap.put(inClassNode.getClassName() + "$" + inClassNode.getInClassName(), inClassNode.getMethodNodes());
//        }
//        if (CollectionUtils.isNotEmpty(visitor.getClassNode().getFieldNodes())) {
//            for (FieldNode fieldNode : visitor.getClassNode().getFieldNodes()) {
//                if (null == valueFieldMap.get(fieldNode.getFieldName())) {
//                    List<FieldNode> fieldNodes = new ArrayList<>();
//                    fieldNodes.add(fieldNode);
//                    valueFieldMap.put(fieldNode.getFieldName(), fieldNodes);
//                } else {
//                    valueFieldMap.get(fieldNode.getFieldName()).add(fieldNode);
//                }
//            }
//        }
//        InitJavaCommon.initCommon(visitor, tokens);
//
//        init = false;
//        visitor = new MyCompilationUnitVisitor(javaFilePath, init, classAndMethodMap, classList, valueFieldMap);
//        visitor.visit(tree);

        System.out.println();
    }


}
