package com.sankuai.deepcode.ast.util;

import com.sankuai.deepcode.ast.model.base.CompareRes;

import java.util.HashSet;
import java.util.Set;

public class CompareUtilTest {

    public static void main(String[] args) throws Exception {
        String gitUrl = "ssh://*******************/wbqa/houyi_lion_proxy_server.git";
        String repos = "houyi_lion_proxy_server";
        String fromBranch = "master";
        String toBranch = "master";
        String buildBranch = "master";
        Set<String> filterPath = new HashSet<>();
        CompareRes compareRes = CompareUtil.getDiffInfos(1L, gitUrl, repos, fromBranch, toBranch, buildBranch, "");


        gitUrl = "ssh://*******************/wbqa/houyi_lion_proxy_server.git";
        repos = "houyi_lion_proxy_server";
        fromBranch = "commit2";
        toBranch = "master";
        buildBranch = "qa";
        filterPath = new HashSet<>();
        compareRes = CompareUtil.getDiffInfos(1L, gitUrl, repos, fromBranch, toBranch, buildBranch, "");

        System.out.println();
    }
}