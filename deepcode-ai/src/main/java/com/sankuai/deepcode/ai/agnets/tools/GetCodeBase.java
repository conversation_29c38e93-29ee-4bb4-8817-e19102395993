package com.sankuai.deepcode.ai.agnets.tools;

import com.alibaba.fastjson.JSON;
import com.google.common.reflect.TypeToken;
import com.sankuai.deepcode.ai.agnets.tool.DeepCodeToolBase;
import com.sankuai.deepcode.ai.agnets.tool.DeepCodeTool;
import com.sankuai.deepcode.ai.llm.model.chat.ChatParameter;
import com.sankuai.deepcode.ai.llm.model.chat.ChatPropertie;
import com.sankuai.deepcode.ast.enums.DiffTypeEnum;
import com.sankuai.deepcode.dao.domain.CodeClassAnalysis;
import com.sankuai.deepcode.dao.domain.CodeMethodAnalysis;
import com.sankuai.deepcode.dao.domain.CodeViewAnalysis;
import com.sankuai.deepcode.dao.service.*;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@DeepCodeTool(name = "getCode", description = "查询代码方法，支持通过方法（method）或文件(file)唯一vid查询源码")
public class GetCodeBase extends DeepCodeToolBase {

    @Autowired
    private CodeViewAnalysisService codeViewAnalysisService;
    @Autowired
    private CodeMethodAnalysisService codeMethodAnalysisService;
    @Autowired
    private CodeClassAnalysisService codeClassAnalysisService;

    @Override
    public void initParameter() {
        this.chatParameter = new ChatParameter();
        chatParameter.setType("object");
        List<String> required = new ArrayList<>();
        required.add("vidType");
        required.add("vid");
        chatParameter.setRequired(required);
        Map<String, ChatPropertie> properties = new HashMap<>();
        ChatPropertie vid = new ChatPropertie();
        vid.setType("vid");
        vid.setDescription("唯一vid");
        properties.put("vid", vid);
        ChatPropertie vidType = new ChatPropertie();
        vidType.setType("vidType");
        vidType.setDescription("vid类型，file为文件，method为方法，field为变量");
        properties.put("vidType", vidType);
        chatParameter.setProperties(properties);
    }

    @Override
    public String invoke() {
        String vid = this.chatParameter.getProperties().get("vid").getValue();
        String vidType = this.chatParameter.getProperties().get("vidType").getValue();
        long itemId = this.itemId;
        List<CodeViewAnalysis> codeViewAnalyses = new ArrayList<>();
        if (vidType.equals("file")) {
            codeViewAnalyses = codeViewAnalysisService.getAllContent(itemId, vid);
        } else if (vidType.equals("method")) {
            CodeMethodAnalysis codeMethodAnalysis = codeMethodAnalysisService.getByMethodVid(itemId, vid);
            if (null != codeMethodAnalysis) {
                CodeClassAnalysis codeClassAnalysis = codeClassAnalysisService.getByClassName(itemId, codeMethodAnalysis.getClassName());
                if (codeClassAnalysis != null) {
                    Type commentLinesType = new TypeToken<List<Integer>>() {
                    }.getType();
                    List<Integer> commentLines = JSON.parseObject(codeClassAnalysis.getCommentLines().toString(), commentLinesType);
                    int startLine = 0;
                    if (CollectionUtils.isNotEmpty(commentLines)) {
                        startLine = commentLines.get(0) > codeMethodAnalysis.getStartLine() ? commentLines.get(0) : codeMethodAnalysis.getStartLine();
                    }
                    codeViewAnalyses = codeViewAnalysisService.getByStartAndEnd(itemId, codeClassAnalysis.getFileVid(), startLine, codeMethodAnalysis.getEndLine());
                }
            }
        }
        StringBuffer stringBuffer = new StringBuffer();
        for (CodeViewAnalysis codeViewAnalysis : codeViewAnalyses) {
            if (codeViewAnalysis.getCodeType() == DiffTypeEnum.ADD.getCode()) {
                stringBuffer.append("+ ").append(codeViewAnalysis.getCodeLine() + " ").append(codeViewAnalysis.getCodeView()).append("\n");
            } else if (codeViewAnalysis.getCodeType() == DiffTypeEnum.DEL.getCode()) {
                stringBuffer.append("- ").append(codeViewAnalysis.getCodeLine() + " ").append(codeViewAnalysis.getCodeView()).append("\n");
            } else {
                stringBuffer.append("  ").append(codeViewAnalysis.getCodeLine() + " ").append(codeViewAnalysis.getCodeView()).append("\n");
            }
            if (stringBuffer.length() > 1024 * 16) {
                break;
            }
        }
        if (stringBuffer.length() == 0) {
            throw new RuntimeException("未查询到源码");
        } else {
            return stringBuffer.toString();
        }
    }

}
