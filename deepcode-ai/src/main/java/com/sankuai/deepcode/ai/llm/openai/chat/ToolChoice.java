package com.sankuai.deepcode.ai.llm.openai.chat;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.*;
import lombok.experimental.Accessors;

import static com.sankuai.deepcode.ai.llm.openai.chat.ToolType.FUNCTION;


/**
 * <AUTHOR>
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
@EqualsAndHashCode
@ToString
@Setter
@Getter
@Accessors(fluent = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ToolChoice {

    @JsonProperty
    @Builder.Default
    private ToolType type = FUNCTION;

    @JsonProperty
    private Function function;


    public static ToolChoice from(String functionName) {
        return builder().function(Function.builder().name(functionName).build()).build();
    }

}
