package com.sankuai.deepcode.ai.llm.openai.chat;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.*;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Setter
@Getter
@Accessors(fluent = true)
@NoArgsConstructor
@AllArgsConstructor
public class JsonArraySchema extends JsonSchemaElement {

    @JsonProperty
    private String description;

    @JsonProperty
    private JsonSchemaElement items;

    @Builder
    public JsonArraySchema(String type, String description, JsonSchemaElement items) {
        super(type);
        this.description = description;
        this.items = items;
    }
}
