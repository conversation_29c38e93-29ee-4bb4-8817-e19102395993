package com.sankuai.deepcode.ai.llm.openai.chat;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.fasterxml.jackson.databind.annotation.JsonPOJOBuilder;
import com.sankuai.deepcode.ai.llm.openai.shared.Usage;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;

import static java.util.Collections.unmodifiableList;

/**
 * <AUTHOR>
 */
@JsonDeserialize(builder = ChatCompletionResponse.Builder.class)
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
@EqualsAndHashCode
@ToString
@Setter
@Getter
@Accessors(fluent = true)
public class ChatCompletionResponse {

    @JsonProperty
    protected String id;

    @JsonProperty
    protected Integer created;

    @JsonProperty
    protected String model;

    @JsonProperty
    protected List<ChatCompletionChoice> choices;

    @JsonProperty
    protected String reasoningContent;

    @JsonProperty
    protected String content;

    @JsonProperty
    protected Usage usage;

    @JsonProperty
    protected String systemFingerprint;

    @JsonProperty
    protected String serviceTier;

    @JsonProperty("lastOne")
    protected Boolean lastOne;

    protected ChatCompletionResponse(Builder builder) {
        this.id = builder.id;
        this.created = builder.created;
        this.model = builder.model;
        this.choices = builder.choices;
        this.content = builder.content;
        this.usage = builder.usage;
        this.systemFingerprint = builder.systemFingerprint;
        this.serviceTier = builder.serviceTier;
        this.lastOne = builder.lastOne;
    }

    public String id() {
        return id;
    }

    public Integer created() {
        return created;
    }

    public String model() {
        return model;
    }

    public List<ChatCompletionChoice> choices() {
        return choices;
    }

    public Usage usage() {
        return usage;
    }

    public String systemFingerprint() {
        return systemFingerprint;
    }

    public String serviceTier() {
        return serviceTier;
    }

    public String content() {
        return content;
    }

    public Boolean lastOne() {
        return lastOne;
    }

    /**
     * Convenience method to get the content of the message from the first choice.
     */
    public String lastContent() {
        return CollectionUtils.isNotEmpty(choices()) ? choices().get(0).message().content() : "";
    }

    public String deltaReasoningContent() {
        return CollectionUtils.isNotEmpty(choices()) ? choices().get(0).delta().reasoningContent() :  "";
    }

    public static Builder builder() {
        return new Builder();
    }

    @JsonPOJOBuilder(withPrefix = "")
    @JsonIgnoreProperties(ignoreUnknown = true)
    @JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
    public static class Builder {

        protected String id;

        protected Integer created;

        protected String model;

        protected List<ChatCompletionChoice> choices;

        protected String content;

        protected Usage usage;

        protected String systemFingerprint;

        protected String serviceTier;

        @JsonProperty("lastOne")
        protected Boolean lastOne;

        protected Builder() {
        }

        public Builder id(String id) {
            this.id = id;
            return this;
        }

        public Builder created(Integer created) {
            this.created = created;
            return this;
        }

        public Builder model(String model) {
            this.model = model;
            return this;
        }

        public Builder choices(List<ChatCompletionChoice> choices) {
//            if (choices != null) {
//                this.choices = unmodifiableList(choices);
//            }
            this.choices = choices;
            return this;
        }

        public Builder content(String content) {
            this.content = content;
            return this;
        }

        public Builder usage(Usage usage) {
            this.usage = usage;
            return this;
        }

        public Builder systemFingerprint(String systemFingerprint) {
            this.systemFingerprint = systemFingerprint;
            return this;
        }

        public Builder serviceTier(String serviceTier) {
            this.serviceTier = serviceTier;
            return this;
        }

        public Builder lastOne(Boolean lastOne) {
            this.lastOne = lastOne;
            return this;
        }

        public ChatCompletionResponse build() {
            return new ChatCompletionResponse(this);
        }
    }
}
