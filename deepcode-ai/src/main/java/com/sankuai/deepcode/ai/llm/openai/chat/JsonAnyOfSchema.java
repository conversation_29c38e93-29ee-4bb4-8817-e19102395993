package com.sankuai.deepcode.ai.llm.openai.chat;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.*;
import lombok.experimental.Accessors;
import lombok.experimental.SuperBuilder;

import java.util.List;

/**
 * <AUTHOR>
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@JsonIgnoreProperties(ignoreUnknown = true)
@Accessors(fluent = true)
@Getter
@Setter
@NoArgsConstructor
public class JsonAnyOfSchema extends JsonSchemaElement {

    @JsonProperty
    private String description;

    @JsonProperty("anyOf")
    private List<JsonSchemaElement> anyOf;

    @Builder // 重写 Builder，处理父类参数
    public JsonAnyOfSchema(String type, String description, List<JsonSchemaElement> anyOf) {
        super(type);
        this.description = description;
        this.anyOf = anyOf;
    }
}
