package com.sankuai.deepcode.ai.prompt;

import com.sankuai.deepcode.ai.enums.DeepCodeSchema;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Package: com.sankuai.deepcode.ai.prompt
 * Description:
 *
 * <AUTHOR>
 * @since 2025/3/20 19:09
 */
public class PromptBuilder {
    private final StringBuilder content;
    private static final String NEW_LINE = System.lineSeparator();
    private static final String SPACE = " ";
    // 默认缩进空格数
    private static final int DEFAULT_SPACE_SIZE = 2;

    private PromptBuilder() {
        this.content = new StringBuilder();
    }

    private PromptBuilder(Object prompt) {
        prompt = prompt == null ? "" : prompt;
        this.content = new StringBuilder(prompt.toString());
    }

    public static PromptBuilder instance() {
        return new PromptBuilder();
    }

    public static PromptBuilder instance(Object prompt) {
        return new PromptBuilder(prompt);
    }


    public PromptBuilder append(Object prompt) {
        content.append(prompt);
        return this;
    }

    public PromptBuilder append(Object... prompts) {
        if (prompts == null) {
            return this;
        }
        for (Object prompt : prompts) {
            content.append(prompt);
        }
        return this;
    }

    public PromptBuilder append(PromptBuilder promptBuilder) {
        return append(promptBuilder.toString());
    }

    public PromptBuilder indent(PromptBuilder promptBuilder) {
        // 明确调用 indent(int, Object...) 方法
        return indent(DEFAULT_SPACE_SIZE, new Object[]{promptBuilder.toString()});
    }

    /**
     * 添加一行文本
     */
    public PromptBuilder line(Object text) {
        content.append(text);
        newLine();
        return this;
    }

    /**
     * 添加多行文本
     */
    public PromptBuilder lines(Object... lines) {
        for (Object line : lines) {
            line(line);
        }
        return this;
    }

    /**
     * 添加带缩进的文本，使用默认缩进空格数(2空格)
     */
    public PromptBuilder indent(Object... lines) {
        return indent(1, lines);
    }

    /**
     * 添加带缩进的文本，指定缩进级别（每级使用默认空格数）
     *
     * @param level 缩进级别，如level=2表示缩进两次(4空格)
     */
    public PromptBuilder indentLevel(int level, Object... lines) {
        return indent(level * DEFAULT_SPACE_SIZE, lines);
    }

    /**
     * 添加带指定空格数的缩进文本
     *
     * @param spaces 具体的空格数
     */
    public PromptBuilder indent(int spaces, Object... lines) {
        String indent = StringUtils.repeat(" ", Math.max(spaces, 0));
        for (Object line : lines) {
            content.append(indent).append(line);
            newLine();
        }
        return this;
    }

    /**
     * 添加分隔符
     */
    public PromptBuilder separator(String separator) {
        content.append(separator);
        newLine();
        return this;
    }

    /**
     * 添加指定数量的空行
     * 
     * @param count 空行数量
     */
    public PromptBuilder newLine(int count) {
        if (count <= 0) {
            return this;
        }
        content.append(StringUtils.repeat(NEW_LINE, count));
        return this;
    }
    
    /**
     * 添加空行
     */
    public PromptBuilder newLine() {
        return newLine(1);
    }

    /**
     * 添加代码块
     */
    public PromptBuilder codeBlock(String language, String... code) {
        return line("```" + language)
                .lines(code)
                .line("```")
                .newLine();
    }


    /**
     * 添加代码块
     *
     * @param language 代码语言
     * @param code     代码内容
     */
    public PromptBuilder codeBlock(CodeLanguage language, String... code) {
        return codeBlock(language.getValue(), code);
    }


    public PromptBuilder codeBlock(String language, Collection<String> code) {
        return codeBlock(language, code.toArray(new String[0]));
    }

    public PromptBuilder codeBlock(CodeLanguage language, Collection<String> code) {
        return codeBlock(language.getValue(), code.toArray(new String[0]));
    }

    /**
     * 添加列表项
     */
    public PromptBuilder listItem(Object... items) {
        for (Object item : items) {
            line("- " + item);
        }
        return this;
    }

    /**
     * 添加带编号的列表
     */
    public PromptBuilder numberedList(Object... items) {
        for (int i = 0; i < items.length; i++) {
            line((i + 1) + ". " + items[i]);
        }
        return this;
    }

    public PromptBuilder bold(Object text) {
        space().append("**").append(text).append("**").space();
        return this;
    }

    /**
     * 添加1级标题
     */
    public PromptBuilder h1(Object text) {
        return line("# " + text);
    }

    /**
     * 添加2级标题
     */
    public PromptBuilder h2(Object text) {
        return line("## " + text);
    }

    /**
     * 添加3级标题
     */
    public PromptBuilder h3(Object text) {
        return line("### " + text);
    }

    /**
     * 添加4级标题
     */
    public PromptBuilder h4(Object text) {
        return line("#### " + text);
    }

    /**
     * 添加5级标题
     */
    public PromptBuilder h5(Object text) {
        return line("##### " + text);
    }

    /**
     * 添加6级标题
     */
    public PromptBuilder h6(Object text) {
        return line("###### " + text);
    }


    /**
     * 添加指定级别的标题
     *
     * @param level 标题级别(1-6)
     * @param level 标题级别(1-4)
     * @param text  标题文本
     */
    public PromptBuilder heading(int level, Object text) {
        // 确保level在1-6之间
        level = Math.min(Math.max(level, 1), 6);
        String prefix = StringUtils.repeat("#", level);
        return line(prefix + " " + text);
    }

    /**
     * 添加表格
     *
     * @param data
     * @return
     */
    public PromptBuilder table(List<List<Object>> data) {
        if (CollectionUtils.isEmpty(data)) {
            return this;
        }

        // 获取第一行作为基准列数
        List<Object> header = data.get(0);
        int columnCount = header.size();

        // 表头
        line("|" + header.stream().map(Object::toString).collect(Collectors.joining("|")) + "|");

        // 分隔行
        StringBuilder separator = new StringBuilder("|");
        for (int i = 0; i < columnCount; i++) {
            separator.append("---|");
        }
        line(separator.toString());

        // 数据行
        for (int i = 1; i < data.size(); i++) {
            List<Object> row = new ArrayList<>(data.get(i));
            // 如果列数不够,补充空字符串
            while (row.size() < columnCount) {
                row.add("");
            }
            // 如果列数超出,截断多余的
            if (row.size() > columnCount) {
                row = row.subList(0, columnCount);
            }
            line("|" + row.stream().map(Object::toString).collect(Collectors.joining("|")) + "|");
        }

        return this;
    }

    public PromptBuilder deepcodeSchema(DeepCodeSchema schema, String content) {
        return line(":::DeepCode(" + schema.getName() + ")")
                .newLine()
                .line(content)
                .line(":::");
    }

    /**
     * 添加变量占位符
     */
    public PromptBuilder variable(String name) {
        content.append("${").append(name).append("}");
        return this;
    }

    /**
     * 添加带默认值的变量占位符
     */
    public PromptBuilder variable(String name, Object defaultValue) {
        content.append("${").append(name).append(":-").append(defaultValue).append("}");
        return this;
    }

    /**
     * 构建为PromptTemplate
     */
    public PromptTemplate toTemplate() {
        return new PromptTemplate(content.toString());
    }

    /**
     * 直接构建字符串
     */
    @Override
    public String toString() {
        return content.toString();
    }

    public String build() {
        return content.toString();
    }

    public PromptBuilder space() {
        return space(1);
    }

    public PromptBuilder space(int count) {
        if (count <= 0) {
            return this;
        }
        this.content.append(StringUtils.repeat(SPACE, count));
        return this;
    }

}
