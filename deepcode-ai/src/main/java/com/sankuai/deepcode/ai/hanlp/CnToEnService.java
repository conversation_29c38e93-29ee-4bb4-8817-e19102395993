package com.sankuai.deepcode.ai.hanlp;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.util.*;

@Service
public class CnToEnService {

    private static final Logger LOGGER = LoggerFactory.getLogger(CnToEnService.class);

    public static final Map<String, String> cn2en = new HashMap<>();

    @PostConstruct
    public void initWordMap() throws IOException {
        InputStream inputStream = getClass().getResourceAsStream("/cn-2-en.txt");
        if (inputStream == null) {
            throw new IllegalStateException("无法找到字典文件: cn-2-en.txt");
        }
        // 使用 BufferedReader 读取文件
        try (BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream, StandardCharsets.UTF_8))) {
            String line;
            while ((line = reader.readLine()) != null) {
                String[] split = line.split("=");
                String cn = split[0];
                String en = split[1];
                cn2en.put(cn, en);
            }
        }
    }

    public List<String> cn2en(List<String> ens) {
        List<String> result = new ArrayList<>();
        for (String en : ens) {
            String res = cn2en.get(en);
            if (StringUtils.isNotEmpty(res)) {
                result.add(res);
            }
        }
        return result;
    }

}
