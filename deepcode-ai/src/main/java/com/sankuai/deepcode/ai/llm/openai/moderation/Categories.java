package com.sankuai.deepcode.ai.llm.openai.moderation;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonSetter;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.fasterxml.jackson.databind.annotation.JsonPOJOBuilder;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * <AUTHOR>
 */
@JsonDeserialize(builder = Categories.Builder.class)
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
@EqualsAndHashCode
@ToString
public final class Categories {

    @JsonProperty
    private final Boolean hate;

    @JsonProperty("hate/threatening")
    private final Boolean hateThreatening;

    @JsonProperty("self-harm")
    private final Boolean selfHarm;

    @JsonProperty
    private final Boolean sexual;

    @JsonProperty("sexual/minors")
    private final Boolean sexualMinors;

    @JsonProperty
    private final Boolean violence;

    @JsonProperty("violence/graphic")
    private final Boolean violenceGraphic;

    private Categories(Builder builder) {
        this.hate = builder.hate;
        this.hateThreatening = builder.hateThreatening;
        this.selfHarm = builder.selfHarm;
        this.sexual = builder.sexual;
        this.sexualMinors = builder.sexualMinors;
        this.violence = builder.violence;
        this.violenceGraphic = builder.violenceGraphic;
    }

    public Boolean hate() {
        return hate;
    }

    public Boolean hateThreatening() {
        return hateThreatening;
    }

    public Boolean selfHarm() {
        return selfHarm;
    }

    public Boolean sexual() {
        return sexual;
    }

    public Boolean sexualMinors() {
        return sexualMinors;
    }

    public Boolean violence() {
        return violence;
    }

    public Boolean violenceGraphic() {
        return violenceGraphic;
    }

    public static Builder builder() {
        return new Builder();
    }

    @JsonPOJOBuilder(withPrefix = "")
    @JsonIgnoreProperties(ignoreUnknown = true)
    @JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
    public static final class Builder {

        private Boolean hate;

        private Boolean hateThreatening;

        private Boolean selfHarm;

        private Boolean sexual;

        private Boolean sexualMinors;

        private Boolean violence;

        private Boolean violenceGraphic;

        private Builder() {
        }

        public Builder hate(Boolean hate) {
            this.hate = hate;
            return this;
        }

        @JsonSetter("hate/threatening")
        public Builder hateThreatening(Boolean hateThreatening) {
            this.hateThreatening = hateThreatening;
            return this;
        }

        @JsonSetter("self-harm")
        public Builder selfHarm(Boolean selfHarm) {
            this.selfHarm = selfHarm;
            return this;
        }

        public Builder sexual(Boolean sexual) {
            this.sexual = sexual;
            return this;
        }

        @JsonSetter("sexual/minors")
        public Builder sexualMinors(Boolean sexualMinors) {
            this.sexualMinors = sexualMinors;
            return this;
        }

        public Builder violence(Boolean violence) {
            this.violence = violence;
            return this;
        }

        @JsonSetter("violence/graphic")
        public Builder violenceGraphic(Boolean violenceGraphic) {
            this.violenceGraphic = violenceGraphic;
            return this;
        }

        public Categories build() {
            return new Categories(this);
        }

    }

}
