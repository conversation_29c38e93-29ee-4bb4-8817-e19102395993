package com.sankuai.deepcode.ai.llm.openai.chat;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.*;
import lombok.experimental.Accessors;

import static com.sankuai.deepcode.ai.llm.openai.chat.Role.TOOL;

/**
 * <AUTHOR>
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
@EqualsAndHashCode
@Setter
@Getter
@Accessors(fluent = true)
@Builder
@ToString(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
public class ToolMessage implements Message {

    @JsonProperty
    @Builder.Default
    private Role role = TOOL;

    @JsonProperty
    private String toolCallId;

    @JsonProperty
    private String content;


    public static ToolMessage from(String toolCallId, String content) {
        return builder().toolCallId(toolCallId).content(content).build();
    }

}
