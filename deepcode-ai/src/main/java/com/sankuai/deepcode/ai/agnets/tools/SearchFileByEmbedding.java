package com.sankuai.deepcode.ai.agnets.tools;

import com.sankuai.deepcode.ai.agnets.tool.DeepCodeToolBase;
import com.sankuai.deepcode.ai.agnets.tool.DeepCodeTool;
import com.sankuai.deepcode.ai.embedding.model.SearchByTextRes;
import com.sankuai.deepcode.ai.embedding.model.SearchFile;
import com.sankuai.deepcode.ai.embedding.service.EmbeddingSearchService;
import com.sankuai.deepcode.ai.llm.model.chat.ChatMessage;
import com.sankuai.deepcode.ai.llm.model.chat.ChatMsgRes;
import com.sankuai.deepcode.ai.llm.service.OneApiService;
import com.sankuai.deepcode.dao.domain.CodeFileAnalysis;
import com.sankuai.deepcode.dao.service.CodeFileAnalysisService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 *
 */
@DeepCodeTool(name = "searchFileByEmbedding", description = "通过rag方式通过知识库多路召回问题关联的文件，用于查看哪些文件能够帮助回答问题")
public class SearchFileByEmbedding extends DeepCodeToolBase {

    @Autowired
    private CodeFileAnalysisService codeFileAnalysisService;
    @Autowired
    private OneApiService oneApiService;
    @Autowired
    private EmbeddingSearchService embeddingSearchService;

    @Override
    public void initParameter() {
        this.chatParameter = null;
    }

    @Override
    public String invoke() throws Exception {
        long itemId = this.itemId;
        String chatText = this.chatText;
        String model = this.model;
        SearchByTextRes searchByTextRes = embeddingSearchService.embeddingSearchService(itemId, chatText);
        List<ChatMessage> chatMessages = new ArrayList<>();
        ChatMessage system = new ChatMessage();
        system.setRole("system");
        StringBuilder sb = new StringBuilder();
        sb.append("请根据以下内容回答问题，最终答案要包含参考了哪些文件做了总结，同时保留文件路径和文件vid：\n");
        for (SearchFile searchFile : searchByTextRes.getSearchFiles()) {
            Optional<CodeFileAnalysis> codeFileAnalysisOpt = codeFileAnalysisService.getByItemAndVid(itemId, searchFile.getFileVid());
            if (codeFileAnalysisOpt.isPresent()) {
                CodeFileAnalysis codeFileAnalysis = codeFileAnalysisOpt.get();
                sb.append("##文件路径：" + codeFileAnalysis.getFilePath() + "(vid:" + codeFileAnalysis.getFileVid() + ")\n");
                if (StringUtils.isNotEmpty(searchFile.getDesc())) {
                    sb.append("文件描述：" + searchFile.getDesc() + "\n");
                }
                if (StringUtils.isNotEmpty(searchFile.getCode())) {
                    sb.append("文件代码：" + searchFile.getCode() + "\n");
                }
            }
        }
        system.setContent(sb.toString());

        ChatMessage user = new ChatMessage();
        user.setRole("user");
        user.setContent(chatText);
        chatMessages.add(system);
        chatMessages.add(user);
        String res = null;
        try {
            ChatMsgRes chatMsgRes = oneApiService.completions(chatMessages, model);
            res = chatMsgRes.getChoices().get(0).getMessage().getContent();
        } catch (Exception e) {
            throw new Exception("向量化后文档召回解析内容异常");
        }
        return res;
    }

}
