package com.sankuai.deepcode.ai.embedding.service;

import com.sankuai.deepcode.ai.embedding.model.EmbeddingsRes;
import com.sankuai.deepcode.ai.embedding.model.SearchByTextRes;
import com.sankuai.deepcode.ai.embedding.model.SearchFile;
import com.sankuai.deepcode.ai.enums.EmbeddingEnum;
import com.sankuai.deepcode.ai.enums.EmbeddingSearchTypeEnum;
import com.sankuai.deepcode.ai.hanlp.CnToEnService;
import com.sankuai.deepcode.ai.hanlp.HanlpService;
import com.sankuai.deepcode.dao.milvus.MilvusService;
import io.milvus.v2.service.vector.request.AnnSearchReq;
import io.milvus.v2.service.vector.request.data.EmbeddedText;
import io.milvus.v2.service.vector.request.data.FloatVec;
import io.milvus.v2.service.vector.response.QueryResp;
import io.milvus.v2.service.vector.response.SearchResp;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

@Service
public class EmbeddingSearchService {
    private static final Logger LOGGER = LoggerFactory.getLogger(EmbeddingSearchService.class);

    @Autowired
    private HanlpService hanlpService;
    @Autowired
    private EmbeddingService embeddingService;
    @Autowired
    private MilvusService milvusService;
    @Autowired
    private CnToEnService cnToEnService;

    public SearchByTextRes embeddingSearchService(long itemId, String text) {
        SearchByTextRes res = null;
        try {
            res = new SearchByTextRes();
            List<String> keyWords = hanlpService.processText(text);
            List<String> enTexts = cnToEnService.cn2en(hanlpService.segment(text));
            String enText = String.join(" ", enTexts);
            List<String> enWords = cnToEnService.cn2en(keyWords);
            res.setEnWords(enWords);
            res.setKeyWords(keyWords);
            String keyText = String.join(" ", keyWords);
            String enKeyText = String.join(" ", enWords);

            SearchResp cnCodeSearchResp = getSearchRespByText("item_code_" + itemId, "embedding", "code_embeddings", text, keyText);
            SearchResp enCodeSearchResp = getSearchRespByText("item_code_" + itemId, "embedding", "code_embeddings", enText, enKeyText);

            SearchResp cnDescSearchResp = getSearchRespByText("item_desc_" + itemId, "embedding", "desc_embeddings", text, keyText);
            SearchResp enDescSearchResp = getSearchRespByText("item_desc_" + itemId, "embedding", "desc_embeddings", enText, enKeyText);

            List<SearchResp.SearchResult> codeSearchResults = new ArrayList<>();
            codeSearchResults.addAll(cnCodeSearchResp.getSearchResults().get(0));
            codeSearchResults.addAll(enCodeSearchResp.getSearchResults().get(0));


            List<SearchResp.SearchResult> descSearchResults = new ArrayList<>();
            descSearchResults.addAll(cnDescSearchResp.getSearchResults().get(0));
            descSearchResults.addAll(enDescSearchResp.getSearchResults().get(0));

            List<SearchFile> searchKeyFiles = new ArrayList<>();
            Map<String, SearchFile> keyFileMap = new HashMap<>();
            for (SearchResp.SearchResult searchResult : codeSearchResults) {
                SearchFile searchFile = new SearchFile();
                searchFile.setFileVid((String) searchResult.getEntity().get("file_vid"));
                if (keyFileMap.containsKey(searchFile.getFileVid())) {
                    continue;
                }
                searchFile.getChunkIds().add((Integer) searchResult.getEntity().get("chunk_id"));
                searchFile.setStartLine((Integer) searchResult.getEntity().get("start_line"));
                searchFile.setEndLine((Integer) searchResult.getEntity().get("end_line"));
                searchFile.setCode((String) searchResult.getEntity().get("code"));
                searchFile.setScore(searchResult.getScore());
                searchFile.getSearchTypes().add(EmbeddingSearchTypeEnum.CODE.getName());
                searchKeyFiles.add(searchFile);
                keyFileMap.put(searchFile.getFileVid(), searchFile);
            }

            for (SearchResp.SearchResult searchResult : descSearchResults) {
                SearchFile searchFile = new SearchFile();
                searchFile.setFileVid((String) searchResult.getEntity().get("file_vid"));
                if (keyFileMap.containsKey(searchFile.getFileVid())) {
                    continue;
                }
                searchFile.setDesc((String) searchResult.getEntity().get("desc"));
                searchFile.setScore(searchResult.getScore());
                searchFile.getSearchTypes().add(EmbeddingSearchTypeEnum.DESC.getName());
                if (keyFileMap.containsKey(searchFile.getFileVid())) {
                    keyFileMap.get(searchFile.getFileVid()).getSearchTypes().addAll(searchFile.getSearchTypes());
                    keyFileMap.get(searchFile.getFileVid()).setDesc(searchFile.getDesc());
                } else {
                    searchKeyFiles.add(searchFile);
                }
            }
            Collections.sort(searchKeyFiles);
            searchKeyFiles = searchKeyFiles.subList(0, Math.min(10, searchKeyFiles.size()));
            res.setSearchFiles(searchKeyFiles);

        } catch (Exception e) {
            LOGGER.error("embeddingSearchService 异常 e:", e);
        }
        return res;
    }


    public SearchResp getSearchRespByText(String collectionName, String annVectorFieldName, String annDataFieldName, String text, String keyText) {
        EmbeddingsRes embeddingsRes = embeddingService.embeddings(text, EmbeddingEnum.JINA_V3.getName());
        List<Float> queryVector = embeddingsRes.getData().get(0).getEmbedding();
        List<Float> floatDataVector = new ArrayList<>();
        for (Float value : queryVector) {
            floatDataVector.add(value);
        }
        FloatVec floatVectorField = new FloatVec(floatDataVector);

        EmbeddingsRes keyEmbeddingsRes = embeddingService.embeddings(keyText, EmbeddingEnum.JINA_V3.getName());
        List<Float> keyQueryVector = keyEmbeddingsRes.getData().get(0).getEmbedding();
        List<Float> keyFloatDataVector = new ArrayList<>();
        for (Float value : keyQueryVector) {
            keyFloatDataVector.add(value);
        }
        FloatVec keyFLoatVectorField = new FloatVec(keyFloatDataVector);

        List<AnnSearchReq> searchRequests = new ArrayList<>();
        AnnSearchReq code = AnnSearchReq
                .builder()
                .vectorFieldName(annVectorFieldName)
                .vectors(Collections.singletonList(floatVectorField))
                .topK(10)
                .build();
        searchRequests.add(code);

        AnnSearchReq codeKey = AnnSearchReq
                .builder()
                .vectorFieldName(annVectorFieldName)
                .vectors(Collections.singletonList(keyFLoatVectorField))
                .topK(10)
                .build();
        searchRequests.add(codeKey);

        AnnSearchReq code_embeddings = AnnSearchReq
                .builder()
                .vectorFieldName(annDataFieldName)
                .vectors((Collections.singletonList(new EmbeddedText(text))))
                .topK(10)
                .build();
        searchRequests.add(code_embeddings);

        AnnSearchReq keyCode_embeddings = AnnSearchReq
                .builder()
                .vectorFieldName(annDataFieldName)
                .vectors((Collections.singletonList(new EmbeddedText(keyText))))
                .topK(10)
                .build();
        searchRequests.add(keyCode_embeddings);
        SearchResp searchResp = null;
        if (collectionName.startsWith("item_code_")) {
            searchResp = milvusService.codeHybridSearch(collectionName, "partition0", searchRequests);
        } else {
            searchResp = milvusService.descHybridSearch(collectionName, "partition0", searchRequests);
        }
        return searchResp;
    }


    public String getCodeDescByFileVid(long itemId, String fileVid) {
        String desc = null;
        try {
            QueryResp queryResp = milvusService.query("item_desc_" + itemId, "partition0", fileVid);
            if (CollectionUtils.isNotEmpty(queryResp.getQueryResults())) {
                QueryResp.QueryResult queryResult = queryResp.getQueryResults().get(0);
                desc = String.valueOf(queryResult.getEntity().get("desc"));
            }
            return desc;
        } catch (Exception e) {
            LOGGER.error("getCodeDescByFileVid 异常 e:", e);
        }
        return desc;
    }

}
