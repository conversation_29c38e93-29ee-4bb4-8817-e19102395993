package com.sankuai.deepcode.ai.embedding.model;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class SearchFile implements Comparable<SearchFile> {
    private String fileVid;
    private List<Integer> chunkIds = new ArrayList<>();
    private int startLine;
    private int endLine;
    private String code;
    private String desc;
    private double score;
    private List<String> searchTypes = new ArrayList<>();

    @Override
    public int compareTo(SearchFile other) {
        // 降序排序
        return Double.compare(other.score, this.score);
    }
}
