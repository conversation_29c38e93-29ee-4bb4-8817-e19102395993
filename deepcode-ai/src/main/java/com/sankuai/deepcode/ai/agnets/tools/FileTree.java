package com.sankuai.deepcode.ai.agnets.tools;

import com.alibaba.fastjson.JSON;
import com.sankuai.deepcode.ai.agnets.tool.DeepCodeToolBase;
import com.sankuai.deepcode.ai.agnets.tool.DeepCodeTool;
import com.sankuai.deepcode.ai.llm.model.chat.ChatMessage;
import com.sankuai.deepcode.ai.llm.model.chat.ChatMsgRes;
import com.sankuai.deepcode.ai.llm.service.OneApiService;
import com.sankuai.deepcode.dao.domain.CodeFileAnalysis;
import com.sankuai.deepcode.dao.service.CodeFileAnalysisService;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.*;

/**
 * <AUTHOR>
 */
@DeepCodeTool(name = "fileTree", description = "获取当前仓库文件树，用于查看哪些文件能够帮助回答问题")
public class FileTree extends DeepCodeToolBase {

    @Autowired
    private CodeFileAnalysisService codeFileAnalysisService;
    @Autowired
    private OneApiService oneApiService;

    @Override
    public void initParameter() {
        this.chatParameter = null;
    }

    @Override
    public String invoke() throws Exception {
        long itemId = this.itemId;
        String chatText = this.chatText;
        String model = this.model;
        List<CodeFileAnalysis> codeFileAnalyses = codeFileAnalysisService.getByItemId(itemId);
        Map<String, String> fileTreeMap = new HashMap<>();
        for (CodeFileAnalysis codeFileAnalysis : codeFileAnalyses) {
            fileTreeMap.put(codeFileAnalysis.getFilePath(), codeFileAnalysis.getFileVid());
            if (fileTreeMap.toString().length() > 1024 * 8) {
                if (codeFileAnalysis.getFilePath().toLowerCase(Locale.ROOT).contains("readme")
                        || codeFileAnalysis.getFilePath().toLowerCase(Locale.ROOT).endsWith(".md")) {
                    fileTreeMap.put(codeFileAnalysis.getFilePath(), codeFileAnalysis.getFileVid());
                }
            }
        }
        List<ChatMessage> chatMessages = new ArrayList<>();
        ChatMessage chatMessage = new ChatMessage();
        chatMessage.setRole("user");
        chatMessage.setContent("已知当前工程文件目录树如下，分别为文件全路径及其唯一vid："
                + JSON.toJSONString(fileTreeMap.toString())
                + "\n" +
                "##回答问题:\"" + chatText + "\"可以参考哪些文件，选择其中最可能用到的5个文件" +
                "，要同时保留文件名和文件唯一vid");

        chatMessages.add(chatMessage);
        String res = null;
        try {
            ChatMsgRes chatMsgRes = oneApiService.completions(chatMessages, model);
            res = chatMsgRes.getChoices().get(0).getMessage().getContent();
        } catch (Exception e) {
            throw new Exception("读取文件目录树异常");
        }
        return res;
    }

}
