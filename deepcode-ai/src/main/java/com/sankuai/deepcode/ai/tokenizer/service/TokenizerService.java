package com.sankuai.deepcode.ai.tokenizer.service;


import com.sankuai.deepcode.ai.tokenizer.model.TokenLength;
import javafx.util.Pair;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.util.*;

@Service
public class TokenizerService {

    private static class TrieNode {
        Map<Integer, TrieNode> children = new HashMap<>();
        boolean isEnd;
    }

    private final TrieNode root = new TrieNode();
    private final Map<ByteArrayWrapper, Integer> tokenRank = new HashMap<>();

    @PostConstruct
    public void initToken() throws IOException {
        InputStream inputStream = getClass().getResourceAsStream("/cl100k_base.tiktoken");
        if (inputStream == null) {
            throw new IllegalStateException("无法找到词表文件: cl100k_base.tiktoken");
        }

        List<Pair<byte[], Integer>> tokens = new ArrayList<>();
        // 使用 BufferedReader 读取文件
        try (BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream, StandardCharsets.UTF_8))) {
            String line;
            while ((line = reader.readLine()) != null) {
                String[] parts = line.split(" ");
                if (parts.length == 2) {
                    byte[] token = Base64.getDecoder().decode(parts[0]);
                    int rank = Integer.parseInt(parts[1]);
                    tokens.add(new Pair<>(token, rank));
                }
            }
        }

        // 按token长度降序排序
        tokens.sort((a, b) -> Integer.compare(b.getKey().length, a.getKey().length));

        // 构建字典树
        for (Pair<byte[], Integer> entry : tokens) {
            byte[] token = entry.getKey();
            TrieNode current = root;
            for (byte b : token) {
                current = current.children.computeIfAbsent((int) b & 0xFF, k -> new TrieNode());
            }
            current.isEnd = true;
            tokenRank.put(new ByteArrayWrapper(token), entry.getValue());
        }
    }


    public int countTokens(String text) {
        byte[] bytes = text.getBytes(StandardCharsets.UTF_8);
        int count = 0;
        int pos = 0;

        while (pos < bytes.length) {
            int maxLength = 0;
            TrieNode current = root;

            // 查找最长匹配
            for (int i = pos; i < bytes.length; i++) {
                int byteVal = bytes[i] & 0xFF;
                current = current.children.get(byteVal);
                if (current == null) break;
                if (current.isEnd) {
                    maxLength = i - pos + 1;
                }
            }

            if (maxLength > 0) {
                pos += maxLength;
            } else {
                pos++; // 处理未知字符
            }
            count++;
        }
        return count;
    }


    //计算token长度  如果超过最大长度记录该长度的字符下标
    public TokenLength countTokensAndMax(String text, int max) {
        TokenLength tokenLength = new TokenLength();
        tokenLength.setMaxToken(max);
        if (text == null || text.isEmpty()) {
            return tokenLength;
        }

        byte[] bytes = text.getBytes(StandardCharsets.UTF_8);
        int count = 0;
        int pos = 0;
        int startPos = 0;  // 记录当前token开始时对应的字符串位置

        while (pos < bytes.length) {
            int maxLength = 0;
            TrieNode current = root;

            // 查找最长匹配
            for (int i = pos; i < bytes.length; i++) {
                int byteVal = bytes[i] & 0xFF;
                current = current.children.get(byteVal);
                if (current == null) break;
                if (current.isEnd) {
                    maxLength = i - pos + 1;
                }
            }

            // 更新位置和计数
            if (maxLength > 0) {
                pos += maxLength;
            } else {
                // 处理未知字符
                byte[] unknownBytes = Arrays.copyOfRange(bytes, pos, Math.min(pos + 3, bytes.length));
                try {
                    new String(unknownBytes, StandardCharsets.UTF_8);
                    pos += unknownBytes.length;
                } catch (Exception e) {
                    pos++;
                }
            }
            count++;

            // 更新字符位置
            if (count == max && tokenLength.getMaxStrIndex() == -1) {
                // 找到第一个超过max的位置，记录字符串的实际下标
                // 计算当前位置对应的字符串索引
                String currentText = new String(bytes, 0, pos, StandardCharsets.UTF_8);
                startPos = currentText.length();
                tokenLength.setMaxStrIndex(startPos);
            }
        }

        tokenLength.setTokenLength(count);
        return tokenLength;
    }


}
