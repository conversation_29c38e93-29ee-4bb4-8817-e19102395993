package com.sankuai.deepcode.ai.llm.openai.chat;

import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * <AUTHOR>
 */

public enum Role {
    @JsonProperty("system")
    SYSTEM,
    @<PERSON>sonProperty("user")
    USER,
    @<PERSON><PERSON><PERSON>roper<PERSON>("assistant")
    ASSISTANT,
    @JsonProperty("tool")
    TOOL,
    @JsonProperty("function")
    @Deprecated
    FUNCTION,
    ;

    public static Role from(String role) {
        role = role.toLowerCase();
        switch (role) {
            case "system":
                return Role.SYSTEM;
            case "user":
                return Role.USER;
            case "assistant":
                return Role.ASSISTANT;
            case "tool":
                return Role.TOOL;
            case "function":
                return Role.FUNCTION;
            default:
                throw new IllegalArgumentException("Invalid role: " + role);
        }
    }
}
