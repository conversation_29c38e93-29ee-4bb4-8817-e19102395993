package com.sankuai.deepcode.ai.llm.oneapi;

import com.sankuai.deepcode.ai.llm.openai.LogLevel;
import com.sankuai.deepcode.ai.llm.openai.chat.ChatCompletionModel;
import lombok.Data;

import java.net.Proxy;


/**
 * 公共配置
 *
 * <AUTHOR>
 * @since 2025/2/16 15:15
 */
@Data
public class OneApiConfig {

    /**
     * 基本 URL
     */
    protected String baseUrl = "https://oneapi.sankuai.com/v1";

    /**
     * API 密钥
     */
    protected String apiKey;

    /**
     * 搜索 API 密钥
     */
    protected String searchApiKey;

    /**
     * 模型名称
     */
    protected String model = ChatCompletionModel.DEEPSEEK_REASONER.getValue();

    protected Integer maxTokens;

    /**
     * 默认系统提示词
     */
    protected boolean defaultSystemPrompt = true;

    /**
     * 日志请求
     */
    protected boolean logRequests;

    /**
     * 日志响应
     */
    protected boolean logResponses;

    /**
     * 代理
     */
    protected Proxy proxy;

    /**
     * 连接超时，单位： S
     */
    protected Integer connectTimeout;

    /**
     * 读取超时 单位： S
     */
    protected Integer readTimeout;

    /**
     * 呼叫超时 单位： S
     */
    protected Integer callTimeout;

    /**
     * 日志级别
     */
    protected LogLevel logLevel = LogLevel.DEBUG;

}
