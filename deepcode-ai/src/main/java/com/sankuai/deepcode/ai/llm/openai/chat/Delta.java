package com.sankuai.deepcode.ai.llm.openai.chat;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.*;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR>
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
@EqualsAndHashCode
@JsonIgnoreProperties(ignoreUnknown = true)
@Accessors(fluent = true)
@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ToString
public class Delta {

    @JsonProperty
    @Builder.Default
    private Role role = Role.ASSISTANT;

    @JsonProperty
    private String content;

    @JsonProperty
    private String processingContent;

    @JsonProperty
    private String reasoningContent;

    @JsonProperty
    private List<ToolCall> toolCalls;

    @JsonProperty
    @Deprecated
    private FunctionCall functionCall;


}
