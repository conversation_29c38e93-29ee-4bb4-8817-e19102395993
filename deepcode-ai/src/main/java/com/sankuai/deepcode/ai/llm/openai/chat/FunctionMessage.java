package com.sankuai.deepcode.ai.llm.openai.chat;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.*;
import lombok.experimental.Accessors;

import static com.sankuai.deepcode.ai.llm.openai.chat.Role.FUNCTION;


/**
 * <AUTHOR>
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
@Deprecated
@EqualsAndHashCode
@JsonIgnoreProperties(ignoreUnknown = true)
@Accessors(fluent = true)
@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ToString
public class FunctionMessage implements Message {

    @JsonProperty
    @Builder.Default
    private Role role = FUNCTION;

    @JsonProperty
    private String name;

    @JsonProperty
    private String content;


    @Deprecated
    public static FunctionMessage from(String name, String content) {
        return FunctionMessage.builder().name(name).content(content).build();
    }

}
