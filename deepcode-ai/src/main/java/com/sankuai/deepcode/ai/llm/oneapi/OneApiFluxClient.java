package com.sankuai.deepcode.ai.llm.oneapi;

import com.sankuai.deepcode.ai.llm.openai.FluxClient;
import com.sankuai.deepcode.ai.llm.openai.SyncRequestExecutor;
import com.sankuai.deepcode.ai.llm.openai.Utils;
import com.sankuai.deepcode.ai.llm.openai.chat.ChatCompletionRequest;
import com.sankuai.deepcode.ai.llm.openai.chat.ChatCompletionResponse;
import com.sankuai.deepcode.ai.llm.openai.search.SearchRequest;
import com.sankuai.deepcode.ai.llm.openai.search.SearchResponse;
import reactor.core.publisher.Flux;

import java.util.Objects;

/**
 * Package: com.sankuai.deepcode.ai.llm.openai
 * Description:
 *
 * <AUTHOR>
 * @since 2025/3/19 19:37
 */
public class OneApiFluxClient extends OneApiClient implements FluxClient {
    public OneApiFluxClient(FluxBuilder builder) {
        super(builder);
    }

    public static FluxBuilder builder() {
        return new FluxBuilder();
    }

    public static class FluxBuilder extends SearchApiBuilder<OneApiFluxClient, FluxBuilder> {
        @Override
        public OneApiFluxClient build() {
            return new OneApiFluxClient(this);
        }
    }


    @Override
    public Flux<ChatCompletionResponse> chatFluxCompletion(ChatCompletionRequest request) {
        ChatCompletionRequest.Builder builder = ChatCompletionRequest.builder().from(request);
        if (Objects.isNull(request.model())) {
            builder.model(this.getModel());
        }

        return Flux.create(emitter -> {
            this.chatCompletion(new OpenAiClientContext(), builder.build()).onPartialResponse(emitter::next)
                    .onComplete(emitter::complete).onError(emitter::error).execute();
        });
    }

    @Override
    public Flux<ChatCompletionResponse> chatFluxCompletion(String userMessage) {
        ChatCompletionRequest.Builder builder = ChatCompletionRequest.builder();

        if (Objects.nonNull(this.getSystemMessage())) {
            builder.addSystemMessage(this.getSystemMessage());
        }

        if (Objects.nonNull(this.getModel())) {
            builder.model(this.getModel());
        }

        builder.addUserMessage(userMessage);
        return Flux.create(emitter -> {
            this.chatCompletion(new OpenAiClientContext(), builder.build()).onPartialResponse(emitter::next)
                    .onComplete(emitter::complete).onError(emitter::error).execute();
        });
    }


    @Override
    public Flux<ChatCompletionResponse> chatSearchFluxCompletion(String userMessage) {
        SearchResponse searchResponse = new SyncRequestExecutor<>(
                this.getSearchApi().webSearch(SearchRequest.builder().enable(true).query(userMessage).build()),
                searchResponse1 -> searchResponse1).execute();
        if (200 == searchResponse.getCode()) {
            String formatted = Utils.format(userMessage, searchResponse.getData().getWebPages().getValue());
            if (Objects.nonNull(formatted)) {
                return this.chatFluxCompletion(formatted);
            }
            return this.chatFluxCompletion(userMessage);
        }

        return this.chatFluxCompletion(userMessage);
    }


    @Override
    public Flux<ChatCompletionResponse> chatSearchFluxCompletion(String userMessage, SearchRequest searchRequest) {

        if (Objects.isNull(searchRequest.getQuery())) {
            searchRequest.setQuery(userMessage);
        }

        SearchResponse searchResponse = new SyncRequestExecutor<>(this.getSearchApi().webSearch(searchRequest),
                searchResponse1 -> searchResponse1).execute();
        if (200 == searchResponse.getCode()) {
            String formatted = Utils.format(userMessage, searchResponse.getData().getWebPages().getValue());
            if (Objects.nonNull(formatted)) {
                return this.chatFluxCompletion(formatted);
            }
            return this.chatFluxCompletion(userMessage);
        }

        return this.chatFluxCompletion(userMessage);
    }

}
