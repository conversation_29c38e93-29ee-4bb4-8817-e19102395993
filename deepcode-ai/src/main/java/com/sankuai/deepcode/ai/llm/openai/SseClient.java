package com.sankuai.deepcode.ai.llm.openai;

import com.sankuai.deepcode.ai.llm.openai.chat.ChatCompletionRequest;
import com.sankuai.deepcode.ai.llm.openai.search.SearchRequest;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

/**
 * Package: com.sankuai.deepcode.ai.llm.openai
 * Description:
 *
 * <AUTHOR>
 * @since 2025/3/19 19:50
 */
public interface SseClient {
    SseEmitter chatSseCompletion(ChatCompletionRequest request);

    SseEmitter chatSseCompletion(String userMessage);

    SseEmitter chatSearchSseCompletion(String userMessage);

    SseEmitter chatSearchSseCompletion(String userMessage, SearchRequest searchRequest);
}
