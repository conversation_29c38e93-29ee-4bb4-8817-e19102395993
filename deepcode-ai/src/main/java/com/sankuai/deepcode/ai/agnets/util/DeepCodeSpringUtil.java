package com.sankuai.deepcode.ai.agnets.util;

import com.sankuai.deepcode.ai.agnets.tool.DeepCodeTool;
import com.sankuai.deepcode.ai.llm.model.chat.ChatFunction;
import com.sankuai.deepcode.ai.llm.model.chat.ChatParameter;
import com.sankuai.deepcode.ai.llm.model.chat.ChatTool;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

import java.lang.reflect.Field;
import java.util.HashMap;
import java.util.Map;

@Component
public class DeepCodeSpringUtil implements ApplicationContextAware {
    private static final Logger LOGGER = LoggerFactory.getLogger(DeepCodeSpringUtil.class);
    private static ApplicationContext applicationContext;
    private static Map<String, ChatTool> tools = new HashMap<>();

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        if (DeepCodeSpringUtil.applicationContext == null) {
            DeepCodeSpringUtil.applicationContext = applicationContext;
        }
        LOGGER.info("ApplicationContext配置成功");
    }

    public static ApplicationContext getApplicationContext() {
        return applicationContext;
    }

    public static Object getBean(String name) {
        return getApplicationContext().getBean(name);
    }

    public static <T> T getBean(Class<T> clazz) {
        return getApplicationContext().getBean(clazz);
    }

    public static <T> T getBean(String name, Class<T> clazz) {
        return getApplicationContext().getBean(name, clazz);
    }

    // 添加上下文就绪事件监听
    @EventListener(ContextRefreshedEvent.class)
    public void initToolsAfterStartup() {
        initTools();
    }

    public void initTools() {
        String[] beanNames = DeepCodeSpringUtil.getApplicationContext().getBeanDefinitionNames();
        for (String beanName : beanNames) {
            Object bean = DeepCodeSpringUtil.getBean(beanName);
            if (bean != null) {
                if (bean.getClass().getAnnotation(DeepCodeTool.class) != null) {
                    ChatTool chatTool = new ChatTool();
                    chatTool.setType("function");
                    ChatFunction chatFunction = new ChatFunction();
                    DeepCodeTool deepCodeTool = bean.getClass().getAnnotation(DeepCodeTool.class);
                    chatFunction.setName(deepCodeTool.name());
                    chatFunction.setDescription(deepCodeTool.description());
                    try {
                        Field field = bean.getClass().getSuperclass().getDeclaredField("chatParameter");
                        if (field != null) {
                            ChatParameter chatParameter = (ChatParameter) field.get(bean);
                            chatFunction.setParameters(chatParameter);
                        }
                    } catch (Exception e) {
//                        throw new RuntimeException(e);
                    }
                    chatTool.setFunction(chatFunction);
                    tools.put(beanName, chatTool);
                }
            }
        }
    }

    public static Map<String, ChatTool> getTools() {
        return tools;
    }
}
