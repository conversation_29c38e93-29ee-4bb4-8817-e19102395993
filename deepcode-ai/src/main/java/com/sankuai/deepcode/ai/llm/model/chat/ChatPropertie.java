package com.sankuai.deepcode.ai.llm.model.chat;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class ChatPropertie {
    private String type;
    private ChatPropertieItmes items;
    private String description;

    private String value;

    public ChatPropertie stringType() {
        this.type = "string";
        return this;
    }

    public ChatPropertie numberType() {
        this.type = "number";
        return this;
    }

    public ChatPropertie integerType() {
        this.type = "integer";
        return this;
    }

    public ChatPropertie booleanType() {
        this.type = "boolean";
        return this;
    }

    public ChatPropertie objectType() {
        this.type = "object";
        return this;
    }

    /**
     * 数组类型
     * 如果类型是数组时， 需要指定数组中元素的类型
     *
     * @param itemType 数组中元素的类型
     * @return ChatPropertie
     */
    public ChatPropertie arrayType(String itemType) {
        this.type = "array";
        this.items = new ChatPropertieItmes();
        this.items.setType(itemType);
        return this;
    }

    public ChatPropertie nullType() {
        this.type = "null";
        return this;
    }
}
