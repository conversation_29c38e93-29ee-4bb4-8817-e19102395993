package com.sankuai.deepcode.ai.llm.openai.chat;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.*;
import lombok.experimental.Accessors;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Getter
@Setter
@Accessors(fluent = true)
@NoArgsConstructor
@AllArgsConstructor
public class JsonObjectSchema extends JsonSchemaElement {

    @JsonProperty
    private String description;

    @JsonProperty
    private Map<String, JsonSchemaElement> properties;

    @JsonProperty
    private List<String> required;

    @JsonProperty("additionalProperties")
    private Boolean additionalProperties;

    @JsonProperty("$defs")
    private Map<String, JsonSchemaElement> definitions;

    @Builder
    public JsonObjectSchema(String type, String description, Map<String, JsonSchemaElement> properties, List<String> required, Boolean additionalProperties, Map<String, JsonSchemaElement> definitions) {
        super(type);
        this.description = description;
        this.properties = properties;
        this.required = required;
        this.additionalProperties = additionalProperties;
        this.definitions = definitions;
    }
}
