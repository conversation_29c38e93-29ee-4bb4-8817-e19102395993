package com.sankuai.deepcode.ai.enums;

public enum EmbeddingEnum {
    JINA_V3(0, "jina-embeddings-v3", 8 * 1024);

    private int code;
    private String name;
    private int context;

    EmbeddingEnum(int code, String name, int context) {
        this.code = code;
        this.name = name;
        this.context = context;
    }

    public int getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public int getContext() {
        return context;
    }

    @Override
    public String toString() {
        return "EmbeddingEnum{" +
                "code=" + code +
                ", name='" + name + '\'' +
                ", context=" + context +
                '}';
    }
}
