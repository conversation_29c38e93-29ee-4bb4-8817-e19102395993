package com.sankuai.deepcode.ai.llm.openai.chat;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.*;
import lombok.experimental.Accessors;

import static com.sankuai.deepcode.ai.llm.openai.chat.Role.SYSTEM;


/**
 * <AUTHOR>
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
@EqualsAndHashCode
@ToString(callSuper = true)
@Builder
@Setter
@Getter
@Accessors(fluent = true)
@NoArgsConstructor
@AllArgsConstructor
public final class SystemMessage implements Message {

    @JsonProperty
    private Role role = SYSTEM;

    @JsonProperty
    private String content;

    @JsonProperty
    private String name;

    public static SystemMessage from(String content) {
        return SystemMessage.builder().content(content).build();
    }
}
