package com.sankuai.deepcode.ai.agnets.tools;

import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.deepcode.ai.agnets.tool.DeepCodeTool;
import com.sankuai.deepcode.ai.agnets.tool.DeepCodeToolBase;
import com.sankuai.deepcode.ai.enums.DeepCodeSchema;
import com.sankuai.deepcode.ai.llm.model.chat.ChatParameter;
import com.sankuai.deepcode.ai.llm.model.chat.ChatPropertie;
import com.sankuai.deepcode.ai.prompt.PromptBuilder;
import com.sankuai.deepcode.commons.JacksonUtils;
import com.sankuai.deepcode.dao.domain.CodeAnalysisItem;
import com.sankuai.deepcode.dao.domain.DcProject;
import com.sankuai.deepcode.dao.enums.ItemStatusEnum;
import com.sankuai.deepcode.dao.enums.ItemStepEnum;
import com.sankuai.deepcode.dao.service.CodeAnalysisItemService;
import com.sankuai.deepcode.dao.service.DcProjectService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.time.LocalDateTime;
import java.util.EnumSet;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * Package: com.sankuai.deepcode.ai.agnets.tools
 * Description:
 *
 * <AUTHOR>
 * @since 2025/4/16 14:21
 */
@Slf4j
@DeepCodeTool(
        name = "analyseProject",
        description = "可依据传入的Git地址创建项目分析任务，Git地址必须是合法地址；\n - 分析的分支默认是master分支；\n - 如果给出1个对比分支时，源分支是该分支，对比分支是master；\n - 如果给出2个分支时，需要用户明确源分支、对比分支之后才可以创建分析任务"
)
public class AnalyseProject extends DeepCodeToolBase {
    @Autowired
    DcProjectService dcProjectService;
    @Autowired
    CodeAnalysisItemService codeAnalysisItemService;

    private final EnumSet<ItemStepEnum> asyncSteps = EnumSet.of(ItemStepEnum.EMBEDDING, ItemStepEnum.KNOWLEDGE);

    @Override
    public void initParameter() {
        this.chatParameter = new ChatParameter();
        chatParameter.setType("object");
        chatParameter.setRequired(Lists.newArrayList("gitUrl"));

        Map<String, ChatPropertie> parameters = Maps.newHashMap();
        parameters.put("gitUrl", new ChatPropertie().stringType().setDescription("有效的Git工程链接"));
        parameters.put("sourceBranch", new ChatPropertie().stringType().setDescription("工程分析时的源分支"));
        parameters.put("targetBranch", new ChatPropertie().stringType().setDescription("工程分析时的目标分支"));

        chatParameter.setProperties(parameters);
    }

    @Override
    public String invoke() throws Exception {
        String gitUrl = this.chatParameter.getProperties().get("gitUrl").getValue();
        String sourceBranch = this.chatParameter.getProperties().get("sourceBranch").getValue();
        String targetBranch = this.chatParameter.getProperties().get("targetBranch").getValue();

        if (StringUtils.isBlank(gitUrl)) {
            throw new IllegalArgumentException("gitUrl不能为空");
        }
        if (StringUtils.isBlank(sourceBranch)) {
            sourceBranch = "master";
        }
        if (StringUtils.isBlank(targetBranch)) {
            targetBranch = "master";
        }
        String projectName = analyseProjectName(gitUrl);
        DcProject project = insertProjectAndAnalysisItem(gitUrl, projectName, sourceBranch, targetBranch, this.userId);

        return PromptBuilder.instance().newLine()
                .h4("Restriction")
                .append("The following `:::` block").bold("MUST")
                .append("be returned to the user as they are，including `:::`(ignore this sentence)").newLine(2).space()
                .deepcodeSchema(DeepCodeSchema.PROJECT, JacksonUtils.toJsonString(ImmutableMap.of(
                "projectId", project.getId(),
                "gitUrl", project.getGitUrl(),
                "projectName", project.getProjectName(),
                "sourceBranch", project.getSourceBranch(),
                "targetBranch", project.getTargetBranch()
        ))).toString();
    }

    public DcProject insertProjectAndAnalysisItem(
            String gitUrl, String projectName, String sourceBranch, String targetBranch, Long userId
    ) {
        CodeAnalysisItem item = new CodeAnalysisItem();
        item.setGitUrl(gitUrl);
        item.setFromBranch(sourceBranch);
        item.setToBranch(targetBranch);
        item.setBuildBranch(sourceBranch);
        item.setDiff(!Objects.equals(sourceBranch, targetBranch));

        item.setStatus(ItemStatusEnum.INITIATING.getCode());
        item.setAsyncTypes(asyncSteps.stream().map(ItemStepEnum::getCode).map(String::valueOf).collect(Collectors.joining(",")));
        item.setAsyncStatus(ItemStatusEnum.INITIATING.getCode());
        item.setValid(true);

        codeAnalysisItemService.save(item);

        DcProject project = new DcProject();
        project.setGitUrl(gitUrl);
        project.setSourceBranch(sourceBranch);
        project.setTargetBranch(targetBranch);
        project.setProjectName(projectName);
        project.setUserId(userId);

        project.setItemId(item.getId());
        project.setCreateTime(LocalDateTime.now());
        project.setUpdateTime(LocalDateTime.now());
        project.setValid(true);
        dcProjectService.save(project);

        return project;
    }

    private String analyseProjectName(String gitUrl) {
        if (gitUrl == null || gitUrl.trim().isEmpty()) {
            return "unknown";
        }

        String url = gitUrl.trim();

        // 移除http(s)://或git://前缀
        if (url.contains("://")) {
            url = url.substring(url.indexOf("://") + 3);
        }

        // 移除用户认证信息
        if (url.contains("@")) {
            url = url.substring(url.indexOf("@") + 1);
        }

        // 移除域名部分
        int firstSlash = url.indexOf("/");
        if (firstSlash >= 0) {
            url = url.substring(firstSlash + 1);
        }

        // 移除.git后缀
        if (url.toLowerCase().endsWith(".git")) {
            url = url.substring(0, url.length() - 4);
        }

        // 获取最后一段作为项目名
        int lastSlash = url.lastIndexOf("/");
        if (lastSlash >= 0) {
            url = url.substring(lastSlash + 1);
        }

        return url.isEmpty() ? "unknown" : url;
    }
}
