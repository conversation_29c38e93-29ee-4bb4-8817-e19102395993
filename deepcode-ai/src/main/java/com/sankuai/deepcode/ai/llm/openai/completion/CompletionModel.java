package com.sankuai.deepcode.ai.llm.openai.completion;

import lombok.Getter;

/**
 * <AUTHOR>
 */
@Getter
public enum CompletionModel {

    GPT_3_5_TURBO_INSTRUCT("gpt-3.5-turbo-instruct"),
    GPT_4O_MINI("gpt-4o-mini"),
    ;

    private final String value;

    CompletionModel(String value) {
        this.value = value;
    }

    @Override
    public String toString() {
        return value;
    }

    public static CompletionModel defaultModel() {
        return GPT_4O_MINI;
    }

}
