package com.sankuai.deepcode.ai.embedding.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.reflect.TypeToken;
import com.google.gson.Gson;
import com.sankuai.deepcode.ai.embedding.model.CheckTokenRes;
import com.sankuai.deepcode.ai.embedding.model.EmbeddingsRes;
import io.restassured.response.Response;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.sankuai.deepcode.ai.common.OneApi.ONE_API_URL;
import static com.sankuai.deepcode.ai.common.OneApi.initHeaders;
import static io.restassured.RestAssured.given;

@Service
public class EmbeddingService {

    private static final Logger LOGGER = LoggerFactory.getLogger(EmbeddingService.class);

    public EmbeddingsRes embeddings(String input, String model) {
        EmbeddingsRes res = null;
        Map<String, String> headersMap = new HashMap<>();
        initHeaders(headersMap);
        Map<String, Object> paramsMap = new HashMap<>();
        paramsMap.put("model", model);
        paramsMap.put("input", input);
        paramsMap.put("operator", "deepcode");

        try {
            Response response = given().headers(headersMap).body(JSONObject.toJSONString(paramsMap))
                    .when().post(ONE_API_URL + "/v1/embeddings");
            Type type = new TypeToken<EmbeddingsRes>() {
            }.getType();
            res = JSON.parseObject(response.getBody().asString(), type);
        } catch (Exception e) {
            LOGGER.error("调用embeddings接口异常e:", e);
        }
        return res;
    }


    public CheckTokenRes checkTokenLength(String text) {
        Map<String, String> headersMap = new HashMap<>();
        initHeaders(headersMap);
        CheckTokenRes res = new CheckTokenRes();
        try {
            Map<String, String> paramsMap = new HashMap<>();
            paramsMap.put("text", text);
            Response response = given().headers(headersMap).body(com.alibaba.fastjson.JSONObject.toJSONString(paramsMap))
                    .when().post("http://10.164.59.28:8416/check_token_length");
            Type type = new TypeToken<CheckTokenRes>() {
            }.getType();
            res = JSON.parseObject(response.getBody().asString(), type);
        } catch (Exception e) {
            LOGGER.error("checkTokenLength e", e);
        }
        return res;
    }

    public List<List<Float>> getEmbeddings(List<String> texts) {
        List<List<Float>> res = new ArrayList<>();
        Map<String, String> headersMap = new HashMap<>();
        initHeaders(headersMap);

        Map<String, Object> paramsMap = new HashMap<>();
        paramsMap.put("texts", texts);
        try {
            Response response = given().headers(headersMap).body(new Gson().toJson(paramsMap))
                    .when().post("http://10.164.59.28:8416/get_embeddings");
            Type type = new TypeToken<List<List<Float>>>() {
            }.getType();
            res = JSON.parseObject(response.getBody().asString(), type);
        } catch (Exception e) {
            LOGGER.error("embeddings e", e);
        }
        return res;
    }
}
