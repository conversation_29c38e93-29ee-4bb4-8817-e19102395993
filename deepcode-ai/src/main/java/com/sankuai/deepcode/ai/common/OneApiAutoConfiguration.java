package com.sankuai.deepcode.ai.common;

import com.sankuai.deepcode.ai.llm.oneapi.OneApiClient;
import com.sankuai.deepcode.ai.llm.oneapi.OneApiSseClient;
import com.sankuai.deepcode.ai.llm.openai.EmbeddingClient;
import com.sankuai.deepcode.ai.llm.openai.OpenAiClient;
import lombok.SneakyThrows;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.io.Resource;
import org.springframework.util.StreamUtils;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.time.Duration;
import java.util.Objects;

/**
 * Package: com.sankuai.deepcode.ai.common
 * Description:
 *
 * <AUTHOR>
 * @since 2025/3/19 20:40
 */
@Configuration(proxyBeanMethods = false)
@EnableConfigurationProperties
public class OneApiAutoConfiguration {
    @Value("classpath:/prompts/system.pt")
    private Resource systemResource;


    @Bean
    @ConditionalOnMissingBean(LLMConfig.class)
    @ConfigurationProperties(prefix = "oneapi.llm")
    public LLMConfig llmConfig() {
        return new LLMConfig();
    }


    @SuppressWarnings("rawtypes")
    @Bean
    @ConditionalOnMissingBean
    @ConditionalOnProperty(prefix = "oneapi.llm", name = "api-key")
    public OneApiClient oneApiClient(LLMConfig llmConfig) {
        OpenAiClient.OpenAiBuilder builder = OneApiClient.builder()
                .baseUrl(llmConfig.getBaseUrl())
                .model(llmConfig.getModel())
                .openAiApiKey(llmConfig.getApiKey())
                .logRequests(llmConfig.isLogRequests())
                .logResponses(llmConfig.isLogResponses());

        if (Objects.nonNull(llmConfig.getProxy())) {
            builder.proxy(llmConfig.getProxy());
        }

        if (Objects.nonNull(llmConfig.getConnectTimeout())) {
            builder.connectTimeout(Duration.ofSeconds(llmConfig.getConnectTimeout()));
        }

        if (Objects.nonNull(llmConfig.getMaxTokens()) && llmConfig.getMaxTokens() > 0) {
            builder.maxTokens(llmConfig.getMaxTokens());
        }

        if (Objects.nonNull(llmConfig.getReadTimeout())) {
            builder.readTimeout(Duration.ofSeconds(llmConfig.getReadTimeout()));
        }

        if (Objects.nonNull(llmConfig.getCallTimeout())) {
            builder.callTimeout(Duration.ofSeconds(llmConfig.getCallTimeout()));
        }

        // 注入系统提示词
        if (llmConfig.isDefaultSystemPrompt()) {
            String systemMessage = null;
            try {
                systemMessage = StreamUtils.copyToString(systemResource.getInputStream(), StandardCharsets.UTF_8);
            } catch (IOException ignore) {

            }
            if (StringUtils.isNotBlank(systemMessage)) {
                builder.systemMessage(systemMessage);
            }
        }

        builder.logLevel(llmConfig.getLogLevel());

        return (OneApiClient) builder.build();
    }


    @SneakyThrows
    @Bean
    @ConditionalOnMissingBean
    @ConditionalOnProperty(prefix = "oneapi.llm", name = "api-key")
    public OneApiSseClient oneApiSseClient(LLMConfig llmConfig) {
        OneApiSseClient.SseBuilder builder = OneApiSseClient.builder()
                .baseUrl(llmConfig.getBaseUrl())
                .model(llmConfig.getModel())
                .openAiApiKey(llmConfig.getApiKey())
                .logRequests(llmConfig.isLogRequests())
                .logResponses(llmConfig.isLogResponses());

        if (Objects.nonNull(llmConfig.getProxy())) {
            builder.proxy(llmConfig.getProxy());
        }

        if (Objects.nonNull(llmConfig.getConnectTimeout())) {
            builder.connectTimeout(Duration.ofSeconds(llmConfig.getConnectTimeout()));
        }

        if (Objects.nonNull(llmConfig.getMaxTokens()) && llmConfig.getMaxTokens() > 0) {
            builder.maxTokens(llmConfig.getMaxTokens());
        }

        if (Objects.nonNull(llmConfig.getReadTimeout())) {
            builder.readTimeout(Duration.ofSeconds(llmConfig.getReadTimeout()));
        }

        if (Objects.nonNull(llmConfig.getCallTimeout())) {
            builder.callTimeout(Duration.ofSeconds(llmConfig.getCallTimeout()));
        }

        builder.logLevel(llmConfig.getLogLevel());

        // 注入系统提示词
        if (llmConfig.isDefaultSystemPrompt()) {
            String systemMessage = null;
            try {
                systemMessage = StreamUtils.copyToString(systemResource.getInputStream(), StandardCharsets.UTF_8);
            } catch (IOException ignore) {

            }
            if (StringUtils.isNotBlank(systemMessage)) {
                builder.systemMessage(systemMessage);
            }
        }

        builder.searchApiKey(llmConfig.getSearchApiKey());
        return builder.build();
    }


    @Bean
    @ConditionalOnMissingBean(EmbeddingConfig.class)
    @ConfigurationProperties(prefix = "oneapi.embedding")
    public EmbeddingConfig embeddingConfig() {
        return new EmbeddingConfig();
    }


    @Bean
    @ConditionalOnMissingBean
    @ConditionalOnProperty(prefix = "oneapi.embedding", name = "api-key")
    public EmbeddingClient embeddingClient(EmbeddingConfig embeddingConfig) {
        EmbeddingClient.Builder builder = EmbeddingClient.builder().baseUrl(embeddingConfig.getBaseUrl())
                .model(embeddingConfig.getModel()).openAiApiKey(embeddingConfig.getApiKey())
                .logRequests(embeddingConfig.isLogRequests()).logResponses(embeddingConfig.isLogResponses());

        if (Objects.nonNull(embeddingConfig.getProxy())) {
            builder.proxy(embeddingConfig.getProxy());
        }

        if (Objects.nonNull(embeddingConfig.getConnectTimeout())) {
            builder.connectTimeout(Duration.ofSeconds(embeddingConfig.getConnectTimeout()));
        }

        if (Objects.nonNull(embeddingConfig.getReadTimeout())) {
            builder.readTimeout(Duration.ofSeconds(embeddingConfig.getReadTimeout()));
        }

        if (Objects.nonNull(embeddingConfig.getCallTimeout())) {
            builder.callTimeout(Duration.ofSeconds(embeddingConfig.getCallTimeout()));
        }

        builder.logLevel(embeddingConfig.getLogLevel());

        return builder.build();
    }
}
