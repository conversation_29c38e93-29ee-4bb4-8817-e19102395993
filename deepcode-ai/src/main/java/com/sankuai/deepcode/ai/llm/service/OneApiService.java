package com.sankuai.deepcode.ai.llm.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.common.reflect.TypeToken;
import com.sankuai.deepcode.ai.agnets.callback.PlanCompleteCallback;
import com.sankuai.deepcode.ai.agnets.callback.StreamCompleteCallback;
import com.sankuai.deepcode.ai.enums.ChatEnum;
import com.sankuai.deepcode.ai.llm.model.chat.*;
import com.sankuai.deepcode.ai.llm.openai.chat.ChatCompletionChoice;
import com.sankuai.deepcode.ai.llm.openai.chat.ChatCompletionResponse;
import io.restassured.response.Response;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.io.BufferedReader;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.sankuai.deepcode.ai.common.OneApi.ONE_API_URL;
import static com.sankuai.deepcode.ai.common.OneApi.initHeaders;
import static io.restassured.RestAssured.given;

@Service
public class OneApiService {

    private static final Logger LOGGER = LoggerFactory.getLogger(OneApiService.class);

    public ChatMsgRes completions(List<ChatMessage> chatMessages, String model) {
        ChatMsgRes res = null;
        Map<String, String> headersMap = new HashMap<>();
        initHeaders(headersMap);
        Map<String, Object> paramsMap = new HashMap<>();
        paramsMap.put("model", model);
        paramsMap.put("messages", chatMessages);
        paramsMap.put("stream", false);
        paramsMap.put("operator", "deepcode");
        paramsMap.put("max_tokens", 8192);
        paramsMap.put("useCache", false);

        try {
            Response response = given().headers(headersMap).body(JSONObject.toJSONString(paramsMap))
                    .when().post(ONE_API_URL + "/v1/chat/completions");
            Type type = new TypeToken<ChatMsgRes>() {
            }.getType();
            res = JSON.parseObject(response.getBody().asString(), type);
        } catch (Exception e) {
            LOGGER.error("调用completions接口异常e:", e);
        }
        return res;
    }

    public ChatMsgRes completions(List<ChatMessage> chatMessages,
                                  String model,
                                  List<ChatTool> chatTools) {
        ChatMsgRes res = null;
        Map<String, String> headersMap = new HashMap<>();
        initHeaders(headersMap);
        Map<String, Object> paramsMap = new HashMap<>();
        paramsMap.put("model", model);
        paramsMap.put("messages", chatMessages);
        paramsMap.put("stream", false);
        paramsMap.put("operator", "deepcode");
        paramsMap.put("max_tokens", 8192);
        paramsMap.put("useCache", false);
        if (CollectionUtils.isNotEmpty(chatTools)) {
            paramsMap.put("tools", chatTools);
        }

        try {
            Response response = given().headers(headersMap).body(JSONObject.toJSONString(paramsMap))
                    .when().post(ONE_API_URL + "/v1/chat/completions");
            Type type = new TypeToken<ChatMsgRes>() {
            }.getType();
            res = JSON.parseObject(response.getBody().asString(), type);
        } catch (Exception e) {
            LOGGER.error("调用completions接口异常e:", e);
        }
        return res;
    }

    public void completionsByStream(List<ChatMessage> chatMessages,
                                    String model,
                                    List<ChatTool> chatTools,
                                    SseEmitter sseEmitter,
                                    ChatCompletionResponse chatCompletionResponse,
                                    boolean processing,
                                    PlanCompleteCallback planCompleteCallback,
                                    StreamCompleteCallback callback,
                                    StringBuffer contentBuffer,
                                    StringBuffer processingContentBuffer,
                                    StringBuffer reasoningContentBuffer) {
        Map<String, String> headersMap = new HashMap<>();
        initHeaders(headersMap);
        Map<String, Object> paramsMap = new HashMap<>();
        paramsMap.put("model", model);
        paramsMap.put("messages", chatMessages);
        paramsMap.put("stream", true);
        paramsMap.put("operator", "deepcode");
        paramsMap.put("max_tokens", 8192);
        paramsMap.put("useCache", false);
        if (CollectionUtils.isNotEmpty(chatTools)) {
            paramsMap.put("tools", chatTools);
        }

        try {
            Response response = given().headers(headersMap).body(JSONObject.toJSONString(paramsMap))
                    .when().post(ONE_API_URL + "/v1/chat/completions");
            ChatMsgRes chatMsgRes = null;
            List<ToolCall> tool_calls = new ArrayList<>();
            if (response.statusCode() == 200) {
                InputStream inputStream = response.getBody().asInputStream();
                try (BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream))) {
                    String line;
                    while ((line = reader.readLine()) != null) {
                        if (!line.startsWith("data:")) {
                            continue;
                        }
                        String tmpStr = line.split("data: ")[1];
                        if (tmpStr.equals("[DONE]")) {
                            break;
                        }
                        Type type = new TypeToken<ChatMsgRes>() {
                        }.getType();
                        chatMsgRes = JSON.parseObject(tmpStr, type);
                        ChatCompletionChoice chatCompletionChoice = chatCompletionResponse.choices().get(0);
                        if (CollectionUtils.isNotEmpty(chatMsgRes.getChoices().get(0).getDelta().getTool_calls())) {
                            if (CollectionUtils.isEmpty(tool_calls)) {
                                tool_calls = chatMsgRes.getChoices().get(0).getDelta().getTool_calls();
                            }
                            for (int i = 0; i < chatMsgRes.getChoices().get(0).getDelta().getTool_calls().size(); i++) {
                                ToolCallFunction toolCallFunction = chatMsgRes.getChoices().get(0).getDelta().getTool_calls().get(i).getFunction();
                                tool_calls.get(i).getFunction().setArguments(tool_calls.get(i).getFunction().getArguments() + toolCallFunction.getArguments());
                            }
                        } else {
                            if (chatMsgRes.getLastOne()) {
                                if (chatMsgRes.getChoices().get(0).getMessage() != null) {
                                    if (StringUtils.isNotEmpty(chatMsgRes.getChoices().get(0).getMessage().getContent())) {
                                        contentBuffer.append(chatMsgRes.getChoices().get(0).getMessage().getContent());
                                    } else if (StringUtils.isNotEmpty(chatMsgRes.getChoices().get(0).getMessage().getReasoning_content())) {
                                        reasoningContentBuffer.append(chatMsgRes.getChoices().get(0).getMessage().getReasoning_content());
                                    }
                                    chatCompletionChoice.message().content(contentBuffer.toString());
                                    chatCompletionChoice.message().reasoningContent(reasoningContentBuffer.toString());
                                    chatCompletionChoice.message().processingContent(processingContentBuffer.toString());
                                }
                                break;
                            }
                            boolean send = false;
                            if (processing) {
                                if (StringUtils.isNotEmpty(chatMsgRes.getChoices().get(0).getDelta().getContent())) {
                                    chatCompletionChoice.delta().content("");
                                    chatCompletionChoice.delta().reasoningContent("");
                                    chatCompletionChoice.delta().processingContent(chatMsgRes.getChoices().get(0).getDelta().getContent());
                                    processingContentBuffer.append(chatMsgRes.getChoices().get(0).getDelta().getContent());
                                    send = true;
                                } else if (StringUtils.isNotEmpty(chatMsgRes.getChoices().get(0).getDelta().getReasoning_content())) {
                                    chatCompletionChoice.delta().content("");
                                    chatCompletionChoice.delta().reasoningContent("");
                                    chatCompletionChoice.delta().processingContent(chatMsgRes.getChoices().get(0).getDelta().getReasoning_content());
                                    processingContentBuffer.append(chatMsgRes.getChoices().get(0).getDelta().getReasoning_content());
                                    send = true;
                                } else {
                                    chatCompletionChoice.delta().content("");
                                    chatCompletionChoice.delta().reasoningContent("");
                                    chatCompletionChoice.delta().processingContent("");
                                }
                            } else {
                                if (StringUtils.isNotEmpty(chatMsgRes.getChoices().get(0).getDelta().getReasoning_content())) {
                                    chatCompletionChoice.delta().content("");
                                    chatCompletionChoice.delta().processingContent("");
                                    chatCompletionChoice.delta().reasoningContent(chatMsgRes.getChoices().get(0).getDelta().getReasoning_content());
                                    reasoningContentBuffer.append(chatMsgRes.getChoices().get(0).getDelta().getReasoning_content());
                                    send = true;
                                } else if (StringUtils.isNotEmpty(chatMsgRes.getChoices().get(0).getDelta().getContent())) {
                                    chatCompletionChoice.delta().reasoningContent("");
                                    chatCompletionChoice.delta().processingContent("");
                                    chatCompletionChoice.delta().content(chatMsgRes.getChoices().get(0).getDelta().getContent());
                                    contentBuffer.append(chatMsgRes.getChoices().get(0).getDelta().getContent());
                                    send = true;
                                } else {
                                    chatCompletionChoice.delta().reasoningContent("");
                                    chatCompletionChoice.delta().processingContent("");
                                    chatCompletionChoice.delta().content("");
                                }
                            }
                            if (send) {
                                chatCompletionChoice.message().content(contentBuffer.toString());
                                chatCompletionChoice.message().reasoningContent(reasoningContentBuffer.toString());
                                chatCompletionChoice.message().processingContent(processingContentBuffer.toString());
                                sseEmitter.send(SseEmitter.event().data(chatCompletionResponse));
                            }
                        }
                    }
                }
            } else {
                String responseStr = response.getBody().asString();
                String errorMsg = "请求失败:" + (responseStr.length() > 256 ? responseStr.substring(0, 256) : responseStr);
                errorCallBack(sseEmitter, chatCompletionResponse, processing, planCompleteCallback, callback, errorMsg, chatMsgRes);
            }
            if (callback != null) {
                if (CollectionUtils.isNotEmpty(tool_calls)) {
                    chatMsgRes.getChoices().get(0).getDelta().setTool_calls(tool_calls);
                }
                callback.onComplete(sseEmitter, chatMsgRes, chatCompletionResponse, planCompleteCallback);
            }
        } catch (Exception e) {
            LOGGER.error("completionsByStream 异常e:", e);
            String responseStr = e.getMessage();
            if (StringUtils.isEmpty(responseStr)) {
                responseStr = "";
            }
            String errorMsg = "请求失败:" + (responseStr.length() > 256 ? responseStr.substring(0, 256) : responseStr);
            errorCallBack(sseEmitter, chatCompletionResponse, processing, planCompleteCallback, callback, errorMsg, null);
        }
    }

    public void errorCallBack(SseEmitter sseEmitter,
                              ChatCompletionResponse chatCompletionResponse,
                              boolean processing,
                              PlanCompleteCallback planCompleteCallback,
                              StreamCompleteCallback callback,
                              String errorMsg,
                              ChatMsgRes chatMsgRes
    ) {
        ChatCompletionChoice chatCompletionChoice = chatCompletionResponse.choices().get(0);
        if (processing) {
            chatCompletionChoice.delta().reasoningContent("");
            chatCompletionChoice.delta().content("");
            chatCompletionChoice.delta().processingContent(errorMsg);
        } else {
            chatCompletionChoice.delta().reasoningContent("");
            chatCompletionChoice.delta().processingContent("");
            chatCompletionChoice.delta().content(errorMsg);
        }
        try {
            chatCompletionResponse.content(errorMsg);
            sseEmitter.send(SseEmitter.event().data(chatCompletionResponse));
        } catch (Exception ex) {
        }
        callback.onComplete(sseEmitter, chatMsgRes, chatCompletionResponse, planCompleteCallback);
    }


    public static void main(String[] args) {
        OneApiService oneApiService = new OneApiService();
        List<ChatMessage> chatMessages = new ArrayList<>();
        ChatMessage chatMessage = new ChatMessage();
        chatMessage.setRole("user");
        chatMessage.setContent("已知今天刮风");

        ChatMessage chatMessage1 = new ChatMessage();
        chatMessage1.setRole("user");
        chatMessage1.setContent("天气怎么样");
        chatMessages.add(chatMessage);
        chatMessages.add(chatMessage1);
        ChatMsgRes chatMsgRes = oneApiService.completions(chatMessages, ChatEnum.GPT_4_1_MINI.getName());
        System.out.println();
    }

}
