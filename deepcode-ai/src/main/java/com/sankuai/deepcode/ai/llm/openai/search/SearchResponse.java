package com.sankuai.deepcode.ai.llm.openai.search;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * 查询响应
 * <AUTHOR>
 */
@Data
public class SearchResponse {

    private Integer code;
    @JsonProperty("log_id")
    private String logId;
    private String msg;
    private ResponseData data;

    @Data
    public static class ResponseData {
        private String _type;
        private QueryContext queryContext;
        private WebPages webPages;
        private Images images;

    }

    @Data
    public static class QueryContext {
        private String originalQuery;

    }

    @Data
    public static class WebPages {
        private String webSearchUrl;
        private Integer totalEstimatedMatches;
        private Value[] value;
        private Boolean someResultsRemoved;
    }

    @Data
    public static class Value {
        private String id;
        private String name;
        private String url;
        private String displayUrl;
        private String snippet;
        private String summary;
        private String siteName;
        private String siteIcon;
        private String dateLastCrawled;
        private String cachedPageUrl;
        private String language;
        private Boolean isFamilyFriendly;
        private Boolean isNavigational;

    }

    @Data
    public static class Images {
        private String id;
        private String readLink;
        private String webSearchUrl;
        private Value[] value;
        private Boolean isFamilyFriendly;
    }

}
