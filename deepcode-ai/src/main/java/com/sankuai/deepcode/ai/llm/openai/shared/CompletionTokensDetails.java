package com.sankuai.deepcode.ai.llm.openai.shared;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.fasterxml.jackson.databind.annotation.JsonPOJOBuilder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.util.Objects;

/**
 * <AUTHOR>
 */
@JsonDeserialize(builder = CompletionTokensDetails.Builder.class)
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
@EqualsAndHashCode
@ToString
@Getter
@Accessors(fluent = true)
public final class CompletionTokensDetails {

	@JsonProperty
	private final Integer reasoningTokens;

	private CompletionTokensDetails(Builder builder) {
		this.reasoningTokens = builder.reasoningTokens;
	}

	public Integer reasoningTokens() {
		return reasoningTokens;
	}


	public static Builder builder() {
		return new Builder();
	}

	@JsonPOJOBuilder(withPrefix = "")
	@JsonIgnoreProperties(ignoreUnknown = true)
	@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
	public static final class Builder {

		private Integer reasoningTokens;

		private Builder() {
		}

		public Builder reasoningTokens(Integer reasoningTokens) {
			this.reasoningTokens = reasoningTokens;
			return this;
		}

		public CompletionTokensDetails build() {
			return new CompletionTokensDetails(this);
		}

	}

}
