package com.sankuai.deepcode.ai.agnets.tools;

import com.alibaba.fastjson.JSON;
import com.sankuai.deepcode.ai.agnets.tool.DeepCodeToolBase;
import com.sankuai.deepcode.ai.agnets.tool.DeepCodeTool;
import com.sankuai.deepcode.ai.llm.model.chat.ChatParameter;
import com.sankuai.deepcode.ai.llm.model.chat.ChatPropertie;
import com.sankuai.deepcode.dao.domain.CodeMethodAnalysis;
import com.sankuai.deepcode.dao.domain.MethodInvokeMethod;
import com.sankuai.deepcode.dao.service.CodeMethodAnalysisService;
import com.sankuai.deepcode.dao.service.MethodInvokeMethodService;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.*;

/**
 * <AUTHOR>
 */
@DeepCodeTool(name = "methodCallHierarchy", description = "当问题需要上游方法时才能更准确回答问题时，根据vid查询方法上游或下游方法，最多返回10层")
public class MethodCallHierarchy extends DeepCodeToolBase {

    @Autowired
    private CodeMethodAnalysisService codeMethodAnalysisService;
    @Autowired
    private MethodInvokeMethodService methodInvokeMethodService;

    @Override
    public void initParameter() {
        this.chatParameter = new ChatParameter();
        chatParameter.setType("object");
        List<String> required = new ArrayList<>();
        required.add("methodVid");
        required.add("rootType");
        chatParameter.setRequired(required);
        Map<String, ChatPropertie> properties = new HashMap<>();
        ChatPropertie methodVid = new ChatPropertie();
        methodVid.setType("string");
        methodVid.setDescription("方法唯一vid");
        properties.put("methodVid", methodVid);

        ChatPropertie rootType = new ChatPropertie();
        rootType.setType("string");
        rootType.setDescription("上游up，下游为down，全部为all");
        properties.put("rootType", rootType);

        ChatPropertie step = new ChatPropertie();
        step.setType("string");
        step.setDescription("层数，默认为3层，最大不超过10层，超过10层设置为10");
        properties.put("step", step);
        chatParameter.setProperties(properties);
    }

    @Override
    public String invoke() {
        String methodVid = this.chatParameter.getProperties().get("methodVid").getValue();
        String rootType = this.chatParameter.getProperties().get("rootType").getValue();
        int step = 10;
        if (this.chatParameter.getProperties().get("step") != null) {
            step = Integer.parseInt(this.chatParameter.getProperties().get("step").getValue());
        }
        long itemId = this.itemId;
        Set<String> vids = new HashSet<>();
        List<MethodInvokeMethod> methodInvokeMethods = new ArrayList<>();
        List<String> list = new ArrayList<>();
        if (rootType.equals("up")) {
            List<MethodInvokeMethod> methods = methodInvokeMethodService.getByTarget(itemId, methodVid);
            getUp(itemId, step, methods, vids, methodInvokeMethods);
        } else if (rootType.equals("down")) {
            List<MethodInvokeMethod> methods = methodInvokeMethodService.getBySource(itemId, methodVid);
            getDown(itemId, step, methods, vids, methodInvokeMethods);
        } else if (rootType.equals("all")) {
            List<MethodInvokeMethod> methods = methodInvokeMethodService.getByTarget(itemId, methodVid);
            getUp(itemId, step, methods, vids, methodInvokeMethods);
            methods = methodInvokeMethodService.getBySource(itemId, methodVid);
            vids = new HashSet<>();
            getDown(itemId, step, methods, vids, methodInvokeMethods);
        }
        initRes(list, methodInvokeMethods);
        if (CollectionUtils.isEmpty(list)) {
            if (rootType.equals("up")) {
                throw new RuntimeException("该方法没有查询到上游方法");
            } else {
                throw new RuntimeException("该方法没有查询到下游方法");
            }
        }
        return JSON.toJSONString(list);
    }


    public void getUp(long itemId, int step, List<MethodInvokeMethod> methods, Set<String> vids, List<MethodInvokeMethod> methodInvokeMethods) {
        if (step >= 10) {
            return;
        }
        for (MethodInvokeMethod methodInvokeMethod : methods) {
            if (vids.contains(methodInvokeMethod.getSource())) {
                continue;
            } else {
                methodInvokeMethods.add(methodInvokeMethod);
                vids.add(methodInvokeMethod.getSource());
            }
            List<MethodInvokeMethod> ups = methodInvokeMethodService.getByTarget(itemId, methodInvokeMethod.getSource());
            if (CollectionUtils.isNotEmpty(ups)) {
                getUp(itemId, step++, ups, vids, methodInvokeMethods);
            }
        }
    }


    public void getDown(long itemId, int step, List<MethodInvokeMethod> methods, Set<String> vids, List<MethodInvokeMethod> methodInvokeMethods) {
        if (step >= 10) {
            return;
        }
        for (MethodInvokeMethod methodInvokeMethod : methods) {
            if (vids.contains(methodInvokeMethod.getTarget())) {
                continue;
            } else {
                vids.add(methodInvokeMethod.getTarget());
                methodInvokeMethods.add(methodInvokeMethod);
            }
            List<MethodInvokeMethod> ups = methodInvokeMethodService.getBySource(itemId, methodInvokeMethod.getTarget());
            if (CollectionUtils.isNotEmpty(ups)) {
                getDown(itemId, step++, ups, vids, methodInvokeMethods);
            }
        }
    }

    public void initRes(List<String> list, List<MethodInvokeMethod> methodInvokeMethods) {
        for (MethodInvokeMethod methodInvokeMethod : methodInvokeMethods) {
            List<String> vids = new ArrayList<>();
            vids.add(methodInvokeMethod.getSource());
            vids.add(methodInvokeMethod.getTarget());
            List<CodeMethodAnalysis> codeMethodAnalyses = codeMethodAnalysisService.getByMethodVids(itemId, vids);
            if (codeMethodAnalyses.size() == 2) {
                list.add(codeMethodAnalyses.get(0).getMethodName() + "(vid:" + methodInvokeMethod.getSource() + ")->"
                        + codeMethodAnalyses.get(1).getMethodName() + "(vid:" + methodInvokeMethod.getTarget() + ")");
            }
        }
    }
}
