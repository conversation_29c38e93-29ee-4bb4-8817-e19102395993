package com.sankuai.deepcode.ai.llm.openai.chat;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.fasterxml.jackson.databind.annotation.JsonPOJOBuilder;
import lombok.*;
import lombok.experimental.Accessors;

import java.util.List;

import static com.sankuai.deepcode.ai.llm.openai.chat.Role.ASSISTANT;

/**
 * <AUTHOR>
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
@JsonIgnoreProperties(ignoreUnknown = true)
@Getter
@EqualsAndHashCode
@ToString
@Builder
@Setter
@Accessors(fluent = true)
@NoArgsConstructor
@AllArgsConstructor
public class AssistantMessage implements Message {

    @JsonProperty
    @lombok.Builder.Default
    private Role role = ASSISTANT;

    @JsonProperty
    private String content;

    @JsonProperty
    private String processingContent;

    @JsonProperty
    private String reasoningContent;

    @JsonProperty
    private String name;

    @JsonProperty
    private List<ToolCall> toolCalls;

    @JsonProperty
    private Boolean refusal;

    @JsonProperty
    @Deprecated
    private FunctionCall functionCall;

    public static AssistantMessage from(String content) {
        return AssistantMessage.builder().content(content).build();
    }
}
