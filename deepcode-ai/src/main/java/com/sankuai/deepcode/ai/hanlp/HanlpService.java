package com.sankuai.deepcode.ai.hanlp;

import com.hankcs.hanlp.HanLP;
import com.hankcs.hanlp.dictionary.stopword.CoreStopWordDictionary;
import com.hankcs.hanlp.seg.common.Term;
import com.hankcs.hanlp.summary.TextRankKeyword;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class HanlpService {

    private static final int DEFAULT_KEYWORD_LIMIT = 10;

    /**
     * 处理文本并提取关键词
     *
     * @param text 输入文本
     * @return 关键词列表
     * @throws IllegalArgumentException 当输入文本为空时
     */
    public List<String> processText(String text) {
        return processText(text, DEFAULT_KEYWORD_LIMIT);
    }

    public List<String> processText(String text, int keywordLimit) {
        if (!StringUtils.hasText(text)) {
            throw new IllegalArgumentException("输入文本不能为空");
        }
        try {
            // 1. TextRank算法提取关键词（给予较高权重）
            List<String> textRankKeywords = TextRankKeyword.getKeywordList(text, keywordLimit * 2);
            // 2. 分词处理
            List<Term> termList = HanLP.segment(text);
            // 3. 统计词频
            Map<String, Integer> wordFreq = new HashMap<>();
            for (Term term : termList) {
                if (!CoreStopWordDictionary.contains(term.word) &&
                        (term.nature.startsWith("n") ||
                                term.nature.startsWith("v") ||
                                term.nature.startsWith("a"))) {
                    wordFreq.merge(term.word, 1, Integer::sum);
                }
            }
            // 4. 计算综合得分
            Map<String, Double> wordScores = new HashMap<>();
            // TextRank结果给予较高的基础分
            for (int i = 0; i < textRankKeywords.size(); i++) {
                String word = textRankKeywords.get(i);
                // 根据位置给予递减的权重
                double textRankScore = 1.0 * (textRankKeywords.size() - i) / textRankKeywords.size();
                wordScores.put(word, textRankScore * 2.0); // TextRank权重加倍
            }
            // 结合词频计算最终得分
            wordFreq.forEach((word, freq) -> {
                double freqScore = Math.log(1 + freq) / 2.0; // 词频使用对数，避免频次差异过大
                wordScores.merge(word, freqScore, Double::sum);
            });
            // 5. 按得分排序并返回结果
            return wordScores.entrySet().stream()
                    .sorted(Map.Entry.<String, Double>comparingByValue().reversed())
                    .limit(keywordLimit)
                    .map(Map.Entry::getKey)
                    .collect(Collectors.toList());
        } catch (Exception e) {
            throw new RuntimeException("文本处理失败: " + e.getMessage(), e);
        }
    }

    public List<String> segment(String text) {
        List<String> result = new ArrayList<>();
        List<Term> termList = HanLP.segment(text);
        for (Term term : termList) {
            result.add(term.word);
        }
        return result;
    }
}
