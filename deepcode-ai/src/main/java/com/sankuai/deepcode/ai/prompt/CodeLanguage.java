package com.sankuai.deepcode.ai.prompt;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * Package: com.sankuai.deepcode.ai.prompt
 * Description:
 *
 * <AUTHOR>
 * @since 2025/3/20 19:22
 */
@Getter
@AllArgsConstructor
public enum CodeLanguage {
    JAVA("java"),
    PYTHON("python"),
    JAVASCRIPT("javascript"),
    TYPESCRIPT("typescript"),
    GO("go"),
    RUST("rust"),
    CPP("cpp"),
    C("c"),
    CSHARP("csharp"),
    PHP("php"),
    RUBY("ruby"),
    SWIFT("swift"),
    KOTLIN("kotlin"),
    SCALA("scala"),
    SQL("sql"),
    HTML("html"),
    CSS("css"),
    XML("xml"),
    YAML("yaml"),
    Y<PERSON>("yml"),
    <PERSON><PERSON><PERSON>("json"),
    MARKDOWN("markdown"),
    SHELL("shell"),
    BASH("bash"),
    THRIFT("thrift"),
    PLAINTEXT("plaintext");

    private final String value;

    @Override
    public String toString() {
        return value;
    }

    public static CodeLanguage from(String value) {
        return Arrays.stream(CodeLanguage.values())
                .filter(language -> language.getValue().equalsIgnoreCase(value))
                .findFirst()
                .orElse(PLAINTEXT);
    }
}
