package com.sankuai.deepcode.ai.llm.oneapi;

import com.sankuai.deepcode.ai.llm.openai.SseClient;
import com.sankuai.deepcode.ai.llm.openai.SyncRequestExecutor;
import com.sankuai.deepcode.ai.llm.openai.Utils;
import com.sankuai.deepcode.ai.llm.openai.chat.ChatCompletionRequest;
import com.sankuai.deepcode.ai.llm.openai.search.SearchRequest;
import com.sankuai.deepcode.ai.llm.openai.search.SearchResponse;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.util.Objects;
import java.util.function.Function;

/**
 * Package: com.sankuai.deepcode.ai.llm.openai
 * Description:
 *
 * <AUTHOR>
 * @since 2025/3/19 19:51
 */
public class OneApiSseClient extends OneApiClient implements SseClient {
    public OneApiSseClient(OneApiSseClient.SseBuilder builder) {
        super(builder);
    }

    public static SseBuilder builder() {
        return new SseBuilder();
    }

    public static class SseBuilder extends SearchApiBuilder<OneApiSseClient, SseBuilder> {
        @Override
        public OneApiSseClient build() {
            return new OneApiSseClient(this);
        }
    }

    @Override
    public SseEmitter chatSseCompletion(ChatCompletionRequest request) {
        ChatCompletionRequest.Builder builder = ChatCompletionRequest.builder().from(request);
        if (Objects.isNull(request.model())) {
            builder.model(this.getModel());
        }

        SseEmitter emitter = new SseEmitter();

        this.chatCompletion(new OpenAiClientContext(), builder.build())
                .onPartialResponse(response -> {
                    try {
                        emitter.send(response);
                    } catch (Exception e) {
                        emitter.completeWithError(e);
                    }
                })
                .onComplete(emitter::complete)
                .onError(emitter::completeWithError)
                .execute();

        return emitter;
    }

    @Override
    public SseEmitter chatSseCompletion(String userMessage) {
        ChatCompletionRequest.Builder builder = ChatCompletionRequest.builder();

        if (Objects.nonNull(this.getSystemMessage())) {
            builder.addSystemMessage(this.getSystemMessage());
        }

        if (Objects.nonNull(this.getModel())) {
            builder.model(this.getModel());
        }

        builder.addUserMessage(userMessage);
        return chatSseCompletion(builder.build());
    }

    @Override
    public SseEmitter chatSearchSseCompletion(String userMessage) {
        SearchResponse searchResponse = new SyncRequestExecutor<>(
                this.getSearchApi().webSearch(SearchRequest.builder().enable(true).query(userMessage).build()),
                Function.identity()
        ).execute();

        if (200 == searchResponse.getCode()) {
            String formatted = Utils.format(userMessage, searchResponse.getData().getWebPages().getValue());
            if (Objects.nonNull(formatted)) {
                return this.chatSseCompletion(formatted);
            }
        }
        return this.chatSseCompletion(userMessage);
    }

    @Override
    public SseEmitter chatSearchSseCompletion(String userMessage, SearchRequest searchRequest) {
        if (Objects.isNull(searchRequest.getQuery())) {
            searchRequest.setQuery(userMessage);
        }

        SearchResponse searchResponse = new SyncRequestExecutor<>(
                this.getSearchApi().webSearch(searchRequest),
                Function.identity()
        ).execute();

        if (200 == searchResponse.getCode()) {
            String formatted = Utils.format(userMessage, searchResponse.getData().getWebPages().getValue());
            if (Objects.nonNull(formatted)) {
                return this.chatSseCompletion(formatted);
            }
        }
        return this.chatSseCompletion(userMessage);
    }
}
