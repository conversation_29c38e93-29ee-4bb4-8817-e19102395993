package com.sankuai.deepcode.ai.common;

import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;

import java.util.concurrent.ThreadPoolExecutor;


@Component
public class ChatPoolConfig {

    private ThreadPoolTaskExecutor executor;

    public ThreadPoolTaskExecutor chatPoolConfig() {
        if (executor == null) {
            executor = new ThreadPoolTaskExecutor();
            executor.setCorePoolSize(32);
            executor.setMaxPoolSize(256);
            executor.setQueueCapacity(128);
            executor.setKeepAliveSeconds(60);
            executor.setThreadNamePrefix("chat-stream-run-");
            executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
            executor.setWaitForTasksToCompleteOnShutdown(true);
            executor.initialize();
            return executor;
        }else {
            return executor;
        }
    }
}
