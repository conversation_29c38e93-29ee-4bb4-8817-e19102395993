package com.sankuai.deepcode.ai.llm.openai.chat;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.*;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Getter
@Setter
@Accessors(fluent = true)
@NoArgsConstructor(force = true)
@AllArgsConstructor
public class JsonReferenceSchema extends JsonSchemaElement {

    @JsonProperty("$ref")
    private String reference;

    @Builder
    public JsonReferenceSchema(String type, String reference) {
        super(type);
        this.reference = reference;
    }
}
