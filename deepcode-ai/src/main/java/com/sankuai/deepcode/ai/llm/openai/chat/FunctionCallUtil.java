package com.sankuai.deepcode.ai.llm.openai.chat;


import com.sankuai.deepcode.ai.llm.openai.Json;

import java.util.Map;

/**
 * <AUTHOR>
 */
public class FunctionCallUtil {

	public static <T> T argument(String name, FunctionCall function) {
		// TODO: Cache
		Map<String, Object> arguments = argumentsAsMap(function.arguments());
		return (T) arguments.get(name);
	}

	public static Map<String, Object> argumentsAsMap(String arguments) {
		return Json.fromJson(arguments, Map.class);
	}

}
