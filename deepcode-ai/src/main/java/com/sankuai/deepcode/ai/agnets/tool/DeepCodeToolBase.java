package com.sankuai.deepcode.ai.agnets.tool;

import com.sankuai.deepcode.ai.llm.model.chat.ChatParameter;
import lombok.Getter;
import lombok.Setter;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

@Getter
@Setter
public abstract class DeepCodeToolBase {

    public ChatParameter chatParameter;

    public long itemId;

    public Long userId;

    public String chatText;

    public String model;

    public SseEmitter sseEmitter;

    public DeepCodeToolBase() {
        initParameter();
    }

    public abstract void initParameter();

    public abstract String invoke() throws Exception;

}
