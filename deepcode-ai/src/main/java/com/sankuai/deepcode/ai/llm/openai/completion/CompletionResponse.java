package com.sankuai.deepcode.ai.llm.openai.completion;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.fasterxml.jackson.databind.annotation.JsonPOJOBuilder;
import com.sankuai.deepcode.ai.llm.openai.shared.Usage;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.List;

import static java.util.Collections.unmodifiableList;

/**
 * <AUTHOR>
 */
@JsonDeserialize(builder = CompletionResponse.Builder.class)
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
@EqualsAndHashCode
@ToString
public final class CompletionResponse {

    @JsonProperty
    private final String id;

    @JsonProperty
    private final Integer created;

    @JsonProperty
    private final String model;

    @JsonProperty
    private final List<CompletionChoice> choices;

    @JsonProperty
    private final Usage usage;

    private CompletionResponse(Builder builder) {
        this.id = builder.id;
        this.created = builder.created;
        this.model = builder.model;
        this.choices = builder.choices;
        this.usage = builder.usage;
    }

    public String id() {
        return id;
    }

    public Integer created() {
        return created;
    }

    public String model() {
        return model;
    }

    public List<CompletionChoice> choices() {
        return choices;
    }

    public Usage usage() {
        return usage;
    }

    /**
     * Convenience method to get the text from the first choice.
     */
    public String text() {
        return choices().get(0).text();
    }

    public static Builder builder() {
        return new Builder();
    }

    @JsonPOJOBuilder(withPrefix = "")
    @JsonIgnoreProperties(ignoreUnknown = true)
    @JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
    public static final class Builder {

        private String id;

        private Integer created;

        private String model;

        private List<CompletionChoice> choices;

        private Usage usage;

        private Builder() {
        }

        public Builder id(String id) {
            this.id = id;
            return this;
        }

        public Builder created(Integer created) {
            this.created = created;
            return this;
        }

        public Builder model(String model) {
            this.model = model;
            return this;
        }

        public Builder choices(List<CompletionChoice> choices) {
            if (choices != null) {
                this.choices = unmodifiableList(choices);
            }
            return this;
        }

        public Builder usage(Usage usage) {
            this.usage = usage;
            return this;
        }

        public CompletionResponse build() {
            return new CompletionResponse(this);
        }

    }

}
