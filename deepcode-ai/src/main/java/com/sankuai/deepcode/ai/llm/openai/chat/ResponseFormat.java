package com.sankuai.deepcode.ai.llm.openai.chat;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.*;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
@EqualsAndHashCode
@ToString
@Accessors(fluent = true)
@Getter
@Setter
@Builder
@NoArgsConstructor
public class ResponseFormat {

    @JsonProperty
    private ResponseFormatType type;

    @JsonProperty
    private JsonSchema jsonSchema;

    @JsonCreator
    public ResponseFormat(
            @JsonProperty("type") ResponseFormatType type,
            @JsonProperty("json_schema") JsonSchema jsonSchema
    ) {
        this.type = type;
        this.jsonSchema = jsonSchema;
    }
}
