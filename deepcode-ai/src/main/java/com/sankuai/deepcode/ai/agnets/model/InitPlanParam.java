package com.sankuai.deepcode.ai.agnets.model;

import com.sankuai.deepcode.ai.agnets.callback.PlanCompleteCallback;
import com.sankuai.deepcode.ai.llm.model.chat.ChatMessage;
import com.sankuai.deepcode.ai.llm.openai.chat.ChatCompletionResponse;
import lombok.Data;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.util.List;

@Data
public class InitPlanParam {
    private long itemId;
    private Long userId;
    private List<ChatMessage> chatMessages;
    private String modelName;
    private SseEmitter sseEmitter;
    private ChatCompletionResponse chatCompletionResponse;
    private boolean usePlan;
    private PlanCompleteCallback planCompleteCallback;
}
