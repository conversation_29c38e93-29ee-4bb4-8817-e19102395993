package com.sankuai.deepcode.ai.llm.openai.completion;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.*;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
@EqualsAndHashCode
@ToString
@Setter
@Getter
@Accessors(fluent = true)
@Builder
public class CompletionChoice {

    @JsonProperty
    private String text;

    @JsonProperty
    private Integer index;

    @JsonProperty
    private Logprobs logprobs;

    @JsonProperty
    private String finishReason;

}
