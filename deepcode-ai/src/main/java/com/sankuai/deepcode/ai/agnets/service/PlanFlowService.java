package com.sankuai.deepcode.ai.agnets.service;

import com.alibaba.fastjson.JSON;
import com.dianping.cat.util.StringUtils;
import com.google.common.reflect.TypeToken;
import com.sankuai.deepcode.ai.agnets.callback.PlanCompleteCallback;
import com.sankuai.deepcode.ai.agnets.callback.StreamCompleteCallback;
import com.sankuai.deepcode.ai.agnets.model.InitPlanParam;
import com.sankuai.deepcode.ai.agnets.model.PlanRes;
import com.sankuai.deepcode.ai.agnets.tool.DeepCodeToolBase;
import com.sankuai.deepcode.ai.agnets.util.DeepCodeSpringUtil;
import com.sankuai.deepcode.ai.common.ChatPoolConfig;
import com.sankuai.deepcode.ai.enums.ChatEnum;
import com.sankuai.deepcode.ai.llm.model.chat.*;
import com.sankuai.deepcode.ai.llm.openai.chat.AssistantMessage;
import com.sankuai.deepcode.ai.llm.openai.chat.ChatCompletionChoice;
import com.sankuai.deepcode.ai.llm.openai.chat.ChatCompletionResponse;
import com.sankuai.deepcode.ai.llm.openai.chat.Delta;
import com.sankuai.deepcode.ai.llm.service.OneApiService;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;

@Service
public class PlanFlowService {

    private static final Logger LOGGER = LoggerFactory.getLogger(PlanFlowService.class);

    @Autowired
    private OneApiService oneApiService;
    @Autowired
    private ChatPoolConfig chatPoolConfig;

    public static int maxStep = 10;

    public static String SystemPrompt = "#你是一个智能代码解析问答工具，你叫做deepcode，所有回答的问题都是基于某个代码仓库\n" +
            "#你目前支持的语言仅有java、python和js等前端语言\n" +
            "#部分问题都需要围绕方法，方法在仓库内会定义一个唯一id，一般需要先获取到唯一vid再进行查询" +
            "，当你需要获取vid时可以使用searchByMethodName工具，使用该工具后再判断是否在计划里追加其他工具\n" +
            "#部分问题需要先判断使用哪些文件，你可以获取整个工程的目录结构，方便判断需要获取哪些文件，例如当询问仓库内某些能力，此时需要先判断需要使用哪些特定的service类文件" +
            "，当你需要查询时请使用fileTree工具，该工具只能返回文件列表，使用该工具后再判断是否在计划里追加其他工具\n" +
            "#部分问题需要使用代码文件的总结内容，不需要遍历服务文件树和阅读源码，当前存在文件描述的知识库，可以通过向量召回的方式查找文件描述信息" +
            "，当你需要查询时请使用searchFileByEmbedding工具，该工具会直接返回文件关联内容，但是要注意根据问题思考一下可能会用到的文件，补充一下问题描述，不需要再追加其他工具\n" +
            "#当定位到需要某些文件后，你可以通过文件、方法、类等代码获取代码内容，例如询问工程是做什么的、xx功能是如何实现的，此时仅有文件名不能获取更多的详细信息" +
            "，当你需要查询源码时请使用getCode工具，该工具可以返回文件源码，使用该工具后再判断是否在计划里追加其他工具\n" +
            "#你可以通过方法获取方法的属性，包括方法唯一id、方法名称、类名、参数、返回值、修饰符、注解、注释、起始行、变更状态、变更行" +
            "，当你需要使用时请使用methodProperty工具，使用该工具后再判断是否在计划里追加其他工具\n" +
            "#除了静态的方法属性外，你还可以通过方法获取该方法上游调用的方法，补充更多链路信息，为了避免数据太多，默认最多支持查询10层调用关系，" +
            "，当你需要查询时请使用methodCallHierarchy工具，使用该工具后再判断是否在计划里追加其他工具\n" +
            "#一般工程师还会关心入口方法（工程内定义的api）或者出口方法（调用工程外的api或中间件），当这类问题是需要查询上游或者下游方法直至没有上下游的根节点" +
            "，当你需要获取方法调用关系时，请使用methodRoot工具，使用该工具后再判断是否在计划里追加其他工具\n" +
            "#一般工程师还会关心方法调用链路，例如方法A是如何调用到方法B的" +
            ",当你需要获取两个方法调用关系是，请使用methodLink工具，使用该工具后再判断是否在计划里追加其他工具\n" +
            "#如果用户还需要对其他项目进行分析，可以使用 analyseProject 工具，需要提供正确的GitUrl" +
            ",可依据传入的Git地址创建项目分析任务，Git地址必须是合法地址；\n - 分析的分支默认是master分支；\n - 如果给出1个对比分支时，源分支是该分支，对比分支是master；\n - 如果给出2个分支时，需要用户明确源分支、对比分支之后才可以创建分析任务；" +
            "###为了更好的处理流程，需要你返回一个标准的json，回答问题时需要先判断该问题是否需要进行其他内容，例如代码片段、文件等仓库内其他信息的补充\n" +
            "1、usePlan字段是布尔类型，标识是否需要进行复杂计划、调用工具后回答问题\n" +
            "2、plans字段是数组类型，复杂问题往往需要多个工具先后组合才能获取到最终的答案，标识需要使用哪些工具才能回答问题" +
            "，你需要将问题拆解，需要使用文件、方法、源码、调用关系等子节点，再根据问题选择需要的工作做串联，设计出一个完整的执行计划" +
            "，例如：方法getMethod方法的入口方法有哪些，需要先查询方法vid，再查询入口方法，如果此时没有vid需要先使用searchByMethodName再使用methodRoot，返回['searchByMethodName','methodRoot]\n" +
            "3、planDesc字段是字符串，当usePlan为true时，需要使用planDesc字段进行回答" +
            "，该字段需要你总结描述详细的思考过程，以及要使用的工具、工具编排的原因和目的\n" +
            "4、工具可以重复调用，例如查询方法vid后，再查询调用关系，再查询方法属性，再查询调用关系等,返回['searchByMethodName','methodCallHierarchy','searchByMethodName','methodProperty','methodCallHierarchy','analyseProject']，但是整体步骤不能超过" + maxStep + "步\n";


    public void initPlanFlow(InitPlanParam initPlanParam) {
        long itemId = initPlanParam.getItemId();
        List<ChatMessage> chatMessages = initPlanParam.getChatMessages();
        String modelName = initPlanParam.getModelName();
        SseEmitter sseEmitter = initPlanParam.getSseEmitter();
        ChatCompletionResponse chatCompletionResponse = initPlanParam.getChatCompletionResponse();
        PlanCompleteCallback planCompleteCallback = initPlanParam.getPlanCompleteCallback();
        if (CollectionUtils.isEmpty(chatCompletionResponse.choices())) {
            List<ChatCompletionChoice> choices = new ArrayList<>();
            ChatCompletionChoice chatCompletionChoice = ChatCompletionChoice.builder()
                    .delta(Delta.builder().build())
                    .message(AssistantMessage.builder().build())
                    .build();
            choices.add(chatCompletionChoice);
            chatCompletionResponse.choices(choices);
        }
        StringBuffer contentBuffer = new StringBuffer();
        StringBuffer processingContentBuffer = new StringBuffer();
        StringBuffer reasoningContentBuffer = new StringBuffer();
        //初始化计划写死使用R1
        chatPoolConfig.chatPoolConfig().submit(() -> {
                    oneApiService.completionsByStream(chatMessages,
                            ChatEnum.DEEPSEEK_R1.getName(),
                            new ArrayList<>(),
                            sseEmitter,
                            chatCompletionResponse,
                            true,
                            planCompleteCallback,
                            new StreamCompleteCallback() {
                                @Override
                                public void onComplete(SseEmitter emitter, ChatMsgRes chatMsgRes, ChatCompletionResponse chatCompletionResponse, PlanCompleteCallback planCompleteCallback) {
                                    // 处理完整内容
                                    if (chatMsgRes != null) {
                                        String fullContent = chatMsgRes.getContent();
                                        if (StringUtils.isNotEmpty(fullContent)) {
                                            PlanRes planRes = null;
                                            if (fullContent.startsWith("```json")) {
                                                fullContent = fullContent.split("```json")[1].split("```")[0].trim();
                                            } else if (fullContent.contains("```json")) {
                                                fullContent = fullContent.split("```json")[1].split("```")[0].trim();
                                            }
                                            try {
                                                Type type = new TypeToken<PlanRes>() {
                                                }.getType();
                                                planRes = JSON.parseObject(fullContent, type);
                                            } catch (Exception e) {
                                                finallyFlow(chatMessages, modelName, emitter, chatCompletionResponse, planCompleteCallback, contentBuffer, processingContentBuffer, reasoningContentBuffer);
                                                LOGGER.error("解析计划失败", e);
                                                return;
                                            }
                                            if (planRes != null && CollectionUtils.isNotEmpty(planRes.getPlans())) {
                                                List<ChatMessage> memorys = new ArrayList<>();
                                                ChatMessage chatMessage = new ChatMessage();
                                                chatMessage.setRole("user");
                                                chatMessage.setContent(planRes.getPlanDesc());
                                                memorys.add(chatMessage);
                                                planFlow(itemId, initPlanParam.getUserId(), chatMessages.get(chatMessages.size() - 1).getContent(), modelName, planRes.getPlans(), memorys, emitter, chatCompletionResponse, processingContentBuffer);
                                                endFlow(chatMessages.get(chatMessages.size() - 1).getContent(), modelName, memorys, emitter, chatCompletionResponse, planCompleteCallback, contentBuffer, processingContentBuffer, reasoningContentBuffer);
                                                return;
                                            }
                                        }
                                    }
                                    finallyFlow(chatMessages, modelName, emitter, chatCompletionResponse, planCompleteCallback, contentBuffer, processingContentBuffer, reasoningContentBuffer);
                                }

                            }
                            , contentBuffer,
                            processingContentBuffer,
                            reasoningContentBuffer
                    );
                }
        );
    }


    public void planFlow(long itemId, Long userId, String text, String modelName, List<String> plans, List<ChatMessage> memorys, SseEmitter sseEmitter, ChatCompletionResponse chatCompletionResponse,
                         StringBuffer processingContentBuffer) {
        LOGGER.info("开始执行计划");
        final AtomicBoolean stopPlan = new AtomicBoolean(false);
        final AtomicInteger index = new AtomicInteger(0);
        ChatCompletionChoice chatCompletionChoice = chatCompletionResponse.choices().get(0);
        int count = 0;
        for (String plan : plans) {
            count++;
            index.set(count);
            if (stopPlan.get()) {
                break;
            }
            try {
                chatCompletionChoice.delta().processingContent("\n开始执行" + plan + "工具\n\n\n ");
                chatCompletionChoice.message().processingContent(processingContentBuffer.toString());
                sseEmitter.send(SseEmitter.event().data(chatCompletionResponse));
            } catch (Exception e) {
//                throw new RuntimeException(e);
            }
            ChatTool chatTool = DeepCodeSpringUtil.getTools().get(plan);
            if (chatTool != null) {
                List<ChatMessage> chatMessages = new ArrayList<>();
                ChatMessage chatMessage = new ChatMessage();
                chatMessage.setRole("user");
                chatMessage.setContent(text);
                chatMessages.add(chatMessage);
                chatMessages.addAll(memorys);
                List<ChatTool> chatTools = new ArrayList<>();
                chatTools.add(chatTool);
                // tools调用写死使用4o mini
                ChatMsgRes chatMsgRes = oneApiService.completions(chatMessages, ChatEnum.GPT_4_1_MINI.getName(), chatTools);
                if (chatMsgRes != null && CollectionUtils.isNotEmpty(chatMsgRes.getChoices())) {
                    List<ToolCall> toolCalls = chatMsgRes.getChoices().get(0).getMessage().getTool_calls();
                    if (CollectionUtils.isNotEmpty(toolCalls)) {
                        Type mapType = new TypeToken<Map<String, String>>() {
                        }.getType();
                        Map<String, String> arguments = new HashMap<>();
                        try {
                            arguments = JSON.parseObject(toolCalls.get(0).getFunction().getArguments(), mapType);
                        } catch (Exception e) {
//                                        throw new RuntimeException(e);
                        }

                        Object object = DeepCodeSpringUtil.getBean(plan);
                        try {
                            DeepCodeToolBase deepCodeToolBase = (DeepCodeToolBase) object;
                            deepCodeToolBase.sseEmitter = sseEmitter;
                            deepCodeToolBase.itemId = itemId;
                            deepCodeToolBase.model = modelName;
                            deepCodeToolBase.chatText = text;
                            deepCodeToolBase.userId = userId;
                            if (deepCodeToolBase.chatParameter != null) {
                                Map<String, ChatPropertie> chatPropertieMap = deepCodeToolBase.chatParameter.getProperties();
                                for (Map.Entry<String, ChatPropertie> entry : chatPropertieMap.entrySet()) {
                                    String key = entry.getKey();
                                    if (arguments.containsKey(key)) {
                                        entry.getValue().setValue(arguments.get(key));
                                    }
                                }
                            }
                            String res = deepCodeToolBase.invoke();
                            if (StringUtils.isNotEmpty(res)) {
                                ChatMessage user = new ChatMessage();
                                user.setRole("user");
                                user.setContent(res);
                                memorys.add(user);

                                chatCompletionResponse.content(res);
                                for (char c : res.toCharArray()) {
                                    Thread.sleep(5);
                                    chatCompletionResponse.choices().get(0).delta().processingContent(String.valueOf(c));
                                    sseEmitter.send(SseEmitter.event().data(chatCompletionResponse));
                                }
                                processingContentBuffer.append("\n\n ").append(res).append("\n\n");
                                chatCompletionChoice.message().processingContent(processingContentBuffer.toString());
                                sseEmitter.send(SseEmitter.event().data(chatCompletionResponse));
                            }
                        } catch (Exception e) {
                            ChatMessage error = new ChatMessage();
                            error.setRole("assistant");
                            error.setContent("执行工具tool:" + plan + "异常");
                            memorys.add(error);
                            stopPlan.set(true);
                            LOGGER.error("planFlow error tool:", plan, e);
                        }
                    }
                }
            }
        }
    }


    public void endFlow(String text, String modelName, List<ChatMessage> memorys, SseEmitter
            sseEmitter, ChatCompletionResponse chatCompletionResponse, PlanCompleteCallback planCompleteCallback,
                        StringBuffer contentBuffer,
                        StringBuffer processingContentBuffer,
                        StringBuffer reasoningContentBuffer) {
        LOGGER.info("endFlow start");
        List<ChatMessage> chatMessages = new ArrayList<>();
        chatMessages.add(new ChatMessage("system", "你是DeepCode问题总结专家, 你会根据下述流程结果，全面总结思考过程，尽量使用markdown、流程图等结构化数据优化渲染问题答案，对于过程中的 Restrictions 、Observations、Reasoning 等信息会优先重点考虑"));
        chatMessages.addAll(memorys);
        ChatMessage chatMessage = new ChatMessage();
        chatMessage.setRole("user");
        chatMessage.setContent("当前问题是" + text + "\n");
        chatMessages.add(chatMessage);
        chatPoolConfig.chatPoolConfig().submit(() -> {
            oneApiService.completionsByStream(chatMessages, modelName, new ArrayList<>(), sseEmitter, chatCompletionResponse, false
                    , planCompleteCallback
                    , new StreamCompleteCallback() {
                        @Override
                        public void onComplete(SseEmitter emitter, ChatMsgRes chatMsgRes, ChatCompletionResponse chatCompletionResponse, PlanCompleteCallback planCompleteCallback) {
                            planCompleteCallback.onComplete(emitter, chatCompletionResponse);
                            LOGGER.info("endFlow complete");
                        }
                    }
                    , contentBuffer,
                    processingContentBuffer,
                    reasoningContentBuffer);
        });
    }

    public void finallyFlow(List<ChatMessage> chatMessages, String modelName, SseEmitter
            sseEmitter, ChatCompletionResponse chatCompletionResponse, PlanCompleteCallback planCompleteCallback,
                            StringBuffer contentBuffer,
                            StringBuffer processingContentBuffer,
                            StringBuffer reasoningContentBuffer) {
        LOGGER.info("finallyFlow start");
        //todo 临时删除
        if (chatMessages.get(0).getRole().equals("system")) {
            chatMessages.remove(0);
        }
        chatPoolConfig.chatPoolConfig().submit(() -> {
            oneApiService.completionsByStream(chatMessages, modelName, new ArrayList<>(), sseEmitter, chatCompletionResponse, false
                    , planCompleteCallback
                    , new StreamCompleteCallback() {
                        @Override
                        public void onComplete(SseEmitter emitter, ChatMsgRes chatMsgRes, ChatCompletionResponse chatCompletionResponse, PlanCompleteCallback planCompleteCallback) {
                            planCompleteCallback.onComplete(emitter, chatCompletionResponse);
                            LOGGER.info("endFlow complete");
                        }
                    }, contentBuffer, processingContentBuffer, reasoningContentBuffer);
        });
    }
}
