package com.sankuai.deepcode.ai.llm.openai;

import com.sankuai.deepcode.ai.llm.openai.chat.ChatCompletionRequest;
import com.sankuai.deepcode.ai.llm.openai.chat.ChatCompletionResponse;
import com.sankuai.deepcode.ai.llm.openai.search.SearchRequest;
import reactor.core.publisher.Flux;

/**
 * Package: com.sankuai.deepcode.ai.llm.openai
 * Description:
 *
 * <AUTHOR>
 * @since 2025/3/19 19:34
 */
public interface FluxClient {

    Flux<ChatCompletionResponse> chatFluxCompletion(ChatCompletionRequest request);

    Flux<ChatCompletionResponse> chatFluxCompletion(String userMessage);


    /**
     * 聊天搜索完成
     *
     * @param userMessage   用户消息
     * @param searchRequest 搜索请求
     * @return {@link Flux }<{@link ChatCompletionResponse }>
     */
    Flux<ChatCompletionResponse> chatSearchFluxCompletion(String userMessage, SearchRequest searchRequest);

    /**
     * 聊天搜索
     *
     * @param userMessage 用户消息
     * @return {@link Flux }<{@link ChatCompletionResponse }>
     */
    Flux<ChatCompletionResponse> chatSearchFluxCompletion(String userMessage);
}
