package com.sankuai.deepcode.ai.enums;

public enum ChatEnum {
    LONGCAT_128K(0, "LongCat-8B-128K-Chat", 128 * 1024, "openApi"),
    GPT_4O_MINI(0, "gpt-4o-mini", 128 * 1024, "openApi"),
    GPT_4_1_MINI(0, "gpt-4.1-mini", 1024 * 1024, "openApi"),
    GPT_4_1(0, "gpt-4.1", 1024 * 1024, "openApi"),
    DEEPSEEK_CHAT(0, "deepseek-chat", 64 * 1024, "openApi"),
    DEEPSEEK_REASONER(0, "deepseek-reasoner", 64 * 1024, "openApi"),
    DEEPSEEK_V3(0, "deepseek-v3-friday", 32 * 1024, "openApi"),
    DEEPSEEK_R1(0, "deepseek-r1-friday", 32 * 1024, "openApi"),
    CLAUDE_3_5(0, "anthropic.claude-3.5-sonnet", 200 * 1024, "claude"),
    CLAUDE_3_7(0, "anthropic.claude-3.7-sonnet", 200 * 1024, "claude"),;

    private int code;
    private String name;
    private int context;
    private String apiType;

    ChatEnum(int code, String name, int context, String apiType) {
        this.code = code;
        this.name = name;
        this.context = context;
        this.apiType = apiType;
    }

    public int getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public int getContext() {
        return context;
    }

    public String getApiType() {
        return apiType;
    }

    @Override
    public String toString() {
        return "ChatEnum{" +
                "code=" + code +
                ", name='" + name + '\'' +
                ", context=" + context +
                ", apiType='" + apiType + '\'' +
                '}';
    }
}
