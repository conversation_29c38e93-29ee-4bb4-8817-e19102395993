package com.sankuai.deepcode.ai.llm.openai;

import com.sankuai.deepcode.ai.llm.openai.chat.ChatCompletionRequest;
import com.sankuai.deepcode.ai.llm.openai.chat.ChatCompletionResponse;
import com.sankuai.deepcode.ai.llm.openai.completion.CompletionRequest;
import com.sankuai.deepcode.ai.llm.openai.completion.CompletionResponse;
import com.sankuai.deepcode.ai.llm.openai.embedding.EmbeddingRequest;
import com.sankuai.deepcode.ai.llm.openai.embedding.EmbeddingResponse;
import com.sankuai.deepcode.ai.llm.openai.models.ModelsResponse;
import com.sankuai.deepcode.ai.llm.openai.moderation.ModerationRequest;
import com.sankuai.deepcode.ai.llm.openai.moderation.ModerationResponse;
import retrofit2.Call;
import retrofit2.http.*;

import java.util.Map;

/**
 * <AUTHOR>
 */
public interface OpenAiApi {

    @POST("completions")
    @Headers("Content-Type: application/json")
    Call<CompletionResponse> completions(
            @Body CompletionRequest request,
            @Query("api-version") String apiVersion
    );

    @POST("completions")
    @Headers("Content-Type: application/json")
    Call<CompletionResponse> completions(
            @HeaderMap Map<String, String> headers,
            @Body CompletionRequest request,
            @Query("api-version") String apiVersion
    );

    @POST("chat/completions")
    @Headers("Content-Type: application/json")
    Call<ChatCompletionResponse> chatCompletions(
            @Body ChatCompletionRequest request,
            @Query("api-version") String apiVersion
    );

    @POST("chat/completions")
    @Headers("Content-Type: application/json")
    Call<ChatCompletionResponse> chatCompletions(
            @HeaderMap Map<String, String> headers,
            @Body ChatCompletionRequest request,
            @Query("api-version") String apiVersion
    );

    @POST("moderations")
    @Headers("Content-Type: application/json")
    Call<ModerationResponse> moderations(
            @Body ModerationRequest request,
            @Query("api-version") String apiVersion
    );

    @POST("moderations")
    @Headers("Content-Type: application/json")
    Call<ModerationResponse> moderations(
            @HeaderMap Map<String, String> headers,
            @Body ModerationRequest request,
            @Query("api-version") String apiVersion
    );

    @GET("models")
    @Headers("Content-Type: application/json")
    Call<ModelsResponse> models(
            @HeaderMap Map<String, String> headers,
            @Query("api-version") String apiVersion
    );

    @POST("embeddings")
    @Headers("Content-Type: application/json")
    Call<EmbeddingResponse> embeddings(
            @Body EmbeddingRequest request,
            @Query("api-version") String apiVersion
    );

    @POST("embeddings")
    @Headers("Content-Type: application/json")
    Call<EmbeddingResponse> embeddings(
            @HeaderMap Map<String, String> headers,
            @Body EmbeddingRequest request,
            @Query("api-version") String apiVersion
    );

}
