package com.sankuai.deepcode.ai.llm.openai.embedding;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.*;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR>
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
@EqualsAndHashCode
@Setter
@Getter
@Accessors(fluent = true)
@Builder(builderClassName = "Builder")
@ToString
public class Embedding {

    @JsonProperty
    private List<Float> embedding;

    @JsonProperty
    private Integer index;

}
