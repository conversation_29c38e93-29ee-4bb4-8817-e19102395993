package com.sankuai.deepcode.ai.llm.openai.embedding;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.fasterxml.jackson.databind.annotation.JsonPOJOBuilder;
import com.sankuai.deepcode.ai.llm.openai.shared.Usage;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.List;

import static java.util.Collections.unmodifiableList;

/**
 * <AUTHOR>
 */
@JsonDeserialize(builder = EmbeddingResponse.Builder.class)
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
@EqualsAndHashCode
@ToString
public final class EmbeddingResponse {

    @JsonProperty
    private final String model;

    @JsonProperty
    private final List<Embedding> data;

    @JsonProperty
    private final Usage usage;

    private EmbeddingResponse(Builder builder) {
        this.model = builder.model;
        this.data = builder.data;
        this.usage = builder.usage;
    }

    public String model() {
        return model;
    }

    public List<Embedding> data() {
        return data;
    }

    public Usage usage() {
        return usage;
    }

    /**
     * Convenience method to get the embedding from the first data.
     */
    public List<Float> embedding() {
        return data.get(0).embedding();
    }

    public static Builder builder() {
        return new Builder();
    }

    @JsonPOJOBuilder(withPrefix = "")
    @JsonIgnoreProperties(ignoreUnknown = true)
    @JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
    public static final class Builder {

        private String model;

        private List<Embedding> data;

        private Usage usage;

        private Builder() {
        }

        public Builder model(String model) {
            this.model = model;
            return this;
        }

        public Builder data(List<Embedding> data) {
            if (data != null) {
                this.data = unmodifiableList(data);
            }
            return this;
        }

        public Builder usage(Usage usage) {
            this.usage = usage;
            return this;
        }

        public EmbeddingResponse build() {
            return new EmbeddingResponse(this);
        }

    }

}
