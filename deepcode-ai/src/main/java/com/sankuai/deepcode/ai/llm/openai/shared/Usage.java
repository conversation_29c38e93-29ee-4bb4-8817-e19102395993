package com.sankuai.deepcode.ai.llm.openai.shared;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.fasterxml.jackson.databind.annotation.JsonPOJOBuilder;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * <AUTHOR>
 */
@JsonDeserialize(builder = Usage.Builder.class)
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
@EqualsAndHashCode
@ToString
public final class Usage {

    @JsonProperty
    private final Integer totalTokens;

    @JsonProperty
    private final Integer promptTokens;

    @JsonProperty
    private final PromptTokensDetails promptTokensDetails;

    @JsonProperty
    private final Integer completionTokens;

    @JsonProperty
    private final CompletionTokensDetails completionTokensDetails;

    private Usage(Builder builder) {
        this.totalTokens = builder.totalTokens;
        this.promptTokens = builder.promptTokens;
        this.promptTokensDetails = builder.promptTokensDetails;
        this.completionTokens = builder.completionTokens;
        this.completionTokensDetails = builder.completionTokensDetails;
    }

    public Integer totalTokens() {
        return totalTokens;
    }

    public Integer promptTokens() {
        return promptTokens;
    }

    public PromptTokensDetails promptTokensDetails() {
        return promptTokensDetails;
    }

    public Integer completionTokens() {
        return completionTokens;
    }

    public CompletionTokensDetails completionTokensDetails() {
        return completionTokensDetails;
    }

    public static Builder builder() {
        return new Builder();
    }

    @JsonPOJOBuilder(withPrefix = "")
    @JsonIgnoreProperties(ignoreUnknown = true)
    @JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
    public static final class Builder {

        private Integer totalTokens;

        private Integer promptTokens;

        private PromptTokensDetails promptTokensDetails;

        private Integer completionTokens;

        private CompletionTokensDetails completionTokensDetails;

        private Builder() {
        }

        public Builder totalTokens(Integer totalTokens) {
            this.totalTokens = totalTokens;
            return this;
        }

        public Builder promptTokens(Integer promptTokens) {
            this.promptTokens = promptTokens;
            return this;
        }

        public Builder promptTokensDetails(PromptTokensDetails promptTokensDetails) {
            this.promptTokensDetails = promptTokensDetails;
            return this;
        }

        public Builder completionTokens(Integer completionTokens) {
            this.completionTokens = completionTokens;
            return this;
        }

        public Builder completionTokensDetails(CompletionTokensDetails completionTokensDetails) {
            this.completionTokensDetails = completionTokensDetails;
            return this;
        }

        public Usage build() {
            return new Usage(this);
        }

    }

}
