package com.sankuai.deepcode.ai.agnets.tools;

import com.sankuai.deepcode.ai.agnets.tool.DeepCodeToolBase;
import com.sankuai.deepcode.ai.agnets.tool.DeepCodeTool;
import com.sankuai.deepcode.ai.llm.model.chat.ChatParameter;
import com.sankuai.deepcode.ai.llm.model.chat.ChatPropertie;
import com.sankuai.deepcode.dao.domain.CodeMethodAnalysis;
import com.sankuai.deepcode.dao.domain.MethodInvokeMethod;
import com.sankuai.deepcode.dao.service.CodeMethodAnalysisService;
import com.sankuai.deepcode.dao.service.MethodInvokeMethodService;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.*;

/**
 * <AUTHOR>
 */
@DeepCodeTool(name = "methodLink", description = "查询两个方法调用链路，例如A方法如何调用B方法")
public class MethodLink extends DeepCodeToolBase {

    @Autowired
    private CodeMethodAnalysisService codeMethodAnalysisService;
    @Autowired
    private MethodInvokeMethodService methodInvokeMethodService;

    @Override
    public void initParameter() {
        this.chatParameter = new ChatParameter();
        chatParameter.setType("object");
        List<String> required = new ArrayList<>();
        required.add("startVid");
        required.add("endVid");
        chatParameter.setRequired(required);
        Map<String, ChatPropertie> properties = new HashMap<>();
        ChatPropertie startVid = new ChatPropertie();
        startVid.setType("string");
        startVid.setDescription("起点方法方法唯一vid");
        properties.put("startVid", startVid);

        ChatPropertie endVid = new ChatPropertie();
        endVid.setType("string");
        endVid.setDescription("终点方法方法唯一vid");
        properties.put("endVid", endVid);
        chatParameter.setProperties(properties);
    }

    @Override
    public String invoke() throws Exception {
        String startVid = this.chatParameter.getProperties().get("startVid").getValue();
        String endVid = this.chatParameter.getProperties().get("endVid").getValue();
        long itemId = this.itemId;
        Set<String> vids = new HashSet<>();
        List<MethodInvokeMethod> methodInvokeMethods = new ArrayList<>();
        List<MethodInvokeMethod> methods = methodInvokeMethodService.getBySource(itemId, startVid);
        boolean links = getDown(itemId, 0, endVid, methods, vids, methodInvokeMethods);
        StringBuilder stringBuilder = new StringBuilder();
        if (links) {
            List<String> res = findShortestPathBFS(startVid, endVid, methodInvokeMethods);
            List<CodeMethodAnalysis> codeMethodAnalyses = codeMethodAnalysisService.getByMethodVids(itemId, res);
            int i = 0;
            for (CodeMethodAnalysis codeMethodAnalysis : codeMethodAnalyses) {
                if (i == codeMethodAnalyses.size() - 1) {
                    stringBuilder.append(codeMethodAnalysis.getMethodName() + "(vid:" + codeMethodAnalysis.getMethodVid() + ")");
                } else {
                    stringBuilder.append(codeMethodAnalysis.getMethodName() + "(vid:" + codeMethodAnalysis.getMethodVid() + ")" + "->");
                }
            }
        }
        if (stringBuilder.length() == 0) {
            throw new Exception("未找到调用链路");
        } else {
            return stringBuilder.toString();
        }
    }


    public boolean getDown(long itemId, int step, String endVid, List<MethodInvokeMethod> methods, Set<String> vids, List<MethodInvokeMethod> methodInvokeMethods) {
        if (step >= 10) {
            return false;
        }
        for (MethodInvokeMethod methodInvokeMethod : methods) {
            if (vids.contains(methodInvokeMethod.getTarget())) {
                continue;
            } else {
                vids.add(methodInvokeMethod.getTarget());
                methodInvokeMethods.add(methodInvokeMethod);
                if (methodInvokeMethod.getTarget().equals(endVid)) {
                    return true;
                }
            }
            List<MethodInvokeMethod> ups = methodInvokeMethodService.getBySource(itemId, methodInvokeMethod.getTarget());
            if (CollectionUtils.isNotEmpty(ups)) {
                boolean res = getDown(itemId, step++, endVid, ups, vids, methodInvokeMethods);
                if (res) {
                    return true;
                }
            }
        }
        return false;
    }


    // 方式2：BFS实现（找到最短路径）
    public static List<String> findShortestPathBFS(String A, String B, List<MethodInvokeMethod> methodInvokeMethods) {
        // 构建邻接表
        Map<String, List<String>> graph = new HashMap<>();
        for (MethodInvokeMethod method : methodInvokeMethods) {
            graph.computeIfAbsent(method.getSource(), k -> new ArrayList<>())
                    .add(method.getTarget());
        }

        // BFS搜索
        Queue<List<String>> queue = new LinkedList<>();
        Set<String> visited = new HashSet<>();
        queue.add(Collections.singletonList(A));
        visited.add(A);

        while (!queue.isEmpty()) {
            List<String> path = queue.poll();
            String current = path.get(path.size() - 1);

            if (current.equals(B)) return path;

            for (String neighbor : graph.getOrDefault(current, Collections.emptyList())) {
                if (!visited.contains(neighbor)) {
                    visited.add(neighbor);
                    List<String> newPath = new ArrayList<>(path);
                    newPath.add(neighbor);
                    queue.add(newPath);
                }
            }
        }
        return Collections.emptyList();
    }

}
