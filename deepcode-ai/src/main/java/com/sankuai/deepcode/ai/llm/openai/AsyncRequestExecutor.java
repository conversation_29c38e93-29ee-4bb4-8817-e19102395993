package com.sankuai.deepcode.ai.llm.openai;

import org.jetbrains.annotations.NotNull;
import retrofit2.Call;

import java.io.IOException;
import java.util.function.Consumer;
import java.util.function.Function;

import static com.sankuai.deepcode.ai.llm.openai.Utils.toException;


class AsyncRequestExecutor<Response, ResponseContent> {

    private final Call<Response> call;
    private final Function<Response, ResponseContent> responseContentExtractor;

    AsyncRequestExecutor(Call<Response> call, Function<Response, ResponseContent> responseContentExtractor) {
        this.call = call;
        this.responseContentExtractor = responseContentExtractor;
    }

    void onResponse(Consumer<ResponseContent> responseHandler, Consumer<Throwable> errorHandler) {
        call.enqueue(new retrofit2.Callback<Response>() {
            @Override
            public void onResponse(@NotNull Call<Response> call, @NotNull retrofit2.Response<Response> response) {
                if (response.isSuccessful()) {
                    responseHandler.accept(responseContentExtractor.apply(response.body()));
                } else {
                    try {
                        errorHandler.accept(toException(response));
                    } catch (IOException e) {
                        throw new RuntimeException(e);
                    }
                }
            }

            @Override
            public void onFailure(@NotNull Call<Response> call, @NotNull Throwable t) {
                errorHandler.accept(t);
            }
        });
    }

}
