package com.sankuai.deepcode.ai.agnets.tools;

import com.alibaba.fastjson.JSON;
import com.sankuai.deepcode.ai.agnets.tool.DeepCodeToolBase;
import com.sankuai.deepcode.ai.agnets.tool.DeepCodeTool;
import com.sankuai.deepcode.ai.llm.model.chat.ChatParameter;
import com.sankuai.deepcode.ai.llm.model.chat.ChatPropertie;
import com.sankuai.deepcode.ast.enums.NodeSourceEnum;
import com.sankuai.deepcode.dao.domain.CodeMethodAnalysis;
import com.sankuai.deepcode.dao.domain.MethodInvokeMethod;
import com.sankuai.deepcode.dao.service.CodeMethodAnalysisService;
import com.sankuai.deepcode.dao.service.MethodInvokeMethodService;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.*;

/**
 * <AUTHOR>
 */
@DeepCodeTool(name = "methodRoot", description = "获取方法入口（定义api/没有工程内方法作为上游调用的）或出口方法(调用外部api/没有工程内方法作为下游调用)")
public class MethodRoot extends DeepCodeToolBase {

    @Autowired
    private CodeMethodAnalysisService codeMethodAnalysisService;
    @Autowired
    private MethodInvokeMethodService methodInvokeMethodService;

    @Override
    public void initParameter() {
        this.chatParameter = new ChatParameter();
        chatParameter.setType("object");
        List<String> required = new ArrayList<>();
        required.add("methodVid");
        required.add("rootType");
        chatParameter.setRequired(required);
        Map<String, ChatPropertie> properties = new HashMap<>();
        ChatPropertie methodVid = new ChatPropertie();
        methodVid.setType("string");
        methodVid.setDescription("方法唯一vid");
        properties.put("methodVid", methodVid);

        ChatPropertie rootType = new ChatPropertie();
        rootType.setType("string");
        rootType.setDescription("入口为up，出口为down");
        properties.put("rootType", rootType);
        chatParameter.setProperties(properties);
    }

    @Override
    public String invoke() throws Exception {
        String methodVid = this.chatParameter.getProperties().get("methodVid").getValue();
        String rootType = this.chatParameter.getProperties().get("rootType").getValue();
        long itemId = this.itemId;
        Set<String> vids = new HashSet<>();

        List<Map<String, String>> list = new ArrayList<>();
        List<String> resVids = new ArrayList<>();
        if (rootType.equals("up")) {
            List<MethodInvokeMethod> methods = methodInvokeMethodService.getByTarget(itemId, methodVid);
            getUp(itemId, methods, vids, resVids);
        } else {
            List<MethodInvokeMethod> methods = methodInvokeMethodService.getBySource(itemId, methodVid);
            getDown(itemId, methods, vids, resVids);
        }
        initRes(list, resVids);
        if (CollectionUtils.isEmpty(list)) {
            if (rootType.equals("up")) {
                throw new Exception("该方法没有查询到上游入口方法");
            } else {
                throw new Exception("该方法没有查询到下游出口方法");
            }
        }
        return JSON.toJSONString(list);
    }

    public void getUp(long itemId, List<MethodInvokeMethod> methods, Set<String> vids, List<String> resVids) {
        for (MethodInvokeMethod methodInvokeMethod : methods) {
            if (vids.contains(methodInvokeMethod.getSource())) {
                continue;
            } else {
                vids.add(methodInvokeMethod.getSource());
            }
            List<MethodInvokeMethod> ups = methodInvokeMethodService.getByTarget(itemId, methodInvokeMethod.getSource());
            if (CollectionUtils.isEmpty(ups)) {
                resVids.add(methodInvokeMethod.getSource());
            } else {
                getUp(itemId, ups, vids, resVids);
            }
        }
    }


    public void getDown(long itemId, List<MethodInvokeMethod> methods, Set<String> vids, List<String> resVids) {
        for (MethodInvokeMethod methodInvokeMethod : methods) {
            if (vids.contains(methodInvokeMethod.getTarget())) {
                continue;
            } else {
                vids.add(methodInvokeMethod.getTarget());
            }
            List<MethodInvokeMethod> ups = methodInvokeMethodService.getBySource(itemId, methodInvokeMethod.getTarget());
            if (CollectionUtils.isEmpty(ups)) {
                if (methodInvokeMethod.getTargetType().equals(NodeSourceEnum.UNKNOWN.getCode())) {
                    resVids.add(methodInvokeMethod.getTarget());
                }
            } else {
                getDown(itemId, ups, vids, resVids);
            }
        }
    }

    public void initRes(List<Map<String, String>> list, List<String> vids) {
        List<CodeMethodAnalysis> codeMethodAnalyses = codeMethodAnalysisService.getByMethodVids(itemId, vids);
        if (CollectionUtils.isNotEmpty(codeMethodAnalyses)) {
            for (CodeMethodAnalysis codeMethodAnalysis : codeMethodAnalyses) {
                Map<String, String> method = new HashMap<>();
                method.put("methodVid", codeMethodAnalysis.getMethodVid());
                method.put("methodName", codeMethodAnalysis.getMethodName());
                list.add(method);
            }
        }
    }

}
