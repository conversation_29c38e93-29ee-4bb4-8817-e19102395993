package com.sankuai.deepcode.ai.llm.openai.chat;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.fasterxml.jackson.databind.annotation.JsonPOJOBuilder;
import lombok.AllArgsConstructor;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

import static com.sankuai.deepcode.ai.llm.openai.chat.ContentType.IMAGE_URL;
import static com.sankuai.deepcode.ai.llm.openai.chat.ContentType.TEXT;
import static com.sankuai.deepcode.ai.llm.openai.chat.Role.USER;
import static java.util.Collections.unmodifiableList;

/**
 * <AUTHOR>
 */
@JsonDeserialize(builder = UserMessage.Builder.class)
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
@EqualsAndHashCode
@ToString(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
public final class UserMessage implements Message {

    @JsonProperty
    private Role role = USER;

    @JsonProperty
    private Object content;

    @JsonProperty
    private String name;

    private UserMessage(Builder builder) {
        this.content = builder.stringContent != null ? builder.stringContent : builder.content;
        this.name = builder.name;
    }

    @Override
    public Role role() {
        return role;
    }

    public Object content() {
        return content;
    }

    public String name() {
        return name;
    }

    public static UserMessage from(String text) {
        return UserMessage.builder().content(text).build();
    }

    public static List<UserMessage> from(List<String> textList) {
        if (CollectionUtils.isEmpty(textList)) {
            return Collections.emptyList();
        }
        return textList.stream().map(String::trim).filter(StringUtils::isNotEmpty).map(UserMessage::from).collect(Collectors.toList());
    }

    public static UserMessage from(String text, String... imageUrls) {
        return UserMessage.builder().addText(text).addImageUrls(imageUrls).build();
    }

    public static Builder builder() {
        return new Builder();
    }

    @JsonPOJOBuilder(withPrefix = "")
    @JsonIgnoreProperties(ignoreUnknown = true)
    @JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
    public static final class Builder {

        private String stringContent; // keeping it for compatibility with other
        // OpenAI-like APIs

        private List<Content> content;

        private String name;

        private Builder() {
        }

        public Builder addText(String text) {
            initializeContent();
            Content content = Content.builder().type(TEXT).text(text).build();
            this.content.add(content);
            return this;
        }

        public Builder addImageUrl(String imageUrl) {
            return addImageUrl(imageUrl, null);
        }

        public Builder addImageUrl(String imageUrl, ImageDetail imageDetail) {
            initializeContent();
            Content content = Content.builder().type(IMAGE_URL)
                    .imageUrl(ImageUrl.builder().url(imageUrl).detail(imageDetail).build()).build();
            this.content.add(content);
            return this;
        }

        public Builder addImageUrls(String... imageUrls) {
            for (String imageUrl : imageUrls) {
                addImageUrl(imageUrl);
            }
            return this;
        }

        public Builder addInputAudio(InputAudio inputAudio) {
            initializeContent();
            this.content.add(Content.builder().type(ContentType.AUDIO).inputAudio(inputAudio).build());

            return this;
        }

        public Builder content(List<Content> content) {
            if (content != null) {
                this.content = unmodifiableList(content);
            }
            return this;
        }

        public Builder content(String content) {
            this.stringContent = content;
            return this;
        }

        public Builder name(String name) {
            this.name = name;
            return this;
        }

        public UserMessage build() {
            return new UserMessage(this);
        }

        private void initializeContent() {
            if (this.content == null) {
                this.content = new ArrayList<>();
            }
        }

    }

}
