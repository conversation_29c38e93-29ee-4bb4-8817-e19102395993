package com.sankuai.deepcode.ai.agnets.tools;

import com.alibaba.fastjson.JSON;
import com.sankuai.deepcode.ai.agnets.tool.DeepCodeToolBase;
import com.sankuai.deepcode.ai.agnets.tool.DeepCodeTool;
import com.sankuai.deepcode.ai.llm.model.chat.ChatParameter;
import com.sankuai.deepcode.ai.llm.model.chat.ChatPropertie;
import com.sankuai.deepcode.dao.domain.CodeMethodAnalysis;
import com.sankuai.deepcode.dao.service.CodeMethodAnalysisService;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@DeepCodeTool(name = "methodProperty", description = "根据方法vid查询方法属性,可以支持查询到的属性有:\n" +
        "模块名称（moduleName）,\n" +
        "className名称（className）,\n" +
        "方法名称（methodName）,\n" +
        "模块名称（moduleName）,\n" +
        "修饰符（access）,\n" +
        "参数（params）,\n" +
        "返回值（returnInfo）,\n" +
        "注解（annotations）,\n" +
        "起始行（startLine）,\n" +
        "截止行（endLine）,\n" +
        "圈复杂度（complexity）,\n" +
        "变更状态 0无变更  1新增 2变更（changeType）,\n" +
        "变更行（changeLines）,\n" +
        "注释行（commentLines)")
public class MethodProperty extends DeepCodeToolBase {

    @Autowired
    private CodeMethodAnalysisService codeMethodAnalysisService;

    @Override
    public void initParameter() {
        this.chatParameter = new ChatParameter();
        chatParameter.setType("object");
        List<String> required = new ArrayList<>();
        required.add("methodVid");
        chatParameter.setRequired(required);
        Map<String, ChatPropertie> properties = new HashMap<>();
        ChatPropertie methodVid = new ChatPropertie();
        methodVid.setType("methodVid");
        methodVid.setDescription("方法唯一vid");
        properties.put("methodVid", methodVid);
        chatParameter.setProperties(properties);
    }

    @Override
    public String invoke() throws Exception {
        String methodVid = this.chatParameter.getProperties().get("methodVid").getValue();
        long itemId = this.itemId;
        CodeMethodAnalysis codeMethodAnalysis = codeMethodAnalysisService.getByMethodVid(itemId, methodVid);
        Map<String, String> method = new HashMap<>();
        if (codeMethodAnalysis != null) {
            method.put("methodVid", codeMethodAnalysis.getMethodVid());
            method.put("方法名称（methodName", codeMethodAnalysis.getMethodName());
            method.put("className名称", codeMethodAnalysis.getClassName());
            method.put("模块名称（moduleName", codeMethodAnalysis.getModuleName());
            method.put("修饰符（access）", codeMethodAnalysis.getAccess());
            method.put("参数（params）", codeMethodAnalysis.getParams());
            method.put("返回值（returnInfo）", codeMethodAnalysis.getReturnInfo());
            method.put("注解（annotations", codeMethodAnalysis.getAnnotations());
            method.put("起始行（startLine）", String.valueOf(codeMethodAnalysis.getStartLine()));
            method.put("截止行（endLine)", String.valueOf(codeMethodAnalysis.getEndLine()));
            method.put("圈复杂度（complexity)", String.valueOf(codeMethodAnalysis.getComplexity()));
            method.put("变更状态 0无变更  1新增 2变更（changeType）", String.valueOf(codeMethodAnalysis.getChangeType()));
            method.put("变更行（changeLines）", String.valueOf(codeMethodAnalysis.getChangeLines()));
            method.put("注释行（commentLines)", String.valueOf(codeMethodAnalysis.getCommentLines()));
        } else {
            throw new Exception("未查询到方法信息");
        }
        return JSON.toJSONString(method);
    }

}
