package com.sankuai.deepcode.ai.agnets.tools;

import com.alibaba.fastjson.JSON;
import com.sankuai.deepcode.ai.agnets.tool.DeepCodeToolBase;
import com.sankuai.deepcode.ai.agnets.tool.DeepCodeTool;
import com.sankuai.deepcode.ai.llm.model.chat.ChatParameter;
import com.sankuai.deepcode.ai.llm.model.chat.ChatPropertie;
import com.sankuai.deepcode.dao.domain.CodeMethodAnalysis;
import com.sankuai.deepcode.dao.service.CodeMethodAnalysisService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@DeepCodeTool(name = "searchByMethodName", description = "根据方法名称查询方法唯一vid，一般都是纯英文或拼音的标准驼峰格式")
public class SearchByMethodName extends DeepCodeToolBase {

    @Autowired
    private CodeMethodAnalysisService codeMethodAnalysisService;

    @Override
    public void initParameter() {
        this.chatParameter = new ChatParameter();
        chatParameter.setType("object");
        List<String> required = new ArrayList<>();
        required.add("methodName");
        chatParameter.setRequired(required);
        Map<String, ChatPropertie> properties = new HashMap<>();
        ChatPropertie chatPropertie = new ChatPropertie();
        chatPropertie.setType("string");
        chatPropertie.setDescription("方法名称");
        properties.put("methodName", chatPropertie);
        chatParameter.setProperties(properties);
    }

    @Override
    public String invoke() throws Exception {
        String methodName = this.chatParameter.getProperties().get("methodName").getValue();
        long itemId = this.itemId;
        if (StringUtils.isNotEmpty(methodName)) {
            List<CodeMethodAnalysis> codeMethodAnalyses = codeMethodAnalysisService.getByItemAndMethodName(itemId, methodName);
            if (CollectionUtils.isEmpty(codeMethodAnalyses)) {
                return "未找到方法";
            } else {
                CodeMethodAnalysis codeMethodAnalysis = codeMethodAnalyses.get(0);
                Map<String, String> map = new HashMap<>();
                map.put("methodVid", codeMethodAnalysis.getMethodVid());
                map.put("methodName", codeMethodAnalysis.getMethodName());
                return JSON.toJSONString(map);
            }
        }
        throw new Exception("通过方法名：" + methodName + "未找到方法唯一id");
    }

}
