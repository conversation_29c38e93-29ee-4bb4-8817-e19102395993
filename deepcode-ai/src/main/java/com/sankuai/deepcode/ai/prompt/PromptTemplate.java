package com.sankuai.deepcode.ai.prompt;

import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.Getter;
import org.apache.commons.text.StringSubstitutor;
import org.apache.commons.text.lookup.StringLookupFactory;

import java.util.Collections;
import java.util.Map;
import java.util.Set;

/**
 * Package: com.sankuai.deepcode.ai.prompt
 * Description:
 *
 * <AUTHOR>
 * @since 2025/3/20 17:43
 */
public class PromptTemplate {
    private final StringSubstitutor templateProcessor;
    private final Map<String, Object> variables = Maps.newHashMap();
    @Getter
    private String template;


    public PromptTemplate(String template) {
        this.template = template;
        this.templateProcessor = new StringSubstitutor(Maps.newHashMap())
                // 不抛异常
                .setEnableUndefinedVariableException(false)
                // 启用默认值支持
                .setValueDelimiter(":-");
    }

    public static PromptTemplate of(String template) {
        return new PromptTemplate(template);
    }

    public PromptTemplate withVariables(Map<String, Object> variables) {
        this.variables.putAll(variables);
        return this;
    }

    public PromptTemplate withVariable(String key, Object value) {
        this.variables.put(key, value);
        return this;
    }

    private Set<String> extractVariables(String template) {
        Set<String> variables = Sets.newHashSet();
        int start = 0;
        while ((start = template.indexOf("${", start)) != -1) {
            int end = template.indexOf("}", start);
            if (end != -1) {
                String var = template.substring(start + 2, end);
                // 处理有默认值的情况
                int defaultSep = var.indexOf(":-");
                if (defaultSep != -1) {
                    var = var.substring(0, defaultSep);
                }
                variables.add(var);
                start = end;
            }
        }
        return variables;
    }

    public PromptTemplate format(Map<String, Object> variables) {
        templateProcessor.setVariableResolver(StringLookupFactory.INSTANCE.mapStringLookup(variables));
        this.template = templateProcessor.replace(template);
        return this;
    }

    public PromptTemplate format(String key, Object value) {
        return format(Collections.singletonMap(key, value));
    }

    public PromptTemplate formatWithDefaultValue(Map<String, Object> variables, String defaultValue) {
        Map<String, Object> varsWithDefaults = Maps.newHashMap(variables);
        // 为所有变量添加默认值
        extractVariables(template).forEach(var ->
                varsWithDefaults.putIfAbsent(var, defaultValue)
        );
        templateProcessor.setVariableResolver(StringLookupFactory.INSTANCE.mapStringLookup(varsWithDefaults));
        this.template = templateProcessor.replace(template);
        return this;
    }

    public PromptTemplate format() {
        format(variables);
        return this;
    }

    @Override
    public String toString() {
        format();
        return template;
    }
}
