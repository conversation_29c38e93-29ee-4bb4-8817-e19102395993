package com.sankuai.deepcode.ai.llm.oneapi;

import com.sankuai.deepcode.ai.llm.openai.*;
import com.sankuai.deepcode.ai.llm.openai.chat.ChatCompletionRequest;
import com.sankuai.deepcode.ai.llm.openai.chat.ChatCompletionResponse;
import com.sankuai.deepcode.ai.llm.openai.completion.CompletionRequest;
import com.sankuai.deepcode.ai.llm.openai.completion.CompletionResponse;
import com.sankuai.deepcode.ai.llm.openai.models.ModelsResponse;
import com.sankuai.deepcode.ai.llm.openai.moderation.ModerationRequest;
import com.sankuai.deepcode.ai.llm.openai.moderation.ModerationResponse;
import com.sankuai.deepcode.ai.llm.openai.moderation.ModerationResult;
import com.sankuai.deepcode.ai.llm.openai.search.SearchApi;
import com.sankuai.deepcode.ai.llm.openai.search.SearchRequest;
import com.sankuai.deepcode.ai.llm.openai.search.SearchResponse;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Cache;
import okhttp3.OkHttpClient;
import org.apache.commons.lang3.StringUtils;
import reactor.core.publisher.Flux;
import retrofit2.Retrofit;
import retrofit2.converter.jackson.JacksonConverterFactory;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;

/**
 * OneApi 客户端
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
@Slf4j
public class OneApiClient extends OpenAiClient {

    private String baseUrl;
    private String apiVersion;
    private String model;
    private Integer maxTokens;
    private OkHttpClient okHttpClient;
    private OpenAiApi openAiApi;
    private SearchApi searchApi;
    private boolean logStreamingResponses = true;
    private String systemMessage;

    public OneApiClient() {
    }

    public OneApiClient(String apiKey) {
        this(new SearchApiBuilder<>().openAiApiKey(apiKey));
    }

    public OneApiClient(SearchApiBuilder serviceBuilder) {
        this.baseUrl = serviceBuilder.baseUrl;
        this.apiVersion = serviceBuilder.apiVersion;
        this.model = serviceBuilder.model;
        this.systemMessage = serviceBuilder.systemMessage;

        OkHttpClient.Builder okHttpClientBuilder = new OkHttpClient.Builder()
                .callTimeout(serviceBuilder.callTimeout)
                .connectTimeout(serviceBuilder.connectTimeout)
                .readTimeout(serviceBuilder.readTimeout)
                .writeTimeout(serviceBuilder.writeTimeout);

        if (serviceBuilder.dispatcher != null) {
            okHttpClientBuilder.dispatcher(serviceBuilder.dispatcher);
        }

        if (serviceBuilder.openAiApiKey == null && serviceBuilder.azureApiKey == null) {
            throw new IllegalArgumentException("openAiApiKey OR azureApiKey must be defined");
        }
        if (serviceBuilder.openAiApiKey != null && serviceBuilder.azureApiKey != null) {
            throw new IllegalArgumentException("openAiApiKey AND azureApiKey cannot both be defined at the same time");
        }
        if (serviceBuilder.logRequests) {
            log.info("Added RequestLoggingInterceptor with level: {}", serviceBuilder.logLevel);
            okHttpClientBuilder.addInterceptor(new RequestLoggingInterceptor(serviceBuilder.logLevel));
        }
        if (serviceBuilder.logResponses) {
            log.info("Added ResponseLoggingInterceptor with level: {}", serviceBuilder.logLevel);
            okHttpClientBuilder.addInterceptor(new ResponseLoggingInterceptor(serviceBuilder.logLevel));
        }

        if (serviceBuilder.openAiApiKey != null) {
            okHttpClientBuilder.addInterceptor(new AuthorizationHeaderInjector(serviceBuilder.openAiApiKey));
        } else {
            okHttpClientBuilder.addInterceptor(new ApiKeyHeaderInjector(serviceBuilder.azureApiKey));
        }

        Map<String, String> headers = new HashMap<>();
        if (serviceBuilder.organizationId != null) {
            headers.put("OpenAI-Organization", serviceBuilder.organizationId);
        }
        if (serviceBuilder.userAgent != null) {
            headers.put("User-Agent", serviceBuilder.userAgent);
        }
        if (serviceBuilder.customHeaders != null) {
            headers.putAll(serviceBuilder.customHeaders);
        }
        if (!headers.isEmpty()) {
            okHttpClientBuilder.addInterceptor(new GenericHeaderInjector(headers));
        }

        if (serviceBuilder.proxy != null) {
            okHttpClientBuilder.proxy(serviceBuilder.proxy);
        }


        if (Objects.nonNull(serviceBuilder.maxTokens) && serviceBuilder.maxTokens > 0) {
            this.maxTokens = serviceBuilder.maxTokens;
        }

        this.logStreamingResponses = serviceBuilder.logStreamingResponses;

        this.okHttpClient = okHttpClientBuilder.build();

        Retrofit.Builder retrofitBuilder = new Retrofit.Builder().baseUrl(serviceBuilder.baseUrl).client(okHttpClient);

        if (serviceBuilder.persistTo != null) {
            retrofitBuilder.addConverterFactory(new PersistorConverterFactory(serviceBuilder.persistTo));
        }

        retrofitBuilder.addConverterFactory(JacksonConverterFactory.create(Json.OBJECT_MAPPER));

        this.openAiApi = retrofitBuilder.build().create(OpenAiApi.class);

        // Search API
        OkHttpClient searchOkHttpClient = new OkHttpClient.Builder()
                .callTimeout(serviceBuilder.callTimeout)
                .connectTimeout(serviceBuilder.connectTimeout)
                .readTimeout(serviceBuilder.readTimeout)
                .writeTimeout(serviceBuilder.writeTimeout)
                .addInterceptor(new RequestLoggingInterceptor(serviceBuilder.logLevel))
                .addInterceptor(new ResponseLoggingInterceptor(serviceBuilder.logLevel))
                .addInterceptor(new AuthorizationHeaderInjector(serviceBuilder.searchApiKey))
                .build();
        Retrofit.Builder searchRetrofitBuilder = new Retrofit.Builder().baseUrl(serviceBuilder.searchEndpoint)
                .client(searchOkHttpClient);
        searchRetrofitBuilder.addConverterFactory(JacksonConverterFactory.create(Json.OBJECT_MAPPER));
        this.searchApi = searchRetrofitBuilder.build().create(SearchApi.class);
    }

    @Override
    public void shutdown() {
        okHttpClient.dispatcher().executorService().shutdown();

        okHttpClient.connectionPool().evictAll();

        Cache cache = okHttpClient.cache();
        if (cache != null) {
            try {
                cache.close();
            } catch (IOException e) {
                log.error("Failed to close cache", e);
            }
        }
    }

    public static SearchApiBuilder builder() {
        return new SearchApiBuilder();
    }

    public static class SearchApiBuilder<T extends OpenAiClient, B extends SearchApiBuilder<T, B>> extends OpenAiBuilder<T, B> {
        private String searchEndpoint = "https://api.bochaai.com/v1/";
        private String searchApiKey;
        private SearchApi searchApi;

        public B searchApi(SearchApi searchApi) {
            this.searchApi = searchApi;
            return (B) this;
        }

        public B searchApiKey(String searchApiKey) {
            this.searchApiKey = searchApiKey;
            return (B) this;
        }

        public B searchEndpoint(String searchEndpoint) {
            this.searchEndpoint = searchEndpoint;
            return (B) this;
        }

        @Override
        public T build() {
            return (T) new OneApiClient(this);
        }
    }

    @Override
    public SyncOrAsyncOrStreaming<CompletionResponse> completion(OpenAiClientContext context, CompletionRequest request) {
        CompletionRequest syncRequest = CompletionRequest.builder().from(request).stream(false).build();

        return new RequestExecutor<>(
                openAiApi.completions(context.headers(), syncRequest, apiVersion),
                r -> r,
                okHttpClient,
                formatUrl("completions"),
                () -> CompletionRequest.builder().from(request).stream(true).build(),
                CompletionResponse.class,
                r -> r,
                logStreamingResponses);
    }

    @Override
    public SyncOrAsyncOrStreaming<String> completion(OpenAiClientContext context, String prompt) {
        CompletionRequest request = CompletionRequest.builder().prompt(prompt).build();

        CompletionRequest syncRequest = CompletionRequest.builder().from(request).stream(false).build();
        return new RequestExecutor<>(
                openAiApi.completions(context.headers(), syncRequest, apiVersion),
                CompletionResponse::text,
                okHttpClient,
                formatUrl("completions"),
                () -> CompletionRequest.builder().from(request).stream(true).build(),
                CompletionResponse.class,
                CompletionResponse::text,
                logStreamingResponses
        );
    }


    @Override
    public SyncOrAsyncOrStreaming<ChatCompletionResponse> chatCompletion(OpenAiClientContext context, ChatCompletionRequest request) {
        ChatCompletionRequest.Builder builder = ChatCompletionRequest.builder().from(request).stream(true);
        if (Objects.isNull(request.maxTokens()) || request.maxTokens() <= 0) {
            builder.maxTokens(this.maxTokens);
        }
        if (Objects.isNull(request.model())) {
            builder.model(this.model);
        }
        if (StringUtils.isNotBlank(this.systemMessage)) {
            builder.addSystemMessage(this.systemMessage);
        }
        if (Objects.isNull(request.stream())) {
            builder.stream(true);
        }

        ChatCompletionRequest syncRequest = builder.build();
        return new RequestExecutor<>(
                openAiApi.chatCompletions(context.headers(), syncRequest, apiVersion),
                Function.identity(),
                okHttpClient,
                formatUrl("chat/completions"),
                () -> syncRequest,
                ChatCompletionResponse.class,
                Function.identity(),
                logStreamingResponses
        );
    }

    @Override
    public SyncOrAsyncOrStreaming<String> chatCompletion(OpenAiClientContext context, String userMessage) {
        ChatCompletionRequest.Builder requestBuilder = ChatCompletionRequest.builder().addUserMessage(userMessage);
        requestBuilder.maxTokens(this.maxTokens).model(this.model).addSystemMessage(this.systemMessage);

        ChatCompletionRequest syncRequest = requestBuilder.stream(true).build();
        return new RequestExecutor<>(
                openAiApi.chatCompletions(context.headers(), syncRequest, apiVersion),
                ChatCompletionResponse::content,
                okHttpClient,
                formatUrl("chat/completions"),
                () -> syncRequest,
                ChatCompletionResponse.class,
                r -> r.choices().get(0).delta().content(),
                logStreamingResponses
        );
    }

    /********************************************* chatCompletion 【非流式接口】 *******************************************/
    public SyncOrAsync<ChatCompletionResponse> syncChatCompletion(ChatCompletionRequest request) {
        return syncChatCompletion(new OpenAiClientContext(), request);
    }

    public SyncOrAsync<ChatCompletionResponse> syncChatCompletion(OpenAiClientContext clientContext,
                                                                  ChatCompletionRequest request) {
        ChatCompletionRequest syncRequest = ChatCompletionRequest.builder().from(request).stream(false).build();
        return new RequestExecutor<>(
                openAiApi.chatCompletions(clientContext.headers(), syncRequest, apiVersion),
                Function.identity()
        );
    }

    public SyncOrAsync<String> syncChatCompletion(String userMessage) {
        return syncChatCompletion(new OpenAiClientContext(), userMessage);
    }

    public SyncOrAsync<String> syncChatCompletion(OpenAiClientContext clientContext, String userMessage) {
        ChatCompletionRequest.Builder requestBuilder = ChatCompletionRequest.builder().addUserMessage(userMessage);
        requestBuilder.maxTokens(this.maxTokens).model(this.model).addSystemMessage(this.systemMessage).stream(false);

        return new RequestExecutor<>(
                openAiApi.chatCompletions(clientContext.headers(), requestBuilder.build(), apiVersion),
                ChatCompletionResponse::content
        );
    }


    /**
     * 聊天搜索
     *
     * @param userMessage 用户消息
     * @return {@link Flux }<{@link ChatCompletionResponse }>
     */
    public SyncOrAsyncOrStreaming<ChatCompletionResponse> chatSearchStreamingCompletion(String userMessage) {
        SearchResponse searchResponse = new SyncRequestExecutor<>(
                searchApi.webSearch(SearchRequest.builder().enable(true).query(userMessage).build()),
                searchResponse1 -> searchResponse1).execute();
        if (200 == searchResponse.getCode()) {
            String formatted = Utils.format(userMessage, searchResponse.getData().getWebPages().getValue());
            if (Objects.nonNull(formatted)) {
                return this.chatCompletion(new OpenAiClientContext(), ChatCompletionRequest.builder().stream(true)
                        .model(this.model).addUserMessage(formatted).build());
            }
        }
        return this.chatCompletion(new OpenAiClientContext(),
                ChatCompletionRequest.builder().stream(true).model(this.model).addUserMessage(userMessage).build());
    }

    @Override
    public SyncOrAsync<ModerationResponse> moderation(OpenAiClientContext context, ModerationRequest request) {
        return new RequestExecutor<>(openAiApi.moderations(context.headers(), request, apiVersion), r -> r);
    }

    @Override
    public SyncOrAsync<ModerationResult> moderation(OpenAiClientContext context, String input) {
        ModerationRequest request = ModerationRequest.builder().input(input).build();

        return new RequestExecutor<>(openAiApi.moderations(context.headers(), request, apiVersion),
                r -> r.results().get(0));
    }

    @Override
    public ModelsResponse models() {
        return new RequestExecutor<>(this.openAiApi.models(new HashMap<>(), apiVersion), r -> r, okHttpClient, null,
                null, ModelsResponse.class, null, logStreamingResponses).execute();
    }

    private String formatUrl(String endpoint) {
        return baseUrl + endpoint + apiVersionQueryParam();
    }

    private String apiVersionQueryParam() {
        if (apiVersion == null || apiVersion.trim().isEmpty()) {
            return "";
        }
        return "?api-version=" + apiVersion;
    }
}
