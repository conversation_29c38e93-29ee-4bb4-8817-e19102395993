package com.sankuai.deepcode.ai.enums;

public enum EmbeddingSearchTypeEnum {
    CODE(0, "code", "源码向量查询"),
    DESC(1, "desc", "源码语义查询"),
    KEY_CODE(2, "keyCode", "源码关键字查询"),
    KEY_DESC(3, "keyDesc", "源码语义关键字查询");

    private int code;
    private String name;
    private String desc;

    EmbeddingSearchTypeEnum(int code, String name, String desc) {
        this.code = code;
        this.name = name;
        this.desc = desc;
    }

    public int getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public String getDesc() {
        return desc;
    }

    @Override
    public String toString() {
        return "EmbeddingSearchTypeEnum{" +
                "code=" + code +
                ", name='" + name + '\'' +
                ", desc='" + desc + '\'' +
                '}';
    }
}
