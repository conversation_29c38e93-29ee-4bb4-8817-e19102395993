package com.sankuai.deepcode.ai.agnets.callback;

import com.sankuai.deepcode.ai.llm.model.chat.ChatMsgRes;
import com.sankuai.deepcode.ai.llm.openai.chat.ChatCompletionResponse;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

public interface StreamCompleteCallback {
    void onComplete(SseEmitter emitter, ChatMsgRes chatMsgRes, ChatCompletionResponse chatCompletionResponse, PlanCompleteCallback planCompleteCallback);
}
