## `module` 说明

| 模块名称                     | 模块简介     | 详细说明                                                                     |
 --------------------------|----------|--------------------------------------------------------------------------|
 deepcode-ai              | 智能化封装    | 封装与 LLM 交互的逻辑，封装 `Embedding`相关的读写能力                                      |
 deepcode-analysis-server | 代码分析服务   | 代码解析的服务，调用 `dao`, `ast`，`ai`等 完成工程的解析和存储                                 |
 deepcode-ast             | AST 解析服务 | 专注 `AST` 语法解析，基于`Antlr4` 封装的纯解析`Java`/`Python`/`JavaScript`工程            |
 deepcode-commons         | 公共工具模块   | 一些通用的工具，供其他各个模块使用                                                        |
 deepcode-dao             | 数据库操作    | 基于`Mybatis-plus`封装的对 `MySQL` 的操作，所有的对`DB`的操作(查询、更新、插入等)不得跳出 `dao` 模块     |
 deepcode_manage_server   | 管理后台服务   | `DeepCode`平台对接的后端服务API，可调用`ai`,`dao`,`commons`服务，不能调用 `ast`,`analysis`服务 |
